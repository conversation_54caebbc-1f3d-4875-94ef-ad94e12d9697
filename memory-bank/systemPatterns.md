# System Patterns

This document outlines the key architectural patterns, technical decisions, and component relationships within the AI Reel Generator project.

## Architecture

The application follows a serverless-first architecture pattern, leveraging Next.js for both frontend and backend API routes. Resource-intensive processes like video generation (including asset creation and data saving for various workflows) and video rendering are handled asynchronously in serverless environments (AWS Lambda) and via a background task processing system (Inngest). A unified data storage approach is used for render-ready video data in a single database table (`video_data`). Cloud storage (AWS S3) is utilized for storing user-uploaded assets and final rendered videos.

## Key Technical Decisions

-   **Frontend Framework:** Next.js is chosen for its React-based framework, server-side rendering capabilities, and integrated API routes.
-   **Database:** PostgreSQL managed via Drizzle ORM is used for database interactions. A key decision is to use the `video_data` table as a **unified repository for all render-ready video data**, regardless of the original workflow. Workflow-specific data is stored within a `jsonb` field (`workflow_data`) in this table, using the `templateId` as a discriminator.
-   **Video Generation:** Remotion is utilized for programmatically generating videos using React components. The rendering process is executed in a scalable cloud environment (AWS Lambda) using `@remotion/lambda`.
-   **AI Services:** External APIs are used for core content generation (script, image generation, audio generation, captioning, media evaluation, etc.). These integrations are orchestrated by backend processes, primarily Inngest functions.
-   **Payment Gateway:** Lemon Squeezy is integrated for handling credit purchases.
-   **Authentication:** Clerk is used for user authentication and management.
-   **Cloud Storage:** AWS S3 is used for storing user-uploaded image/video assets and the final rendered video files.
-   **Background Task Processing:** **Inngest** is used to handle long-running, asynchronous workflows, including AI video generation, Stock Media video generation, Reddit/Twitter video generation, Meme video generation, and Podcast Clipper processing. Inngest functions orchestrate calls to AI services, handle data processing, and save render-ready data to the unified `video_data` table.

## Component Relationships

-   **Frontend (Next.js):**
    -   Video creation pages (`app/dashboard/create-new-short/.../page.jsx`) capture user input and trigger **server actions**.
    -   Server actions (e.g., `actions/aiVideoGeneration.js`, `actions/redditVideoGeneration.js`) perform basic validation and trigger **Inngest events**.
    -   The `VideoList.jsx` component fetches video data from `/api/user-videos` (which reads from `video_data`).
    -   The `PlayerDialog.jsx` component is triggered from the video list, fetches a specific video record from `/api/video/[videoId]`, uses the data (including `templateId` and `workflow_data`) for preview in the Remotion Player, and triggers the rendering process by calling the `/api/render-lambda` backend endpoint with the `videoId`.

-   **Backend API Routes (Next.js):**
    -   `/api/user-videos` and `/api/video/[videoId]` fetch video data from the **unified `video_data` table**.
    -   `/api/render-lambda` (POST) fetches the video record from **`video_data`**, extracts `templateId` and data from `workflow_data`, and invokes the AWS Lambda function with this data as `inputProps`.
    -   `/api/render-lambda` (GET) fetches the render status and `renderedVideoUrl` from the **`video_data` table**.
    -   Other APIs handle specific tasks like asset uploads (`/api/upload-asset`) or interactions with external services, often called by Inngest functions.

-   **Database (PostgreSQL with Drizzle ORM):**
    -   The **`video_data` table** is the central table for render-ready data, storing common fields and workflow-specific data in `workflow_data` (JSONB), identified by `templateId`. It also stores `renderId`, `bucketName`, and `renderedVideoUrl`.
    -   Other tables (`users`, `reddit_post_data`, `stockMediaVideo`, `uploadedFile`, `clip`) store user information, original source data, or intermediate processing results, potentially linked to records in `video_data`.

-   **Inngest Functions:**
    -   Triggered by server actions/events.
    -   Orchestrate multi-step workflows (AI asset generation, stock media processing, etc.).
    -   Interact with external AI APIs and other backend services.
    -   **Save the final render-ready data into the unified `video_data` table.**

-   **AWS Lambda Function:**
    -   Invoked by the `/api/render-lambda` POST endpoint.
    -   Executes the Remotion Lambda bundle code.
    -   Receives the unified `video_data` as `inputProps`.
    -   **Requires modification to interpret the `video_data` structure (using `templateId` and `workflow_data`) and pass data to the correct Remotion composition.**
    -   Renders the video.
    -   Saves the output to AWS S3.
    -   **Requires modification to update the `renderedVideoUrl` and status in the `video_data` table.**

-   **External AI APIs:** Provide content generation services, called by Inngest functions.
-   **Payment Gateway (Lemon Squeezy):** Handles billing, integrated via backend APIs and webhooks.
-   **Cloud Storage (AWS S3):** Stores assets and rendered videos.

## Critical Implementation Paths

-   **Video Generation Workflows (Inngest-Driven):**
    Frontend Page -> Server Action -> Inngest Event -> Inngest Function (orchestrates AI calls, processing) -> **Saves render-ready data to unified `video_data` table**.

-   **Video Rendering Flow (Backend-Triggered):**
    Frontend (PlayerDialog) -> Calls `/api/render-lambda` (POST with `videoId`) -> Backend fetches record from **`video_data`** -> Invokes AWS Lambda (with `video_data` as `inputProps`) -> AWS Lambda (Remotion bundle) **(Requires modification to interpret `video_data`)** -> Renders video -> Saves to S3 -> **Updates `renderedVideoUrl` and status in `video_data` table** -> Frontend (PlayerDialog) polls `/api/render-lambda` (GET with `videoId`) -> Backend fetches status/URL from **`video_data`** -> Returns status/URL -> Frontend updates UI (shows progress, enables download).

-   **Billing Flow:** User initiates credit purchase -> Frontend calls billing API -> Backend interacts with Payment Gateway -> Payment Gateway webhook -> Backend webhook handler -> Updates user credits in `users` table.

## Known Issues

-   **BLOCKED:** Enhancing AWS Lambda Function Logic requires modifying the Remotion Lambda bundle code and redeploying it, which cannot be done with current tools.
