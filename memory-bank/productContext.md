# Product Context

This document describes the purpose, target audience, and user experience goals for the AI Reel Generator project.

## Purpose

The AI Reel Generator aims to simplify and accelerate the process of creating short, engaging video content for social media and other platforms. It leverages artificial intelligence to automate scriptwriting, image selection, and audio generation, allowing users to quickly produce videos without requiring extensive video editing skills or resources. It supports multiple video creation workflows, including AI-driven content generation, incorporating user-provided assets (User-Generated Content), and generating videos directly from existing content like Reddit posts and Twitter tweets, and clipping highlights from podcasts.

## Problems Solved

- **Time-consuming video creation:** Manually creating short videos with script, visuals, and audio is a lengthy process.
- **Lack of video editing skills:** Many users lack the technical skills or software to create professional-looking videos.
- **Content generation challenges:** Coming up with ideas, writing scripts, and finding relevant visuals can be difficult.
- **Integrating user-provided assets:** Providing a streamlined way for users to incorporate their own images and videos into AI-assisted generation.
- **Clipping highlights from long-form content:** Automating the process of identifying and extracting key moments from podcasts or videos.

## How it Should Work (Workflows)

The application supports several distinct video creation workflows, all leveraging asynchronous backend processing (via Inngest) and saving render-ready data to a unified database table (`video_data`) for rendering:

-   **AI-Driven Video:** Users provide a topic or script. The system generates script, images, audio, and captions using AI services.
-   **Stock Media Video:** Users provide a prompt. The system generates a script, searches and evaluates stock media, and generates a voiceover.
-   **Reddit Post Video:** Users provide Reddit post details. The system formats the post content for video and allows selecting a background video.
-   **Twitter Post Video:** Users provide Twitter post details. The system formats the tweet content for video and allows selecting a background video.
-   **Meme Video:** Users upload a video/image and add text/styling. The system processes the video source and applies the meme elements.
-   **Podcast Clipper:** Users provide a YouTube link or upload a file. The system analyzes the audio/video, identifies clips, and extracts them.

For all workflows, after the initial data/asset generation and saving to the unified `video_data` table, the user can view a preview and trigger the final video rendering process via the Player Dialog. The rendering is handled asynchronously by AWS Lambda, and the final video URL is saved back to the `video_data` table.

## SAS Features (Monetization)

Credits System:
Users are allocated a certain number of credits. Each video generation or rendering action consumes credits.
Billing Section:
Displays the user's current credit balance.
Offers plans or packages to purchase additional credits.
Payment Integration:
A payment gateway service is integrated to securely process transactions when users buy credits.

## User Experience Goals

- **Simplicity:** The interface should be intuitive and easy to navigate.
- **Speed:** Video generation and rendering should be as fast as possible, with asynchronous processing providing responsiveness.
- **Quality:** Generated videos should be high-quality and visually appealing.
- **Flexibility:** Users should have enough options to customize videos and incorporate their own assets.
- **Reliability:** The generation, rendering, and billing systems should be reliable and error-free.
- **Unified Experience:** Despite different workflows, the process of viewing previews, triggering renders, and downloading videos should be consistent (handled via the Player Dialog).
