# Tech Context

This document details the technologies, development setup, constraints, and dependencies for the AI Reel Generator project.

## Technologies Used

- **Frontend:**
    - React, Next.js
    - Tailwind CSS, Shadcn UI (for UI components)
    - React Context API (for global state management, e.g., `UserDetailContext`)
    - Sonner (for toast notifications)
- **Backend:** Next.js API Routes, Node.js
- **Database:** PostgreSQL (via Drizzle ORM)
- **Video Generation:** Remotion
- **AI Services:**
    - Script Generation: OpenAI (GPT series) or similar
    - Image Generation: OpenAI (DALL-E) or similar
    - Audio Generation: Eleven Labs or similar
    - Other AI services for media evaluation, etc.
- **Payment Gateway:** Lemon Squeezy
- **Authentication:** Clerk
- **Background Task Processing:** **Inngest**

## Development Setup

- Node.js (v18 or later)
- pnpm package manager
- PostgreSQL database
- Environment variables for API keys and database connection strings
- AWS account for Remotion Lambda and S3

## Technical Constraints

- Reliance on external APIs for core functionality.
- Video generation time is dependent on Inngest workflow execution, external API response times, and Remotion rendering.
- Hosting environment must support Next.js, a PostgreSQL database, AWS Lambda, and S3.

## Dependencies

- See `package.json` for a comprehensive list of project dependencies. Key dependencies include:
    - `next`, `react`, `react-dom`
    - `drizzle-orm`, `drizzle-kit` (for database)
    - `remotion`, `@remotion/lambda` (for video generation and cloud rendering)
    - `@clerk/nextjs` (for authentication)
    - `lemonsqueezy.js` (or similar for payment gateway interaction)
    - `openai`, `elevenlabs`, `@deepgram/sdk`, `@google/generative-ai`, `@runware/sdk-js` (or similar AI SDKs/libraries)
    - `tailwindcss`, `shadcn-ui` components
    - `sonner` (for notifications)
    - `lucide-react` (for icons)
    - `inngest` (for background task processing)
    - `@aws-sdk/client-s3` (for S3 interaction)

## Tool Usage Patterns

- **VS Code:** Primary IDE for development.
- **pnpm:** Used for package management.
- **Drizzle Kit:** Used for database schema migrations.
- **Remotion CLI:** Used for local rendering/preview and bundling for cloud rendering.
- **Git:** Version control.
- **Server Actions:** Used on the frontend to trigger backend processes (specifically Inngest events).
- **Inngest:** Used for orchestrating asynchronous backend workflows.
- **Fetch API / Axios:** Used within backend code (API routes, Inngest functions) to interact with external APIs and potentially other internal services.
- **AWS SDK:** Used within backend code (Inngest functions, potentially API routes) for interacting with AWS services like S3.
- **Remotion Lambda Client (`@remotion/lambda/client`):** Used in the `/api/render-lambda` backend route to initiate and check the status of Remotion Lambda renders.
