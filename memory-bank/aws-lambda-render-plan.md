# Plan: Integrate AWS Lambda for Remotion Video Rendering

**Objective:** Modify the application to trigger an AWS Lambda function for Remotion video rendering when the user clicks the "Render Video" button in the Player Dialog.

**Current Situation:**
- The `PlayerDialog.jsx` component currently attempts to call a non-existent `/api/render-local` endpoint for rendering.
- The goal is to switch from local rendering to using AWS Lambda.

**Plan Steps:**

1.  **Update Frontend (`PlayerDialog.jsx`)**:
    *   Modify the `handleRenderRequest` function to change the API endpoint it calls from `/api/render-local` to a new endpoint, for example, `/api/render-lambda`.
    *   Adjust the user feedback messages (toasts, loading states) to reflect that a cloud render request is being initiated.

2.  **Create New Backend API Route (`app/api/render-lambda/route.js`)**:
    *   Create a new file at `app/api/render-lambda/route.js`.
    *   Implement a POST handler in this file that will receive the necessary data from the frontend (like `videoId`, `compositionId`, and `inputProps`).
    *   Inside this handler, use the AWS SDK to invoke your AWS Lambda function, passing the received data as the payload.
    *   Return an appropriate response to the frontend (e.g., a 202 Accepted status) to indicate that the Lambda invocation request has been sent successfully.
    *   *(Note: This step will require configuring AWS credentials and region in your backend environment.)*

3.  **Set up AWS Lambda Function**:
    *   If you don't already have one, create an AWS Lambda function specifically for Remotion rendering (e.g., `remotion-video-renderer`).
    *   Configure the Lambda function with the appropriate runtime (Node.js is suitable for Remotion Lambda).
    *   Set up necessary IAM permissions for the Lambda function, particularly allowing it to write the rendered video file to an S3 bucket.
    *   Configure any necessary environment variables for the Lambda function (e.g., the name of the S3 bucket where videos will be stored).

4.  **Implement Remotion Rendering Logic in Lambda**:
    *   Package your Remotion project code and its dependencies, including `@remotion/lambda`, for deployment to the AWS Lambda environment.
    *   Implement the Lambda handler function. This function will receive the payload sent from your new backend API route.
    *   Use the `@remotion/lambda` library within the handler to render the specified Remotion composition using the provided input props.
    *   Save the final rendered video output to the designated S3 bucket, using a unique identifier like the `videoId` for the filename.

5.  **Implement Status Tracking and Download (Subsequent Steps)**:
    *   After the initial Lambda invocation is working, we will need to implement a mechanism for the frontend to track the rendering status (since Lambda rendering is asynchronous). This could involve polling a status endpoint or using WebSockets.
    *   Once the video is rendered and saved to S3, we will need to provide a way for the user to download it, likely by generating a pre-signed URL for the S3 object.

**Flow Diagram:**

```mermaid
graph TD
    A[User clicks Render in PlayerDialog] --> B{PlayerDialog.jsx};
    B -- POST /api/render-lambda --> C[New API Route: app/api/render-lambda/route.js];
    C -- Invoke Lambda (Payload: videoId, compositionId, inputProps) --> D[AWS Lambda Function (remotion-video-renderer)];
    D --> E[Remotion Lambda Renders Video];
    E -- Save Video --> F[AWS S3 Bucket];
    F --> G[Update Video Metadata/Status (e.g., in DB)];
    G --> H{Frontend (PlayerDialog.jsx)};
    H -- Poll/Receive Status Update --> I[Rendering Complete];
    I --> J[Enable Download Button];
    J -- Click Download --> K[Call /api/video/[videoId]/download];
    K -- Generate S3 Pre-signed URL --> L[AWS S3 Bucket];
    L -- Redirect/Download --> M[User Downloads Video];

    %% Styling for clarity
    classDef aws fill:#FF9900,stroke:#333,stroke-width:2px;
    class D,F,L aws;