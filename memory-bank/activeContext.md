# Active Context

This document tracks the current work focus, recent changes, next steps, active decisions, and key learnings for the AI Reel Generator project.

## Current Work Focus

Implementing a scalable AWS Lambda based Remotion video rendering pipeline using a unified data storage approach. Currently blocked on modifying the Remotion Lambda bundle code.

## Recent Changes

- Implemented Inngest functions and server actions for AI, Reddit, Twitter, and Meme video generation workflows.
- Refactored Stock Media and Podcast Clipper workflows to save render-ready data to the unified `video_data` table.
- Updated the `/api/render-lambda` endpoint to fetch video data from the unified `video_data` table and initiate renders.
- Updated the `PlayerDialog.jsx` component to work with the unified `video_data` structure for preview and rendering.
- Updated the database schema (`configs/schema.js`) to include rendering-related fields and a general `workflow_data` JSONB field in the `videoData` table.
- Removed the `useVideoGeneration` hook.
- Moved the `generateStockMediaVideo` Inngest function to a separate file (`app/inngest/functions/stockMediaVideoGeneration.js`).

## Next Steps

- **BLOCKED:** Enhance AWS Lambda Function Logic for Multiple Data Structures (Requires modifying Remotion Lambda bundle code and redeployment).
- Continue to maintain and update the memory bank as the project progresses and new work is Completed.

## Active Decisions and Considerations

- Successfully transitioned multiple video generation workflows to Inngest for asynchronous processing.
- Implemented a unified data storage strategy in the `video_data` table for render-ready data, improving scalability.
- Resolved Drizzle JSONB update syntax issue for the Podcast Clipper workflow using a fetch-modify-save approach.
- The primary remaining technical hurdle for the rendering pipeline is updating the Remotion Lambda bundle code.

## Learnings and Project Insights

- Inngest provides a robust framework for orchestrating asynchronous, multi-step generation workflows.
- Unifying render-ready data simplifies backend and frontend logic for the rendering pipeline.
- Careful consideration is needed when updating JSONB fields in Drizzle, sometimes requiring fetching and merging data in application code.
- Modifying the Remotion Lambda bundle code is a necessary step to adapt the rendering logic to new data structures.
