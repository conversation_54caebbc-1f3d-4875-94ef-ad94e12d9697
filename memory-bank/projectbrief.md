# Project Brief

This document serves as the foundational document for the AI Reel Generator project. It defines the core requirements, goals, and scope of the project.

## Project Goals

- To create an application that automatically generates short video reels based on user-provided topics or scripts.
- To provide a user-friendly interface for configuring video aspects like style, aspect ratio, voice, and captions.
- To integrate with necessary APIs for script generation, image generation, and audio generation.
- To implement a billing system for managing user credits.
- To allow users to view and manage their generated videos.

## Scope

The project includes:
- Frontend user interface (Next.js)
- Backend API endpoints for AI integrations and data persistence (Next.js API routes)
- Database for storing user data and video information (Drizzle ORM)
- Integration with AI services for content generation (e.g., OpenAI, Eleven Labs, etc.)
- Integration with a payment gateway for credit purchases (e.g., Lemon Squeezy)
- Video generation logic (Remotion)

## Out of Scope

- Advanced video editing features beyond the initial generation.
- Support for multiple languages (initially English only).
- Real-time video generation (generation will be an asynchronous process).

## Key Stakeholders

- Users: Individuals or businesses needing quick video content.
- Developers: The team building and maintaining the application.
- API Providers: Services providing AI capabilities.
- Payment Gateway Provider: Service handling transactions.
