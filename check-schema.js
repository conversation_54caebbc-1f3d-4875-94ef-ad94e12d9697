import 'dotenv/config';
import { drizzle } from 'drizzle-orm/neon-serverless';
import { Pool } from '@neondatabase/serverless';
import * as schema from './configs/schema.js';

// Log environment variables for debugging
console.log('DATABASE_URL:', process.env.DATABASE_URL ? 'Set' : 'Not set');

const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: true
});

const db = drizzle(pool, { schema });

async function checkSchema() {
  try {
    console.log('Checking database schema...');
    
    // Check if table exists
    const result = await pool.query(
      `SELECT column_name, data_type
       FROM information_schema.columns
       WHERE table_name = 'video_data'`
    );

    console.log('Current columns in video_data table:');
    console.table(result.rows);

  } catch (error) {
    console.error('Error checking schema:', error);
  } finally {
    await pool.end();
  }
}

checkSchema();
