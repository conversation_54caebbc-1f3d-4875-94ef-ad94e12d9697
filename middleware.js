import { clerkMiddleware, createRouteMatcher } from '@clerk/nextjs/server'

// Add root page ("/"), terms of service, and privacy policy to public routes
const isPublicRoute = createRouteMatcher([
  '/', // root page is public
  '/sign-in(.*)',
  '/sign-up(.*)',
  '/terms-of-service(.*)', // terms of service page is public
  '/privacy-policy(.*)',   // privacy policy page is public
  // Public API routes for Inngest functions
  '/api/webhook(.*)', // webhook route is public
  '/api/inngest(.*)', // inngest API route is public
  '/api/generate-stock-media-script(.*)',
  '/api/pexels-search(.*)',
  '/api/pixabay-search(.*)',
  '/api/pexels-video-search(.*)',
  '/api/generate-audio(.*)',
  '/api/evaluate-media(.*)'
])

export default clerkMiddleware(async (auth, req) => {
  if (!isPublicRoute(req)) {
    await auth.protect()
  }
})

export const config = {
  matcher: [
    // Skip Next.js internals and all static files, unless found in search params
    '/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)',
    // Always run for API routes
    '/(api|trpc)(.*)',
  ],
}