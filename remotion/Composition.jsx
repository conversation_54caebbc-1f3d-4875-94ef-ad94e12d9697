import {
	Composition,
	getInputProps,
	AbsoluteFill
} from 'remotion';
import React from 'react';
import { getTemplateComponent } from './templates';

// --- Aspect Ratio Helper ---
const BASE_DIMENSION = 1080;

const getDimensionsFromRatio = (aspectRatioString) => {
	let wRatio = 9, hRatio = 16;
	try {
		[wRatio, hRatio] = aspectRatioString.split(':').map(Number);
		if (!wRatio || !hRatio || isNaN(wRatio) || isNaN(hRatio)) {
			throw new Error('Invalid ratio numbers');
		}
	} catch (e) {
		console.warn(`Invalid aspect ratio string: ${aspectRatioString}. Defaulting to 9:16.`);
		wRatio = 9;
		hRatio = 16;
	}

	if (wRatio > hRatio) { // Landscape
		return { width: BASE_DIMENSION, height: Math.round(BASE_DIMENSION * (hRatio / wRatio)) };
	} else if (hRatio > wRatio) { // Portrait
		return { width: Math.round(BASE_DIMENSION * (wRatio / hRatio)), height: BASE_DIMENSION };
	} else { // Square
		return { width: BASE_DIMENSION, height: BASE_DIMENSION };
	}
};

// --- Composition Settings ---
const COMPOSITION_ID = 'MyVideo';
const FPS = 30;
const DURATION_IN_SECONDS = 10;
const COMPOSITION_DURATION_FRAMES = DURATION_IN_SECONDS * FPS;

// --- Default Video Data for Studio Preview ---
const studioPreviewData = {
	audioUrl: 'https://www.learningcontainer.com/wp-content/uploads/2020/02/Kalimba.mp3?_=1',
	images: [
		'https://picsum.photos/720/1280?random=1',
		'https://picsum.photos/720/1280?random=2',
		'https://picsum.photos/720/1280?random=3',
	],
	captionJson: [
		{ "text": "Studio", "start": 0, "end": 500 },
		{ "text": "Preview", "start": 500, "end": 1200 },
		{ "text": "Mode", "start": 1200, "end": 2000 },
	],
	captionStyle: { color: 'lightblue' },
	captionContainerStyle: { paddingBottom: 150 },
	aspectRatio: '9:16',
	templateId: 'default',
};

// --- Helper function to create template props ---
const createTemplateProps = (videoData, workflowData, templateId) => {
	const baseProps = {
		aspectRatio: videoData.aspectRatio,
		templateId: videoData.templateId,
		audioUrl: videoData.audioUrl,
		captionJson: videoData.captionJson,
		captionStyle: videoData.captionStyleJson,
		captionContainerStyle: videoData.captionContainerStyle,
	};

	switch (templateId) {
		case 'MyVideo':
		case 'default':
			return {
				...baseProps,
				images: videoData.images,
				voice: videoData.voice,
				audioSpeed: videoData.audioSpeed,
				backgroundMusic: videoData.backgroundMusic,
				estimatedDurationSeconds: videoData.estimatedDurationSeconds,
			};

		case 'RedditPostVideo':
			if (workflowData?.type === 'reddit-video') {
				return {
					...baseProps,
					title: workflowData.redditPost?.title,
					subreddit: workflowData.redditPost?.subreddit,
					username: workflowData.redditPost?.username,
					postContent: workflowData.redditPost?.postContent,
					upvotes: workflowData.redditPost?.upvotes,
					comments: workflowData.redditPost?.comments,
					avatar: workflowData.redditPost?.avatar,
					badge: workflowData.redditPost?.badge,
					darkMode: workflowData.redditPost?.darkMode,
					wideLayout: workflowData.redditPost?.wideLayout,
					approximateCounts: workflowData.redditPost?.approximateCounts,
					hideTrophies: workflowData.redditPost?.hideTrophies,
					hideUpvotes: workflowData.redditPost?.hideUpvotes,
					hideComments: workflowData.redditPost?.hideComments,
					hideShare: workflowData.redditPost?.hideShare,
					redditPostX: workflowData.redditPost?.redditPostX,
					redditPostY: workflowData.redditPost?.redditPostY,
					redditPostScale: workflowData.redditPost?.redditPostScale,
					backgroundVideoUrl: workflowData.backgroundVideoUrls?.[0],
				};
			}
			break;

		case 'TwitterPostVideo':
			if (workflowData?.type === 'twitter-video') {
				return {
					...baseProps,
					username: workflowData.twitterPost?.username,
					tweetContent: workflowData.twitterPost?.tweetContent,
					likes: workflowData.twitterPost?.likes,
					retweets: workflowData.twitterPost?.retweets,
					avatar: workflowData.twitterPost?.avatar,
					handle: workflowData.twitterPost?.handle,
					darkMode: workflowData.twitterPost?.darkMode,
					twitterPostX: workflowData.twitterPost?.twitterPostX,
					twitterPostY: workflowData.twitterPost?.twitterPostY,
					twitterPostScale: workflowData.twitterPost?.twitterPostScale,
					backgroundVideoUrl: workflowData.backgroundVideoUrls?.[0],
				};
			}
			break;

		case 'FactBombTemplate':
			if (workflowData?.type === 'fact-bomb') {
				return {
					...baseProps,
					backgroundMusic: videoData.backgroundMusic,
					facts: workflowData.facts,
				};
			}
			break;

		case 'InspoQuoteTemplate':
			if (workflowData?.type === 'inspo-quote') {
				return {
					...baseProps,
					backgroundMusic: videoData.backgroundMusic,
					quote: workflowData.quote,
					author: workflowData.author,
					backgroundImageUrl: workflowData.backgroundImageUrl,
					quoteAnimation: workflowData.quoteAnimation,
				};
			}
			break;

		case 'Template1':
			if (workflowData?.type === 'template1') {
				return {
					...baseProps,
					images: videoData.images,
				};
			}
			break;

		case 'Template2':
			if (workflowData?.type === 'template2') {
				return {
					...baseProps,
					images: videoData.images,
				};
			}
			break;

		case 'Template3':
			if (workflowData?.type === 'template3') {
				return {
					...baseProps,
					images: videoData.images,
					backgroundMusic: videoData.backgroundMusic,
				};
			}
			break;

		case 'Template4':
			if (workflowData?.type === 'template4') {
				return {
					...baseProps,
					images: videoData.images,
					backgroundMusic: videoData.backgroundMusic,
				};
			}
			break;

		case 'StockMediaVideo':
			if (workflowData?.type === 'stock-media-video') {
				console.log('StockMediaVideo: Processing workflow data:', {
					scenes: workflowData.scenes?.length || 0,
					selectedMedia: workflowData.selectedMedia?.length || 0,
					workflowData
				});

				// Transform the data to match what StockMediaVideoComposition expects
				const transformedScenes = workflowData.scenes?.map(scene => {
					// Find the corresponding selected media for this scene
					const selectedMediaForScene = workflowData.selectedMedia?.find(
						media => media.scene_number === scene.scene_number
					);

					console.log(`Scene ${scene.scene_number}:`, {
						hasSelectedMedia: !!selectedMediaForScene,
						selectedMedia: selectedMediaForScene?.selectedMedia,
						sceneData: scene
					});

					return {
						...scene,
						// Add the media property that StockMediaVideoComposition expects
						media: selectedMediaForScene?.selectedMedia || null,
						// Ensure duration is set (default to 5 seconds if not provided)
						duration: scene.duration || 5
					};
				}) || [];

				console.log('StockMediaVideo: Transformed scenes:', transformedScenes);

				// StockMediaVideoComposition expects videoData with workflow_data
				// So we need to pass the entire videoData object with the transformed scenes
				return {
					videoData: {
						...videoData,
						workflow_data: {
							...workflowData,
							scenes: transformedScenes
						}
					}
				};
			}
			break;

		case 'MemeVideoTemplate':
			if (workflowData?.type === 'meme-video') {
				return {
					...baseProps,
					videoSource: workflowData.videoSource,
					memeText: workflowData.memeText,
					font: workflowData.font,
					fontSize: workflowData.fontSize,
					textColor: workflowData.textColor,
					textOutline: workflowData.textOutline,
					outlineColor: workflowData.outlineColor,
					outlineThickness: workflowData.outlineThickness,
					textShadow: workflowData.textShadow,
					backgroundColor: workflowData.backgroundColor,
					textPosition: workflowData.textPosition,
					videoStartTime: workflowData.videoStartTime,
					videoEndTime: workflowData.videoEndTime,
					useOriginalAudio: workflowData.useOriginalAudio,
					backgroundMusic: workflowData.backgroundMusic,
					originalAudioVolume: workflowData.originalAudioVolume,
					backgroundMusicVolume: workflowData.originalAudioVolume,
				};
			}
			break;

		case 'PodcastClipVideo':
			if (workflowData?.type === 'podcast-clipper') {
				return {
					...baseProps,
					uploadedFileId: workflowData.uploadedFileId,
					userPrompt: workflowData.userPrompt,
					numberOfClips: workflowData.numberOfClips,
					requestId: workflowData.requestId,
					clips: workflowData.clips,
				};
			}
			break;

		default:
			console.warn(`Unknown templateId: ${templateId}. Using default props.`);
			return { videoData };
	}

	// Fallback if workflow type doesn't match
	console.warn(`Template ${templateId} workflow type mismatch. Expected: ${templateId.toLowerCase()}, Got: ${workflowData?.type}`);
	return { videoData };
};

// --- Main Composition Component ---
export const MyComposition = ({ videoData }) => {
	if (!videoData) {
		console.error("CRITICAL REMOTION ERROR: videoData is missing in inputProps.");
		return (
			<AbsoluteFill style={{ backgroundColor: 'red', justifyContent: 'center', alignItems: 'center', fontSize: 30, color: 'white' }}>
				Error: Video data missing.
			</AbsoluteFill>
		);
	}

	const { redditDurationFrames, twitterDurationFrames } = videoData;

	// Calculate duration based on videoData
	let durationInFrames;
	if (videoData?.templateId === 'RedditPostVideo') {
		durationInFrames = redditDurationFrames || (45 * FPS);
	} else if (videoData?.templateId === 'TwitterPostVideo') {
		durationInFrames = twitterDurationFrames || (45 * FPS);
	} else if (videoData?.estimatedDurationSeconds && typeof videoData.estimatedDurationSeconds === 'number' && videoData.estimatedDurationSeconds > 0) {
		durationInFrames = Math.round(videoData.estimatedDurationSeconds * FPS);
	} else if (videoData?.captionJson && Array.isArray(videoData.captionJson) && videoData.captionJson.length > 0) {
		durationInFrames = Math.ceil(Math.max(...videoData.captionJson.map(segment => segment.end || 0)) / 1000 * FPS);
	} else if (videoData?.workflow_data?.clips && Array.isArray(videoData.workflow_data.clips) && videoData.workflow_data.clips.length > 0) {
		durationInFrames = Math.round(videoData.workflow_data.clips.reduce((total, clip) => total + (clip.duration || 5), 0) * FPS);
	} else {
		durationInFrames = COMPOSITION_DURATION_FRAMES;
	}

	const aspectRatio = videoData?.aspectRatio ?? '9:16';
	const templateId = videoData?.templateId ?? 'default';
	const { width, height } = getDimensionsFromRatio(aspectRatio);

	const TemplateComponent = getTemplateComponent(templateId);
	const workflowData = videoData?.workflow_data;
	const templateProps = createTemplateProps(videoData, workflowData, templateId);

	console.log(`Rendering Composition with ID: ${COMPOSITION_ID}, Template: ${templateId}, Aspect Ratio: ${aspectRatio}, Dimensions: ${width}x${height}`);

	return (
		<AbsoluteFill style={{ width, height }}>
			<TemplateComponent {...templateProps} />
		</AbsoluteFill>
	);
};

// --- Root Component for Remotion ---
export const Root = () => {
	const inputProps = getInputProps();
	const videoData = inputProps?.videoData || studioPreviewData;

	const aspectRatio = videoData?.aspectRatio ?? '9:16';
	const { width, height } = getDimensionsFromRatio(aspectRatio);

	// Calculate duration
	let durationInFrames;
	if (videoData?.templateId === 'RedditPostVideo') {
		durationInFrames = videoData.redditDurationFrames || (45 * FPS);
	} else if (videoData?.templateId === 'TwitterPostVideo') {
		durationInFrames = videoData.twitterDurationFrames || (45 * FPS);
	} else if (videoData?.estimatedDurationSeconds && typeof videoData.estimatedDurationSeconds === 'number' && videoData.estimatedDurationSeconds > 0) {
		durationInFrames = Math.round(videoData.estimatedDurationSeconds * FPS);
	} else if (videoData?.captionJson && Array.isArray(videoData.captionJson) && videoData.captionJson.length > 0) {
		durationInFrames = Math.ceil(Math.max(...videoData.captionJson.map(segment => segment.end || 0)) / 1000 * FPS);
	} else if (videoData?.workflow_data?.clips && Array.isArray(videoData.workflow_data.clips) && videoData.workflow_data.clips.length > 0) {
		durationInFrames = Math.round(videoData.workflow_data.clips.reduce((total, clip) => total + (clip.duration || 5), 0) * FPS);
	} else {
		durationInFrames = COMPOSITION_DURATION_FRAMES;
	}

	console.log(`Root: Registering composition with duration: ${durationInFrames} frames, dimensions: ${width}x${height}`);

	return (
		<Composition
			id={COMPOSITION_ID}
			component={MyComposition}
			durationInFrames={durationInFrames}
			fps={FPS}
			width={width}
			height={height}
			defaultProps={{ videoData }}
		/>
	);
};
