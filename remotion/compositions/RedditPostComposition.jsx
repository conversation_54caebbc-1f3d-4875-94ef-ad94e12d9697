import React from 'react';
import { AbsoluteFill, Img, Video } from 'remotion'; // Import AbsoluteFill, Img, and Video

// Helper function to format counts
const formatCount = (count, approximate) => {
  if (approximate && count >= 1000) {
    return (count / 1000).toFixed(1) + 'k';
  }
  return count;
};

// This component will render the Reddit post based on provided data
const RedditPostComposition = ({
  title,
  subreddit,
  username,
  postContent,
  upvotes,
  comments,
  avatar,
  badge,
  darkMode,
  wideLayout,
  approximateCounts,
  hideTrophies,
  hideUpvotes,
  hideComments,
  hideShare,
  redditPostX = 0, // Default to 0
  redditPostY = 0, // Default to 0
  redditPostScale = 1, // Default to 1
  backgroundVideoUrl, // Accept the new prop
  // Remove videoData prop if it was used directly before
}) => {
  const backgroundColor = darkMode ? '#1a1a1a' : '#ffffff';
  const textColor = darkMode ? '#ffffff' : '#000000';
  const secondaryTextColor = darkMode ? '#818384' : '#555';
  const iconColor = darkMode ? '#d7dadc' : '#555'; // Define icon color based on dark mode

  return (
    <AbsoluteFill style={{ backgroundColor, color: textColor, fontFamily: 'sans-serif', display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'center' }}> {/* Align content to the center */}
      {/* Background Video */}
      {backgroundVideoUrl && (
        <Video
          src={backgroundVideoUrl} // Use the provided background video URL
          style={{ position: 'absolute', width: '100%', height: '100%', objectFit: 'cover' }}
        />
      )}

      {/* Content container with responsive width using div */}
      <div
        style={{
          width: wideLayout ? '80%' : '90%',
          maxWidth: wideLayout ? '800px' : '700px',
          padding: '30px', // Increased padding
          backgroundColor: darkMode ? '#1a1a1b' : '#ffffff',
          borderRadius: '8px',
          position: 'relative',
          zIndex: 1,
          transform: `translate(${redditPostX}px, ${redditPostY}px) scale(${redditPostScale})`, // Apply position and scale
        }}
      > {/* Increased max-width, removed bottom margin, added padding, solid background, and zIndex */}

        {/* Header using div and span */}
        <div style={{ display: 'flex', alignItems: 'center', marginBottom: '15px', fontSize: '18px', color: secondaryTextColor }}> {/* Increased font size and margin */}
          {avatar && (
            <Img src={avatar} style={{ width: '32px', height: '32px', borderRadius: '50%', marginRight: '12px' }} /> // Increased avatar size and margin
          )}
          <span style={{ marginRight: '10px' }}>r/{subreddit || 'subreddit'}</span> {/* Increased margin */}
          <span>•</span>
          <span style={{ marginLeft: '10px' }}>Posted by u/{username || 'username'}</span> {/* Increased margin */}
          {badge && (
            <span style={{ marginLeft: '10px', backgroundColor: darkMode ? '#d7dadc' : '#000', color: darkMode ? '#000' : '#fff', padding: '3px 8px', borderRadius: '4px', fontSize: '14px' }}> {/* Increased padding and font size */}
              {badge.toUpperCase()}
            </span>
          )}
        </div>

        {/* Title using span */}
        <span style={{ fontSize: '32px', fontWeight: 'bold', marginBottom: '20px', color: textColor, display: 'block', wordWrap: 'break-word' }}> {/* Increased font size and margin, added wordWrap */}
          {title || 'Your post title will appear here'}
        </span>

        {/* Content using span */}
        <span style={{ fontSize: '22px', marginBottom: '25px', color: textColor, whiteSpace: 'pre-wrap', display: 'block', wordWrap: 'break-word' }}> {/* Increased font size and margin, added wordWrap */}
          {postContent || 'Your post content will appear here...'}
        </span>

        {/* Footer (Upvotes, Comments, and Share) using div and span */}
        <div style={{ display: 'flex', alignItems: 'center', fontSize: '18px', color: secondaryTextColor }}> {/* Increased font size */}
          {!hideUpvotes && (
            <div style={{ display: 'flex', alignItems: 'center', marginRight: '20px' }}> {/* Increased margin */}
              {/* Upvote Icon */}
              <Img src="/upvote-svgrepo-com.svg" style={{ width: '24px', height: '24px', marginRight: '6px', filter: darkMode ? 'invert(1)' : 'none' }} /> {/* Use SVG, increased size and margin, apply invert filter for dark mode */}
              <span>{formatCount(upvotes, approximateCounts)}</span>
              {/* Downvote Icon (using inverted upvote for simplicity) */}
              <Img src="/upvote-svgrepo-com.svg" style={{ width: '24px', height: '24px', marginLeft: '6px', transform: 'rotate(180deg)', filter: darkMode ? 'invert(1)' : 'none' }} /> {/* Use SVG, increased size and margin, rotate for downvote, apply invert filter for dark mode */}
            </div>
          )}
          {!hideComments && (
            <div style={{ display: 'flex', alignItems: 'center', marginRight: '20px' }}> {/* Added margin */}
              {/* Comment Icon */}
              <Img src="/comment.svg" style={{ width: '24px', height: '24px', marginRight: '6px', filter: darkMode ? 'invert(1)' : 'none' }} /> {/* Use SVG, increased size and margin, apply invert filter for dark mode */}
              <span>{formatCount(comments, approximateCounts)} Comments</span>
            </div>
          )}
          {!hideShare && (
             <div style={{ display: 'flex', alignItems: 'center' }}> {/* Removed margin-left */}
               {/* Share Icon */}
               <Img src="/share-svgrepo-com.svg" style={{ width: '24px', height: '24px', marginRight: '6px', filter: darkMode ? 'invert(1)' : 'none' }} /> {/* Use SVG, increased size and margin, apply invert filter for dark mode */}
               <span>Share</span>
             </div>
          )}
          {/* Add logic for hideTrophies if needed in the composition */}
          {/* For example, you might conditionally render a trophy icon */}
          {!hideTrophies && (
             <div style={{ display: 'flex', alignItems: 'center', marginLeft: '20px' }}> {/* Increased margin */}
               {/* Placeholder for Trophy icon */}
               <span style={{ marginRight: '6px' }}>🏆</span> {/* Increased margin */}
               <span>Trophies</span>
             </div>
          )}
        </div>
      </div>
    </AbsoluteFill>
  );
};

export default RedditPostComposition;
