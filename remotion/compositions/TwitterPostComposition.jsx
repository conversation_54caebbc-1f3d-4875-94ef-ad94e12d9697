import React from 'react';
import { AbsoluteFill, Img, Video } from 'remotion';

// Helper function to format counts
const formatCount = (count) => {
  if (count >= 1000000) {
    return (count / 1000000).toFixed(1) + 'M';
  }
  if (count >= 1000) {
    return (count / 1000).toFixed(1) + 'K';
  }
  return count;
};

const TwitterPostComposition = ({
  username,
  tweetContent,
  likes,
  retweets,
  avatar,
  handle,
  darkMode,
  twitterPostX = 0,
  twitterPostY = 0,
  twitterPostScale = 1,
  backgroundVideoUrl, // Accept the new prop
  // Remove videoData prop if it was used directly before
}) => {
  const backgroundColor = darkMode ? '#000000' : '#ffffff'; // Twitter dark mode is black
  const cardBackgroundColor = darkMode ? '#000000' : '#ffffff'; // Card background is also black in dark mode
  const textColor = darkMode ? '#ffffff' : '#000000';
  const secondaryTextColor = darkMode ? '#8899a6' : '#536471'; // Twitter secondary text color
  const borderColor = darkMode ? '#2f3336' : '#e0e0e0'; // Border color for separators
  const iconColor = darkMode ? '#8899a6' : '#536471'; // Twitter icon color
  const primaryBlue = '#1d9bf0'; // Twitter blue color

  // Placeholder date and views - these could be made dynamic later
  const postDate = "10:43 PM · May 19, 2025";
  const views = "0 Views"; // This could also be dynamic

  return (
    <AbsoluteFill style={{ backgroundColor, fontFamily: 'sans-serif', display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'center' }}>
      {/* Background Video */}
      {backgroundVideoUrl && (
        <Video
          src={backgroundVideoUrl} // Use the provided background video URL
          style={{ position: 'absolute', width: '100%', height: '100%', objectFit: 'cover' }}
        />
      )}

      {/* Content container */}
      <div
        style={{
          width: '90%', // Adjust width as needed
          maxWidth: '600px', // Adjust max-width as needed
          padding: '20px',
          backgroundColor: cardBackgroundColor,
          borderRadius: '16px', // More rounded corners for Twitter style
          position: 'relative',
          zIndex: 1,
          transform: `translate(${twitterPostX}px, ${twitterPostY}px) scale(${twitterPostScale})`,
          color: textColor, // Apply text color to the container
          border: `1px solid ${borderColor}`, // Add a subtle border
          overflow: 'hidden', // Ensure content doesn't overflow rounded corners
        }}
      >
        {/* Header */}
        <div style={{ display: 'flex', alignItems: 'center', marginBottom: '10px' }}>
          {avatar && (
            <Img src={avatar} style={{ width: '48px', height: '48px', borderRadius: '50%', marginRight: '10px' }} />
          )}
          <div style={{ display: 'flex', flexDirection: 'column' }}>
            <span style={{ fontWeight: 'bold', fontSize: '18px' }}>{username || 'Username'}</span>
            <span style={{ fontSize: '15px', color: secondaryTextColor }}>{handle || '@handle'}</span>
          </div>
          {/* Verified badge placeholder - could add an SVG here */}
          {/* <span style={{ marginLeft: '5px', fontSize: '14px', color: primaryBlue }}>✓</span> */}
        </div>

        {/* Tweet Content */}
        <div style={{ fontSize: '18px', marginBottom: '15px', whiteSpace: 'pre-wrap', wordWrap: 'break-word', lineHeight: '1.4' }}>
          {tweetContent || 'Your tweet content will appear here...'}
        </div>

        {/* Timestamp and Views */}
        <div style={{ fontSize: '15px', color: secondaryTextColor, marginBottom: '15px', borderBottom: `1px solid ${borderColor}`, paddingBottom: '15px' }}>
            <span>{postDate}</span>
            <span style={{ margin: '0 5px' }}>•</span>
            <span>{views}</span>
        </div>

        {/* Engagement Metrics (Retweets, Quotes, Likes, Bookmarks) */}
        <div style={{ display: 'flex', alignItems: 'center', fontSize: '15px', color: secondaryTextColor, marginBottom: '15px', borderBottom: `1px solid ${borderColor}`, paddingBottom: '15px' }}>
            <div style={{ display: 'flex', alignItems: 'center', marginRight: '20px' }}>
                <span style={{ fontWeight: 'bold', marginRight: '5px', color: textColor }}>{formatCount(retweets)}</span>
                <span>Retweets</span>
            </div>
            {/* Quotes placeholder - could add a count here if data is available */}
            {/* <div style={{ display: 'flex', alignItems: 'center', marginRight: '20px' }}>
                <span style={{ fontWeight: 'bold', marginRight: '5px', color: textColor }}>0</span>
                <span>Quotes</span>
            </div> */}
            <div style={{ display: 'flex', alignItems: 'center', marginRight: '20px' }}>
                <span style={{ fontWeight: 'bold', marginRight: '5px', color: textColor }}>{formatCount(likes)}</span>
                <span>Likes</span>
            </div>
             {/* Bookmarks placeholder - could add a count here if data is available */}
            {/* <div style={{ display: 'flex', alignItems: 'center' }}>
                <span style={{ fontWeight: 'bold', marginRight: '5px', color: textColor }}>0</span>
                <span>Bookmarks</span>
            </div> */}
        </div>

        {/* Action Icons (Comment, Retweet, Like, Share, Bookmark) */}
         <div style={{ display: 'flex', justifyContent: 'space-around', alignItems: 'center', paddingTop: '10px' }}>
            {/* Comment Icon */}
            <Img src="/comment.svg" style={{ width: '20px', height: '20px', filter: darkMode ? 'invert(1)' : 'none' }} />
            {/* Retweet Icon */}
            <Img src="/upvote-svgrepo-com.svg" style={{ width: '20px', height: '20px', transform: 'rotate(90deg)', filter: darkMode ? 'invert(1)' : 'none' }} /> {/* Using upvote and rotating */}
            {/* Like Icon */}
            <Img src="/upvote-svgrepo-com.svg" style={{ width: '20px', height: '20px', filter: darkMode ? 'invert(1)' : 'none' }} /> {/* Using upvote */}
             {/* Share Icon */}
            <Img src="/share-svgrepo-com.svg" style={{ width: '20px', height: '20px', filter: darkMode ? 'invert(1)' : 'none' }} />
             {/* Bookmark Icon Placeholder */}
            <span style={{ color: iconColor, fontSize: '20px' }}>🔖</span> {/* Using text placeholder for now */}
        </div>

      </div>
    </AbsoluteFill>
  );
};

export default TwitterPostComposition;
