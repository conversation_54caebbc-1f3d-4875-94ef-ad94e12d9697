import { AbsoluteFill, Sequence, Img, Video, Audio, useVideoConfig, useCurrentFrame, interpolate } from 'remotion';
import React from 'react';

// Define the types for the input props
// This will be the data passed from the frontend to the Remotion composition
export const myCompSchema = ({
  script: {
    type: 'string',
    description: 'The generated script for the video.',
  },
  media: {
    type: 'array',
    description: 'An array of evaluated media objects (images, videos, music).',
    items: {
      type: 'object',
      properties: {
        id: { type: 'string' },
        type: { type: 'string', enum: ['image', 'video', 'music'] },
        url: { type: 'string' },
        previewUrl: { type: 'string' },
        tags: { type: 'array', items: { type: 'string' } },
        source: { type: 'string' },
        title: { type: 'string' }, // For music
        evaluation: { // Evaluation results from the AI
          type: 'object',
          properties: {
            qualityScore: { type: 'number' },
            relevanceScore: { type: 'number' },
            notes: { type: 'string' },
          },
        },
      },
      required: ['id', 'type', 'url', 'source'],
    },
  },
  // Add other potential input props here, e.g., video duration, aspect ratio, etc.
});

const StockMediaVideoComposition = ({
  videoData, // Receive the entire videoData object
}) => {
  const { fps, width, height } = useVideoConfig();
  const frame = useCurrentFrame();

  // Extract relevant data from videoData.workflow_data
  const workflowData = videoData?.workflow_data;
  const script = workflowData?.script;
  const scenesToRender = Array.isArray(workflowData?.scenes) ? workflowData.scenes : [];
  const audioUrl = videoData?.audioUrl; // Audio URL is top-level in videoData
  const voiceConfig = workflowData?.voiceConfig || {};

  console.log("StockMediaVideoComposition: received videoData", videoData);
  console.log("StockMediaVideoComposition: extracted workflowData", workflowData);
  console.log("StockMediaVideoComposition: scenesToRender", scenesToRender);

  if (scenesToRender.length === 0) {
    return (
      <AbsoluteFill style={{ backgroundColor: 'grey', justifyContent: 'center', alignItems: 'center', fontSize: 30, color: 'white' }}>
        No media assets provided for scenes.
      </AbsoluteFill>
    );
  }

  // Calculate total duration from scenesToRender
  const totalCalculatedDurationInFrames = scenesToRender.reduce((acc, sceneAsset) => {
    return acc + (sceneAsset.duration || 5) * fps; // Default 5 seconds if duration missing
  }, 0);

  return (
    <AbsoluteFill style={{ backgroundColor: 'black' }}>
      {audioUrl && <Audio src={audioUrl} volume={voiceConfig?.volumeGainDb !== undefined ? Math.pow(10, voiceConfig.volumeGainDb / 20) : 0.7} />}

      {scenesToRender.map((sceneAsset, index) => {
        const scriptTextForScene = sceneAsset.script_text || ''; // Script text is now directly in sceneAsset
        const media = sceneAsset.media; // Media object is now directly in sceneAsset
        
        let currentStartFrame = 0;
        for (let i = 0; i < index; i++) {
          currentStartFrame += (scenesToRender[i].duration || 5) * fps;
        }
        const sceneSpecificDurationInFrames = (sceneAsset.duration || 5) * fps;

        const opacity = interpolate(
          frame,
          [currentStartFrame, currentStartFrame + fps, currentStartFrame + sceneSpecificDurationInFrames - fps, currentStartFrame + sceneSpecificDurationInFrames],
          [0, 1, 1, 0],
          { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
        );

        const hasValidMedia = media && media.url && typeof media.url === 'string';

        return (
          <Sequence key={`scene-${sceneAsset.scene_number || index}`} from={currentStartFrame} durationInFrames={sceneSpecificDurationInFrames}>
            <AbsoluteFill style={{ opacity }}>
              {hasValidMedia ? (
                <>{
                  media.type === 'image' && (
                    <Img 
                      src={media.url} 
                      style={{ width: '100%', height: '100%', objectFit: 'cover' }} 
                      onError={(e) => console.error('Error loading image:', media.url, e)}
                    />
                  )
                }{
                  media.type === 'video' && (
                    <Video 
                      src={media.url} 
                      style={{ width: '100%', height: '100%', objectFit: 'cover' }} 
                      startFrom={0} 
                      volume={0}
                      loop={false}
                      onError={(e) => console.error('Error loading video:', media.url, e)}
                    />
                  )
                }</>
              ) : (
                <AbsoluteFill style={{ backgroundColor: '#333', justifyContent: 'center', alignItems: 'center' }}>
                  <div style={{ color: 'white', fontSize: 20, textAlign: 'center', padding: 20 }}>
                    Media not available for scene {sceneAsset.scene_number}.
                  </div>
                </AbsoluteFill>
              )}
              
              {scriptTextForScene && (
                <AbsoluteFill style={{
                  justifyContent: 'flex-end',
                  alignItems: 'center',
                  paddingBottom: '10%',
                }}>
                  <div 
                    style={{
                      backgroundColor: 'rgba(0, 0, 0, 0.6)',
                      color: 'white',
                      padding: '15px 25px',
                      borderRadius: '8px',
                      fontSize: '28px',
                      textAlign: 'center',
                      maxWidth: '90%',
                      boxShadow: '0px 4px 15px rgba(0,0,0,0.2)',
                      lineHeight: '1.4',
                    }}
                  >
                    {scriptTextForScene}
                  </div>
                </AbsoluteFill>
              )}
            </AbsoluteFill>
          </Sequence>
        );
      })}
    </AbsoluteFill>
  );
};

StockMediaVideoComposition.defaultProps = {
  videoData: { // Provide a default videoData structure for local development/testing
    script: '',
    audioUrl: '',
    workflow_data: {
      type: 'stock-media-video',
      scenes: [],
      voiceConfig: {},
      videoConfig: {},
    },
    aspectRatio: '9:16',
    templateId: 'StockMediaVideo',
  },
};

export default StockMediaVideoComposition;
