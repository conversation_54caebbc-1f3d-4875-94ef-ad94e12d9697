import React from 'react';
import { AbsoluteFill, Series, Video, staticFile } from 'remotion';
import { z } from 'zod';
// import { zColor } from '@remotion/zod-schema'; // Removed problematic import
import { NarratorCaption } from '../templates/components/NarratorCaption'; // Assuming a component for captions

export const NarratorVideoSchema = z.object({
  videoData: z.object({
    title: z.string(),
    aspectRatio: z.string(),
    workflow_data: z.object({
      workflowType: z.literal('narratorVideo'),
      originalVideoUrl: z.string(),
      narrativeScript: z.string(),
      scriptTimings: z.array(z.object({
        text: z.string(),
        start: z.number(),
        end: z.number(),
      })),
      videoAnalysis: z.any().optional(), // Optional, for debugging/info
    }),
    captionStyleJson: z.any().optional(), // For dynamic caption styling
    captionContainerStyle: z.any().optional(), // For dynamic caption container styling
  }),
});

export const NarratorVideoComposition = ({ videoData }) => {
  const { originalVideoUrl, scriptTimings } = videoData.workflow_data;
  const { captionStyleJson, captionContainerStyle } = videoData;

  return (
    <AbsoluteFill style={{ backgroundColor: 'black' }}>
      {/* Background Video */}
      <Video src={originalVideoUrl} style={{ width: '100%', height: '100%', objectFit: 'contain' }} />

      {/* On-screen Text Overlays (Narrative Script) */}
      <AbsoluteFill>
        {scriptTimings.map((timing, index) => (
          <Series.Sequence
            key={index}
            durationInFrames={(timing.end - timing.start) * 30} // Assuming 30 FPS
            from={timing.start * 30}
          >
            <NarratorCaption
              text={timing.text}
              captionStyle={captionStyleJson}
              containerStyle={captionContainerStyle}
            />
          </Series.Sequence>
        ))}
      </AbsoluteFill>
    </AbsoluteFill>
  );
};
