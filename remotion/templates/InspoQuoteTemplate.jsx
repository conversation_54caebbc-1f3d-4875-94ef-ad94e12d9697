// remotion/templates/InspoQuoteTemplate.jsx
import {
	Sequence,
	AbsoluteFill,
	Audio,
	Img,
	useCurrentFrame,
	useVideoConfig,
	interpolate,
	Easing,
	Text, // Import Text component
} from 'remotion';
import React, { useMemo } from 'react';
import { DynamicCaption } from './components/Caption'; // Import the caption component

export const InspoQuoteTemplate = ({ videoData }) => {
	const frame = useCurrentFrame();
	const { durationInFrames, fps } = useVideoConfig();

	// --- Safely Destructure Input Data ---
	const {
		audioUrl,
		backgroundMusic,
		captionJson = [],
		captionStyle = {},
		captionContainerStyle = {},
		quote = '', // Destructure the new 'quote' string
		author = '', // Destructure the new 'author' string
		backgroundImageUrl = '', // Destructure the new 'backgroundImageUrl' string
		quoteAnimation = { type: 'fade', duration: 30, delay: 10 }, // Destructure and provide default animation
	} = videoData || {};

	const mainAudio = audioUrl;

	// --- Background Music Mapping ---
	const backgroundMusicMap = {
		'izzamuzzic': 'https://cdn.revid.ai/audio/_izzamuzzic.mp3',
		'snowfall': 'https://cdn.revid.ai/audio/_snowfall.mp3',
		'roaming-free': 'https://cdn.revid.ai/audio/roaming-free.mp3',
		'scary-song': 'https://cdn.revid.ai/audio/scary_song.mp3',
		'observer': 'https://cdn.revid.ai/audio/observer.mp3',
		'paris-else': 'https://cdn.revid.ai/audio/_paris-else.mp3',
		'burlesque': 'https://cdn.revid.ai/audio/burlesque.mp3',
	};

	const backgroundMusicSrc = backgroundMusic && backgroundMusic !== 'none' ? backgroundMusicMap[backgroundMusic] : null;

	// --- Caption Logic (similar to DefaultTemplate) ---
	const currentCaptionWord = useMemo(() => {
		if (!Array.isArray(captionJson) || captionJson.length === 0) {
			return '';
		}
		const currentTimeSeconds = frame / fps;

		const currentWordEntry = captionJson.find(word => {
			const startTime = typeof word?.start === 'number' ? word.start / 1000 : -1;
			const endTime = typeof word?.end === 'number' ? word.end / 1000 : -1;
			return startTime >= 0 && endTime >= 0 && currentTimeSeconds >= startTime && currentTimeSeconds < endTime;
		});

		return currentWordEntry?.text ?? '';
	}, [captionJson, frame, fps]);

	// --- Quote Animation Logic ---
	const animationStartFrame = quoteAnimation.delay || 0;
	const animationEndFrame = animationStartFrame + (quoteAnimation.duration || 30);

	const getQuoteOpacity = () => {
		if (quoteAnimation.type === 'fade') {
			return interpolate(
				frame,
				[animationStartFrame, animationEndFrame],
				[0, 1],
				{ extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
			);
		}
		return 1; // Default to visible if no specific fade animation
	};

	const getQuoteTransform = () => {
		if (quoteAnimation.type === 'slideUp') {
			const translateY = interpolate(
				frame,
				[animationStartFrame, animationEndFrame],
				[50, 0], // Start 50px down, end at original position
				{ extrapolateLeft: 'clamp', extrapolateRight: 'clamp', easing: Easing.out(Easing.ease) }
			);
			return `translateY(${translateY}px)`;
		}
		if (quoteAnimation.type === 'scaleIn') {
			const scale = interpolate(
				frame,
				[animationStartFrame, animationEndFrame],
				[0.8, 1], // Start scaled down, end at original size
				{ extrapolateLeft: 'clamp', extrapolateRight: 'clamp', easing: Easing.out(Easing.ease) }
			);
			return `scale(${scale})`;
		}
		return 'none'; // Default no transform
	};


	return (
		<AbsoluteFill style={{ backgroundColor: 'black' }}>

			{mainAudio && <Audio src={mainAudio} />}
			{backgroundMusicSrc && <Audio src={backgroundMusicSrc} volume={0.3} loop />}

			{/* Background Image/Video */}
			{backgroundImageUrl && (
				<Sequence from={0} durationInFrames={durationInFrames}>
					<AbsoluteFill>
						<Img
							src={backgroundImageUrl}
							alt="Background"
							style={{
								width: '100%',
								height: '100%',
								objectFit: 'cover',
								// Add subtle animation like slow zoom if desired
							}}
						/>
					</AbsoluteFill>
				</Sequence>
			)}

			{/* Quote and Author */}
			<AbsoluteFill style={{
				justifyContent: 'center',
				alignItems: 'center',
				padding: '0 80px', // Add padding
				opacity: getQuoteOpacity(),
				transform: getQuoteTransform(),
			}}>
				<Text style={{
					fontSize: 50, // Adjust size as needed
					fontWeight: 'bold',
					color: 'white',
					textAlign: 'center',
					textShadow: '2px 2px 4px rgba(0,0,0,0.5)',
					marginBottom: 20, // Space between quote and author
				}}>
					{quote}
				</Text>
				{author && (
					<Text style={{
						fontSize: 30, // Adjust size as needed
						color: 'white',
						textAlign: 'center',
						textShadow: '1px 1px 3px rgba(0,0,0,0.5)',
						fontStyle: 'italic',
					}}>
						- {author}
					</Text>
				)}
			</AbsoluteFill>


			{/* Dynamic Captions */}
			{Array.isArray(captionJson) && captionJson.length > 0 && currentCaptionWord && (
				<DynamicCaption
					currentWord={currentCaptionWord}
					textStyle={captionStyle}
					containerStyle={captionContainerStyle}
				/>
			)}
		</AbsoluteFill>
	);
};