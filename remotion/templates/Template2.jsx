// remotion/templates/FilmstripCarouselTemplate.jsx (or your Template2.jsx)
import {
	Sequence,
	AbsoluteFill,
	Audio,
	Img,
	useCurrentFrame,
	useVideoConfig,
	interpolate,
	Easing,
	spring,
} from 'remotion';
import React, { useMemo } from 'react'; // Removed useState, useEffect as they weren't used

// --- Configuration ---
const FOCUS_IMAGE_SCALE = 1;
const PREVIEW_IMAGE_SCALE = 0.6;
const PREVIEW_IMAGE_OPACITY = 0.7;
const IMAGE_SPACING_PERCENT = 10;
const TRANSITION_SPRING_CONFIG = {
	stiffness: 120,
	damping: 22,
	mass: 1,
};
const DEFAULT_WORD_STYLE = {
	color: 'white',
	fontSize: 48,
	fontWeight: '500',
	lineHeight: '1.3',
};
const FIXED_PROGRESS_BAR_STYLE = {
	height: '8px',
	indicatorColor: 'white',
	trackColor: 'rgba(255, 255, 255, 0.2)',
};

export const Template2 = ({ videoData }) => { // Renamed to match original if you copy-paste
	const frame = useCurrentFrame();
	const { durationInFrames, fps, width: videoWidth, height: videoHeight } = useVideoConfig();

	const {
		audioUrl,
		images = [],
		captionJson = [],
		captionStyle: rawCaptionStyle = {}, // Renamed to avoid conflict
		captionContainerStyle: rawCaptionContainerStyle = {}, // Renamed
	} = videoData || {};

	// --- GUARD: Ensure style props are valid objects ---
	const captionStyle = (typeof rawCaptionStyle === 'object' && !Array.isArray(rawCaptionStyle) && rawCaptionStyle !== null)
		? rawCaptionStyle
		: {};
	const captionContainerStyle = (typeof rawCaptionContainerStyle === 'object' && !Array.isArray(rawCaptionContainerStyle) && rawCaptionContainerStyle !== null)
		? rawCaptionContainerStyle
		: {};
	// --- END GUARD ---


	const numImages = images.length;

	const timePerImage = numImages > 0 ? durationInFrames / numImages : durationInFrames;
	const currentImageIndex = numImages > 0 ? Math.min(numImages - 1, Math.floor(frame / timePerImage)) : 0;

	const focusedImageHeight = videoHeight * 0.6;
	const tempFocusedImageWidth = (focusedImageHeight / 9) * 16;
	const maxFocusedImageWidth = videoWidth * 0.8;
	const actualFocusedImageWidth = Math.max(50, Math.min(tempFocusedImageWidth, maxFocusedImageWidth)); // Ensure not zero for calcs

	const previewImageWidth = actualFocusedImageWidth * PREVIEW_IMAGE_SCALE;
	const imageSpacing = actualFocusedImageWidth * (IMAGE_SPACING_PERCENT / 100);

	// Calculate the X offset needed to center the currentImageIndex
	// This is the sum of widths and spacings of all items *before* the current focused image,
	// plus half the width of the focused image itself, then adjusted by half the video width.
	let totalWidthBeforeFocusedCenter = 0;
	for (let i = 0; i < numImages; i++) {
		const currentItemWidth = (i === currentImageIndex) ? actualFocusedImageWidth : previewImageWidth;
		if (i < currentImageIndex) {
			totalWidthBeforeFocusedCenter += currentItemWidth + imageSpacing;
		} else if (i === currentImageIndex) {
			totalWidthBeforeFocusedCenter += currentItemWidth / 2;
			break; 
		}
	}
	const targetFilmstripX = (videoWidth / 2) - totalWidthBeforeFocusedCenter;


	const filmstripTranslateX = spring({
		frame,
		fps,
		config: TRANSITION_SPRING_CONFIG,
		to: targetFilmstripX,
	});

	const currentWordObject = useMemo(() => {
		if (!Array.isArray(captionJson) || captionJson.length === 0) return null;
		const currentTimeSeconds = frame / fps;
		const activeWord = captionJson.find(word => {
			const startTime = typeof word?.start === 'number' ? word.start / 1000 : -1;
			const endTime = typeof word?.end === 'number' ? word.end / 1000 : -1;
			return startTime >= 0 && endTime >= 0 && currentTimeSeconds >= startTime && currentTimeSeconds < endTime;
		});
		return activeWord || null;
	}, [captionJson, frame, fps]);

	const currentVideoProgress = interpolate(frame, [0, durationInFrames], [0, 100], { extrapolateRight: 'clamp' });

	return (
		<AbsoluteFill style={{ backgroundColor: 'black', overflow: 'hidden' }}>
			{audioUrl && <Audio src={audioUrl} />}

			<div style={{ position: 'absolute', top: 0, left: 0, width: '100%', height: FIXED_PROGRESS_BAR_STYLE.height, backgroundColor: FIXED_PROGRESS_BAR_STYLE.trackColor, zIndex: 99 }}>
				<div style={{ width: `${currentVideoProgress}%`, height: '100%', backgroundColor: FIXED_PROGRESS_BAR_STYLE.indicatorColor }} />
			</div>

			<AbsoluteFill
				style={{
					justifyContent: 'center',
					alignItems: 'flex-start', // Critical: Align items to the start of the line
					flexDirection: 'row',    // Ensure items are in a row
					transform: `translateX(${filmstripTranslateX}px)`,
				}}
			>
				{images.map((imageSrc, index) => {
					const isFocused = index === currentImageIndex;
					const targetScale = isFocused ? FOCUS_IMAGE_SCALE : PREVIEW_IMAGE_SCALE;
					const targetOpacity = isFocused ? 1 : PREVIEW_IMAGE_OPACITY;
					
					const animatedScale = spring({ frame, fps, config: TRANSITION_SPRING_CONFIG, to: targetScale });
					const animatedOpacity = spring({ frame, fps, config: TRANSITION_SPRING_CONFIG, to: targetOpacity });

					// Base width/height on the *focused* dimensions, then scale
					const baseWidth = actualFocusedImageWidth;
					const baseHeight = (actualFocusedImageWidth / 16) * 9; // Maintain 16:9 aspect for container

					return (
						<div
							key={imageSrc + index}
							style={{
								width: baseWidth,  // Apply base width
								height: baseHeight, // Apply base height
								marginRight: index < numImages - 1 ? imageSpacing : 0, // Non-scaled spacing
								display: 'flex',
								justifyContent: 'center',
								alignItems: 'center',
								opacity: animatedOpacity,
								transform: `scale(${animatedScale})`, // Apply scale via transform
								transformOrigin: 'center center', // Scale from center
							}}
						>
							<Img
								src={imageSrc}
								alt={`Gallery image ${index + 1}`}
								style={{
									width: '100%',
									height: '100%',
									objectFit: 'cover',
									borderRadius: '8px',
								}}
							/>
						</div>
					);
				})}
			</AbsoluteFill>

			{currentWordObject && (
				<AbsoluteFill 
					style={{ 
						justifyContent: 'center',
						alignItems: 'flex-end',
						paddingBottom: `${Math.max(0, videoHeight) * 0.1}px`, // Guard videoHeight
						pointerEvents: 'none',
						...captionContainerStyle // GUARDED version used
					}}>
					<div style={{ 
						backgroundColor: 'rgba(0, 0, 0, 0.7)', 
						padding: '10px 25px', 
						borderRadius: '6px', 
						minWidth: '100px', 
						maxWidth: '60%',
						textAlign: 'center', 
					}}>
						<span style={{ ...DEFAULT_WORD_STYLE, ...captionStyle /* GUARDED version used */ }}>
							{currentWordObject.text}
						</span>
					</div>
				</AbsoluteFill>
			)}
		</AbsoluteFill>
	);
};