// remotion/templates/DefaultTemplate.jsx

import {
	Sequence,
	AbsoluteFill,
	Audio,
	Img,
	useCurrentFrame,
	useVideoConfig,
	interpolate,
	Easing,
} from 'remotion';
import React, { useMemo } from 'react';
import { DynamicCaption } from './components/Caption'; // Import the new component

// This is the extracted VideoContent component, now acting as the default template.
// It receives specific props extracted from videoData in MyComposition.jsx
export const DefaultTemplate = ({
	audioUrl, // correct property name from API/database
	images = [],
	captionJson = [],
	captionStyle = {},
	captionContainerStyle = {},
	audioSpeed = 1.0, // Destructure audioSpeed, default to 1.0
	backgroundMusic, // Destructure backgroundMusic ID
	// aspectRatio is available in videoData but not directly used here
	// as the dimensions are set in the Composition component itself.
	// Add other AI-specific props if needed
}) => {
	const frame = useCurrentFrame();
	const { durationInFrames, fps } = useVideoConfig();

	const mainAudio = audioUrl; // Renamed for clarity
	// const currentPlaybackRate = typeof audioSpeed === 'number' ? audioSpeed : 1.0; // No longer needed, audio is generated at correct speed

	// --- Background Music Mapping ---
	// Updated background music mapping with provided URLs
	const backgroundMusicMap = {
		'izzamuzzic': 'https://cdn.revid.ai/audio/_izzamuzzic.mp3',
		'snowfall': 'https://cdn.revid.ai/audio/_snowfall.mp3',
		'roaming-free': 'https://cdn.revid.ai/audio/roaming-free.mp3',
		'scary-song': 'https://cdn.revid.ai/audio/scary_song.mp3',
		'observer': 'https://cdn.revid.ai/audio/observer.mp3',
		'paris-else': 'https://cdn.revid.ai/audio/_paris-else.mp3',
		'burlesque': 'https://cdn.revid.ai/audio/burlesque.mp3',
		// Add more mappings as needed if more options are added to BackgroundMusicSelector.jsx
	};

	const backgroundMusicSrc = backgroundMusic && backgroundMusic !== 'none' ? backgroundMusicMap[backgroundMusic] : null;

	const TRANSITION_DURATION_FRAMES = 15;
	const ZOOM_AMOUNT = 1.08;

	const numImages = images.length;

	const totalTransitionTime = numImages > 1 ? (numImages - 1) * TRANSITION_DURATION_FRAMES : 0;
	const timeAvailableForImages = durationInFrames - totalTransitionTime;
	const baseImageDuration = numImages > 0
		? timeAvailableForImages / numImages
		: durationInFrames;

	const imageDurationFrames = Math.max(1, Math.floor(baseImageDuration));

	const currentCaptionWord = useMemo(() => {
		if (!Array.isArray(captionJson) || captionJson.length === 0) {
			return '';
		}
		const currentTimeSeconds = frame / fps;

		const currentWordEntry = captionJson.find(word => {
			const startTime = typeof word?.start === 'number' ? word.start / 1000 : -1;
			const endTime = typeof word?.end === 'number' ? word.end / 1000 : -1;
			return startTime >= 0 && endTime >= 0 && currentTimeSeconds >= startTime && currentTimeSeconds < endTime;
		});

		return currentWordEntry?.text ?? '';
	}, [captionJson, frame, fps]);

	return (
		<AbsoluteFill style={{ backgroundColor: 'black' }}>

			{mainAudio && <Audio src={mainAudio} />} {/* playbackRate defaults to 1.0 */}
			{backgroundMusicSrc && <Audio src={backgroundMusicSrc} volume={0.09} loop />} {/* Added background audio with lower volume and loop */}

			{/* 1. Images with Transitions and Zoom */}
			{numImages > 0 ? (
				images.map((imageSrc, index) => {
					if (typeof imageSrc !== 'string' || imageSrc.trim() === '') {
						console.warn(`Invalid or empty image source at index ${index}:`, imageSrc);
						return (
							<Sequence key={`placeholder-${index}`} from={0} durationInFrames={durationInFrames}>
								<AbsoluteFill style={{backgroundColor: '#333', justifyContent: 'center', alignItems: 'center'}}>
									<p style={{color: 'white', fontSize: 30}}>Invalid Image {index + 1}</p>
								</AbsoluteFill>
							</Sequence>
						);
					}

					const sequenceStartFrame = index * (imageDurationFrames + TRANSITION_DURATION_FRAMES);

					let sequenceDuration = imageDurationFrames;
					if (index > 0) sequenceDuration += TRANSITION_DURATION_FRAMES;
					if (index < numImages - 1) sequenceDuration += TRANSITION_DURATION_FRAMES;

					const clampedStartFrame = Math.max(0, sequenceStartFrame);
					const maxEndFrame = durationInFrames;
					const clampedEndFrame = Math.min(maxEndFrame, clampedStartFrame + sequenceDuration);
					const clampedDuration = Math.max(1, clampedEndFrame - clampedStartFrame);

					const fadeInStart = clampedStartFrame;
					const fadeInEnd = clampedStartFrame + TRANSITION_DURATION_FRAMES;

					const fadeOutStart = clampedEndFrame - TRANSITION_DURATION_FRAMES;
					const fadeOutEnd = clampedEndFrame;

					const zoomStartFrame = index === 0 ? clampedStartFrame : fadeInEnd;
					const zoomEndFrame = index === numImages - 1 ? clampedEndFrame : fadeOutStart;

					return (
						<Sequence
							key={`${imageSrc}-${index}`}
							from={clampedStartFrame}
							durationInFrames={clampedDuration}
							name={`Image ${index + 1}`}
						>
							<AbsoluteFill>
								<Img
									src={imageSrc}
									alt={`Scene image ${index + 1}`}
									style={{
										width: '100%',
										height: '100%',
										objectFit: 'cover',
										opacity: interpolate(
											frame,
											[
												fadeInStart,
												Math.min(fadeInEnd, durationInFrames),
												Math.max(fadeInStart, fadeOutStart),
												Math.min(fadeOutEnd, durationInFrames)
											],
											[
												index === 0 ? 1 : 0,
												1,
												1,
												index === numImages - 1 ? 1 : 0
											],
											{ extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
										),
										transform: `scale(${interpolate(
											frame,
											[zoomStartFrame, Math.max(zoomStartFrame, zoomEndFrame)],
											[1, ZOOM_AMOUNT],
											{
												extrapolateLeft: 'clamp',
												extrapolateRight: 'clamp',
												easing: Easing.bezier(0.42, 0, 0.58, 1)
											}
										)})`,
									}}
								/>
							</AbsoluteFill>
						</Sequence>
					);
				})
			) : (
				<AbsoluteFill style={{ justifyContent: 'center', alignItems: 'center' }}>
					<p style={{ color: 'white', fontSize: 40, textAlign: 'center', padding: 30 }}>
						No images provided in videoData input.
					</p>
				</AbsoluteFill>
			)}

			{/* 2. Audio is handled within the image sequence logic above */}

			{/* 3. Captions */}
			{Array.isArray(captionJson) && captionJson.length > 0 && currentCaptionWord && (
				<DynamicCaption
					currentWord={currentCaptionWord}
					textStyle={captionStyle}
					containerStyle={captionContainerStyle}
				/>
			)}
		</AbsoluteFill>
	);
};
