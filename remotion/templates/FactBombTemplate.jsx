// remotion/templates/FactBombTemplate.jsx
import {
	Sequence,
	AbsoluteFill,
	Audio,
	Img,
	useCurrentFrame,
	useVideoConfig,
	interpolate,
	Easing,
	Text, // Import Text component
} from 'remotion';
import React, { useMemo } from 'react';
import { DynamicCaption } from './components/Caption'; // Import the caption component

export const FactBombTemplate = ({ videoData }) => {
	const frame = useCurrentFrame();
	const { durationInFrames, fps } = useVideoConfig();

	// --- Safely Destructure Input Data ---
	const {
		audioUrl,
		backgroundMusic,
		captionJson = [],
		captionStyle = {},
		captionContainerStyle = {},
		facts = [], // Destructure the new 'facts' array
	} = videoData || {};

	const mainAudio = audioUrl;

	// --- Background Music Mapping ---
	const backgroundMusicMap = {
		'izzamuzzic': 'https://cdn.revid.ai/audio/_izzamuzzic.mp3',
		'snowfall': 'https://cdn.revid.ai/audio/_snowfall.mp3',
		'roaming-free': 'https://cdn.revid.ai/audio/roaming-free.mp3',
		'scary-song': 'https://cdn.revid.ai/audio/scary_song.mp3',
		'observer': 'https://cdn.revid.ai/audio/observer.mp3',
		'paris-else': 'https://cdn.revid.ai/audio/_paris-else.mp3',
		'burlesque': 'https://cdn.revid.ai/audio/burlesque.mp3',
	};

	const backgroundMusicSrc = backgroundMusic && backgroundMusic !== 'none' ? backgroundMusicMap[backgroundMusic] : null;

	// --- Caption Logic (similar to DefaultTemplate) ---
	const currentCaptionWord = useMemo(() => {
		if (!Array.isArray(captionJson) || captionJson.length === 0) {
			return '';
		}
		const currentTimeSeconds = frame / fps;

		const currentWordEntry = captionJson.find(word => {
			const startTime = typeof word?.start === 'number' ? word.start / 1000 : -1;
			const endTime = typeof word?.end === 'number' ? word.end / 1000 : -1;
			return startTime >= 0 && endTime >= 0 && currentTimeSeconds >= startTime && currentTimeSeconds < endTime;
		});

		return currentWordEntry?.text ?? '';
	}, [captionJson, frame, fps]);


	// --- Fact Bomb Specific Logic ---
	// This will involve sequencing background images/videos based on fact timing
	// and rendering text overlays for each fact.

	return (
		<AbsoluteFill style={{ backgroundColor: 'black' }}>

			{mainAudio && <Audio src={mainAudio} />}
			{backgroundMusicSrc && <Audio src={backgroundMusicSrc} volume={0.3} loop />}

			{/* Background Images/Videos based on Facts */}
			{facts.map((fact, index) => {
				// Calculate sequence timing based on fact start/end times
				const startFrame = Math.floor((fact.startTime || 0) * fps);
				const endFrame = Math.floor((fact.endTime || durationInFrames / fps) * fps);
				const durationFrames = Math.max(1, endFrame - startFrame);

				if (!fact.imageUrl) {
					// Render a placeholder if no image URL is provided for a fact
					return (
						<Sequence key={`fact-bg-placeholder-${index}`} from={startFrame} durationInFrames={durationFrames}>
							<AbsoluteFill style={{backgroundColor: '#222', justifyContent: 'center', alignItems: 'center'}}>
								<p style={{color: 'white', fontSize: 30}}>Fact {index + 1} Background</p>
							</AbsoluteFill>
						</Sequence>
					);
				}

				return (
					<Sequence
						key={`fact-background-${index}`}
						from={startFrame}
						durationInFrames={durationFrames}
						name={`Fact ${index + 1} Background`}
					>
						<AbsoluteFill>
							<Img
								src={fact.imageUrl}
								alt={`Background for fact ${index + 1}`}
								style={{
									width: '100%',
									height: '100%',
									objectFit: 'cover',
									// Add transition/animation logic here if needed
								}}
							/>
						</AbsoluteFill>
					</Sequence>
				);
			})}


			{/* Fact Text Overlays */}
			{facts.map((fact, index) => {
				const startFrame = Math.floor((fact.startTime || 0) * fps);
				const endFrame = Math.floor((fact.endTime || durationInFrames / fps) * fps);
				const durationFrames = Math.max(1, endFrame - startFrame);

				return (
					<Sequence
						key={`fact-text-${index}`}
						from={startFrame}
						durationInFrames={durationFrames}
						name={`Fact ${index + 1} Text`}
					>
						<AbsoluteFill style={{
							justifyContent: 'center',
							alignItems: 'center',
							padding: '0 50px', // Add some padding
						}}>
							<Text style={{
								fontSize: 60, // Adjust size as needed
								fontWeight: 'bold',
								color: 'white',
								textAlign: 'center',
								textShadow: '2px 2px 4px rgba(0,0,0,0.5)', // Add text shadow for readability
								// Add animation logic for text appearance/disappearance here
								opacity: interpolate(
									frame,
									[startFrame, startFrame + 15, endFrame - 15, endFrame], // Fade in/out over 15 frames
									[0, 1, 1, 0],
									{ extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
								),
							}}>
								{fact.text}
							</Text>
						</AbsoluteFill>
					</Sequence>
				);
			})}


			{/* Dynamic Captions */}
			{Array.isArray(captionJson) && captionJson.length > 0 && currentCaptionWord && (
				<DynamicCaption
					currentWord={currentCaptionWord}
					textStyle={captionStyle}
					containerStyle={captionContainerStyle}
				/>
			)}
		</AbsoluteFill>
	);
};