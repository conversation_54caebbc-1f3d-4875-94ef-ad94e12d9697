// remotion/templates/components/Caption.jsx
import React from 'react';
import { AbsoluteFill } from 'remotion';

export const DynamicCaption = ({
	currentWord,
	textStyle,
	containerStyle,
}) => {
	if (!currentWord) {
		return null;
	}

	// Default styles that can be overridden by props
	const defaultContainerStyle = {
		justifyContent: 'center',
		alignItems: 'center',
	};

	const defaultTextStyle = {
		color: 'white',
		fontSize: 60,
		fontWeight: 'bold',
		textAlign: 'center',
		backgroundColor: 'rgba(0, 0, 0, 0.6)',
		padding: '12px 24px',
		borderRadius: '8px',
	};

	// Merge default styles with provided styles
	// Ensure provided styles are objects before spreading
	const finalContainerStyle = {
		...defaultContainerStyle,
		...(typeof containerStyle === 'object' && containerStyle !== null && !Array.isArray(containerStyle) ? containerStyle : {}),
	};

	const finalTextStyle = {
		...defaultTextStyle,
		...(typeof textStyle === 'object' && textStyle !== null && !Array.isArray(textStyle) ? textStyle : {}),
	};

	return (
		<AbsoluteFill style={finalContainerStyle}>
			<span style={finalTextStyle}>{currentWord}</span>
		</AbsoluteFill>
	);
};