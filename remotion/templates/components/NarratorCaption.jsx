import React from 'react';
import { AbsoluteFill, staticFile, useCurrentFrame, useVideoConfig } from 'remotion';
import { z } from 'zod';


export const NarratorCaptionSchema = z.object({
  text: z.string(),
  captionStyle: z.any().optional(), // JSON for text styling
  containerStyle: z.any().optional(), // JSON for container styling
});

export const NarratorCaption = ({ text, captionStyle, containerStyle }) => {
  const frame = useCurrentFrame();
  const { durationInFrames, fps } = useVideoConfig();

  // Default styles
  const defaultCaptionStyle = {
    fontSize: 60,
    fontWeight: 'bold',
    color: 'white',
    textAlign: 'center',
    textShadow: '2px 2px 4px rgba(0,0,0,0.7)',
  };

  const defaultContainerStyle = {
    position: 'absolute',
    bottom: 50,
    width: '100%',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    padding: '0 50px',
  };

  // Merge default styles with provided styles
  const mergedCaptionStyle = { ...defaultCaptionStyle, ...captionStyle };
  const mergedContainerStyle = { ...defaultContainerStyle, ...containerStyle };

  return (
    <AbsoluteFill style={mergedContainerStyle}>
      <div style={mergedCaptionStyle}>
        {text}
      </div>
    </AbsoluteFill>
  );
};
