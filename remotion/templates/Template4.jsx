// remotion/templates/Template4.jsx
import React, { useMemo } from 'react';
import {
  AbsoluteFill,
  Sequence,
  useCurrentFrame,
  useVideoConfig,
  interpolate,
  Img,
  Easing,
  Audio,
} from 'remotion';
import { DynamicCaption } from './components/Caption'; // Import the Caption component

export const SlideUpCaptionTemplate = ({ videoData }) => {
  const frame = useCurrentFrame();
  const { durationInFrames, fps, width, height } = useVideoConfig();

  // --- Safely Destructure Input Data ---
  const {
    audioUrl,
    images = [],
    captionJson = [],
    captionStyle = {},
    captionContainerStyle = {},
    backgroundMusic,
  } = videoData || {};

  const mainAudio = audioUrl;

  // --- Background Music Mapping ---
  const backgroundMusicMap = {
    'izzamuzzic': 'https://cdn.revid.ai/audio/_izzamuzzic.mp3',
    'snowfall': 'https://cdn.revid.ai/audio/_snowfall.mp3',
    'roaming-free': 'https://cdn.revid.ai/audio/roaming-free.mp3',
    'scary-song': 'https://cdn.revid.ai/audio/scary_song.mp3',
    'observer': 'https://cdn.revid.ai/audio/observer.mp3',
    'paris-else': 'https://cdn.revid.ai/audio/_paris-else.mp3',
    'burlesque': 'https://cdn.revid.ai/audio/burlesque.mp3',
  };

  const backgroundMusicSrc = backgroundMusic && backgroundMusic !== 'none' ? backgroundMusicMap[backgroundMusic] : null;

  const numImages = images.length;
  const TRANSITION_DURATION_FRAMES = fps * 0.8; // 0.8 seconds transition

  const totalTransitionTime = numImages > 1 ? (numImages - 1) * TRANSITION_DURATION_FRAMES : 0;
  const timeAvailableForImages = durationInFrames - totalTransitionTime;
  const baseImageDuration = numImages > 0
    ? timeAvailableForImages / numImages
    : durationInFrames;

  const imageDisplayDuration = Math.max(1, Math.floor(baseImageDuration));

  const currentCaptionWord = useMemo(() => {
    if (!Array.isArray(captionJson) || captionJson.length === 0) {
      return '';
    }
    const currentTimeSeconds = frame / fps;

    const currentWordEntry = captionJson.find(word => {
      const startTime = typeof word?.start === 'number' ? word.start / 1000 : -1;
      const endTime = typeof word?.end === 'number' ? word.end / 1000 : -1;
      return startTime >= 0 && endTime >= 0 && currentTimeSeconds >= startTime && currentTimeSeconds < endTime;
    });

    return currentWordEntry?.text ?? '';
  }, [captionJson, frame, fps]);

  // Caption container slide-up animation
  const captionTranslateY = interpolate(
    frame,
    [fps * 0.5, fps * 1.5, durationInFrames - fps * 1.5, durationInFrames - fps * 0.5], // Appear after 0.5s, fully visible by 1.5s, starts sliding down 1.5s before end, off-screen by 0.5s before end
    [styles.captionContainer.height, 0, 0, styles.captionContainer.height], // Slides from off-screen bottom (its height) to 0 (at bottom edge), stays, then slides back down
    { easing: Easing.out(Easing.ease), extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
  );

  // Caption container opacity animation
  const captionOpacity = interpolate(
    frame,
    [fps * 0.2, fps * 0.8, durationInFrames - fps * 0.8, durationInFrames - fps * 0.2], // Fade in slightly before slide up, fade out slightly after slide down
    [0, 1, 1, 0],
    { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
  );


  return (
    <AbsoluteFill style={{ backgroundColor: 'black' }}>

      {mainAudio && <Audio src={mainAudio} />}
      {backgroundMusicSrc && <Audio src={backgroundMusicSrc} volume={0.3} loop />}

      {/* Images with Crossfade Transition */}
      {numImages > 0 ? (
        images.map((imgSrc, index) => {
          if (typeof imgSrc !== 'string' || imgSrc.trim() === '') {
            console.warn(`Invalid or empty image source at index ${index}:`, imgSrc);
            return (
              <Sequence key={`placeholder-${index}`} from={0} durationInFrames={durationInFrames}>
                <AbsoluteFill style={{backgroundColor: '#333', justifyContent: 'center', alignItems: 'center'}}>
                  <p style={{color: 'white', fontSize: 30}}>Invalid Image {index + 1}</p>
                </AbsoluteFill>
              </Sequence>
            );
          }

          const sequenceStartFrame = index * (imageDisplayDuration + TRANSITION_DURATION_FRAMES);
          const sequenceDuration = imageDisplayDuration + (index < numImages - 1 ? TRANSITION_DURATION_FRAMES : 0);

          const clampedStartFrame = Math.max(0, sequenceStartFrame);
          const maxEndFrame = durationInFrames;
          const clampedEndFrame = Math.min(maxEndFrame, clampedStartFrame + sequenceDuration);
          const clampedDuration = Math.max(1, clampedEndFrame - clampedStartFrame);

          // Opacity for crossfade
          const opacity = interpolate(
            frame,
            [
              clampedStartFrame,
              clampedStartFrame + TRANSITION_DURATION_FRAMES,
              clampedEndFrame - TRANSITION_DURATION_FRAMES,
              clampedEndFrame
            ],
            [
              index === 0 ? 1 : 0, // First image starts visible, others fade in
              1,
              1,
              index === numImages - 1 ? 1 : 0 // Last image stays visible, others fade out
            ],
            { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
          );

          return (
            <Sequence
              key={`${imgSrc}-${index}`}
              from={clampedStartFrame}
              durationInFrames={clampedDuration}
              name={`Image ${index + 1}`}
            >
              <AbsoluteFill>
                <Img
                  src={imgSrc}
                  alt={`Scene image ${index + 1}`}
                  style={{
                    ...styles.image,
                    opacity, // Apply crossfade
                  }}
                />
              </AbsoluteFill>
            </Sequence>
          );
        })
      ) : (
        <AbsoluteFill style={{ justifyContent: 'center', alignItems: 'center' }}>
          <p style={{ color: 'white', fontSize: 40, textAlign: 'center', padding: 30 }}>
            No images provided in videoData input.
          </p>
        </AbsoluteFill>
      )}

      {/* Captions */}
      {Array.isArray(captionJson) && captionJson.length > 0 && currentCaptionWord && (
        <div style={{
            ...styles.captionContainer, // Apply new caption container styles
            ...captionContainerStyle, // Allow override from videoData
            transform: `translateY(${captionTranslateY}px)`, // Apply slide animation
            opacity: captionOpacity, // Apply fade animation
          }}>
          <DynamicCaption
            currentWord={currentCaptionWord}
            textStyle={{
                ...styles.captionText, // Apply new caption text styles
                ...captionStyle, // Allow override from videoData
            }}
            containerStyle={{}} // Container style handled by the parent div
          />
        </div>
      )}
    </AbsoluteFill>
  );
};

const styles = {
  image: {
    width: '100%',
    height: '100%',
    objectFit: 'cover',
  },
  captionContainer: {
    position: 'absolute',
    bottom: 0, // Positioned at the bottom, translateY handles the rest
    left: 0,
    right: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.7)', // Semi-transparent dark background
    padding: 30,
    justifyContent: 'center',
    alignItems: 'center',
    height: 200, // Fixed height for the slide-up container
  },
  captionText: {
    fontSize: 50, // Adjust font size
    fontWeight: 'bold',
    color: 'white', // White text
    textAlign: 'center',
    lineHeight: 1.3,
  },
};