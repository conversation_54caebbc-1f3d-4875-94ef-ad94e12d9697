// remotion/templates/Template1_SpringSlide.jsx
import {
	Sequence,
	AbsoluteFill,
	Audio,
	Img,
	useCurrentFrame,
	useVideoConfig,
	interpolate, // Still used for pan
	Easing,      // Still used for pan
	spring,      // For the image slide transition
} from 'remotion';
import React, { useMemo } from 'react';

// --- Configuration ---
const TRANSITION_DURATION_FRAMES = 30; // Might want slightly longer for spring to settle
const PAN_AMOUNT_PERCENT = 5;
const IMAGE_SCALE_FOR_PAN = 1.1;

// --- Default style for the displayed word ---
const DEFAULT_WORD_STYLE = {
	color: 'white',
	fontSize: 48,
	fontWeight: '500',
	lineHeight: '1.3',
};

// --- Style for the fixed progress bar ---
const FIXED_PROGRESS_BAR_STYLE = {
	height: '8px',
	indicatorColor: 'white',
	trackColor: 'rgba(255, 255, 255, 0.2)',
};

// --- Spring Configuration for Image Slide ---
const SLIDE_SPRING_CONFIG = {
	// Experiment with these values!
	stiffness: 100, // How "springy" it is. Higher is stiffer.
	damping: 20,   // How much resistance. Higher means less oscillation.
	mass: 1,       // Affects speed and overshoot.
};


export const Template1= ({ videoData }) => {
	const frame = useCurrentFrame();
	const { durationInFrames, fps, width } = useVideoConfig();

	const panAmountPixels = (PAN_AMOUNT_PERCENT / 100) * width;

	// --- Safely Destructure Input Data ---
	const {
		audioUrl,
		images = [],
		captionJson = [],
		captionStyle = {},
		captionContainerStyle = {},
	} = videoData || {};

	const numImages = images.length;

	// --- Timing Calculation (for images) ---
	const totalTransitionTime = numImages > 1 ? (numImages - 1) * TRANSITION_DURATION_FRAMES : 0;
	const timeAvailableForImages = durationInFrames - totalTransitionTime;
	const baseImageDuration = numImages > 0
		? timeAvailableForImages / numImages
		: durationInFrames;
	const imageHoldDurationFrames = Math.max(1, Math.floor(baseImageDuration));

	// --- Caption Logic (same) ---
	const currentWordObject = useMemo(() => { /* ... same ... */ }, [captionJson, frame, fps]);

	// --- Progress Bar Calculation (same) ---
	const currentProgress = interpolate(frame, [0, durationInFrames], [0, 100], { extrapolateRight: 'clamp' });


	// --- Main Render ---
	return (
		<AbsoluteFill style={{ backgroundColor: 'black', overflow: 'hidden' }}>
			{audioUrl && <Audio src={audioUrl} />}

			{/* 0. Video Progress Bar (same) */}
			<div style={{ position: 'absolute', top: 0, left: 0, width: '100%', height: FIXED_PROGRESS_BAR_STYLE.height, backgroundColor: FIXED_PROGRESS_BAR_STYLE.trackColor, zIndex: 99, }}>
				<div style={{ width: `${currentProgress}%`, height: '100%', backgroundColor: FIXED_PROGRESS_BAR_STYLE.indicatorColor }} />
			</div>

			{/* 1. Images Section with Spring Transitions */}
			{numImages > 0 ? (
				images.map((imageSrc, index) => {
					const sequenceStartFrame = index * (imageHoldDurationFrames + TRANSITION_DURATION_FRAMES);
					const fullyVisibleStartFrame = sequenceStartFrame + TRANSITION_DURATION_FRAMES; // When it *should* be centered
					const slideOutStartFrame = fullyVisibleStartFrame + imageHoldDurationFrames;
					// Sequence duration now mainly ensures the spring has time to act
					const sequenceEndFrame = slideOutStartFrame + TRANSITION_DURATION_FRAMES;

					const clampedStart = Math.max(0, sequenceStartFrame);
					const clampedEnd = Math.min(durationInFrames, sequenceEndFrame);
					const clampedDuration = Math.max(1, clampedEnd - clampedStart);

					// Determine the target X position for the spring
					// - width: Off-screen to the left
					// - 0: Centered
					// - width: Off-screen to the right
					let targetX = 0;
					if (frame < clampedStart) { // Before this sequence starts
						targetX = width; // Start off-screen to the right
					} else if (frame >= clampedStart && frame < fullyVisibleStartFrame) {
						targetX = 0; // Target is center (sliding in)
					} else if (frame >= fullyVisibleStartFrame && frame < slideOutStartFrame) {
						targetX = 0; // Target is center (holding)
					} else if (frame >= slideOutStartFrame && frame < clampedEnd) {
						targetX = -width; // Target is off-screen left (sliding out)
					} else { // After this sequence ends
						targetX = -width; // Stay off-screen to the left
					}
					
					// Special handling for the first image (no slide-in from right)
					// and last image (no slide-out to left, unless it's the only image)
					if (index === 0 && frame < fullyVisibleStartFrame) {
						targetX = 0; // First image starts centered (or quickly becomes so)
					}
					if (index === numImages - 1 && frame >= slideOutStartFrame) {
						targetX = 0; // Last image stays centered (unless it's also the first)
						if (numImages === 1 && frame >= slideOutStartFrame) targetX = 0; // Single image always stays
					}


					const slideTranslation = spring({
						frame: frame - clampedStart, // Frame relative to this sequence's start
						fps,
						config: SLIDE_SPRING_CONFIG,
						// `to` is the target value the spring animates towards
						to: targetX,
						// `from` is the initial value. We need to manage this carefully
						// For simplicity, let's assume it starts off-screen right or left
						// based on whether it's sliding in or already out.
						// A more robust `from` would consider previous state if sequence re-enters.
						from: (frame < fullyVisibleStartFrame) ? width : -width,
					});
					
					// --- Pan effect (remains the same) ---
					const panInputRange = [fullyVisibleStartFrame, slideOutStartFrame];
					const panOutputRange = index % 2 === 0 ? [-panAmountPixels, panAmountPixels] : [panAmountPixels, -panAmountPixels];
					const panTranslateXEffect = interpolate( frame, panInputRange, panOutputRange, { extrapolateLeft: "clamp", extrapolateRight: "clamp", easing: Easing.bezier(0.42, 0, 0.58, 1) } );


					if (typeof imageSrc !== 'string' || imageSrc.trim() === '') {
						// Placeholder logic
						return ( <Sequence key={`placeholder-${index}`} from={clampedStart} durationInFrames={clampedDuration}> <AbsoluteFill style={{backgroundColor: '#333', justifyContent: 'center', alignItems: 'center'}}> <p style={{color: 'white', fontSize: 30}}>Invalid Image {index + 1}</p> </AbsoluteFill> </Sequence> );
					}

					return (
						<Sequence
							key={`${imageSrc}-${index}`}
							from={clampedStart}
							durationInFrames={clampedDuration}
							name={`Image ${index + 1}`}
						>
							<AbsoluteFill
								style={{
									// Apply the spring-driven translation
									transform: `translateX(${slideTranslation}px)`,
								}}
							>
								<Img
									src={imageSrc}
									alt={`Scene image ${index + 1}`}
									style={{
										width: '100%',
										height: '100%',
										objectFit: 'cover',
										// Apply pan effect *within* the sliding container
										transform: `scale(${IMAGE_SCALE_FOR_PAN}) translateX(${panTranslateXEffect}px)`,
									}}
								/>
							</AbsoluteFill>
						</Sequence>
					);
				})
			) : (
				<AbsoluteFill style={{ justifyContent: 'center', alignItems: 'center' }}> <p style={{ color: 'white', fontSize: 40, textAlign: 'center', padding: 30 }}> No images provided. </p> </AbsoluteFill>
			)}

			{/* 2. Captions (same) */}
			{currentWordObject }
		</AbsoluteFill>
	);
};

// --- Re-add definitions for unchanged parts for completeness ---
const currentWordObject = (captionJson, frame, fps) => {
    if (!Array.isArray(captionJson) || captionJson.length === 0) return null;
    const currentTimeSeconds = frame / fps;
    return captionJson.find(word => {
        const startTime = typeof word?.start === 'number' ? word.start / 1000 : -1;
        const endTime = typeof word?.end === 'number' ? word.end / 1000 : -1;
        return startTime >= 0 && endTime >= 0 && currentTimeSeconds >= startTime && currentTimeSeconds < endTime;
    }) || null;
};

// Caption rendering part (pasted for completeness, no changes)
const renderCaptions = (currentWordObjectData, DEFAULT_WORD_STYLE_DATA, captionStyleData, captionContainerStyleData) => {
	if (!currentWordObjectData) return null;
	return (
		<AbsoluteFill style={{ justifyContent: 'flex-end', alignItems: 'center', paddingBottom: '12%', ...captionContainerStyleData }}>
			<div style={{ backgroundColor: 'rgba(0, 0, 0, 0.7)', padding: '10px 25px', borderRadius: '6px', minWidth: '100px', maxWidth: '80%', textAlign: 'center', }}>
				<span style={{ ...DEFAULT_WORD_STYLE_DATA, ...captionStyleData, }}>
					{currentWordObjectData.text}
				</span>
			</div>
		</AbsoluteFill>
	);
};