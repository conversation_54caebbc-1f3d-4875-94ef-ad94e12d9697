// remotion/templates/index.js
// This file acts as a registry for all available video templates.

import { DefaultTemplate } from './DefaultTemplate';
import { Template1 } from './Template1';
import { Template2 } from './Template2';
import { RevealCaptionTemplate } from './Template3';
import { FactBombTemplate } from './FactBombTemplate';
import { SlideUpCaptionTemplate } from './Template4';
import RedditPostComposition from '../compositions/RedditPostComposition'; // Import RedditPostComposition
import TwitterPostComposition from '../compositions/TwitterPostComposition'; // Import TwitterPostComposition
import { NarratorVideoComposition } from '../compositions/NarratorVideoComposition'; // Import NarratorVideoComposition
import StockMediaVideoComposition from '../compositions/StockMediaVideoComposition'; // Import StockMediaVideoComposition

export const templates = {
  template1: {
    name: 'Template 1 - <PERSON> Blue',
    component: Template1,
  },
  template2: {
    name: 'Template 2 - Light Coral',
    component: Template2,
  },
  template3: {
    name: 'Template 3 - Light Green',
    component: RevealCaptionTemplate,
  },
  factbomb: {
    name: 'Fact Bomb',
    component: FactBombTemplate,
  },
  template4: {
    name: 'Template 4 - Slide Up Caption',
    component: SlideUpCaptionTemplate,
  },
  RedditPostVideo: { // Register RedditPostComposition
    name: 'Reddit Post Video',
    component: RedditPostComposition,
  },
  TwitterPostVideo: { // Register TwitterPostComposition
    name: 'Twitter Post Video',
    component: TwitterPostComposition,
  },
  NarratorVideo: { // Register NarratorVideoComposition
    name: 'Narrator Video',
    component: NarratorVideoComposition,
  },
  StockMediaVideo: { // Register StockMediaVideoComposition
    name: 'Stock Media Video',
    component: StockMediaVideoComposition,
  },
  default: DefaultTemplate,
};

// Function to get a template component by its ID
export const getTemplateComponent = (templateId) => {
	const templateEntry = templates[templateId];

	if (templateEntry) {
		// If it's an object with a 'component' property, return that
		if (typeof templateEntry === 'object' && templateEntry.component) {
			return templateEntry.component;
		}
		// Otherwise, assume it's the component itself (like the default)
		return templateEntry;
	}

	// Fallback to the default component if templateId is not found
	return templates.default;
};
