// remotion/templates/Template3.jsx
import React, { useMemo } from 'react';
import {
  AbsoluteFill,
  Sequence,
  useCurrentFrame,
  useVideoConfig,
  interpolate,
  Img,
  Easing,
  Audio,
} from 'remotion';
import { DynamicCaption } from './components/Caption'; // Import the Caption component

export const RevealCaptionTemplate = ({ videoData }) => {
  const frame = useCurrentFrame();
  const { durationInFrames, fps, width, height } = useVideoConfig();

  // --- Safely Destructure Input Data ---
  const {
    audioUrl,
    images = [],
    captionJson = [],
    captionStyle = {},
    captionContainerStyle = {},
    backgroundMusic,
  } = videoData || {};

  const mainAudio = audioUrl;

  // --- Background Music Mapping ---
  const backgroundMusicMap = {
    'izzamuzzic': 'https://cdn.revid.ai/audio/_izzamuzzic.mp3',
    'snowfall': 'https://cdn.revid.ai/audio/_snowfall.mp3',
    'roaming-free': 'https://cdn.revid.ai/audio/roaming-free.mp3',
    'scary-song': 'https://cdn.revid.ai/audio/scary_song.mp3',
    'observer': 'https://cdn.revid.ai/audio/observer.mp3',
    'paris-else': 'https://cdn.revid.ai/audio/_paris-else.mp3',
    'burlesque': 'https://cdn.revid.ai/audio/burlesque.mp3',
  };

  const backgroundMusicSrc = backgroundMusic && backgroundMusic !== 'none' ? backgroundMusicMap[backgroundMusic] : null;

  const numImages = images.length;
  const TRANSITION_DURATION_FRAMES = fps * 0.7; // 0.7 seconds transition

  const totalTransitionTime = numImages > 1 ? (numImages - 1) * TRANSITION_DURATION_FRAMES : 0;
  const timeAvailableForImages = durationInFrames - totalTransitionTime;
  const baseImageDuration = numImages > 0
    ? timeAvailableForImages / numImages
    : durationInFrames;

  const imageDisplayDuration = Math.max(1, Math.floor(baseImageDuration));

  const currentCaptionWord = useMemo(() => {
    if (!Array.isArray(captionJson) || captionJson.length === 0) {
      return '';
    }
    const currentTimeSeconds = frame / fps;

    const currentWordEntry = captionJson.find(word => {
      const startTime = typeof word?.start === 'number' ? word.start / 1000 : -1;
      const endTime = typeof word?.end === 'number' ? word.end / 1000 : -1;
      return startTime >= 0 && endTime >= 0 && currentTimeSeconds >= startTime && currentTimeSeconds < endTime;
    });

    return currentWordEntry?.text ?? '';
  }, [captionJson, frame, fps]);


  return (
    <AbsoluteFill style={{ backgroundColor: 'black' }}>

      {mainAudio && <Audio src={mainAudio} />}
      {backgroundMusicSrc && <Audio src={backgroundMusicSrc} volume={0.3} loop />}

      {/* Images with Reveal Transition */}
      {numImages > 0 ? (
        images.map((imgSrc, index) => {
          if (typeof imgSrc !== 'string' || imgSrc.trim() === '') {
            console.warn(`Invalid or empty image source at index ${index}:`, imgSrc);
            return (
              <Sequence key={`placeholder-${index}`} from={0} durationInFrames={durationInFrames}>
                <AbsoluteFill style={{backgroundColor: '#333', justifyContent: 'center', alignItems: 'center'}}>
                  <p style={{color: 'white', fontSize: 30}}>Invalid Image {index + 1}</p>
                </AbsoluteFill>
              </Sequence>
            );
          }

          const sequenceStartFrame = index * (imageDisplayDuration + TRANSITION_DURATION_FRAMES);
          const sequenceDuration = imageDisplayDuration + (index < numImages - 1 ? TRANSITION_DURATION_FRAMES : 0);

          const clampedStartFrame = Math.max(0, sequenceStartFrame);
          const maxEndFrame = durationInFrames;
          const clampedEndFrame = Math.min(maxEndFrame, clampedStartFrame + sequenceDuration);
          const clampedDuration = Math.max(1, clampedEndFrame - clampedStartFrame);

          // Animate width for wipe effect
          const wipeWidth = interpolate(
            frame,
            [clampedStartFrame, clampedStartFrame + TRANSITION_DURATION_FRAMES],
            [0, width],
            { easing: Easing.ease, extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
          );

          // Subtle zoom effect during display
           const scale = interpolate(
            frame,
            [clampedStartFrame, clampedEndFrame],
            [1, 1.03], // Zoom in slightly
            { easing: Easing.linear, extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
           );


          return (
            <Sequence
              key={`${imgSrc}-${index}`}
              from={clampedStartFrame}
              durationInFrames={clampedDuration}
              name={`Image ${index + 1}`}
            >
              <AbsoluteFill>
                <Img
                  src={imgSrc}
                  alt={`Scene image ${index + 1}`}
                  style={{
                    ...styles.image,
                    transform: `scale(${scale})`, // Apply zoom
                    // Use clipPath for wipe effect
                    clipPath: `inset(0px ${width - wipeWidth}px 0px 0px)`,
                  }}
                />
              </AbsoluteFill>
            </Sequence>
          );
        })
      ) : (
        <AbsoluteFill style={{ justifyContent: 'center', alignItems: 'center' }}>
          <p style={{ color: 'white', fontSize: 40, textAlign: 'center', padding: 30 }}>
            No images provided in videoData input.
          </p>
        </AbsoluteFill>
      )}

      {/* Captions */}
      {Array.isArray(captionJson) && captionJson.length > 0 && currentCaptionWord && (
        <div style={{
            ...styles.captionContainer, // Apply new caption container styles
            ...captionContainerStyle, // Allow override from videoData
            // Animate caption container opacity
            opacity: interpolate(
                frame,
                [0, fps * 0.5, durationInFrames - fps * 0.5, durationInFrames],
                [0, 1, 1, 0],
                { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
            )
          }}>
          <DynamicCaption
            currentWord={currentCaptionWord}
            textStyle={{
                ...styles.captionText, // Apply new caption text styles
                ...captionStyle, // Allow override from videoData
            }}
            containerStyle={{}} // Container style handled by the parent div
          />
        </div>
      )}
    </AbsoluteFill>
  );
};

const styles = {
  image: {
    width: '100%',
    height: '100%',
    objectFit: 'cover',
  },
  captionContainer: {
    position: 'absolute',
    top: '50%', // Center vertically
    left: '50%', // Center horizontally
    transform: 'translate(-50%, -50%)', // Adjust for centering
    backgroundColor: 'rgba(0, 0, 0, 0.8)', // More opaque dark background
    padding: 30,
    borderRadius: 15,
    display: 'flex', // Use flexbox for centering content within the div
    justifyContent: 'center',
    alignItems: 'center',
    maxWidth: '80%', // Limit width
    width: 'auto', // Allow width to be determined by content and maxWidth
  },
  captionText: {
    fontSize: 60, // Larger font size
    fontWeight: 'bold',
    color: 'white', // White text
    textAlign: 'center',
    lineHeight: 1.4,
  },
};