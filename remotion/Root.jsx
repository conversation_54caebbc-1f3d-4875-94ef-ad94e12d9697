import { Composition, getInputProps } from 'remotion';
import { MyComposition } from './Composition'; // Import the component to be registered
import RedditPostComposition from './compositions/RedditPostComposition'; // Import the Reddit post composition
import TwitterPostComposition from './compositions/TwitterPostComposition'; // Import the Twitter post composition
import StockMediaVideoComposition from './compositions/StockMediaVideoComposition'; // Import the Stock Media Video composition

// --- Constants and Helpers ---
// These are needed here for registration before the component instance exists.
const FPS = 30; // Frames per second
const DURATION_IN_SECONDS = 10; // Default duration if calculation fails
const BASE_DIMENSION = 1080; // Base dimension for calculations

// Composition IDs
const MY_VIDEO_COMPOSITION_ID = 'MyVideo'; // ID for the main composition
const REDDIT_POST_COMPOSITION_ID = 'RedditPostVideo'; // ID for the Reddit post composition
const TWITTER_POST_COMPOSITION_ID = 'TwitterPostVideo'; // ID for the Twitter post composition
const STOCK_MEDIA_VIDEO_COMPOSITION_ID = 'StockMediaVideo'; // ID for the Stock Media Video composition

// Duration constants
const DEFAULT_DURATION_FRAMES = DURATION_IN_SECONDS * FPS;
const REDDIT_POST_DURATION_FRAMES = 30 * FPS; // 30 seconds for Reddit post video
const STOCK_MEDIA_VIDEO_DURATION_FRAMES = 30 * FPS; // 30 seconds for stock media video

// Dimension constants
const REDDIT_POST_WIDTH = 1080; // Fixed width for Reddit post video
const REDDIT_POST_HEIGHT = 1920; // Fixed height for Reddit post video
const STOCK_MEDIA_VIDEO_WIDTH = 1080; // Fixed width for stock media video
const STOCK_MEDIA_VIDEO_HEIGHT = 1920; // Fixed height for stock media video

/**
 * Calculates width and height based on a base dimension and an aspect ratio string.
 * @param {string} aspectRatioString - e.g., "9:16", "16:9", "1:1"
 * @returns {{width: number, height: number}}
 */
const getDimensionsFromRatio = (aspectRatioString) => {
	let wRatio = 9, hRatio = 16; // Default to 9:16 (portrait)
	try {
        // Ensure aspectRatioString is treated as a string before splitting
        const ratioString = String(aspectRatioString ?? '9:16');
		[wRatio, hRatio] = ratioString.split(':').map(Number);
		if (!wRatio || !hRatio || isNaN(wRatio) || isNaN(hRatio) || wRatio <= 0 || hRatio <= 0) {
			throw new Error('Invalid ratio numbers (non-positive or NaN)');
		}
	} catch (e) {
		console.warn(`Invalid aspect ratio string: "${aspectRatioString}". Error: ${e?.message}. Defaulting to 9:16.`);
		wRatio = 9; hRatio = 16;
	}

	// Normalize ratios to avoid extremely large/small intermediate values if needed, though division handles it
	// const gcd = (a, b) => b === 0 ? a : gcd(b, a % b);
	// const commonDivisor = gcd(wRatio, hRatio);
	// wRatio /= commonDivisor;
	// hRatio /= commonDivisor;
	// Note: Normalization isn't strictly necessary for the calculation below

	if (wRatio > hRatio) { // Landscape or wide
		return { width: BASE_DIMENSION, height: Math.round(BASE_DIMENSION * (hRatio / wRatio)) };
	} else if (hRatio > wRatio) { // Portrait or tall
		return { width: Math.round(BASE_DIMENSION * (wRatio / hRatio)), height: BASE_DIMENSION };
	} else { // Square
		return { width: BASE_DIMENSION, height: BASE_DIMENSION };
	}
};

/**
 * Calculates the composition duration based on the end time of captions.
 * Falls back to a default duration if captions are missing, invalid, or calculation fails.
 * @param {object | undefined | null} videoData - The input data potentially containing captionJson.
 * @param {number} fps - Frames per second for converting time to frames.
 * @returns {number} Duration in frames.
 */
const calculateDuration = (videoData, fps) => {
	// Check if captionJson is a non-empty array
	if (Array.isArray(videoData?.captionJson) && videoData.captionJson.length > 0) {
		try {
			// Use reduce for safer iteration and finding the maximum valid end time
			const maxEndTimeMs = videoData.captionJson.reduce((maxEnd, segment) => {
				// Ensure segment exists and 'end' property is a valid, positive number
				const endTime = segment?.end;
				if (typeof endTime === 'number' && isFinite(endTime) && endTime > maxEnd) {
					return endTime; // Update maxEnd if current segment's end is larger and valid
				}
				return maxEnd; // Otherwise, keep the current maxEnd
			}, 0); // Initialize maxEnd to 0

			if (maxEndTimeMs > 0) {
                const calculatedFrames = Math.ceil(maxEndTimeMs / 1000 * fps);
                console.log(`Calculated duration from captions: ${maxEndTimeMs}ms -> ${calculatedFrames} frames.`);
				return calculatedFrames; // Convert milliseconds to frames, rounding up
			} else {
                console.warn("Captions found, but no valid positive end times detected.");
            }
		} catch (e) {
			console.error("Error calculating duration from captions:", e);
			// Fall through to default duration if any error occurs during processing
		}
	} else {
        console.log("No valid caption data found for duration calculation.");
    }

	// Fallback duration if captions are missing, empty, invalid, or processing failed
	console.warn(`Using default duration: ${DEFAULT_DURATION_FRAMES} frames.`);
	return DEFAULT_DURATION_FRAMES;
};

// --- Root Component ---
// This component reads input props and registers the Composition
// It does not render the video content itself, but defines its properties.
export const RemotionRoot = () => {
	// getInputProps() fetches props provided via CLI (--props) or renderMedia(..., { inputProps })
	const inputProps = getInputProps();
	const videoData = inputProps?.videoData; // Safely access videoData

	// --- Critical Check ---
	// If videoData is essential for rendering, failing early is better for automation.
	// If you prefer to render an error video, register a dedicated ErrorComposition instead.
	if (!videoData) {
		// Log the error for debugging in Studio or server logs
		console.log('CRITICAL ERROR: `videoData` not found in input props. Cannot register composition.');
		// Throwing an error will stop `remotion render` explicitly.
		
		// --- Alternative: Register an Error Composition ---
		// return (
		//   <Composition id="ErrorVideo" component={ErrorComponent} ... />
		// );
	}

	// --- Calculate Composition Properties ---
	// Safely get aspect ratio, defaulting to '9:16'
	const aspectRatio = videoData?.aspectRatio ?? '9:16';
	const { width, height } = getDimensionsFromRatio(aspectRatio);

	// Calculate duration, passing FPS for accuracy
	const durationInFrames = calculateDuration(videoData, FPS);

	// --- Log Registration Details ---
	console.log(
		`Registering Composition:
		  ID: {COMPOSITION_ID}
		  Aspect Ratio: ${aspectRatio}
		  Calculated Dimensions: ${width}x${height}
		  Calculated Duration: ${durationInFrames} frames (${(durationInFrames / FPS).toFixed(2)}s)
		  FPS: ${FPS}`
	);

	// --- Register the Composition ---
	return (
		<>
			<Composition
				id={MY_VIDEO_COMPOSITION_ID}
				component={MyComposition} // The actual component rendering the video
				durationInFrames={durationInFrames}
				fps={FPS}
				width={width}
				height={height}
				// Pass the entire videoData object as defaultProps.
				// MyComposition should use `useInputProps()` hook to access this data.
				defaultProps={{ 
					videoData: {
						...videoData,
						redditDurationFrames: REDDIT_POST_DURATION_FRAMES, // Pass Reddit duration
						twitterDurationFrames: TWITTER_POST_DURATION_FRAMES, // Pass Twitter duration
					}
				}}
			/>
			{/* Register the new RedditPostComposition */}
			<Composition
				id={REDDIT_POST_COMPOSITION_ID}
				component={RedditPostComposition}
				durationInFrames={REDDIT_POST_DURATION_FRAMES}
				fps={FPS}
				width={REDDIT_POST_WIDTH}
				height={REDDIT_POST_HEIGHT}
				// Pass videoData from inputProps to the RedditPostComposition
				defaultProps={{ videoData: inputProps.videoData }}
			/>
			{/* Register the new TwitterPostComposition */}
			<Composition
				id={TWITTER_POST_COMPOSITION_ID}
				component={TwitterPostComposition}
				durationInFrames={TWITTER_POST_DURATION_FRAMES} // Use specific Twitter duration
				fps={FPS}
				width={REDDIT_POST_WIDTH} // Use same width as Reddit for shorts
				height={REDDIT_POST_HEIGHT} // Use same height as Reddit for shorts
				// Pass videoData from inputProps to the TwitterPostComposition
				defaultProps={{ videoData: inputProps.videoData }}
			/>
			{/* Register the new StockMediaVideoComposition */}
			<Composition
				id={STOCK_MEDIA_VIDEO_COMPOSITION_ID}
				component={StockMediaVideoComposition}
				durationInFrames={STOCK_MEDIA_VIDEO_DURATION_FRAMES}
				fps={FPS}
				width={STOCK_MEDIA_VIDEO_WIDTH}
				height={STOCK_MEDIA_VIDEO_HEIGHT}
				// Pass videoData from inputProps to the StockMediaVideoComposition
				defaultProps={{ videoData: inputProps.videoData }}
			/>
		</>
	);
};
