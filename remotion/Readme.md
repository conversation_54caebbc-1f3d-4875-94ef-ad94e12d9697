# AI Reel Generator

This Remotion project is designed to generate short videos or "reels" dynamically based on provided input data and a selection of predefined templates. It's structured to be flexible and extensible, allowing for easy integration with external data sources and the addition of new video styles.

## Project Structure

The project follows a standard Remotion structure with key components:

-   `Root.jsx`: The main entry point of the Remotion project. It initializes the composition and retrieves input data passed during the rendering process (e.g., from Remotion Lambda or Remotion Studio).
-   `Composition.jsx`: Defines the primary Remotion composition (`MyComposition`). This component is responsible for handling dynamic video dimensions based on the input `aspectRatio`, selecting the appropriate video template based on the `templateId`, and passing the input `videoData` to the chosen template component.
-   `templates/`: This directory contains the different video templates available for generating reels.
-   `templates/index.js`: Acts as a registry for all video templates. It imports each template component and provides a `getTemplateComponent` function to retrieve a template by its ID.
-   Individual template files (e.g., `templates/DefaultTemplate.jsx`, `templates/Template1.jsx`): Each file defines a React component that renders the visual and audio content for a specific video style, consuming the `videoData` provided to it.

## Input Data (`videoData`)

This section describes the structure of the `videoData` object, which is the "exported data" passed into the Remotion composition as input properties during rendering.

The project expects a `videoData` object to be passed as input props. This object contains all the necessary information for generating the video. The structure and properties of `videoData` can vary slightly depending on the template used, but commonly include:

-   `audioUrl`: (string, optional) URL of the audio track for the video.
-   `images`: (string[], optional) An array of image URLs to be displayed in the video.
-   `captionJson`: (array, optional) An array of objects containing caption data, typically with `text`, `start`, and `end` properties for timed captions.
-   `captionStyle`: (object, optional) CSS style object to apply to the captions.
-   `captionContainerStyle`: (object, optional) CSS style object to apply to the container holding the captions.
-   `aspectRatio`: (string, optional) The desired aspect ratio of the video (e.g., '16:9', '9:16', '1:1'). Defaults to '9:16'.
-   `templateId`: (string, optional) The ID of the template to use for rendering the video. Defaults to 'default'.
-   `script`: (string, optional) Text content that might be used by some templates (e.g., for text overlays or narration).
-   `title`: (string, optional) A title for the video, potentially used by some templates.

Each template component will consume the relevant properties from this `videoData` object.

## Available Templates

The following templates are registered and available for use (identified by their `templateId`):

-   `default`: Default Template. Based on analysis, this template handles multiple images with transitions and zoom, and displays timed captions.
-   `template1`: Template 1 - Light Blue. Based on analysis, this template displays a title, the first image with a scale animation, and a truncated script. It does not appear to use multiple images or timed captions.
-   `template2`: Template 2 - Light Coral
-   `template3`: Template 3 - Light Green
-   `template4`: Template 4 - Light Yellow
-   `template5`: Template 5 - Light Pink
-   `template6`: Template 6 - Light Salmon
-   `template7`: Template 7 - Light Sky Blue
-   `template8`: Template 8 - Light Cyan

(Note: Detailed descriptions for templates 2-8 are not available without further code analysis of each template file.)

## How to Use

The main Remotion composition is exported as `RemotionRoot` from `src/Root.jsx`, allowing it to be imported and used within a larger application.

1.  **Provide Input Data:** Prepare a `videoData` object containing the necessary information (audio URL, image URLs, captions, desired template, aspect ratio, etc.).
2.  **Select Template and Aspect Ratio:** Ensure the `videoData` object includes the desired `templateId` and `aspectRatio`.
3.  **Render the Video:** Use the Remotion CLI or integrate with Remotion Lambda to render the `MyVideo` composition, passing the `videoData` as input props.

    Example using Remotion CLI:

    ```bash
    remotion render src/index.js MyVideo --props='{"videoData": { "audioUrl": "...", "images": ["...", "..."], "templateId": "default", "aspectRatio": "16:9" }}'
    ```

## Potential Issues and Improvements

-   **Dynamic Duration:** The current implementation has a `TODO` to dynamically set the video duration based on the audio length. This is a crucial improvement for synchronizing video content with audio.
-   **Input Data Validation:** While there is basic handling for missing `videoData` and invalid image sources, more robust validation of the `videoData` structure and content within each template could prevent unexpected errors.
-   **Template Specifics:** The usage and required properties of `videoData` can differ significantly between templates. More detailed documentation or examples for each template would be beneficial.
-   **Performance:** Rendering complex compositions with many images or long durations might encounter performance limitations. Optimizations within templates or rendering settings might be required.
-   **Template1 Script Truncation:** Template1 currently truncates the `script` to 100 characters. This might need to be adjusted or made configurable.
-   **Template1 Single Image:** Template1 only uses the first image provided in the `images` array. If multiple images are provided with this template, the others will be ignored.

## Adding New Templates

To add a new video template:

1.  Create a new `.jsx` file in the `remotion/templates/` directory (e.g., `NewTemplate.jsx`).
2.  Define your Remotion component within this file. It should accept `videoData` as a prop and render your desired video content.
3.  Import your new template component in `remotion/templates/index.js`.
4.  Add your new template to the `templates` object in `remotion/templates/index.js` with a unique ID and the component.

    ```javascript
    import { NewTemplate } from './NewTemplate';

    export const templates = {
      // ... existing templates
      newTemplate: {
        name: 'My Awesome New Template',
        component: NewTemplate,
      },
    };
    ```
