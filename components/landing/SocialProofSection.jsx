'use client'; // Directive remains

import React from 'react';

import { motion } from 'framer-motion';


const testimonialCardVariant = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: { duration: 0.5, ease: "easeOut" },
  },
};

const gridContainerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2,
    },
  },
};

function TestimonialCard({ quote, name, role, avatar }) {
  return (
    <motion.div
      className="p-6 border rounded-lg shadow-sm bg-card space-y-4"
      variants={testimonialCardVariant}
    >
      <p className="text-muted-foreground italic">"{quote}"</p>
      <div className="flex items-center space-x-3">
       
        <div>
          <p className="font-semibold">{name}</p>
          <p className="text-sm text-muted-foreground">{role}</p>
        </div>
      </div>
    </motion.div>
  );
}

export default function SocialProofSection() {
  const testimonials= [
    {
      quote: "AI Shorts Studio cut my content creation time in half! The quality is amazing.",
      name: "<PERSON>",
      role: "Social Media Manager",
      avatar: "/placeholder-avatar-1.jpg", // Example path - update if needed
    },
    {
      quote: "Finally, an easy way to make professional-looking shorts without complex software.",
      name: "Samantha Lee",
      role: "Fitness Influencer",
       avatar: "/placeholder-avatar-2.jpg", // Example path - update if needed
    },
     {
      quote: "The AI script generation is surprisingly good. It's a game-changer for brainstorming.",
      name: "David Chen",
      role: "Marketing Consultant",
       avatar: "/placeholder-avatar-3.jpg", // Example path - update if needed
    },
  ];

  const clientLogos = [
    "/placeholder-logo-1.svg", // Example path - update if needed
    "/placeholder-logo-2.svg", // Example path - update if needed
    "/placeholder-logo-3.svg", // Example path - update if needed
    "/placeholder-logo-4.svg", // Example path - update if needed
    "/placeholder-logo-5.svg", // Example path - update if needed
  ];

  return (
    <section className="w-full py-16 md:py-24 bg-muted/40 dark:bg-muted/20 overflow-hidden">
      <div className="container mx-auto px-4 space-y-16">
        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.1 }}
        >
          <motion.h2
            className="text-3xl md:text-4xl font-bold text-center mb-12"
            variants={testimonialCardVariant}
          >
            Trusted by Creators & Brands
          </motion.h2>
          <motion.div
            className="grid grid-cols-1 md:grid-cols-3 gap-6 md:gap-8"
            variants={gridContainerVariants}
          >
            {testimonials.map((testimonial) => (
              <TestimonialCard key={testimonial.name} {...testimonial} />
            ))}
          </motion.div>
        </motion.div>

        <motion.div
          className="text-center"
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          viewport={{ once: true, amount: 0.3 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
           <h3 className="text-lg font-semibold text-muted-foreground mb-6">Powering content for leading names</h3>
           <div className="flex flex-wrap justify-center items-center gap-8 md:gap-12 opacity-75">
             {clientLogos.map((logo, index) => (
               // Ensure image paths are correct
               <img key={index} src={logo} alt={`Client Logo ${index + 1}`} className="h-8 md:h-10" />
             ))}
           </div>
        </motion.div>
      </div>
    </section>
  );
}