'use client'; // Directive remains

import { Bo<PERSON>, Clapperboard, Palette, Share2 } from 'lucide-react';
import { motion } from 'framer-motion';


const featureItemVariants = {
  hidden: { opacity: 0, y: 0, rotate: 0 },
  visible: {
    opacity: 1,
    y: 0,
    rotate: 0,
    transition: {
      duration: 0.6,
      ease: "easeOut",
      type: "spring",
      damping: 12,
      stiffness: 100
    },
  },
};

const gridContainerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.15,
    },
  },
};

function FeatureItem({ icon: Icon, title, description }) {
  return (
    <motion.div
      className="relative flex flex-col items-center text-center p-6 bg-background/80 backdrop-blur-sm border border-border/50 rounded-2xl shadow-soft hover:shadow-medium interactive-scale transition-all duration-300"
      variants={featureItemVariants}
      whileHover={{ scale: 1.02 }}
      transition={{ duration: 0.3, ease: "easeOut" }}
    >
      <div className="mb-4 p-3 bg-gradient-to-br from-emerald-500 to-purple-500 rounded-xl shadow-soft">
        <Icon className="h-6 w-6 text-white" />
      </div>
      <h3 className="text-heading-3 text-gradient mb-3">{title}</h3>
      <p className="text-body-small text-muted-foreground leading-relaxed">{description}</p>
    </motion.div>
  );
}

export default function FeaturesSection() {
  const features = [
    {
      icon: Bot,
      title: "AI Superpowers",
      description: "Generate scripts, voiceovers, captions, and visuals automatically. Smart editing like auto-cropping saves you time.",
    },
    {
      icon: Clapperboard,
      title: "Template Library",
      description: "Kickstart your creation with diverse templates for various niches like Fitness, Gaming, Marketing, and more.",
    },
    {
      icon: Palette,
      title: "Full Customization",
      description: "Tailor your shorts with custom text overlays, background music, filters, and your own branding elements.",
    },
    {
      icon: Share2,
      title: "Easy Export & Sharing",
      description: "Download in various resolutions or share directly to TikTok, Instagram Reels, YouTube Shorts, and other platforms.",
    },
  ];

  return (
    <section id="features" className="relative w-full py-16 md:py-24 bg-gradient-to-br from-emerald-50/50 to-purple-50/50 dark:from-emerald-950/10 dark:to-purple-950/10 overflow-hidden">
      {/* Background decoration */}
      <div className="absolute top-0 left-0 w-64 h-64 bg-gradient-to-br from-emerald-400/10 to-purple-400/10 rounded-full blur-3xl"></div>
      <div className="absolute bottom-0 right-0 w-48 h-48 bg-gradient-to-tr from-purple-400/10 to-emerald-400/10 rounded-full blur-3xl"></div>

      <div className="relative container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-16">
          <motion.h2
            className="text-heading-1 text-gradient mb-6"
            initial={{ opacity: 0, y: -20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.5 }}
            transition={{ duration: 0.5, ease: "easeOut" }}
          >
            Everything You Need to Go Viral
          </motion.h2>
          <motion.p
            className="text-body-large text-muted-foreground max-w-3xl mx-auto"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.5 }}
            transition={{ duration: 0.5, ease: "easeOut", delay: 0.1 }}
          >
            Our comprehensive AI-powered platform provides all the tools you need to create engaging videos across multiple platforms and formats.
          </motion.p>
        </div>

        <motion.div
          className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 md:gap-8"
          variants={gridContainerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.2 }}
        >
          {features.map((feature) => (
            <FeatureItem key={feature.title} {...feature} />
          ))}
        </motion.div>
      </div>
    </section>
  );
}