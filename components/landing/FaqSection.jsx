'use client'; // Directive remains

import React from 'react';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { motion } from 'framer-motion';



const faqItemVariant = {
  hidden: { opacity: 0, x: -20 },
  visible: {
    opacity: 1,
    x: 0,
    transition: { duration: 0.4, ease: "easeOut" },
  },
};

const faqContainerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.15,
    },
  },
};

export default function FaqSection() {
  const faqs = [
    {
      question: "Who owns the content created with AI Shorts Studio?",
      answer: "You own all the rights to the videos you create and download using our platform. You can use them commercially without any restrictions from us.",
    },
    {
      question: "What video formats and resolutions are supported?",
      answer: "We primarily focus on vertical formats suitable for TikTok, Reels, and Shorts (9:16 aspect ratio). Export options typically include MP4 format in resolutions like 720p and 1080p, depending on your plan.",
    },
    {
      question: "How does the AI generation work?",
      answer: "Our AI analyzes your input (topic, script) and leverages large language models and media databases to generate relevant script variations, select appropriate visuals, and synthesize natural-sounding voiceovers.",
    },
    {
      question: "Can I upload my own voiceover or music?",
      answer: "Yes! While we offer AI voiceovers, you can upload your own audio files. You can also add your own background music tracks to customize your videos further.",
    },
     {
      question: "What if I need help or have more questions?",
      answer: "We offer support through our Help Center documentation, email support, and live chat for Pro/Enterprise users. Don't hesitate to reach out!",
    },
  ];

  return (
    <motion.section id="faq"
      className="w-full py-16 md:py-24 bg-muted/40 dark:bg-muted/20 overflow-hidden"
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true, amount: 0.1 }}
    >
      <div className="container mx-auto px-4 max-w-3xl">
        <motion.h2
          className="text-3xl md:text-4xl font-bold text-center mb-12"
          variants={faqItemVariant}
        >
          Frequently Asked Questions
        </motion.h2>

        <motion.div
          className="space-y-4 mb-12"
          variants={faqContainerVariants}
        >
          {faqs.map((faq, index) => (
            <motion.details
              key={index}
              className="group border rounded-lg p-4 bg-background hover:bg-muted/50 hover:scale-[1.01] transition-all duration-300"
              variants={faqItemVariant}
              whileHover={{ scale: 1.01 }}
              transition={{ duration: 0.3, ease: "easeOut" }}
            >
              <summary className="list-none flex justify-between items-center cursor-pointer font-medium text-left">
                {faq.question}
                <span className="ml-2 transition-transform duration-200 group-open:rotate-45">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-plus"><path d="M5 12h14"/><path d="M12 5v14"/></svg>
                </span>
              </summary>
              <div className="mt-3 pt-3 border-t text-muted-foreground text-sm">
                {faq.answer}
              </div>
            </motion.details>
          ))}
        </motion.div>

        <motion.div
          className="text-center space-y-4"
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          viewport={{ once: true, amount: 0.5 }}
          transition={{ duration: 0.5, delay: 0.3 }}
        >
           <p className="text-muted-foreground">Can't find the answer you're looking for?</p>
           <div>
              {/* Link to contact remains the same - no Clerk needed here */}
             <Link href="/contact" passHref>
                <Button variant="outline">Contact Support</Button>
             </Link>
           </div>
        </motion.div>
      </div>
    </motion.section>
  );
}