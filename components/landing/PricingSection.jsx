'use client'; // Directive remains

import React from 'react';
import { Button } from '@/components/ui/button';
import { Check } from 'lucide-react';
import { motion } from 'framer-motion';
import { useUser, SignUpButton } from '@clerk/nextjs'; // Import Clerk's hooks and components
import Link from 'next/link'; // Import Link for the Contact button


const pricingCardVariant = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: { duration: 0.5, ease: "easeOut" },
  },
};

const gridContainerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2,
    },
  },
};

function PricingCard({ title, price, credits, description, features, isPopular, ctaText, ctaLink }) {
  const { isSignedIn } = useUser();
  const postAuthRedirectUrl = "/dashboard"; // Define where users go after sign-up/sign-in

  const renderButton = () => {
    if (isSignedIn) {
      // If user is signed in, link to the billing page
      return (
        <Link href="/dashboard/billing" passHref>
          <Button className={`w-full ${isPopular ? '' : 'variant="outline"'}`}>
            Get Credits
          </Button>
        </Link>
      );
    } else if (ctaLink) {
      // Existing logic for contact link (Enterprise tier)
      return (
        <Link href={ctaLink} passHref>
          <Button className={`w-full ${isPopular ? '' : 'variant="outline"'}`}>
            {ctaText}
          </Button>
        </Link>
      );
    } else {
      // If user is not signed in and it's not the contact link, show sign up button
      return (
        <SignUpButton
          
        >
          {/* Use your existing shadcn/ui Button for styling */}
          <Button className={`w-full ${isPopular ? '' : 'variant="outline"'}`}>
            {ctaText}
          </Button>
        </SignUpButton>
      );
    }
  };

  return (
    <motion.div
      className={`relative p-6 md:p-8 border rounded-lg shadow-sm bg-card flex flex-col ${isPopular ? 'border-primary border-2' : ''} hover:shadow-md hover:scale-[1.02] transition-all duration-300`}
      variants={pricingCardVariant}
      whileHover={{ scale: 1.02 }}
      transition={{ duration: 0.07, ease: "easeOut" }}
    >
      {isPopular && (
        <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-primary text-primary-foreground px-3 py-1 text-xs font-semibold rounded-full">
          Most Popular
        </div>
      )}
      <h3 className="text-xl font-semibold mb-2">{title}</h3>
      <p className="text-muted-foreground text-sm mb-4">{description}</p>
      <div className="mb-6">
        <span className="text-4xl font-bold">{price}</span>
        {credits && <span className="text-muted-foreground"> ({credits} credits)</span>}
      </div>
      <ul className="space-y-3 mb-8 flex-grow">
        {features.map((feature, index) => (
          <li key={index} className="flex items-start space-x-2">
            <Check className="h-5 w-5 text-green-500 flex-shrink-0 mt-0.5" />
            <span className="text-sm">{feature}</span>
          </li>
        ))}
      </ul>
      {renderButton()} {/* Render the appropriate button */}
    </motion.div>
  );
}

export default function PricingSection() {
  const tiers = [
    {
      title: "Small Package",
      price: "$10",
      credits: 100,
      description: "Perfect for getting started with video generation.",
      features: [
        "100 Credits",
        "Access to all templates",
        "Standard AI voiceovers",
        "No watermarks",
      ],
      ctaText: "Get Started", // Keep "Get Started" for signed-out users
    },
    {
      title: "Medium Package",
      price: "$45",
      credits: 500,
      description: "More credits for frequent video creators.",
      features: [
        "500 Credits",
        "Access to all templates",
        "Premium AI voiceovers",
        "No watermarks",
        "Priority processing",
      ],
      isPopular: true,
      ctaText: "Get Started", // Keep "Get Started" for signed-out users
    },
    {
      title: "Large Package",
      price: "$80",
      credits: 1000,
      description: "Best value for high-volume video production.",
      features: [
        "1000 Credits",
        "Access to all templates",
        "Premium AI voiceovers",
        "No watermarks",
        "Priority processing",
        "Dedicated support",
      ],
      ctaText: "Get Started", // Keep "Get Started" for signed-out users
    },
   
  ];

  const textFadeInVariant = {
    hidden: { opacity: 0 },
    visible: { opacity: 1, transition: { duration: 0.6 } },
  };

  return (
    <motion.section id="pricing"
      className="w-full py-16 md:py-24 bg-background overflow-hidden"
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true, amount: 0.1 }}
    >
      <div className="container mx-auto px-4">
        <motion.h2
          className="text-3xl md:text-4xl font-bold text-center mb-4"
          variants={textFadeInVariant}
        >
          Simple, Transparent Pricing
        </motion.h2>
        <motion.p
          className="text-lg text-muted-foreground text-center mb-12 md:mb-16 max-w-xl mx-auto"
          variants={textFadeInVariant}
          transition={{ delay: 0.1 }}
        >
          Choose the plan that fits your creative needs. Cancel anytime.
        </motion.p>

        <motion.div
          className="grid grid-cols-1 md:grid-cols-3 gap-6 md:gap-8 max-w-5xl mx-auto"
          variants={gridContainerVariants}
        >
          {tiers.map((tier) => (
            <PricingCard key={tier.title} {...tier} />
          ))}
        </motion.div>

         <motion.div
           className="flex justify-center items-center gap-4 mt-12 opacity-60"
           variants={textFadeInVariant}
           transition={{ delay: 0.2 }}
         >
            <span className="text-sm text-muted-foreground">Secure Payments:</span>
            <div className="h-6 w-10 bg-muted rounded flex items-center justify-center text-xs">SSL</div>
            <div className="h-6 w-10 bg-muted rounded flex items-center justify-center text-xs">Visa</div>
            <div className="h-6 w-10 bg-muted rounded flex items-center justify-center text-xs">MC</div>
            <div className="h-6 w-10 bg-muted rounded flex items-center justify-center text-xs">PayPal</div>
         </motion.div>
      </div>
    </motion.section>
  );
}