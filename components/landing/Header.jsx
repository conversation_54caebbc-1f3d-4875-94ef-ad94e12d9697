'use client'; // Keep this directive for hooks and event handlers

import React from 'react';
import Link from 'next/link';
import { Button } from '../ui/button';
import Image from 'next/image';
import mainlogo from '../../lib/logo.png';
import { motion } from 'framer-motion';

// Import Clerk components
import { SignedIn, SignedOut, UserButton, SignInButton } from "@clerk/nextjs";

export default function Header() {
  const postAuthRedirectUrl = process.env.NEXT_PUBLIC_POST_AUTH_REDIRECT_URL || "/dashboard";

  return (
    <header className="sticky top-0 z-50 w-full border-b border-border/40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      {/* Container for logo and navigation (left/center part) */}
      <div
        className="container flex h-14 max-w-screen-2xl items-center px-4 sm:px-6 lg:px-8"
        // ^-- REMOVED 'justify-between'
      >
        {/* Left side: Logo and Brand Name + Main Navigation */}
        <div className="flex items-center"> {/* This div now groups logo and nav */}
          <Link
            href="/"
            className="relative flex items-center py-2 rounded-lg focus-visible:ring-2 focus-visible:ring-primary/50 outline-none mr-4 md:mr-6"
            aria-label="Go to homepage"
          >
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, ease: "easeOut" }}
              className="relative flex items-center justify-center h-10 w-10 sm:h-12 sm:w-12" // Adjusted size
            >
              <Image
                src={mainlogo}
                alt="AI Short Logo"
                fill
                className="object-contain rounded-full select-none pointer-events-none"
                sizes="(max-width: 640px) 40px, (max-width: 768px) 48px, 64px"
                priority
                draggable={false}
              />
            </motion.div>
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, ease: "easeOut", delay: 0.1 }}
              className="flex flex-col justify-center ml-2"
            >
              <span className="text-lg sm:text-xl md:text-2xl font-extrabold tracking-tight text-gradient leading-none">
                AI Short
              </span>
              <span className="hidden sm:block text-[10px] sm:text-xs text-muted-foreground font-medium tracking-wide uppercase leading-none mt-0.5">
                Create Viral Clips
              </span>
            </motion.div>
          </Link>

          {/* Main Navigation - Stays with the logo group */}
          <nav className="hidden md:flex items-center space-x-4 lg:space-x-6 text-sm font-medium ml-6">
            <Link href="/#features" className="text-muted-foreground transition-colors hover:text-primary">
              Features
            </Link>
            <Link href="/#how-it-works" className="text-muted-foreground transition-colors hover:text-primary">
              How It Works
            </Link>
            <Link href="/#pricing" className="text-muted-foreground transition-colors hover:text-primary">
              Pricing
            </Link>
            <Link href="/#faq" className="text-muted-foreground transition-colors hover:text-primary">
              FAQ
            </Link>
          </nav>
        </div>
      </div>

      {/* Right side Authentication section - ABSOLUTELY POSITIONED to the edge of the header */}
      <div className="absolute top-0 right-0 h-14 flex items-center space-x-2 md:space-x-3 px-4 sm:px-6 lg:px-8">
        {/* ^-- Key changes: 'absolute top-0 right-0 h-14 flex items-center' */}
        {/*     'px-4 sm:px-6 lg:px-8' adds padding from the screen edge. */}
        {/*     Removed 'ml-auto' and 'flex-shrink-0' as they are not needed with absolute positioning. */}
        <SignedIn>
           {/* Changed Dashboard link to a button for better visual prominence */}
           <Link href={postAuthRedirectUrl} passHref>
             <Button variant="outline" size="sm" className="hidden md:inline-flex"> {/* Use inline-flex to match button display */}
               Dashboard
             </Button>
           </Link>
        </SignedIn>
        <SignedIn>
          <UserButton/>
        </SignedIn>
        <SignedOut>
           <SignInButton mode="modal" redirectUrl={postAuthRedirectUrl}>
               <Button variant="outline" size="sm">
                   Sign In
               </Button>
             </SignInButton>
          </SignedOut>
        </div>
    </header>
  );
}