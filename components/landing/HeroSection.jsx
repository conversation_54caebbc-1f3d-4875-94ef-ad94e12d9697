'use client'; // Directive remains

import { Button } from "@/components/ui/button";
import { motion } from "framer-motion";
import { SignUpButton } from "@clerk/nextjs";

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2,
      delayChildren: 0.1,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.5,
      ease: "easeOut",
    },
  },
};

export default function HeroSection() {
  const postAuthRedirectUrl = "/dashboard";

  return (
    <motion.section
      className="relative flex flex-col items-center justify-center min-h-screen px-4 md:px-20 text-center pt-24 pb-20 md:pt-32 md:pb-28 overflow-hidden bg-gradient-to-br from-purple-50 via-blue-50 to-purple-50 dark:from-purple-950/20 dark:via-blue-950/20 dark:to-purple-950/20"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      {/* Background decoration */}
      <div className="absolute top-0 right-0 w-64 h-64 bg-gradient-to-br from-purple-400/10 to-blue-400/10 rounded-full blur-3xl"></div>
      <div className="absolute bottom-0 left-0 w-48 h-48 bg-gradient-to-tr from-blue-400/10 to-purple-400/10 rounded-full blur-3xl"></div>

      {/* Demo Video Placeholder */}
      <motion.div
        className="relative mb-12 w-full max-w-4xl bg-gradient-to-br from-muted to-muted/50 rounded-2xl overflow-hidden border border-border/50 shadow-medium"
        variants={itemVariants}
      >
        <div className="aspect-video flex items-center justify-center bg-gradient-to-br from-indigo-500 to-purple-600">
          <div className="text-center p-8">
            <div className="text-6xl mb-4">🎬</div>
            <h3 className="text-heading-2 text-white mb-2">AI Video Creation Demo</h3>
            <p className="text-body text-white/80">
              Watch how our AI transforms ideas into viral videos in seconds
            </p>
          </div>
        </div>
      </motion.div>

      {/* Heading */}
      <motion.h1
        className="text-heading-1 text-gradient mb-6 leading-tight max-w-5xl"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        <motion.span variants={itemVariants}>Create</motion.span>{' '}
        <motion.span variants={itemVariants}>Stunning</motion.span>{' '}
        <motion.span variants={itemVariants} className="text-gradient">AI-Powered</motion.span>{' '}
        <motion.span variants={itemVariants}>Videos</motion.span>{' '}
        <motion.span variants={itemVariants}>in</motion.span>{' '}
        <motion.span variants={itemVariants}>Seconds</motion.span>
      </motion.h1>

      {/* Subheadline */}
      <motion.p
        className="text-body-large text-muted-foreground max-w-3xl mb-12"
        variants={itemVariants}
      >
        Transform ideas into viral clips with AI magic. Create Reddit posts, Twitter videos, stock media content, and podcast clips with our comprehensive video generation platform.
      </motion.p>

      {/* CTA Buttons */}
      <motion.div variants={itemVariants} className="flex flex-col sm:flex-row gap-4 items-center">
        <SignUpButton>
          <Button size="lg" className="bg-gradient-to-br from-indigo-500 to-purple-500 hover:from-indigo-600 hover:to-purple-600 text-white border-0 shadow-medium interactive-scale h-12 px-8">
            <span className="text-body font-semibold">Get Started for Free</span>
          </Button>
        </SignUpButton>
        <Button variant="outline" size="lg" className="h-12 px-8">
          <span className="text-body font-semibold">Watch Demo</span>
        </Button>
      </motion.div>

      {/* Feature highlights */}
      <motion.div
        variants={itemVariants}
        className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl w-full"
      >
        <div className="text-center p-6 bg-background/80 backdrop-blur-sm border border-border/50 rounded-xl">
          <div className="text-3xl mb-3">🤖</div>
          <h3 className="text-body font-semibold mb-2">AI-Powered</h3>
          <p className="text-body-small text-muted-foreground">Advanced AI handles scripts, voiceovers, and visuals</p>
        </div>
        <div className="text-center p-6 bg-background/80 backdrop-blur-sm border border-border/50 rounded-xl">
          <div className="text-3xl mb-3">⚡</div>
          <h3 className="text-body font-semibold mb-2">Lightning Fast</h3>
          <p className="text-body-small text-muted-foreground">Create professional videos in seconds, not hours</p>
        </div>
        <div className="text-center p-6 bg-background/80 backdrop-blur-sm border border-border/50 rounded-xl">
          <div className="text-3xl mb-3">🎯</div>
          <h3 className="text-body font-semibold mb-2">Multi-Platform</h3>
          <p className="text-body-small text-muted-foreground">Perfect for TikTok, Instagram, YouTube, and more</p>
        </div>
      </motion.div>
    </motion.section>
  );
}