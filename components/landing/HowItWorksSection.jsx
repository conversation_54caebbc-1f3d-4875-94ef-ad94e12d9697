"use client" // Directive remains

import React from 'react';
import { motion } from 'framer-motion';


const stepItemVariants = {
  hidden: { opacity: 0, y: 50, rotate: -5 },
  visible: {
    opacity: 1,
    y: 0,
    rotate: 0,
    transition: {
      duration: 0.6,
      ease: "easeOut",
      type: "spring", // Use spring physics for a subtle bounce
      damping: 10,
      stiffness: 100
    },
  },
};

const gridContainerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2,
    },
  },
};

function Step({ stepNumber, title, description }) {
  return (
    <motion.div
      className="relative flex flex-col items-center text-center p-6 bg-background/80 backdrop-blur-sm border border-border/50 rounded-2xl shadow-soft hover:shadow-medium interactive-scale transition-all duration-300"
      variants={stepItemVariants}
    >
      <div className="mb-6 flex items-center justify-center w-16 h-16 rounded-2xl bg-gradient-to-br from-blue-500 to-cyan-500 text-white font-bold text-2xl shadow-soft">
        {stepNumber}
      </div>
      <h3 className="text-heading-3 text-gradient mb-3">{title}</h3>
      <p className="text-body-small text-muted-foreground leading-relaxed">{description}</p>
    </motion.div>
  );
}

export default function HowItWorksSection() {
  const steps= [
    {
      stepNumber: 1,
      title: "Provide Your Idea",
      description: "Start with a topic, script, existing video, or choose from our diverse template library.",
    },
    {
      stepNumber: 2,
      title: "AI Magic & Customize",
      description: "Our AI generates visuals, voiceovers, and captions. Fine-tune with text, music, and branding tools.",
    },
    {
      stepNumber: 3,
      title: "Export & Share",
      description: "Preview your creation, make final adjustments, and download or share directly to your favorite platforms.",
    },
  ];

  return (
    <motion.section id="how-it-works"
      className="relative w-full py-16 md:py-24 bg-gradient-to-br from-blue-50/50 to-cyan-50/50 dark:from-blue-950/10 dark:to-cyan-950/10 overflow-hidden"
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true, amount: 0.1 }}
    >
      {/* Background decoration */}
      <div className="absolute top-0 right-0 w-64 h-64 bg-gradient-to-br from-blue-400/10 to-cyan-400/10 rounded-full blur-3xl"></div>
      <div className="absolute bottom-0 left-0 w-48 h-48 bg-gradient-to-tr from-cyan-400/10 to-blue-400/10 rounded-full blur-3xl"></div>

      <div className="relative container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-16">
          <motion.h2
            className="text-heading-1 text-gradient mb-6"
            initial={{ opacity: 0, y: -20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.5 }}
            transition={{ duration: 0.5, ease: "easeOut" }}
          >
            Get Started in 3 Simple Steps
          </motion.h2>
          <motion.p
            className="text-body-large text-muted-foreground max-w-3xl mx-auto"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.5 }}
            transition={{ duration: 0.5, ease: "easeOut", delay: 0.1 }}
          >
            Our streamlined process makes video creation effortless. From concept to viral content in minutes.
          </motion.p>
        </div>

        <motion.div
          className="grid grid-cols-1 md:grid-cols-3 gap-8 md:gap-12 mb-12 md:mb-16"
          variants={gridContainerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.2 }}
        >
          {steps.map((step) => (
            <Step key={step.stepNumber} {...step} />
          ))}
        </motion.div>

        <motion.div
          className="max-w-4xl mx-auto bg-gradient-to-br from-muted to-muted/50 rounded-2xl overflow-hidden border border-border/50 shadow-medium"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, ease: "easeOut" }}
        >
          <div className="aspect-video flex items-center justify-center bg-gradient-to-br from-blue-500 to-cyan-600">
            <div className="text-center p-8">
              <div className="text-6xl mb-4">🎯</div>
              <h3 className="text-heading-2 text-white mb-2">Interactive Walkthrough</h3>
              <p className="text-body text-white/80">
                See our complete video creation process in action
              </p>
            </div>
          </div>
        </motion.div>
      </div>
    </motion.section>
  );
}