'use client'; // Directive remains

import React from 'react';
import { CheckCircle } from 'lucide-react';
import { motion } from 'framer-motion';


const benefitItemVariant = {
  hidden: { opacity: 0 },
  visible: { opacity: 1, transition: { duration: 0.5, delay: 0.2 } },
};

function BenefitItem({ title, description }) {
  return (
    <motion.div className="flex items-start space-x-3" variants={benefitItemVariant}>
      <CheckCircle className="h-5 w-5 text-green-500 mt-1 flex-shrink-0" />
      <div>
        <h4 className="font-semibold">{title}</h4>
        <p className="text-sm text-muted-foreground">{description}</p>
      </div>
    </motion.div>
  );
}

export default function UseCasesSection() {
  const benefits = [
    {
      title: "Save Massive Time",
      description: "Automate scriptwriting, voiceovers, and visual selection. Focus on ideas, not tedious editing.",
    },
    {
      title: "Boost Engagement",
      description: "Create professional, eye-catching shorts that capture attention and drive views on social platforms.",
    },
    {
      title: "Effortless Content Creation",
      description: "Perfect for marketers, influencers, educators, and businesses needing consistent video content.",
    },
     {
      title: "Professional Quality",
      description: "Access high-quality AI voices, relevant visuals, and smooth animations without needing expert skills.",
    },
  ];

  const imageColumnVariant = {
    hidden: { opacity: 0, x: -50 },
    visible: { opacity: 1, x: 0, transition: { duration: 0.6, ease: "easeOut" } },
  };

  const textColumnVariant = {
    hidden: { opacity: 0, x: 50 },
    visible: { opacity: 1, x: 0, transition: { duration: 0.6, ease: "easeOut", staggerChildren: 0.1 } },
  };


  return (
    <section className="w-full py-16 md:py-24 bg-background overflow-hidden">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-12 md:gap-16 items-center">
          <motion.div
            className="order-last md:order-first"
            variants={imageColumnVariant}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, amount: 0.3 }}
          >
             <div className="w-full h-64 md:h-80 bg-muted rounded-lg flex items-center justify-center text-muted-foreground border shadow-sm">
                [Engaging Visual/Use Case Illustration Placeholder]
             </div>
          </motion.div>

          <motion.div
            className="space-y-8"
            variants={textColumnVariant}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, amount: 0.3 }}
          >
            <motion.h2 className="text-3xl md:text-4xl font-bold" variants={benefitItemVariant}>
              Unlock Your Content Potential
            </motion.h2>
            <motion.p className="text-lg text-muted-foreground" variants={benefitItemVariant}>
              Whether you're a seasoned creator or just starting, AI Shorts Studio empowers you to produce high-quality short videos faster than ever before.
            </motion.p>
            <div className="space-y-4">
              {benefits.map((benefit) => (
                <BenefitItem key={benefit.title} {...benefit} />
              ))}
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}