/**
 * Unified Option Selector Component
 * Consolidates AspectRatioSelector, AudioSpeedSelector, BackgroundMusicSelector, and other selector patterns
 * Handles grid-based selection with icons, descriptions, previews, and validation
 */

import React, { useState, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Play, Pause, Volume2, VolumeX } from 'lucide-react';
import { cn } from '@/lib/utils';

/**
 * OptionSelector Component
 * @param {Object} props - Component props
 * @param {string} props.title - Selector title
 * @param {string} props.description - Selector description
 * @param {Array} props.options - Array of option objects
 * @param {string|number} props.selectedValue - Currently selected value
 * @param {Function} props.onSelect - Selection handler
 * @param {string} props.layout - Layout type ('grid', 'dropdown', 'slider', 'cards')
 * @param {number} props.columns - Number of grid columns (for grid layout)
 * @param {boolean} props.showPreview - Whether to show preview functionality
 * @param {boolean} props.disabled - Whether selector is disabled
 * @param {string} props.className - Additional CSS classes
 * @param {boolean} props.required - Whether selection is required
 * @param {string} props.placeholder - Placeholder text for dropdown
 * @param {Object} props.sliderConfig - Configuration for slider layout
 * @param {Function} props.renderCustomOption - Custom option renderer
 * @param {boolean} props.allowDeselect - Whether to allow deselecting current option
 */
export function OptionSelector({
  title,
  description,
  options = [],
  selectedValue,
  onSelect,
  layout = 'grid',
  columns = 3,
  showPreview = false,
  disabled = false,
  className = '',
  required = false,
  placeholder = 'Select an option',
  sliderConfig = {},
  renderCustomOption,
  allowDeselect = false,
  ...props
}) {
  const [playingPreview, setPlayingPreview] = useState(null);
  const audioRef = useRef(null);

  // Handle option selection
  const handleSelect = (value) => {
    if (disabled) return;
    
    // Allow deselection if enabled and same value is clicked
    if (allowDeselect && selectedValue === value) {
      onSelect?.(null);
      return;
    }
    
    // Check if option is enabled
    const option = options.find(opt => opt.value === value || opt.id === value);
    if (option && option.enabled === false) return;
    
    onSelect?.(value);
  };

  // Handle audio preview
  const handlePreview = (event, optionValue, previewUrl) => {
    event.stopPropagation();
    
    if (!audioRef.current || !previewUrl) return;
    
    if (playingPreview === optionValue) {
      audioRef.current.pause();
      setPlayingPreview(null);
    } else {
      audioRef.current.src = previewUrl;
      audioRef.current.play().catch(error => console.error("Error playing audio:", error));
      setPlayingPreview(optionValue);
    }
  };

  // Handle audio end
  const handleAudioEnd = () => {
    setPlayingPreview(null);
  };

  // Render grid layout
  const renderGridLayout = () => (
    <div className={cn(
      'grid gap-4',
      columns === 2 && 'grid-cols-2',
      columns === 3 && 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3',
      columns === 4 && 'grid-cols-2 sm:grid-cols-3 md:grid-cols-4'
    )}>
      {options.map((option) => {
        const optionValue = option.value || option.id;
        const isSelected = selectedValue === optionValue;
        const isEnabled = option.enabled !== false;
        const isPlaying = playingPreview === optionValue;
        const IconComponent = option.icon;

        const content = (
          <div
            onClick={() => handleSelect(optionValue)}
            className={cn(
              "group rounded-lg border bg-background p-4 transition-all duration-200 ease-in-out flex flex-col h-full",
              isEnabled ? "cursor-pointer hover:shadow-md" : "opacity-50 cursor-not-allowed",
              isSelected && isEnabled 
                ? "border-primary ring-2 ring-primary ring-offset-2 ring-offset-background" 
                : (isEnabled ? "hover:border-muted-foreground/50" : "border-dashed"),
              disabled && "opacity-50 cursor-not-allowed"
            )}
          >
            {/* Icon */}
            {IconComponent && (
              <div className={cn(
                "p-2 rounded-lg mb-3 self-center",
                option.color || "bg-muted"
              )}>
                <IconComponent className="h-4 w-4 text-white" />
              </div>
            )}

            {/* Image */}
            {option.imageUrl && (
              <div className="mb-3">
                <img 
                  src={option.imageUrl} 
                  alt={option.name}
                  className="w-full h-20 object-cover rounded"
                />
              </div>
            )}

            {/* Content */}
            <div className="flex-1 text-center">
              <h4 className="font-medium text-sm mb-1">{option.name}</h4>
              {option.description && (
                <p className="text-xs text-muted-foreground">{option.description}</p>
              )}
              {option.metadata && (
                <div className="mt-2 text-xs text-muted-foreground">
                  {Object.entries(option.metadata).map(([key, value]) => (
                    <div key={key}>{key}: {value}</div>
                  ))}
                </div>
              )}
            </div>

            {/* Preview Button */}
            {showPreview && option.previewUrl && (
              <Button
                variant={isPlaying ? "secondary" : "outline"}
                size="sm"
                onClick={(e) => handlePreview(e, optionValue, option.previewUrl)}
                className="w-full mt-3 text-xs h-8"
                aria-label={`Preview ${option.name}`}
              >
                {isPlaying ? <Pause className="h-4 w-4 mr-1.5" /> : <Play className="h-4 w-4 mr-1.5" />}
                {isPlaying ? 'Pause' : 'Preview'}
              </Button>
            )}
          </div>
        );

        // Wrap disabled options in tooltip
        if (!isEnabled) {
          return (
            <TooltipProvider key={optionValue} delayDuration={300}>
              <Tooltip>
                <TooltipTrigger asChild>{content}</TooltipTrigger>
                <TooltipContent>
                  <p>{option.disabledReason || 'Coming Soon'}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          );
        }

        return <div key={optionValue}>{content}</div>;
      })}
    </div>
  );

  // Render dropdown layout
  const renderDropdownLayout = () => (
    <Select
      value={selectedValue?.toString() || ''}
      onValueChange={handleSelect}
      disabled={disabled}
    >
      <SelectTrigger className="w-full">
        <SelectValue placeholder={placeholder} />
      </SelectTrigger>
      <SelectContent>
        {options.map((option) => {
          const optionValue = option.value || option.id;
          return (
            <SelectItem 
              key={optionValue} 
              value={optionValue.toString()}
              disabled={option.enabled === false}
            >
              {option.name}
            </SelectItem>
          );
        })}
      </SelectContent>
    </Select>
  );

  // Render slider layout
  const renderSliderLayout = () => {
    const { min = 0, max = 100, step = 1, unit = '', showValue = true } = sliderConfig;
    
    return (
      <div className="space-y-2">
        {showValue && (
          <Label htmlFor="slider">
            {title} ({selectedValue}{unit})
          </Label>
        )}
        <Slider
          id="slider"
          min={min}
          max={max}
          step={step}
          value={[selectedValue || min]}
          onValueChange={(value) => handleSelect(value[0])}
          disabled={disabled}
        />
        {sliderConfig.helpText && (
          <p className="text-xs text-muted-foreground">{sliderConfig.helpText}</p>
        )}
      </div>
    );
  };

  // Render custom layout
  const renderCustomLayout = () => {
    if (renderCustomOption) {
      return (
        <div className="space-y-2">
          {options.map((option) => {
            const optionValue = option.value || option.id;
            const isSelected = selectedValue === optionValue;
            return renderCustomOption(option, isSelected, () => handleSelect(optionValue));
          })}
        </div>
      );
    }
    return null;
  };

  return (
    <div className={cn('space-y-4', className)} {...props}>
      {/* Header */}
      {(title || description) && (
        <div className="space-y-1">
          {title && (
            <h3 className="text-lg font-semibold">
              {title}
              {required && <span className="text-destructive ml-1">*</span>}
            </h3>
          )}
          {description && (
            <p className="text-sm text-muted-foreground">{description}</p>
          )}
        </div>
      )}

      {/* Content */}
      {layout === 'grid' && renderGridLayout()}
      {layout === 'dropdown' && renderDropdownLayout()}
      {layout === 'slider' && renderSliderLayout()}
      {layout === 'custom' && renderCustomLayout()}

      {/* Selected Value Display */}
      {selectedValue && layout === 'grid' && (
        <p className="text-sm text-muted-foreground">
          Selected: <span className="font-medium text-foreground">
            {options.find(opt => (opt.value || opt.id) === selectedValue)?.name}
          </span>
        </p>
      )}

      {/* Audio Element for Previews */}
      {showPreview && (
        <audio
          ref={audioRef}
          onEnded={handleAudioEnd}
          style={{ display: 'none' }}
        />
      )}
    </div>
  );
}

export default OptionSelector;
