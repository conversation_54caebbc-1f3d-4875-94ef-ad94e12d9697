/**
 * Unified Form Section Component
 * Standardizes form section layout and validation display across all video creation workflows
 * Eliminates duplicate form section patterns and provides consistent UX
 */

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { AlertCircle, CheckCircle2, Info, HelpCircle } from 'lucide-react';
import { cn } from '@/lib/utils';

/**
 * FormSection Component
 * @param {Object} props - Component props
 * @param {string} props.title - Section title
 * @param {string} props.description - Section description
 * @param {React.ReactNode} props.children - Form content
 * @param {string|Object} props.error - Error message or error object
 * @param {boolean} props.required - Whether section is required
 * @param {string} props.className - Additional CSS classes
 * @param {string} props.variant - Section variant ('default', 'card', 'minimal')
 * @param {string} props.status - Section status ('default', 'success', 'warning', 'error')
 * @param {string} props.badge - Badge text to display
 * @param {string} props.badgeVariant - Badge variant
 * @param {React.ReactNode} props.icon - Icon to display in header
 * @param {React.ReactNode} props.headerActions - Actions to display in header
 * @param {string} props.helpText - Help text to display
 * @param {boolean} props.collapsible - Whether section can be collapsed
 * @param {boolean} props.defaultCollapsed - Default collapsed state
 * @param {number} props.stepNumber - Step number for multi-step forms
 */
export function FormSection({
  title,
  description,
  children,
  error,
  required = false,
  className = '',
  variant = 'card',
  status = 'default',
  badge,
  badgeVariant = 'secondary',
  icon,
  headerActions,
  helpText,
  collapsible = false,
  defaultCollapsed = false,
  stepNumber,
  ...props
}) {
  const [isCollapsed, setIsCollapsed] = React.useState(defaultCollapsed);

  // Determine status based on error prop
  const sectionStatus = error ? 'error' : status;

  // Status icon mapping
  const statusIcons = {
    default: null,
    success: CheckCircle2,
    warning: AlertCircle,
    error: AlertCircle,
    info: Info
  };

  // Status colors
  const statusColors = {
    default: '',
    success: 'border-green-200 bg-green-50/50 dark:border-green-800 dark:bg-green-950/20',
    warning: 'border-yellow-200 bg-yellow-50/50 dark:border-yellow-800 dark:bg-yellow-950/20',
    error: 'border-red-200 bg-red-50/50 dark:border-red-800 dark:bg-red-950/20',
    info: 'border-blue-200 bg-blue-50/50 dark:border-blue-800 dark:bg-blue-950/20'
  };

  const StatusIcon = statusIcons[sectionStatus];

  // Render minimal variant
  if (variant === 'minimal') {
    return (
      <div className={cn('space-y-4', className)} {...props}>
        {(title || description) && (
          <div className="space-y-1">
            {title && (
              <div className="flex items-center gap-2">
                {stepNumber && (
                  <div className="flex items-center justify-center w-6 h-6 rounded-full bg-primary text-primary-foreground text-sm font-medium">
                    {stepNumber}
                  </div>
                )}
                {icon && <div className="text-muted-foreground">{icon}</div>}
                <h3 className="text-lg font-semibold">
                  {title}
                  {required && <span className="text-destructive ml-1">*</span>}
                </h3>
                {badge && <Badge variant={badgeVariant}>{badge}</Badge>}
                {StatusIcon && (
                  <StatusIcon className={cn(
                    'h-4 w-4',
                    sectionStatus === 'success' && 'text-green-600',
                    sectionStatus === 'warning' && 'text-yellow-600',
                    sectionStatus === 'error' && 'text-red-600',
                    sectionStatus === 'info' && 'text-blue-600'
                  )} />
                )}
                {headerActions && <div className="ml-auto">{headerActions}</div>}
              </div>
            )}
            {description && (
              <p className="text-sm text-muted-foreground">{description}</p>
            )}
          </div>
        )}

        {/* Error Display */}
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              {typeof error === 'string' ? error : error.message || 'An error occurred'}
            </AlertDescription>
          </Alert>
        )}

        {/* Help Text */}
        {helpText && (
          <Alert>
            <HelpCircle className="h-4 w-4" />
            <AlertDescription>{helpText}</AlertDescription>
          </Alert>
        )}

        {/* Content */}
        <div className="space-y-4">
          {children}
        </div>
      </div>
    );
  }

  // Render card variant (default)
  return (
    <Card className={cn(
      'transition-all duration-200',
      statusColors[sectionStatus],
      className
    )} {...props}>
      <CardHeader className={cn(
        'pb-4',
        collapsible && 'cursor-pointer',
        isCollapsed && 'pb-6'
      )} onClick={collapsible ? () => setIsCollapsed(!isCollapsed) : undefined}>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            {stepNumber && (
              <div className="flex items-center justify-center w-8 h-8 rounded-full bg-primary text-primary-foreground text-sm font-medium">
                {stepNumber}
              </div>
            )}
            {icon && <div className="text-muted-foreground">{icon}</div>}
            <div className="space-y-1">
              <CardTitle className="flex items-center gap-2">
                {title}
                {required && <span className="text-destructive">*</span>}
                {badge && <Badge variant={badgeVariant}>{badge}</Badge>}
                {StatusIcon && (
                  <StatusIcon className={cn(
                    'h-4 w-4',
                    sectionStatus === 'success' && 'text-green-600',
                    sectionStatus === 'warning' && 'text-yellow-600',
                    sectionStatus === 'error' && 'text-red-600',
                    sectionStatus === 'info' && 'text-blue-600'
                  )} />
                )}
              </CardTitle>
              {description && (
                <CardDescription>{description}</CardDescription>
              )}
            </div>
          </div>
          <div className="flex items-center gap-2">
            {headerActions}
            {collapsible && (
              <div className={cn(
                'transition-transform duration-200',
                isCollapsed && 'rotate-180'
              )}>
                ↓
              </div>
            )}
          </div>
        </div>
      </CardHeader>

      {!isCollapsed && (
        <CardContent className="space-y-4">
          {/* Error Display */}
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                {typeof error === 'string' ? error : error.message || 'An error occurred'}
              </AlertDescription>
            </Alert>
          )}

          {/* Help Text */}
          {helpText && (
            <Alert>
              <HelpCircle className="h-4 w-4" />
              <AlertDescription>{helpText}</AlertDescription>
            </Alert>
          )}

          {/* Content */}
          {children}
        </CardContent>
      )}
    </Card>
  );
}

/**
 * FormSectionGroup Component
 * Groups multiple form sections with consistent spacing
 */
export function FormSectionGroup({ children, className = '', ...props }) {
  return (
    <div className={cn('space-y-6', className)} {...props}>
      {children}
    </div>
  );
}

/**
 * FormSectionHeader Component
 * Standalone header for form sections without card wrapper
 */
export function FormSectionHeader({
  title,
  description,
  required = false,
  badge,
  badgeVariant = 'secondary',
  icon,
  actions,
  stepNumber,
  className = ''
}) {
  return (
    <div className={cn('space-y-1', className)}>
      <div className="flex items-center gap-2">
        {stepNumber && (
          <div className="flex items-center justify-center w-6 h-6 rounded-full bg-primary text-primary-foreground text-sm font-medium">
            {stepNumber}
          </div>
        )}
        {icon && <div className="text-muted-foreground">{icon}</div>}
        <h3 className="text-lg font-semibold">
          {title}
          {required && <span className="text-destructive ml-1">*</span>}
        </h3>
        {badge && <Badge variant={badgeVariant}>{badge}</Badge>}
        {actions && <div className="ml-auto">{actions}</div>}
      </div>
      {description && (
        <p className="text-sm text-muted-foreground">{description}</p>
      )}
    </div>
  );
}

export default FormSection;
