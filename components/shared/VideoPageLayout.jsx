/**
 * Shared Video Page Layout Component
 * Eliminates 400+ lines of duplicate layout code across all video pages
 * Preserves ALL existing functionality while reducing code duplication
 */

import React from 'react';
import { cn } from '@/lib/utils';

/**
 * @param {Object} props
 * @param {string} props.title - Page title
 * @param {string} props.description - Page description
 * @param {string} props.emoji - Page emoji
 * @param {number} props.credits - User credits
 * @param {string} props.gradientFrom - Gradient start color
 * @param {string} props.gradientTo - Gradient end color
 * @param {React.ReactNode} props.children - Page content
 * @param {string} props.className - Additional CSS classes
 */
export function VideoPageLayout({
  title,
  description,
  emoji = "✨",
  credits = 0,
  gradientFrom = "blue",
  gradientTo = "purple",
  children,
  className,
  ...props
}) {
  // Gradient color mappings
  const gradientClasses = {
    blue: "from-blue-50 via-purple-50 to-blue-50 dark:from-blue-950/20 dark:via-purple-950/20 dark:to-blue-950/20",
    purple: "from-purple-50 via-pink-50 to-purple-50 dark:from-purple-950/20 dark:via-pink-950/20 dark:to-purple-950/20",
    green: "from-green-50 via-blue-50 to-green-50 dark:from-green-950/20 dark:via-blue-950/20 dark:to-green-950/20",
    orange: "from-orange-50 via-red-50 to-orange-50 dark:from-orange-950/20 dark:via-red-950/20 dark:to-orange-950/20",
    pink: "from-pink-50 via-purple-50 to-pink-50 dark:from-pink-950/20 dark:via-purple-950/20 dark:to-pink-950/20"
  };

  const textGradientClasses = {
    blue: "from-blue-600 to-purple-600",
    purple: "from-purple-600 to-pink-600",
    green: "from-green-600 to-blue-600",
    orange: "from-orange-600 to-red-600",
    pink: "from-pink-600 to-purple-600"
  };

  const decorationClasses = {
    blue: "from-blue-500/10 to-purple-500/10",
    purple: "from-purple-500/10 to-pink-500/10",
    green: "from-green-500/10 to-blue-500/10",
    orange: "from-orange-500/10 to-red-500/10",
    pink: "from-pink-500/10 to-purple-500/10"
  };

  const bgGradient = gradientClasses[gradientFrom] || gradientClasses.blue;
  const textGradient = textGradientClasses[gradientFrom] || textGradientClasses.blue;
  const decoration = decorationClasses[gradientFrom] || decorationClasses.blue;

  return (
    <div className={cn("space-y-8 max-w-7xl mx-auto p-6", className)} {...props}>
      {/* Header Section */}
      <div className={cn(
        "relative overflow-hidden rounded-2xl p-8 border border-border/50",
        `bg-gradient-to-br ${bgGradient}`
      )}>
        <div className="relative z-10">
          <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-6">
            <div className="space-y-3">
              <h1 className={cn(
                "text-4xl font-bold bg-gradient-to-r bg-clip-text text-transparent",
                `${textGradient}`
              )}>
                {title} {emoji}
              </h1>
              {description && (
                <p className="text-lg text-muted-foreground max-w-2xl">
                  {description}
                </p>
              )}
            </div>
            <div className="flex items-center gap-4">
              <div className="text-right">
                <p className="text-sm text-muted-foreground">Credits Available</p>
                <p className="text-2xl font-bold text-primary">
                  {credits}
                </p>
              </div>
            </div>
          </div>
        </div>
        {/* Background decoration */}
        <div className={cn(
          "absolute inset-0 rounded-2xl",
          `bg-gradient-to-br ${decoration}`
        )}></div>
      </div>

      {/* Main Content */}
      {children}
    </div>
  );
}

/**
 * Two-column layout for video creation pages
 */
export function VideoTwoColumnLayout({
  leftColumn,
  rightColumn,
  leftColumnClassName = "lg:col-span-2",
  rightColumnClassName = "lg:col-span-1",
  className,
  ...props
}) {
  return (
    <div className={cn("grid grid-cols-1 lg:grid-cols-3 gap-8", className)} {...props}>
      <div className={cn("space-y-6", leftColumnClassName)}>
        {leftColumn}
      </div>
      <div className={cn("space-y-6", rightColumnClassName)}>
        {rightColumn}
      </div>
    </div>
  );
}

/**
 * Simple form section wrapper with consistent styling
 * Used by VideoPageLayout for basic form sections
 */
export function SimpleFormSection({
  title,
  description,
  icon,
  children,
  className,
  ...props
}) {
  return (
    <div className={cn(
      "bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 space-y-6",
      className
    )} {...props}>
      {(title || description) && (
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            {icon && <span className="text-xl">{icon}</span>}
            {title && (
              <h2 className="text-2xl font-semibold text-gray-800 dark:text-gray-200">
                {title}
              </h2>
            )}
          </div>
          {description && (
            <p className="text-gray-600 dark:text-gray-400">
              {description}
            </p>
          )}
        </div>
      )}
      {children}
    </div>
  );
}

/**
 * Preview section wrapper with consistent styling
 */
export function PreviewSection({
  title = "Preview & Generate",
  children,
  className,
  ...props
}) {
  return (
    <div className={cn(
      "bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 space-y-6",
      className
    )} {...props}>
      <h2 className="text-2xl font-semibold text-gray-800 dark:text-gray-200">
        {title}
      </h2>
      {children}
    </div>
  );
}

/**
 * Complete video page layout with all common elements
 */
export function CompleteVideoPageLayout({
  title,
  description,
  emoji,
  credits,
  gradientFrom,
  gradientTo,
  formSections,
  previewContent,
  className,
  ...props
}) {
  return (
    <VideoPageLayout
      title={title}
      description={description}
      emoji={emoji}
      credits={credits}
      gradientFrom={gradientFrom}
      gradientTo={gradientTo}
      className={className}
      {...props}
    >
      <VideoTwoColumnLayout
        leftColumn={
          <div className="space-y-6">
            {formSections}
          </div>
        }
        rightColumn={
          <PreviewSection>
            {previewContent}
          </PreviewSection>
        }
      />
    </VideoPageLayout>
  );
}
