/**
 * Unified Video Preview Panel Component
 * Consolidates RedditPostPreview, TwitterPostPreview, StockMediaPreview, PodcastClipperPreview, and VideoPreviewPanel
 * Provides configurable preview sections for all video creation workflows
 */

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Player } from '@remotion/player';
import { 
  Sparkles, 
  Loader2, 
  Video, 
  CheckCircle2, 
  AlertCircle, 
  Info,
  Film,
  Play,
  Settings
} from 'lucide-react';
import { cn } from '@/lib/utils';

/**
 * VideoPreviewPanel Component
 * @param {Object} props - Component props
 * @param {string} props.videoType - Type of video being created
 * @param {Object} props.previewConfig - Configuration for preview sections
 * @param {Object} props.formData - Current form data
 * @param {Function} props.onGenerate - Video generation handler
 * @param {boolean} props.isGenerating - Whether video is being generated
 * @param {string} props.generationMessage - Current generation message
 * @param {boolean} props.isGenerateButtonDisabled - Whether generate button is disabled
 * @param {Object} props.userDetail - User details including credits
 * @param {number} props.VIDEO_GENERATION_COST - Cost to generate video
 * @param {React.ReactNode} props.children - Custom preview content
 * @param {string} props.className - Additional CSS classes
 * @param {Object} props.remotionProps - Props for Remotion player
 * @param {Array} props.configSummary - Configuration summary items
 * @param {string} props.error - Error message
 * @param {Array} props.results - Generation results
 */
export function VideoPreviewPanel({
  videoType = 'default',
  previewConfig = {},
  formData = {},
  onGenerate,
  isGenerating = false,
  generationMessage = 'Generating...',
  isGenerateButtonDisabled = false,
  userDetail,
  VIDEO_GENERATION_COST = 10,
  children,
  className = '',
  remotionProps,
  configSummary = [],
  error,
  results,
  ...props
}) {
  const {
    showRemotionPlayer = false,
    showConfigSummary = true,
    showGenerationStatus = true,
    customSections = [],
    title,
    description,
    icon: CustomIcon
  } = previewConfig;

  // Get video type specific configuration
  const getVideoTypeConfig = () => {
    const configs = {
      'ai-video': {
        title: 'AI Video Preview',
        icon: Sparkles,
        buttonText: 'Generate AI Video',
        color: 'from-blue-500 to-purple-500'
      },
      'meme-video': {
        title: 'Meme Video Preview',
        icon: Film,
        buttonText: 'Generate Meme Video',
        color: 'from-purple-500 to-pink-500'
      },
      'podcast-clipper': {
        title: 'Podcast Clips Preview',
        icon: Play,
        buttonText: 'Generate Clips',
        color: 'from-orange-500 to-red-500'
      },
      'reddit-post': {
        title: 'Reddit Video Preview',
        icon: Video,
        buttonText: 'Generate Reddit Video',
        color: 'from-red-500 to-orange-500'
      },
      'twitter-post': {
        title: 'Twitter Video Preview',
        icon: Video,
        buttonText: 'Generate Twitter Video',
        color: 'from-sky-500 to-blue-500'
      },
      'stock-media': {
        title: 'Stock Media Preview',
        icon: Video,
        buttonText: 'Generate Stock Media Video',
        color: 'from-indigo-500 to-purple-500'
      },
      'ai-ugc': {
        title: 'UGC Video Preview',
        icon: Video,
        buttonText: 'Generate UGC Video',
        color: 'from-green-500 to-emerald-500'
      }
    };
    
    return configs[videoType] || configs['ai-video'];
  };

  const config = getVideoTypeConfig();
  const IconComponent = CustomIcon || config.icon;
  const panelTitle = title || config.title;
  const buttonText = config.buttonText;

  // Generate configuration summary
  const getCompletionStatus = (value, label) => {
    const isComplete = Boolean(value);
    return {
      isComplete,
      label,
      icon: isComplete ? CheckCircle2 : AlertCircle,
      color: isComplete ? 'text-green-600' : 'text-muted-foreground'
    };
  };

  // Default configuration summary based on common form fields
  const defaultConfigSummary = [
    formData.projectTitle && getCompletionStatus(formData.projectTitle, 'Project Title'),
    formData.script && getCompletionStatus(formData.script, 'Script'),
    formData.topic && getCompletionStatus(formData.topic, 'Topic'),
    formData.voice && getCompletionStatus(formData.voice, 'Voice'),
    formData.aspectRatio && getCompletionStatus(formData.aspectRatio, 'Aspect Ratio'),
    formData.videoStyle && getCompletionStatus(formData.videoStyle, 'Video Style'),
    formData.username && getCompletionStatus(formData.username, 'Username'),
    formData.content && getCompletionStatus(formData.content, 'Content'),
    formData.youtubeLink && getCompletionStatus(formData.youtubeLink, 'YouTube Link'),
    formData.uploadedFiles?.length && getCompletionStatus(formData.uploadedFiles.length, 'Uploaded Files')
  ].filter(Boolean);

  const summaryItems = configSummary.length > 0 ? configSummary : defaultConfigSummary;

  // Check if user has sufficient credits
  const hasSufficientCredits = userDetail && userDetail.credits >= VIDEO_GENERATION_COST;

  return (
    <div className={cn('relative sticky top-4 xl:top-20 h-fit', className)} {...props}>
      <div className="absolute inset-0 bg-gradient-to-br from-indigo-50/50 to-purple-50/50 dark:from-indigo-950/10 dark:to-purple-950/10 rounded-2xl"></div>
      <div className="relative bg-background/80 backdrop-blur-sm border border-border/50 rounded-2xl p-6 max-h-[calc(100vh-2rem)] xl:max-h-[calc(100vh-6rem)] overflow-y-auto">
        
        {/* Section Header */}
        <div className="flex items-center gap-3 mb-6 pb-4 border-b border-border/30">
          <div className="p-2 bg-gradient-to-br from-primary/10 to-primary/5 rounded-lg">
            <IconComponent className="h-5 w-5 text-primary" />
          </div>
          <div>
            <h3 className="text-heading-3 font-semibold">{panelTitle}</h3>
            {description && (
              <p className="text-body-small text-muted-foreground">{description}</p>
            )}
          </div>
        </div>

        {/* Remotion Player */}
        {showRemotionPlayer && remotionProps && (
          <div className="mb-6">
            <div className="aspect-[9/16] w-full bg-black rounded-lg overflow-hidden">
              <Player {...remotionProps} />
            </div>
          </div>
        )}

        {/* Custom Preview Content */}
        {children && (
          <div className="mb-6">
            {children}
          </div>
        )}

        {/* Custom Sections */}
        {customSections.map((section, index) => (
          <div key={index} className="mb-6">
            {section}
          </div>
        ))}

        {/* Configuration Summary */}
        {showConfigSummary && summaryItems.length > 0 && (
          <Card className="mb-6">
            <CardHeader className="pb-3">
              <CardTitle className="text-body font-medium flex items-center gap-2">
                <Settings className="h-4 w-4" />
                Configuration Summary
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-3">
                {summaryItems.map((item, index) => {
                  const StatusIcon = item.icon;
                  return (
                    <div key={index} className="flex items-center gap-2">
                      <StatusIcon className={cn('h-3 w-3', item.color)} />
                      <span className="text-caption">{item.label}</span>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Error Display */}
        {error && (
          <Alert variant="destructive" className="mb-6">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Results Display */}
        {results && results.length > 0 && (
          <Card className="mb-6">
            <CardHeader className="pb-3">
              <CardTitle className="text-body font-medium">Results</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {results.map((result, index) => (
                  <div key={index} className="p-3 bg-muted/30 rounded-lg">
                    <p className="text-sm">{result.title || `Result ${index + 1}`}</p>
                    {result.description && (
                      <p className="text-xs text-muted-foreground">{result.description}</p>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Generation Status */}
        {showGenerationStatus && (
          <div className="space-y-4">
            {/* Credit Check */}
            {!hasSufficientCredits && (
              <div className="bg-destructive/10 border border-destructive/20 rounded-lg p-3">
                <p className="text-caption text-destructive flex items-center gap-2">
                  <AlertCircle className="h-3 w-3" />
                  Insufficient credits. You need {VIDEO_GENERATION_COST} credits to generate a video.
                  (Have: {userDetail?.credits || 0})
                </p>
              </div>
            )}

            {/* Validation Status */}
            {isGenerateButtonDisabled && !isGenerating && hasSufficientCredits && (
              <div className="p-3 bg-yellow-50/50 dark:bg-yellow-950/20 rounded-lg border border-yellow-200/30 dark:border-yellow-800/30">
                <p className="text-body-small text-yellow-800 dark:text-yellow-200 text-center">
                  Please fill in all required fields to generate your video.
                </p>
              </div>
            )}
          </div>
        )}

        {/* Generate Video Button */}
        <div className="pt-4 border-t border-border/30">
          <Button
            onClick={onGenerate}
            disabled={isGenerateButtonDisabled}
            size="lg"
            className={cn(
              "w-full text-white border-0 shadow-medium interactive-scale h-12",
              `bg-gradient-to-br ${config.color} hover:opacity-90`
            )}
          >
            {isGenerating ? (
              <div className="flex items-center gap-2">
                <Loader2 className="h-5 w-5 animate-spin" />
                <span className="text-body">{generationMessage}</span>
              </div>
            ) : (
              <div className="flex items-center gap-2">
                <Sparkles className="h-5 w-5" />
                <span className="text-body font-semibold">{buttonText}</span>
              </div>
            )}
          </Button>
        </div>
      </div>
    </div>
  );
}

export default VideoPreviewPanel;
