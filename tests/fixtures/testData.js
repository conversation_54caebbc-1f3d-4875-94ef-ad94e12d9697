/**
 * Test data fixtures
 * Centralized test data for consistent testing across all test suites
 */

export const testUsers = {
  sufficientCredits: {
    id: 'test_user_123',
    clerkId: 'test_user_123',
    email: '<EMAIL>',
    credits: 20,
    currentCreditBalance: 20,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
  },
  
  insufficientCredits: {
    id: 'test_user_456',
    clerkId: 'test_user_456',
    email: '<EMAIL>',
    credits: 3,
    currentCreditBalance: 3,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
  },
  
  newUser: {
    id: 'test_user_789',
    clerkId: 'test_user_789',
    email: '<EMAIL>',
    credits: 0,
    currentCreditBalance: 0,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
  },
};

export const testFormData = {
  valid: {
    projectTitle: 'Test AI Video Project',
    topic: 'The Future of Artificial Intelligence in Healthcare Technology',
    videoStyle: 'Professional',
    aspectRatio: '16:9',
    script: 'In 2024, artificial intelligence is revolutionizing healthcare through predictive diagnostics, personalized treatment plans, and automated medical imaging analysis. AI-powered systems can now detect diseases earlier than traditional methods, leading to better patient outcomes and reduced healthcare costs.',
    voice: 'en-US-Wavenet-D',
    audioSpeed: 1.0,
    backgroundMusic: 'upbeat',
    caption: 'enabled',
    templateId: 'AI_VIDEO_TEMPLATE',
  },
  
  minimal: {
    projectTitle: 'Min',
    topic: 'AI in',
    videoStyle: 'Professional',
    aspectRatio: '16:9',
    script: 'Short test',
    voice: 'en-US-Wavenet-D',
    audioSpeed: 0.5,
    backgroundMusic: 'none',
    caption: 'disabled',
    templateId: 'AI_VIDEO_TEMPLATE',
  },
  
  invalid: {
    emptyTitle: {
      projectTitle: '',
      topic: 'Valid topic for testing',
      videoStyle: 'Professional',
      aspectRatio: '16:9',
      script: 'Valid script content',
      voice: 'en-US-Wavenet-D',
      audioSpeed: 1.0,
      backgroundMusic: 'upbeat',
      caption: 'enabled',
      templateId: 'AI_VIDEO_TEMPLATE',
    },
    
    shortTopic: {
      projectTitle: 'Valid Title',
      topic: 'AI',
      videoStyle: 'Professional',
      aspectRatio: '16:9',
      script: 'Valid script content',
      voice: 'en-US-Wavenet-D',
      audioSpeed: 1.0,
      backgroundMusic: 'upbeat',
      caption: 'enabled',
      templateId: 'AI_VIDEO_TEMPLATE',
    },
    
    invalidAudioSpeed: {
      projectTitle: 'Valid Title',
      topic: 'Valid topic for testing',
      videoStyle: 'Professional',
      aspectRatio: '16:9',
      script: 'Valid script content',
      voice: 'en-US-Wavenet-D',
      audioSpeed: 3.0, // Invalid - out of range
      backgroundMusic: 'upbeat',
      caption: 'enabled',
      templateId: 'AI_VIDEO_TEMPLATE',
    },
  },
};

export const testApiResponses = {
  scriptGeneration: {
    success: 'In 2024, artificial intelligence is revolutionizing healthcare through predictive diagnostics, personalized treatment plans, and automated medical imaging analysis. AI-powered systems can now detect diseases earlier than traditional methods.',
    
    fallback: 'This is an emergency fallback script generated when the primary AI service is unavailable. It covers the basic topic requirements.',
  },
  
  imageGeneration: {
    success: [
      {
        url: 'https://example.com/ai-healthcare-1.jpg',
        prompt: 'Futuristic hospital with AI diagnostic equipment',
        source: 'runware',
      },
      {
        url: 'https://example.com/ai-healthcare-2.jpg',
        prompt: 'Doctor using AI tablet for patient diagnosis',
        source: 'runware',
      },
    ],
    
    fallback: [
      {
        url: 'https://fallback.com/generic-healthcare.jpg',
        prompt: 'Generic healthcare image',
        source: 'fallback',
      },
    ],
  },
  
  audioGeneration: {
    success: {
      audioUrl: 'https://example.com/generated-audio.mp3',
      duration: 45,
      voice: 'en-US-Wavenet-D',
    },
  },
  
  videoGeneration: {
    success: {
      videoId: 'video_123456',
      status: 'completed',
      videoUrl: 'https://example.com/completed-video.mp4',
      thumbnailUrl: 'https://example.com/video-thumbnail.jpg',
    },
    
    pending: {
      videoId: 'video_123456',
      status: 'processing',
      progress: 75,
    },
    
    failed: {
      videoId: 'video_123456',
      status: 'failed',
      error: 'Video generation failed due to external API error',
    },
  },
};

export const testCreditTransactions = {
  debit: {
    id: 'transaction_123',
    userId: 'test_user_123',
    transactionType: 'DEBIT',
    amount: -5,
    balanceBefore: 20,
    balanceAfter: 15,
    relatedEntityType: 'VIDEO',
    relatedEntityId: 'video_123',
    notes: 'AI Video generation',
    metadata: { workflowType: 'AI_VIDEO' },
    createdAt: new Date('2024-01-01T10:00:00Z'),
  },
  
  refund: {
    id: 'transaction_124',
    userId: 'test_user_123',
    transactionType: 'REFUND',
    amount: 5,
    balanceBefore: 15,
    balanceAfter: 20,
    relatedEntityType: 'VIDEO',
    relatedEntityId: 'video_123',
    notes: 'Video generation failed - automatic refund',
    metadata: { originalTransactionId: 'transaction_123' },
    createdAt: new Date('2024-01-01T10:30:00Z'),
  },
  
  purchase: {
    id: 'transaction_125',
    userId: 'test_user_123',
    transactionType: 'PURCHASE',
    amount: 100,
    balanceBefore: 20,
    balanceAfter: 120,
    relatedEntityType: 'PURCHASE',
    relatedEntityId: 'purchase_456',
    notes: 'Credit purchase via LemonSqueezy',
    metadata: { orderId: 'ls_order_789' },
    createdAt: new Date('2024-01-01T09:00:00Z'),
  },
};

export const testVideoData = {
  pending: {
    id: 'video_123',
    clerkId: 'test_user_123',
    title: 'Test AI Video',
    topic: 'AI in Healthcare',
    script: 'Test script content',
    videoStyle: 'Professional',
    aspectRatio: '16:9',
    templateId: 'AI_VIDEO_TEMPLATE',
    voice: 'en-US-Wavenet-D',
    audioSpeed: 1.0,
    backgroundMusic: 'upbeat',
    status: 'Pending',
    costInCredits: 5,
    creditTransactionId: 'transaction_123',
    workflow_data: {
      workflowType: 'AI_VIDEO',
      projectTitle: 'Test AI Video',
    },
    createdAt: new Date('2024-01-01T10:00:00Z'),
    updatedAt: new Date('2024-01-01T10:00:00Z'),
  },
  
  completed: {
    id: 'video_124',
    clerkId: 'test_user_123',
    title: 'Completed AI Video',
    status: 'Completed',
    videoUrl: 'https://example.com/completed-video.mp4',
    thumbnailUrl: 'https://example.com/video-thumbnail.jpg',
    costInCredits: 5,
    creditTransactionId: 'transaction_123',
    createdAt: new Date('2024-01-01T09:00:00Z'),
    updatedAt: new Date('2024-01-01T10:00:00Z'),
  },
  
  failed: {
    id: 'video_125',
    clerkId: 'test_user_123',
    title: 'Failed AI Video',
    status: 'Failed',
    errorMessage: 'External API failure',
    costInCredits: 5,
    creditTransactionId: 'transaction_123',
    createdAt: new Date('2024-01-01T09:00:00Z'),
    updatedAt: new Date('2024-01-01T10:00:00Z'),
  },
};

export const testWorkflowCosts = {
  AI_VIDEO: 5,
  MEME_VIDEO: 2,
  UGC_VIDEO: 8,
  NARRATOR_VIDEO: 5, // 4 base + 1 for 90 second duration
  REDDIT_VIDEO: 3,  // 3 base (60 seconds = no extra cost)
  TWITTER_VIDEO: 3, // 3 base (60 seconds = no extra cost)
  STOCK_MEDIA_VIDEO: 8, // 6 base + 2 for 120 second duration
};

export const testErrorMessages = {
  insufficientCredits: 'Insufficient credits for video generation.',
  invalidInput: 'Please fill in all required fields.',
  apiFailure: 'Service temporarily unavailable. Please try again.',
  authenticationRequired: 'Please sign in to continue.',
  networkError: 'Network error. Please check your connection.',
};
