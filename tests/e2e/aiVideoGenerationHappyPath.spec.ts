/**
 * AIVID_E2E_001: Complete AI Video Generation Happy Path
 * 
 * End-to-end test for the complete AI video generation workflow
 * from user input to successful video creation.
 */

import { test, expect } from '@playwright/test';
import { e2eHelpers } from '../utils/testHelpers';

test.describe('AIVID_E2E_001: Complete AI Video Generation Happy Path', () => {
  test.beforeEach(async ({ page }) => {
    // Mock authentication - user is already signed in
    await page.addInitScript(() => {
      // Mock Clerk authentication
      window.__CLERK_PUBLISHABLE_KEY = 'test_key';
      window.__clerk_user = {
        id: 'test_user_123',
        emailAddresses: [{ emailAddress: '<EMAIL>' }],
        firstName: 'Test',
        lastName: 'User',
      };
    });

    // Mock API responses for successful workflow
    await page.route('**/api/generate-script', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          script: 'In 2024, artificial intelligence is revolutionizing healthcare through predictive diagnostics, personalized treatment plans, and automated medical imaging analysis. AI-powered systems can now detect diseases earlier than traditional methods, leading to better patient outcomes and reduced healthcare costs.',
        }),
      });
    });

    await page.route('**/api/video/initiate-generation', async route => {
      await route.fulfill({
        status: 202,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          videoId: 'video_123456',
          creditsRemaining: 15,
          eventId: 'video-123456',
        }),
      });
    });

    await page.route('**/api/get-credits', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          credits: 20,
        }),
      });
    });
  });

  test('should complete full AI video generation workflow successfully', async ({ page }) => {
    // Step 1: Navigate to AI video creation page
    await page.goto(e2eHelpers.urls.aiVideoCreation);
    
    // Verify page loaded correctly
    await expect(page).toHaveTitle(/AI Video Creator/);
    await expect(page.locator('h1')).toContainText(/AI Video/i);

    // Step 2: Fill project title
    const projectTitleInput = page.locator(e2eHelpers.selectors.projectTitleInput);
    await expect(projectTitleInput).toBeVisible();
    await projectTitleInput.fill(e2eHelpers.testData.validProject.title);
    
    // Verify input was filled
    await expect(projectTitleInput).toHaveValue(e2eHelpers.testData.validProject.title);

    // Step 3: Enter topic
    const topicInput = page.locator(e2eHelpers.selectors.topicInput);
    await expect(topicInput).toBeVisible();
    await topicInput.fill(e2eHelpers.testData.validProject.topic);
    
    // Verify topic was entered
    await expect(topicInput).toHaveValue(e2eHelpers.testData.validProject.topic);

    // Step 4: Select video style
    const videoStyleSelect = page.locator(e2eHelpers.selectors.videoStyleSelect);
    await expect(videoStyleSelect).toBeVisible();
    await videoStyleSelect.selectOption(e2eHelpers.testData.validProject.style);

    // Step 5: Select aspect ratio
    const aspectRatioSelect = page.locator(e2eHelpers.selectors.aspectRatioSelect);
    await expect(aspectRatioSelect).toBeVisible();
    await aspectRatioSelect.selectOption(e2eHelpers.testData.validProject.aspectRatio);

    // Step 6: Generate script preview
    const generateScriptButton = page.locator('button:has-text("Generate Script")');
    await expect(generateScriptButton).toBeVisible();
    await generateScriptButton.click();

    // Wait for script generation to complete
    await expect(page.locator('[data-testid="loading-script"]')).toBeVisible();
    await expect(page.locator('[data-testid="loading-script"]')).toBeHidden({ timeout: 10000 });

    // Verify script was generated
    const scriptEditor = page.locator(e2eHelpers.selectors.scriptEditor);
    await expect(scriptEditor).toBeVisible();
    await expect(scriptEditor).toContainText(/artificial intelligence/i);

    // Step 7: Review and edit script (optional)
    const currentScript = await scriptEditor.textContent();
    expect(currentScript?.length).toBeGreaterThan(50);
    
    // Make a small edit to the script
    await scriptEditor.click();
    await scriptEditor.press('End');
    await scriptEditor.type(' This is an additional sentence for testing.');

    // Step 8: Select voice
    const voiceSelect = page.locator(e2eHelpers.selectors.voiceSelect);
    await expect(voiceSelect).toBeVisible();
    await voiceSelect.selectOption('en-US-Wavenet-D');

    // Step 9: Adjust audio speed
    const audioSpeedSlider = page.locator(e2eHelpers.selectors.audioSpeedSlider);
    await expect(audioSpeedSlider).toBeVisible();
    await audioSpeedSlider.fill('1.2');

    // Step 10: Select background music
    const backgroundMusicSelect = page.locator('[data-testid="background-music-select"]');
    await expect(backgroundMusicSelect).toBeVisible();
    await backgroundMusicSelect.selectOption('upbeat');

    // Step 11: Enable captions
    const captionsToggle = page.locator('[data-testid="captions-toggle"]');
    await expect(captionsToggle).toBeVisible();
    await captionsToggle.check();

    // Step 12: Verify credits display
    const creditsDisplay = page.locator(e2eHelpers.selectors.creditsDisplay);
    await expect(creditsDisplay).toBeVisible();
    await expect(creditsDisplay).toContainText('20'); // Initial credits

    // Step 13: Generate video
    const generateVideoButton = page.locator(e2eHelpers.selectors.generateButton);
    await expect(generateVideoButton).toBeVisible();
    await expect(generateVideoButton).toBeEnabled();
    
    // Click generate video button
    await generateVideoButton.click();

    // Step 14: Verify generation started
    await expect(page.locator('[data-testid="generation-loading"]')).toBeVisible();
    
    // Verify success message appears
    const successMessage = page.locator(e2eHelpers.selectors.successMessage);
    await expect(successMessage).toBeVisible({ timeout: 10000 });
    await expect(successMessage).toContainText(/generation started/i);

    // Step 15: Verify credit deduction
    await expect(creditsDisplay).toContainText('15'); // Credits after deduction

    // Step 16: Verify redirect to dashboard
    await expect(page).toHaveURL(e2eHelpers.urls.dashboard, { timeout: 10000 });
    
    // Verify user is on dashboard with video in progress
    await expect(page.locator('h1')).toContainText(/dashboard/i);
    await expect(page.locator('[data-testid="video-in-progress"]')).toBeVisible();
  });

  test('should handle form validation correctly', async ({ page }) => {
    await page.goto(e2eHelpers.urls.aiVideoCreation);

    // Try to generate video without filling required fields
    const generateVideoButton = page.locator(e2eHelpers.selectors.generateButton);
    await expect(generateVideoButton).toBeDisabled();

    // Fill only project title
    const projectTitleInput = page.locator(e2eHelpers.selectors.projectTitleInput);
    await projectTitleInput.fill('Test Title');
    
    // Button should still be disabled
    await expect(generateVideoButton).toBeDisabled();

    // Fill topic but make it too short
    const topicInput = page.locator(e2eHelpers.selectors.topicInput);
    await topicInput.fill('AI');
    await topicInput.blur();

    // Should show validation error
    await expect(page.locator('[data-testid="topic-error"]')).toBeVisible();
    await expect(page.locator('[data-testid="topic-error"]')).toContainText(/at least/i);

    // Fix the topic
    await topicInput.fill('Artificial Intelligence in Healthcare');
    await topicInput.blur();

    // Error should disappear
    await expect(page.locator('[data-testid="topic-error"]')).toBeHidden();
  });

  test('should preserve form data during navigation', async ({ page }) => {
    await page.goto(e2eHelpers.urls.aiVideoCreation);

    // Fill some form data
    await page.locator(e2eHelpers.selectors.projectTitleInput).fill('Test Project');
    await page.locator(e2eHelpers.selectors.topicInput).fill('Test Topic for Navigation');

    // Navigate away and back
    await page.goto(e2eHelpers.urls.dashboard);
    await page.goto(e2eHelpers.urls.aiVideoCreation);

    // Form should be reset (this is expected behavior)
    await expect(page.locator(e2eHelpers.selectors.projectTitleInput)).toHaveValue('');
    await expect(page.locator(e2eHelpers.selectors.topicInput)).toHaveValue('');
  });

  test('should handle slow network conditions', async ({ page }) => {
    // Simulate slow network
    await page.route('**/api/generate-script', async route => {
      await new Promise(resolve => setTimeout(resolve, 2000)); // 2 second delay
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          script: 'Generated script after delay',
        }),
      });
    });

    await page.goto(e2eHelpers.urls.aiVideoCreation);

    // Fill required fields
    await page.locator(e2eHelpers.selectors.projectTitleInput).fill('Slow Network Test');
    await page.locator(e2eHelpers.selectors.topicInput).fill('Testing slow network conditions');
    await page.locator(e2eHelpers.selectors.videoStyleSelect).selectOption('Professional');
    await page.locator(e2eHelpers.selectors.aspectRatioSelect).selectOption('16:9');

    // Generate script
    await page.locator('button:has-text("Generate Script")').click();

    // Should show loading state
    await expect(page.locator('[data-testid="loading-script"]')).toBeVisible();

    // Should eventually complete
    await expect(page.locator(e2eHelpers.selectors.scriptEditor)).toContainText('Generated script after delay', { timeout: 15000 });
  });
});
