/**
 * AIVID_E2E_002: Video Generation with Insufficient Credits
 * 
 * End-to-end test for handling insufficient credits scenario
 * including error display, prevention of generation, and billing redirect.
 */

import { test, expect } from '@playwright/test';
import { e2eHelpers } from '../utils/testHelpers';

test.describe('AIVID_E2E_002: Video Generation with Insufficient Credits', () => {
  test.beforeEach(async ({ page }) => {
    // Mock authentication - user is signed in but has insufficient credits
    await page.addInitScript(() => {
      window.__CLERK_PUBLISHABLE_KEY = 'test_key';
      window.__clerk_user = {
        id: 'test_user_insufficient',
        emailAddresses: [{ emailAddress: '<EMAIL>' }],
        firstName: 'Test',
        lastName: 'User',
      };
    });

    // Mock API responses for insufficient credits scenario
    await page.route('**/api/get-credits', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          credits: 3, // Insufficient for AI video (requires 5)
        }),
      });
    });

    await page.route('**/api/generate-script', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          script: 'Generated script for insufficient credits test.',
        }),
      });
    });

    await page.route('**/api/video/initiate-generation', async route => {
      await route.fulfill({
        status: 400,
        contentType: 'application/json',
        body: JSON.stringify({
          success: false,
          error: 'Insufficient credits for video generation.',
        }),
      });
    });
  });

  test('should prevent video generation with insufficient credits', async ({ page }) => {
    // Step 1: Navigate to AI video creation page
    await page.goto(e2eHelpers.urls.aiVideoCreation);
    
    // Verify page loaded
    await expect(page).toHaveTitle(/AI Video Creator/);

    // Step 2: Verify insufficient credits are displayed
    const creditsDisplay = page.locator(e2eHelpers.selectors.creditsDisplay);
    await expect(creditsDisplay).toBeVisible();
    await expect(creditsDisplay).toContainText('3');

    // Step 3: Fill out the form completely
    await page.locator(e2eHelpers.selectors.projectTitleInput).fill('Insufficient Credits Test');
    await page.locator(e2eHelpers.selectors.topicInput).fill('Testing insufficient credits scenario');
    await page.locator(e2eHelpers.selectors.videoStyleSelect).selectOption('Professional');
    await page.locator(e2eHelpers.selectors.aspectRatioSelect).selectOption('16:9');

    // Step 4: Generate script
    const generateScriptButton = page.locator('button:has-text("Generate Script")');
    await generateScriptButton.click();

    // Wait for script generation
    await expect(page.locator(e2eHelpers.selectors.scriptEditor)).toBeVisible();
    await expect(page.locator(e2eHelpers.selectors.scriptEditor)).toContainText('Generated script');

    // Step 5: Complete form configuration
    await page.locator(e2eHelpers.selectors.voiceSelect).selectOption('en-US-Wavenet-D');
    await page.locator(e2eHelpers.selectors.audioSpeedSlider).fill('1.0');
    await page.locator('[data-testid="background-music-select"]').selectOption('upbeat');
    await page.locator('[data-testid="captions-toggle"]').check();

    // Step 6: Attempt to generate video
    const generateVideoButton = page.locator(e2eHelpers.selectors.generateButton);
    await expect(generateVideoButton).toBeVisible();
    await generateVideoButton.click();

    // Step 7: Verify insufficient credits error appears
    const errorMessage = page.locator(e2eHelpers.selectors.errorMessage);
    await expect(errorMessage).toBeVisible({ timeout: 5000 });
    await expect(errorMessage).toContainText(/insufficient credits/i);

    // Step 8: Verify credits remain unchanged
    await expect(creditsDisplay).toContainText('3');

    // Step 9: Verify no generation process started
    await expect(page.locator('[data-testid="generation-loading"]')).not.toBeVisible();
    await expect(page.locator('[data-testid="video-in-progress"]')).not.toBeVisible();

    // Step 10: Verify user remains on the same page
    await expect(page).toHaveURL(e2eHelpers.urls.aiVideoCreation);
  });

  test('should display credit requirement information', async ({ page }) => {
    await page.goto(e2eHelpers.urls.aiVideoCreation);

    // Should show cost information
    const costInfo = page.locator('[data-testid="video-cost-info"]');
    await expect(costInfo).toBeVisible();
    await expect(costInfo).toContainText('5 credits'); // AI video cost

    // Should show current credits
    const creditsDisplay = page.locator(e2eHelpers.selectors.creditsDisplay);
    await expect(creditsDisplay).toContainText('3');

    // Should indicate insufficient credits
    const insufficientWarning = page.locator('[data-testid="insufficient-credits-warning"]');
    await expect(insufficientWarning).toBeVisible();
    await expect(insufficientWarning).toContainText(/need 2 more credits/i);
  });

  test('should provide link to purchase credits', async ({ page }) => {
    await page.goto(e2eHelpers.urls.aiVideoCreation);

    // Fill form to trigger insufficient credits check
    await page.locator(e2eHelpers.selectors.projectTitleInput).fill('Test Project');
    await page.locator(e2eHelpers.selectors.topicInput).fill('Test topic for credits');
    await page.locator(e2eHelpers.selectors.videoStyleSelect).selectOption('Professional');
    await page.locator(e2eHelpers.selectors.aspectRatioSelect).selectOption('16:9');

    // Generate script
    await page.locator('button:has-text("Generate Script")').click();
    await expect(page.locator(e2eHelpers.selectors.scriptEditor)).toBeVisible();

    // Complete form
    await page.locator(e2eHelpers.selectors.voiceSelect).selectOption('en-US-Wavenet-D');

    // Try to generate video
    await page.locator(e2eHelpers.selectors.generateButton).click();

    // Error should appear with purchase link
    const errorMessage = page.locator(e2eHelpers.selectors.errorMessage);
    await expect(errorMessage).toBeVisible();

    const purchaseLink = page.locator('[data-testid="buy-credits-link"]');
    await expect(purchaseLink).toBeVisible();
    await expect(purchaseLink).toContainText(/buy credits/i);

    // Click purchase link should redirect to billing
    await purchaseLink.click();
    await expect(page).toHaveURL(/billing/, { timeout: 5000 });
  });

  test('should preserve form data when credits are insufficient', async ({ page }) => {
    await page.goto(e2eHelpers.urls.aiVideoCreation);

    // Fill form completely
    const formData = {
      title: 'Preserved Form Data Test',
      topic: 'Testing form data preservation with insufficient credits',
      style: 'Educational',
      aspectRatio: '9:16',
    };

    await page.locator(e2eHelpers.selectors.projectTitleInput).fill(formData.title);
    await page.locator(e2eHelpers.selectors.topicInput).fill(formData.topic);
    await page.locator(e2eHelpers.selectors.videoStyleSelect).selectOption(formData.style);
    await page.locator(e2eHelpers.selectors.aspectRatioSelect).selectOption(formData.aspectRatio);

    // Generate script
    await page.locator('button:has-text("Generate Script")').click();
    await expect(page.locator(e2eHelpers.selectors.scriptEditor)).toBeVisible();

    // Complete form
    await page.locator(e2eHelpers.selectors.voiceSelect).selectOption('en-US-Wavenet-D');
    await page.locator(e2eHelpers.selectors.audioSpeedSlider).fill('1.5');

    // Try to generate video (will fail)
    await page.locator(e2eHelpers.selectors.generateButton).click();

    // Wait for error
    await expect(page.locator(e2eHelpers.selectors.errorMessage)).toBeVisible();

    // Verify form data is preserved
    await expect(page.locator(e2eHelpers.selectors.projectTitleInput)).toHaveValue(formData.title);
    await expect(page.locator(e2eHelpers.selectors.topicInput)).toHaveValue(formData.topic);
    await expect(page.locator(e2eHelpers.selectors.videoStyleSelect)).toHaveValue(formData.style);
    await expect(page.locator(e2eHelpers.selectors.aspectRatioSelect)).toHaveValue(formData.aspectRatio);
    await expect(page.locator(e2eHelpers.selectors.voiceSelect)).toHaveValue('en-US-Wavenet-D');
    await expect(page.locator(e2eHelpers.selectors.audioSpeedSlider)).toHaveValue('1.5');
  });

  test('should handle edge case with exactly required credits', async ({ page }) => {
    // Mock user with exactly 5 credits (minimum required)
    await page.route('**/api/get-credits', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          credits: 5, // Exactly enough for AI video
        }),
      });
    });

    // Mock successful generation for this case
    await page.route('**/api/video/initiate-generation', async route => {
      await route.fulfill({
        status: 202,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          videoId: 'video_123456',
          creditsRemaining: 0,
          eventId: 'video-123456',
        }),
      });
    });

    await page.goto(e2eHelpers.urls.aiVideoCreation);

    // Verify exactly 5 credits shown
    const creditsDisplay = page.locator(e2eHelpers.selectors.creditsDisplay);
    await expect(creditsDisplay).toContainText('5');

    // Fill form and generate
    await page.locator(e2eHelpers.selectors.projectTitleInput).fill('Exact Credits Test');
    await page.locator(e2eHelpers.selectors.topicInput).fill('Testing with exactly enough credits');
    await page.locator(e2eHelpers.selectors.videoStyleSelect).selectOption('Professional');
    await page.locator(e2eHelpers.selectors.aspectRatioSelect).selectOption('16:9');

    // Generate script
    await page.locator('button:has-text("Generate Script")').click();
    await expect(page.locator(e2eHelpers.selectors.scriptEditor)).toBeVisible();

    // Complete form
    await page.locator(e2eHelpers.selectors.voiceSelect).selectOption('en-US-Wavenet-D');

    // Generate video - should succeed
    await page.locator(e2eHelpers.selectors.generateButton).click();

    // Should show success
    await expect(page.locator(e2eHelpers.selectors.successMessage)).toBeVisible();

    // Credits should be 0 after generation
    await expect(creditsDisplay).toContainText('0');
  });

  test('should handle concurrent insufficient credits attempts', async ({ page, context }) => {
    // Open multiple tabs with the same user
    const page2 = await context.newPage();

    // Both pages navigate to video creation
    await Promise.all([
      page.goto(e2eHelpers.urls.aiVideoCreation),
      page2.goto(e2eHelpers.urls.aiVideoCreation),
    ]);

    // Both should show insufficient credits
    await Promise.all([
      expect(page.locator(e2eHelpers.selectors.creditsDisplay)).toContainText('3'),
      expect(page2.locator(e2eHelpers.selectors.creditsDisplay)).toContainText('3'),
    ]);

    // Fill forms on both pages
    const fillForm = async (targetPage: any) => {
      await targetPage.locator(e2eHelpers.selectors.projectTitleInput).fill('Concurrent Test');
      await targetPage.locator(e2eHelpers.selectors.topicInput).fill('Testing concurrent access');
      await targetPage.locator(e2eHelpers.selectors.videoStyleSelect).selectOption('Professional');
      await targetPage.locator(e2eHelpers.selectors.aspectRatioSelect).selectOption('16:9');
      await targetPage.locator('button:has-text("Generate Script")').click();
      await expect(targetPage.locator(e2eHelpers.selectors.scriptEditor)).toBeVisible();
      await targetPage.locator(e2eHelpers.selectors.voiceSelect).selectOption('en-US-Wavenet-D');
    };

    await Promise.all([fillForm(page), fillForm(page2)]);

    // Try to generate on both pages simultaneously
    await Promise.all([
      page.locator(e2eHelpers.selectors.generateButton).click(),
      page2.locator(e2eHelpers.selectors.generateButton).click(),
    ]);

    // Both should show insufficient credits error
    await Promise.all([
      expect(page.locator(e2eHelpers.selectors.errorMessage)).toBeVisible(),
      expect(page2.locator(e2eHelpers.selectors.errorMessage)).toBeVisible(),
    ]);

    await page2.close();
  });
});
