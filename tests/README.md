# AI Video Generation Platform - Test Suite

This directory contains comprehensive tests for the AI Video Generation Platform, focusing on the AI Video Generation Workflow (End-to-End).

**Current Status**: 79 of 99 tests passing (79.8% success rate)
**Last Updated**: December 2024

## Test Structure

```
tests/
├── __mocks__/           # API mocks and test utilities
│   ├── database.js      # Database operation mocks
│   └── externalApis.js  # External API service mocks
├── unit/               # Component and utility function tests
│   ├── creditCostCalculation.test.js
│   └── projectTitleInput.test.js
├── integration/        # Server action and API integration tests
│   ├── creditCheckAndDebit.test.js
│   └── scriptGenerationServerAction.test.js
├── e2e/               # End-to-end user journey tests
│   ├── aiVideoGenerationHappyPath.spec.ts
│   └── insufficientCreditsScenario.spec.ts
├── fixtures/          # Test data and mock responses
│   └── testData.js
├── setup/             # Test configuration and helpers
│   └── jest.setup.js
└── utils/             # Test utilities and helpers
    └── testHelpers.js
```

## Current Test Status

### ✅ **Working Tests (79/99 passing - 79.8% success rate)**

#### Unit Tests (58/58 passing - 100% success rate)
- **AIVID_UNIT_005**: Credit Cost Calculation Utility (20 tests) ✅
- **AIVID_UNIT_001**: Project Title Input Component Validation (17 tests) ✅
- **Setup Validation**: Testing infrastructure validation (21 tests) ✅

#### Integration Tests (21/41 passing - 51.2% success rate)
- **Basic Credit System**: Cost calculation functions (3/3 tests) ✅
- **AIVID_INT_001**: Script Generation Server Action (16/20 tests) 🔧
- **AIVID_INT_002**: Credit Check and Debit Integration (1/17 tests) ❌

#### End-to-End Tests (Not yet executed)
- **AIVID_E2E_001**: Complete AI Video Generation Happy Path 📝
- **AIVID_E2E_002**: Video Generation with Insufficient Credits 📝

### 🔧 **Known Issues**

**Integration Tests (20 failing)**:
- Missing `setupDatabaseMocks` and `setupApiMocks` helper functions
- Mock expectations don't match actual implementation

**E2E Tests**:
- Playwright browsers not installed (`npx playwright install` needed)
- Missing data-testid attributes in actual components

### 🚀 **Quick Commands That Work**

```bash
# Run passing unit tests (58/58)
npm run test:unit

# Run basic integration tests (21/41)
npm test -- --testPathPattern="creditSystemBasic|scriptGenerationServerAction"

# Run all Jest tests (79/99)
npm test

# Coverage report
npm run test:coverage
```

## Setup Instructions

### 1. Install Dependencies

```bash
npm install --save-dev @playwright/test @testing-library/jest-dom @testing-library/react @testing-library/user-event @types/jest jest jest-environment-jsdom msw
```

### 2. Install Playwright Browsers

```bash
npx playwright install
```

### 3. Environment Setup

Create a `.env.test` file with test environment variables:

```env
NODE_ENV=test
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=test_clerk_key
DATABASE_URL=postgresql://test_user:test_pass@localhost:5432/test_db
```

## Running Tests

### All Tests
```bash
npm run test:all
```

### Unit Tests Only
```bash
npm run test:unit
```

### Integration Tests Only
```bash
npm run test:integration
```

### End-to-End Tests Only
```bash
npm run test:e2e
```

### With Coverage
```bash
npm run test:coverage
```

### Watch Mode (Development)
```bash
npm run test:watch
```

## Test Configuration

### Jest Configuration
- **Environment**: jsdom for React component testing
- **Setup**: Automatic mocking of Next.js, Clerk, and external APIs
- **Coverage**: 70% threshold for branches, functions, lines, and statements
- **Timeout**: 10 seconds for async operations

### Playwright Configuration
- **Browsers**: Chromium, Firefox, WebKit, Mobile Chrome, Mobile Safari
- **Base URL**: http://localhost:3000
- **Retries**: 2 on CI, 0 locally
- **Screenshots**: On failure only
- **Videos**: Retained on failure

## Mock Strategy

### Database Mocks
- Consistent mock responses for user data, credit transactions, and video records
- Scenario-based setup functions for different test conditions
- Atomic transaction simulation

### External API Mocks
- Google AI script generation with success/failure scenarios
- ElevenLabs audio generation mocking
- Image API (Runware, Pexels, Pixabay) with fallback chain testing
- Circuit breaker behavior simulation

### Authentication Mocks
- Clerk authentication with test users
- Different credit balance scenarios
- Session management simulation

## Test Data Management

### Fixtures
- Predefined test users with various credit balances
- Valid and invalid form data scenarios
- API response templates
- Error message constants

### Test Utilities
- Custom render functions with providers
- Form validation helpers
- Async operation helpers
- E2E selector and data helpers

## Best Practices

### Writing Tests
1. Use descriptive test names following the pattern: `should [expected behavior] when [condition]`
2. Follow the AAA pattern: Arrange, Act, Assert
3. Use data-testid attributes for reliable element selection
4. Mock external dependencies consistently
5. Test both success and failure scenarios

### Test Isolation
1. Each test should run independently
2. Clean up after each test
3. Use beforeEach/afterEach for setup/teardown
4. Avoid shared state between tests

### Performance
1. Keep unit tests fast (< 100ms each)
2. Limit integration test scope
3. Use efficient selectors in E2E tests
4. Parallelize test execution where possible

## Continuous Integration

### GitHub Actions (Recommended)
```yaml
name: Test Suite
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: npm ci
      - run: npm run test:coverage
      - run: npx playwright install
      - run: npm run test:e2e
```

## Troubleshooting

### Common Issues

1. **Tests timing out**: Increase timeout in jest.config.js or use waitFor helpers
2. **Mock not working**: Ensure mocks are imported before the tested modules
3. **E2E tests flaky**: Add proper wait conditions and use data-testid selectors
4. **Database tests failing**: Check mock setup and transaction simulation

### Debug Mode

```bash
# Debug Jest tests
npm run test -- --verbose --no-cache

# Debug Playwright tests
npm run test:e2e -- --debug

# Run specific test file
npm run test -- creditCostCalculation.test.js
```

## Future Enhancements

### Additional Test Cases (Planned)
- **AIVID_UNIT_002**: Topic Input with Content Filtering
- **AIVID_UNIT_003**: Video Style Dropdown Selection Logic
- **AIVID_UNIT_004**: Audio Speed Range Validation
- **AIVID_INT_003**: Image Generation API Circuit Breaker
- **AIVID_INT_004**: Form Submission to Video Generation API
- **AIVID_INT_005**: Real-time Status Updates Integration
- **AIVID_E2E_003**: Video Generation with API Failures and Recovery
- **AIVID_E2E_004**: Concurrent User Video Generation
- **AIVID_E2E_005**: Video Generation Cancellation and Refund

### Performance Testing
- Load testing for concurrent users
- Memory leak detection
- API response time benchmarks

### Accessibility Testing
- Screen reader compatibility
- Keyboard navigation
- Color contrast validation
- ARIA attribute verification
