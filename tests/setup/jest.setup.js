import '@testing-library/jest-dom';

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter() {
    return {
      push: jest.fn(),
      replace: jest.fn(),
      prefetch: jest.fn(),
      back: jest.fn(),
      forward: jest.fn(),
      refresh: jest.fn(),
    };
  },
  useSearchParams() {
    return new URLSearchParams();
  },
  usePathname() {
    return '/';
  },
}));

// Mock Clerk authentication
jest.mock('@clerk/nextjs', () => ({
  useUser: () => ({
    isLoaded: true,
    isSignedIn: true,
    user: {
      id: 'test_user_123',
      emailAddresses: [{ emailAddress: '<EMAIL>' }],
      firstName: 'Test',
      lastName: 'User',
    },
  }),
  useAuth: () => ({
    isLoaded: true,
    isSignedIn: true,
    userId: 'test_user_123',
  }),
  auth: () => Promise.resolve({
    userId: 'test_user_123',
  }),
}));

// Mock UserDetailContext
jest.mock('@/context/UserDetailContext', () => {
  const React = require('react');
  return {
    UserDetailContext: React.createContext({
      userDetail: {
        id: 'test_user_123',
        credits: 20,
        currentCreditBalance: 20,
      },
      setUserDetail: jest.fn(),
      invalidateVideoCache: jest.fn(),
    }),
    UserDetailProvider: ({ children }) => children,
  };
});

// Mock toast notifications
jest.mock('sonner', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
    info: jest.fn(),
    warning: jest.fn(),
  },
}));

// Mock environment variables
process.env.NODE_ENV = 'test';
process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY = 'test_clerk_key';

// Global test utilities
global.testUtils = {
  createMockUser: (overrides = {}) => ({
    id: 'test_user_123',
    clerkId: 'test_user_123',
    credits: 20,
    currentCreditBalance: 20,
    ...overrides,
  }),
  
  createMockFormData: (overrides = {}) => ({
    projectTitle: 'Test AI Video',
    topic: 'Artificial Intelligence in 2024',
    videoStyle: 'Professional',
    aspectRatio: '16:9',
    script: 'This is a test script for AI video generation.',
    voice: 'en-US-Wavenet-D',
    audioSpeed: 1.0,
    backgroundMusic: 'upbeat',
    caption: 'enabled',
    templateId: 'AI_VIDEO_TEMPLATE',
    ...overrides,
  }),
  
  createMockVideoData: (overrides = {}) => ({
    id: 'video_123',
    clerkId: 'test_user_123',
    title: 'Test AI Video',
    status: 'Pending',
    costInCredits: 5,
    ...overrides,
  }),
};

// Add missing globals for Node.js environment
global.TextEncoder = require('util').TextEncoder;
global.TextDecoder = require('util').TextDecoder;

// Suppress console warnings in tests
const originalWarn = console.warn;
console.warn = (...args) => {
  if (args[0]?.includes?.('Warning: ReactDOM.render is no longer supported')) {
    return;
  }
  originalWarn.call(console, ...args);
};
