/**
 * Test Database Manager for Integration Tests
 * 
 * Provides transaction-based test isolation with automatic rollback
 * and baseline seed data for consistent test environments.
 */

import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import { migrate } from 'drizzle-orm/postgres-js/migrator';
import { users, creditTransactions, videoData } from '@/configs/schema';
import { eq } from 'drizzle-orm';

export class TestDatabaseManager {
  constructor() {
    this.client = null;
    this.db = null;
    this.isSetup = false;
  }

  /**
   * Initialize test database connection and run migrations
   */
  async setup() {
    if (this.isSetup) {
      return;
    }

    try {
      // Create test database connection
      const testDbUrl = process.env.TEST_DATABASE_URL || 
        'postgresql://postgres:password@localhost:5432/ai_video_test';
      
      this.client = postgres(testDbUrl, {
        max: 1, // Single connection for tests
        idle_timeout: 20,
        connect_timeout: 10,
      });
      
      this.db = drizzle(this.client);
      
      // Run migrations to ensure schema is up to date
      await migrate(this.db, { migrationsFolder: './drizzle' });
      
      // Seed baseline data
      await this.seedBaselineData();
      
      this.isSetup = true;
      console.log('[TestDB] Database setup completed');
    } catch (error) {
      console.error('[TestDB] Setup failed:', error);
      throw error;
    }
  }

  /**
   * Clean up database connections
   */
  async cleanup() {
    if (this.client) {
      await this.client.end();
      this.client = null;
      this.db = null;
      this.isSetup = false;
      console.log('[TestDB] Database cleanup completed');
    }
  }

  /**
   * Execute test within a transaction that automatically rolls back
   * This ensures test isolation and prevents data pollution
   */
  async withTransaction(callback) {
    if (!this.db) {
      throw new Error('Database not initialized. Call setup() first.');
    }

    let testResult;
    let testError;

    try {
      await this.db.transaction(async (tx) => {
        try {
          // Execute the test callback with transaction context
          testResult = await callback(tx);
          
          // Force rollback by throwing a special error
          // This ensures no test data persists in the database
          throw new Error('ROLLBACK_TEST_TRANSACTION');
        } catch (error) {
          if (error.message === 'ROLLBACK_TEST_TRANSACTION') {
            // This is our intentional rollback, not a real error
            return;
          }
          // Store real test errors to re-throw after rollback
          testError = error;
          throw error;
        }
      });
    } catch (error) {
      if (error.message !== 'ROLLBACK_TEST_TRANSACTION') {
        // Re-throw real errors that occurred during test execution
        throw testError || error;
      }
    }

    // Return the test result (transaction was rolled back but test succeeded)
    return testResult;
  }

  /**
   * Get database instance for direct queries (use with caution)
   */
  getDatabase() {
    return this.db;
  }

  /**
   * Seed baseline data that all tests can rely on
   * This data is inserted once during setup and persists across tests
   */
  async seedBaselineData() {
    try {
      // Clear existing baseline data
      await this.db.delete(creditTransactions);
      await this.db.delete(videoData);
      await this.db.delete(users);

      // Insert baseline test users
      await this.db.insert(users).values([
        {
          clerkId: 'test_user_baseline',
          email: '<EMAIL>',
          credits: 50,
          currentCreditBalance: 50,
          createdAt: new Date('2024-01-01T00:00:00Z'),
          updatedAt: new Date('2024-01-01T00:00:00Z')
        },
        {
          clerkId: 'test_user_low_credits',
          email: '<EMAIL>',
          credits: 2,
          currentCreditBalance: 2,
          createdAt: new Date('2024-01-01T00:00:00Z'),
          updatedAt: new Date('2024-01-01T00:00:00Z')
        },
        {
          clerkId: 'test_user_no_credits',
          email: '<EMAIL>',
          credits: 0,
          currentCreditBalance: 0,
          createdAt: new Date('2024-01-01T00:00:00Z'),
          updatedAt: new Date('2024-01-01T00:00:00Z')
        }
      ]);

      console.log('[TestDB] Baseline seed data inserted');
    } catch (error) {
      console.error('[TestDB] Seed data insertion failed:', error);
      throw error;
    }
  }

  /**
   * Create a test user within a transaction
   */
  async createTestUser(tx, userData) {
    const defaultUser = {
      clerkId: `test_user_${Date.now()}`,
      email: `test${Date.now()}@example.com`,
      credits: 20,
      currentCreditBalance: 20,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    const user = { ...defaultUser, ...userData };
    
    const result = await tx.insert(users).values(user).returning();
    return result[0];
  }

  /**
   * Create a test video record within a transaction
   */
  async createTestVideo(tx, videoData) {
    const defaultVideo = {
      clerkId: 'test_user_baseline',
      title: 'Test Video',
      workflowType: 'AI_VIDEO',
      status: 'Pending',
      costInCredits: 5,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    const video = { ...defaultVideo, ...videoData };
    
    const result = await tx.insert(videoData).values(video).returning();
    return result[0];
  }

  /**
   * Verify database state for testing
   */
  async verifyDatabaseState(tx, expectations) {
    const results = {};

    if (expectations.userCount !== undefined) {
      const userCount = await tx.select().from(users);
      results.userCount = userCount.length;
    }

    if (expectations.transactionCount !== undefined) {
      const transactionCount = await tx.select().from(creditTransactions);
      results.transactionCount = transactionCount.length;
    }

    if (expectations.videoCount !== undefined) {
      const videoCount = await tx.select().from(videoData);
      results.videoCount = videoCount.length;
    }

    return results;
  }
}

// Singleton instance for test usage
export const testDbManager = new TestDatabaseManager();

// Helper function for quick transaction testing
export async function withTestTransaction(callback) {
  return await testDbManager.withTransaction(callback);
}
