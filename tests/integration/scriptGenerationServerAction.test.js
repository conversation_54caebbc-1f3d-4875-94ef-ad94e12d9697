/**
 * AIVID_INT_001: Test Script Generation Server Action Integration
 * 
 * Tests the integration between the UI and server action for AI script generation,
 * including API calls, circuit breaker behavior, and fallback mechanisms.
 */

// Mock external dependencies first
jest.mock('@/src/lib/aiUtils', () => ({
  generateScriptFromAI: jest.fn(),
}));

jest.mock('@/lib/inngestApiHelpers', () => ({
  resilientGoogleAI: jest.fn(),
  resilientElevenLabs: jest.fn(),
  resilientPexels: jest.fn(),
  resilientPixabay: jest.fn(),
  createEmergencyFallbacks: jest.fn(),
  checkAPIHealth: jest.fn(),
}));

jest.mock('@/lib/circuitBreaker', () => ({
  withCircuitBreaker: jest.fn(),
  getCircuitBreakerStatus: jest.fn(),
}));

import { generateScriptFromAI } from '@/src/lib/aiUtils';
import { testFormData, testApiResponses } from '../fixtures/testData';

describe('AIVID_INT_001: Script Generation Server Action Integration', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    // Setup default successful mock
    generateScriptFromAI.mockResolvedValue(testApiResponses.scriptGeneration.success);
  });

  describe('Successful script generation', () => {
    test('should generate script with valid topic and style', async () => {
      const topic = testFormData.valid.topic;
      const videoStyle = testFormData.valid.videoStyle;

      const result = await generateScriptFromAI(topic);

      expect(result).toBe(testApiResponses.scriptGeneration.success);
      expect(generateScriptFromAI).toHaveBeenCalledWith(topic);
    });

    test('should return properly formatted script', async () => {
      const result = await generateScriptFromAI('AI in Healthcare');

      expect(typeof result).toBe('string');
      expect(result.length).toBeGreaterThan(50); // Minimum meaningful length
      expect(result.trim()).toBe(result); // Should be trimmed
    });

    test('should handle different video styles', async () => {
      const styles = ['Professional', 'Educational', 'Entertainment', 'Documentary'];
      
      for (const style of styles) {
        const result = await generateScriptFromAI('Test Topic');
        expect(result).toBeTruthy();
        expect(typeof result).toBe('string');
      }
    });

    test('should handle various topic lengths', async () => {
      const topics = [
        'AI', // Short
        'Artificial Intelligence in Modern Healthcare', // Medium
        'The Revolutionary Impact of Artificial Intelligence on Modern Healthcare Systems and Patient Outcomes', // Long
      ];

      for (const topic of topics) {
        const result = await generateScriptFromAI(topic);
        expect(result).toBeTruthy();
        expect(typeof result).toBe('string');
      }
    });
  });

  describe('API failure and fallback scenarios', () => {
    test('should use fallback when Google AI fails', async () => {
      // Setup Google AI failure scenario
      generateScriptFromAI.mockRejectedValueOnce(new Error('Google AI API unavailable'));

      // Mock the fallback mechanism
      const mockFallback = jest.fn().mockResolvedValue({
        script: 'Fallback script content...',
        metadata: { source: 'fallback', wordCount: 100 }
      });
      const mockFallbacks = {
        scriptFallback: jest.fn().mockResolvedValue(testApiResponses.scriptGeneration.fallback),
      };
      createEmergencyFallbacks.mockReturnValue(mockFallbacks);

      // This should trigger the fallback since Google AI is set to fail
      generateScriptFromAI.mockImplementation(() => {
        throw new Error('Google AI API unavailable');
      });

      try {
        await generateScriptFromAI('Test Topic');
      } catch (error) {
        expect(error.message).toBe('Google AI API unavailable');
      }

      expect(generateScriptFromAI).toHaveBeenCalled();
    });

    test('should handle circuit breaker open state', async () => {
      const { withCircuitBreaker } = require('@/lib/circuitBreaker');
      
      // Mock circuit breaker to throw error (open state)
      withCircuitBreaker.mockImplementation(() => {
        throw new Error('Circuit breaker is open for GOOGLE_AI');
      });

      generateScriptFromAI.mockImplementation(() => {
        throw new Error('Circuit breaker is open for GOOGLE_AI');
      });

      await expect(generateScriptFromAI('Test Topic')).rejects.toThrow('Circuit breaker is open for GOOGLE_AI');
    });

    test('should retry on temporary failures', async () => {
      let callCount = 0;
      generateScriptFromAI.mockImplementation(() => {
        callCount++;
        if (callCount < 3) {
          throw new Error('Temporary API failure');
        }
        return testApiResponses.scriptGeneration.success;
      });

      const result = await generateScriptFromAI('Test Topic');
      expect(result).toBe(testApiResponses.scriptGeneration.success);
      expect(callCount).toBe(3); // Should have retried twice
    });
  });

  describe('Input validation and sanitization', () => {
    test('should handle empty topic gracefully', async () => {
      generateScriptFromAI.mockImplementation((topic) => {
        if (!topic || !topic.trim()) {
          throw new Error('Topic is required');
        }
        return testApiResponses.scriptGeneration.success;
      });

      await expect(generateScriptFromAI('')).rejects.toThrow('Topic is required');
      await expect(generateScriptFromAI('   ')).rejects.toThrow('Topic is required');
    });

    test('should handle special characters in topic', async () => {
      const topicWithSpecialChars = 'AI & Machine Learning: The Future (2024)!';
      
      const result = await generateScriptFromAI(topicWithSpecialChars);
      expect(result).toBeTruthy();
    });

    test('should handle unicode characters', async () => {
      const unicodeTopic = 'Intelligence Artificielle et Santé 🏥';
      
      const result = await generateScriptFromAI(unicodeTopic);
      expect(result).toBeTruthy();
    });

    test('should handle very long topics', async () => {
      const longTopic = 'A'.repeat(1000);
      
      const result = await generateScriptFromAI(longTopic);
      expect(result).toBeTruthy();
    });
  });

  describe('Performance and timeout handling', () => {
    test('should complete within reasonable time', async () => {
      const startTime = Date.now();
      
      await generateScriptFromAI('Test Topic');
      
      const duration = Date.now() - startTime;
      expect(duration).toBeLessThan(5000); // Should complete within 5 seconds
    });

    test('should handle API timeout', async () => {
      generateScriptFromAI.mockImplementation(() => {
        return new Promise((_, reject) => {
          setTimeout(() => reject(new Error('Request timeout')), 100);
        });
      });

      await expect(generateScriptFromAI('Test Topic')).rejects.toThrow('Request timeout');
    });
  });

  describe('Script quality validation', () => {
    test('should return script with minimum length', async () => {
      const result = await generateScriptFromAI('Test Topic');
      
      expect(result.length).toBeGreaterThan(20); // Minimum meaningful script length
    });

    test('should return script with maximum length limit', async () => {
      const result = await generateScriptFromAI('Test Topic');
      
      expect(result.length).toBeLessThan(2000); // Maximum script length for UI
    });

    test('should return coherent script content', async () => {
      const topic = 'Artificial Intelligence in Healthcare';
      const result = await generateScriptFromAI(topic);
      
      // Basic content validation
      expect(result).toMatch(/\w+/); // Should contain words
      expect(result.split(' ').length).toBeGreaterThan(10); // Should have multiple words
    });
  });

  describe('Error handling and user feedback', () => {
    test('should provide meaningful error messages', async () => {
      const errorScenarios = [
        { error: 'API rate limit exceeded', expectedMessage: /rate limit/i },
        { error: 'Invalid API key', expectedMessage: /authentication/i },
        { error: 'Service unavailable', expectedMessage: /unavailable/i },
      ];

      for (const scenario of errorScenarios) {
        generateScriptFromAI.mockRejectedValueOnce(new Error(scenario.error));
        
        await expect(generateScriptFromAI('Test Topic')).rejects.toThrow(scenario.error);
      }
    });

    test('should handle network errors', async () => {
      generateScriptFromAI.mockRejectedValueOnce(new Error('Network error'));
      
      await expect(generateScriptFromAI('Test Topic')).rejects.toThrow('Network error');
    });

    test('should handle malformed API responses', async () => {
      generateScriptFromAI.mockResolvedValueOnce(null);
      
      const result = await generateScriptFromAI('Test Topic');
      expect(result).toBeNull();
    });
  });

  describe('Integration with form state', () => {
    test('should integrate with form validation', async () => {
      // Simulate form submission with script generation
      const formData = {
        topic: testFormData.valid.topic,
        videoStyle: testFormData.valid.videoStyle,
      };

      const script = await generateScriptFromAI(formData.topic);
      
      expect(script).toBeTruthy();
      expect(typeof script).toBe('string');
    });

    test('should handle form state updates during generation', async () => {
      let generationInProgress = false;
      
      generateScriptFromAI.mockImplementation(async (topic) => {
        generationInProgress = true;
        
        // Simulate async generation
        await new Promise(resolve => setTimeout(resolve, 100));
        
        generationInProgress = false;
        return testApiResponses.scriptGeneration.success;
      });

      const promise = generateScriptFromAI('Test Topic');
      
      // Check that generation is in progress
      expect(generationInProgress).toBe(true);
      
      const result = await promise;
      
      // Check that generation completed
      expect(generationInProgress).toBe(false);
      expect(result).toBeTruthy();
    });
  });
});
