/**
 * Server Actions Integration Tests
 *
 * Tests the actual Server Action implementations with real functionality
 * validation instead of mocks.
 */

// Mock external dependencies
jest.mock('@/src/lib/aiUtils', () => ({
  generateScriptFromAI: jest.fn(),
}));

jest.mock('@/lib/circuitBreaker', () => ({
  withCircuitBreaker: jest.fn(),
}));

jest.mock('@/configs/db', () => ({
  db: {
    select: jest.fn().mockReturnThis(),
    from: jest.fn().mockReturnThis(),
    where: jest.fn().mockReturnThis(),
    limit: jest.fn().mockReturnThis(),
    insert: jest.fn().mockReturnThis(),
    values: jest.fn().mockReturnThis(),
    returning: jest.fn().mockReturnThis(),
    update: jest.fn().mockReturnThis(),
    set: jest.fn().mockReturnThis(),
  },
}));

import { aiVideoScriptGeneration, validateAIVideoInput, getAIVideoCostEstimate } from '@/app/actions/aiVideoActions';
import { initializeUser, getUserByClerkId, updateUserCredits } from '@/app/actions/userActions';
import { initiateVideoGeneration, getVideoGenerationStatus } from '@/app/actions/videoActions';
import { generateScriptFromAI } from '@/src/lib/aiUtils';
import { withCircuitBreaker } from '@/lib/circuitBreaker';
import { db } from '@/configs/db';

describe('Server Actions Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('AIVID_INT_SA_001: AI Script Generation Server Action', () => {
    test('should generate script successfully with valid input', async () => {
      // Setup mocks
      const mockScriptResult = {
        script: 'Generated script about AI in healthcare...',
        metadata: { wordCount: 150, estimatedDuration: 60 }
      };

      withCircuitBreaker.mockImplementation(async (serviceName, primaryFn, fallbackFn) => {
        return await primaryFn();
      });

      generateScriptFromAI.mockResolvedValue(mockScriptResult);

      // Test input
      const input = {
        topic: 'The Future of Artificial Intelligence in Healthcare',
        videoStyle: 'Professional',
        userId: 'test_user_123'
      };

      // Act
      const result = await aiVideoScriptGeneration(input);

      // Assert
      expect(result.success).toBe(true);
      expect(result.script).toBe(mockScriptResult.script);
      expect(result.metadata).toEqual(mockScriptResult.metadata);
      expect(generateScriptFromAI).toHaveBeenCalledWith({
        topic: input.topic,
        style: input.videoStyle,
        maxWords: 200
      });
    });

    test('should handle input validation errors', async () => {
      const testCases = [
        {
          input: { topic: '', videoStyle: 'Professional' },
          expectedError: 'Please enter a topic'
        },
        {
          input: { topic: 'Valid topic', videoStyle: '' },
          expectedError: 'Please select a video style'
        },
        {
          input: { topic: 'A'.repeat(501), videoStyle: 'Professional' },
          expectedError: 'Please shorten your topic'
        },
        {
          input: null,
          expectedError: 'An error occurred while generating the script'
        }
      ];

      for (const testCase of testCases) {
        const result = await aiVideoScriptGeneration(testCase.input);

        expect(result.success).toBe(false);
        expect(result.error).toContain(testCase.expectedError);
      }
    });

    test('should use fallback when circuit breaker is open', async () => {
      // Setup circuit breaker to use fallback
      withCircuitBreaker.mockImplementation(async (serviceName, primaryFn, fallbackFn) => {
        return await fallbackFn();
      });

      const input = {
        topic: 'Test Topic',
        videoStyle: 'Professional'
      };

      // Act
      const result = await aiVideoScriptGeneration(input);

      // Assert
      expect(result.success).toBe(true);
      expect(result.script).toContain('Test Topic');
      expect(result.metadata.source).toBe('fallback');
    });
  });

  describe('AIVID_INT_SA_002: Input Validation Functions', () => {
    test('should validate AI video input correctly', async () => {
      const validInput = {
        projectTitle: 'Valid Title',
        topic: 'Valid topic',
        videoStyle: 'Professional',
        aspectRatio: '16:9'
      };

      const result = await validateAIVideoInput(validInput);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    test('should detect validation errors', async () => {
      const invalidInput = {
        projectTitle: '',
        topic: '',
        videoStyle: 'InvalidStyle',
        aspectRatio: 'InvalidRatio'
      };

      const result = await validateAIVideoInput(invalidInput);
      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });
  });

  describe('AIVID_INT_SA_003: Cost Estimation', () => {
    test('should calculate cost estimate correctly', async () => {
      const formData = {
        projectTitle: 'Test Video',
        topic: 'Short topic'
      };

      const result = await getAIVideoCostEstimate(formData);

      expect(result.success).toBe(true);
      expect(result.cost).toBeGreaterThan(0);
      expect(result.breakdown).toBeDefined();
      expect(result.breakdown.baseCost).toBe(5);
    });
  });
});
