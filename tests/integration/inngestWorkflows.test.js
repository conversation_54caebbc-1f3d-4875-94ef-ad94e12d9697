/**
 * AIVID_INT_IW_001 through AIVID_INT_IW_005
 * Inngest Workflow Integration Tests
 * 
 * Tests event-driven workflows, state persistence, error handling,
 * and integration between API routes and Inngest functions.
 */

import { IntegrationTestBase } from '../utils/IntegrationTestBase.js';
import { users, videoData, creditTransactions } from '@/configs/schema';
import { eq } from 'drizzle-orm';

describe('Inngest Workflow Integration Tests', () => {
  let testBase;

  beforeEach(async () => {
    testBase = new IntegrationTestBase();
    await testBase.beforeEach();
  });

  afterEach(async () => {
    await testBase.afterEach();
  });

  describe('AIVID_INT_IW_001: Event Triggering from API Routes to Inngest', () => {
    test('should trigger Inngest event from API route with correct payload', async () => {
      await testBase.runInTransaction(async (tx) => {
        // Arrange
        const testUser = await testBase.createTestUser(tx, {
          clerkId: 'test_user_api',
          credits: 20
        });

        const apiRequest = {
          userId: testUser.clerkId,
          workflowType: 'AI_VIDEO',
          formData: {
            projectTitle: 'Test Video',
            topic: 'Integration testing',
            videoStyle: 'Professional'
          }
        };

        // Act - Simulate API route call
        const response = await simulateApiRoute('/api/video/initiate-generation', apiRequest, tx);

        // Assert
        expect(response).toMatchObject({
          success: true,
          videoId: expect.any(String),
          eventId: expect.any(String),
          newCreditBalance: 15
        });

        // Verify Inngest event triggered
        const events = testBase.mocks.inngest.getEvents();
        expect(events).toHaveLength(1);
        expect(events[0]).toMatchObject({
          name: 'video/generation.requested',
          data: {
            videoId: response.videoId,
            userId: testUser.clerkId,
            workflowType: 'AI_VIDEO',
            formData: apiRequest.formData
          }
        });

        // Verify event contains all required fields
        expect(events[0].data).toHaveProperty('timestamp');
        expect(events[0].data).toHaveProperty('videoId');
        expect(events[0].data).toHaveProperty('userId');
        expect(events[0].data).toHaveProperty('workflowType');
        expect(events[0].data).toHaveProperty('formData');
      });
    });

    test('should handle Inngest unavailable scenario with graceful degradation', async () => {
      await testBase.runInTransaction(async (tx) => {
        // Arrange
        const testUser = await testBase.createTestUser(tx, {
          clerkId: 'test_user_unavailable',
          credits: 20
        });

        // Disable Inngest to simulate unavailability
        testBase.mocks.inngest.setEnabled(false);

        const apiRequest = {
          userId: testUser.clerkId,
          workflowType: 'AI_VIDEO',
          formData: { projectTitle: 'Test Video' }
        };

        // Act & Assert
        await expect(
          simulateApiRoute('/api/video/initiate-generation', apiRequest, tx)
        ).rejects.toThrow('Inngest client is disabled');

        // Verify database rollback occurred
        const videos = await tx
          .select()
          .from(videoData)
          .where(eq(videoData.clerkId, testUser.clerkId));

        expect(videos).toHaveLength(0);

        // Verify credits not debited
        const user = await tx
          .select()
          .from(users)
          .where(eq(users.clerkId, testUser.clerkId))
          .limit(1);

        expect(user[0].credits).toBe(20); // Unchanged
      });
    });

    test('should implement retry logic for failed event triggers', async () => {
      await testBase.runInTransaction(async (tx) => {
        // Arrange
        const testUser = await testBase.createTestUser(tx, {
          clerkId: 'test_user_retry',
          credits: 20
        });

        // Configure Inngest to fail initially then succeed
        testBase.mocks.inngest.setFailureSimulation(true, 0.5); // 50% failure rate

        const apiRequest = {
          userId: testUser.clerkId,
          workflowType: 'AI_VIDEO',
          formData: { projectTitle: 'Test Video' }
        };

        // Act - Multiple attempts should eventually succeed
        let response;
        let attempts = 0;
        const maxAttempts = 5;

        while (attempts < maxAttempts) {
          try {
            response = await simulateApiRoute('/api/video/initiate-generation', apiRequest, tx);
            break;
          } catch (error) {
            attempts++;
            if (attempts >= maxAttempts) {
              throw error;
            }
            // Wait before retry
            await new Promise(resolve => setTimeout(resolve, 100));
          }
        }

        // Assert
        expect(response.success).toBe(true);
        expect(attempts).toBeLessThan(maxAttempts);

        // Verify event eventually triggered
        const events = testBase.mocks.inngest.getEvents();
        expect(events.length).toBeGreaterThan(0);
      });
    });
  });

  describe('AIVID_INT_IW_002: Initial Workflow Steps with Database Lookups', () => {
    test('should execute initial workflow steps with proper data validation', async () => {
      await testBase.runInTransaction(async (tx) => {
        // Arrange
        const testUser = await testBase.createTestUser(tx, {
          clerkId: 'test_user_workflow',
          credits: 15
        });

        const testVideo = await testBase.createTestVideo(tx, {
          clerkId: testUser.clerkId,
          workflowType: 'AI_VIDEO',
          status: 'Pending'
        });

        const workflowEvent = {
          name: 'video/generation.requested',
          data: {
            videoId: testVideo.id,
            userId: testUser.clerkId,
            workflowType: 'AI_VIDEO',
            formData: {
              projectTitle: 'Test Video',
              topic: 'Test topic'
            }
          }
        };

        // Act - Trigger workflow
        await testBase.mocks.inngest.send(workflowEvent);

        // Wait for workflow to complete
        await testBase.waitForAsyncOperations(500);

        // Assert
        const workflows = testBase.mocks.inngest.getWorkflows();
        expect(workflows).toHaveLength(1);

        const workflow = workflows[0];
        expect(workflow.status).toBe('completed');
        expect(workflow.name).toBe('videoGenerationWorkflow');

        // Verify workflow steps executed in correct order
        const expectedSteps = [
          'validateInput',
          'enrichUserData',
          'generateScript',
          'generateAudio',
          'generateImages',
          'compileVideo',
          'updateVideoStatus'
        ];

        expect(workflow.steps).toHaveLength(expectedSteps.length);
        workflow.steps.forEach((step, index) => {
          expect(step.name).toBe(expectedSteps[index]);
          expect(step.status).toBe('completed');
        });

        // Verify workflow data enrichment
        expect(workflow.data).toMatchObject({
          validated: true,
          userPlan: 'free',
          script: expect.any(String),
          audioUrl: expect.any(String),
          videoUrl: expect.any(String)
        });
      });
    });

    test('should handle missing database records gracefully', async () => {
      // Arrange
      const workflowEvent = {
        name: 'video/generation.requested',
        data: {
          videoId: 'nonexistent_video',
          userId: 'nonexistent_user',
          workflowType: 'AI_VIDEO'
        }
      };

      // Act
      await testBase.mocks.inngest.send(workflowEvent);
      await testBase.waitForAsyncOperations(200);

      // Assert
      const workflows = testBase.mocks.inngest.getWorkflows();
      expect(workflows).toHaveLength(1);

      const workflow = workflows[0];
      expect(workflow.status).toBe('failed');
      expect(workflow.error).toContain('Missing videoId');
    });

    test('should persist workflow state between steps', async () => {
      await testBase.runInTransaction(async (tx) => {
        // Arrange
        const testUser = await testBase.createTestUser(tx);
        const testVideo = await testBase.createTestVideo(tx, {
          clerkId: testUser.clerkId
        });

        const workflowEvent = {
          name: 'video/generation.requested',
          data: {
            videoId: testVideo.id,
            userId: testUser.clerkId,
            workflowType: 'AI_VIDEO'
          }
        };

        // Act
        await testBase.mocks.inngest.send(workflowEvent);
        await testBase.waitForAsyncOperations(300);

        // Assert
        const workflow = testBase.mocks.inngest.getWorkflows()[0];
        
        // Verify state accumulation across steps
        expect(workflow.data).toMatchObject({
          videoId: testVideo.id,
          userId: testUser.clerkId,
          validated: true,
          userPlan: expect.any(String),
          script: expect.any(String),
          audioUrl: expect.any(String),
          videoUrl: expect.any(String)
        });

        // Verify each step had access to previous step outputs
        const scriptStep = workflow.steps.find(s => s.name === 'generateScript');
        const audioStep = workflow.steps.find(s => s.name === 'generateAudio');
        
        expect(scriptStep.output).toHaveProperty('script');
        expect(audioStep.output).toHaveProperty('audioUrl');
      });
    });
  });

  describe('AIVID_INT_IW_003: Error Handling in Workflows Triggering Refunds', () => {
    test('should trigger credit refund when workflow fails', async () => {
      await testBase.runInTransaction(async (tx) => {
        // Arrange
        const testUser = await testBase.createTestUser(tx, {
          clerkId: 'test_user_refund',
          credits: 15 // After debit
        });

        const testVideo = await testBase.createTestVideo(tx, {
          clerkId: testUser.clerkId,
          status: 'Processing',
          costInCredits: 5
        });

        // Create initial debit transaction
        await tx.insert(creditTransactions).values({
          userId: testUser.clerkId,
          type: 'DEBIT',
          amount: -5,
          balanceBefore: 20,
          balanceAfter: 15,
          description: 'AI Video Generation',
          videoId: testVideo.id
        });

        // Act - Trigger error handling workflow
        const errorEvent = {
          name: 'video/generation.failed',
          data: {
            videoId: testVideo.id,
            userId: testUser.clerkId,
            errorReason: 'GOOGLE_AI_API_UNAVAILABLE',
            refundAmount: 5
          }
        };

        await testBase.mocks.inngest.send(errorEvent);
        await testBase.waitForAsyncOperations(200);

        // Assert
        const workflows = testBase.mocks.inngest.getWorkflowsByName('creditRefundWorkflow');
        expect(workflows).toHaveLength(1);
        expect(workflows[0].status).toBe('completed');

        // Verify refund workflow steps
        const refundWorkflow = workflows[0];
        const expectedSteps = [
          'validateRefundRequest',
          'processRefund',
          'updateVideoStatus',
          'notifyUser'
        ];

        expect(refundWorkflow.steps).toHaveLength(expectedSteps.length);
        refundWorkflow.steps.forEach((step, index) => {
          expect(step.name).toBe(expectedSteps[index]);
          expect(step.status).toBe('completed');
        });
      });
    });

    test('should handle partial workflow failures with appropriate cleanup', async () => {
      await testBase.runInTransaction(async (tx) => {
        // Arrange
        const testUser = await testBase.createTestUser(tx);
        const testVideo = await testBase.createTestVideo(tx, {
          clerkId: testUser.clerkId
        });

        // Configure workflow to fail at specific step
        const originalExecuteStep = testBase.mocks.inngest.executeWorkflowStep;
        testBase.mocks.inngest.executeWorkflowStep = async function(stepDef, workflowData) {
          if (stepDef.name === 'generateAudio') {
            throw new Error('Audio generation service unavailable');
          }
          return originalExecuteStep.call(this, stepDef, workflowData);
        };

        const workflowEvent = {
          name: 'video/generation.requested',
          data: {
            videoId: testVideo.id,
            userId: testUser.clerkId,
            workflowType: 'AI_VIDEO'
          }
        };

        // Act
        await testBase.mocks.inngest.send(workflowEvent);
        await testBase.waitForAsyncOperations(300);

        // Assert
        const workflows = testBase.mocks.inngest.getWorkflows();
        const mainWorkflow = workflows.find(w => w.name === 'videoGenerationWorkflow');
        
        expect(mainWorkflow.status).toBe('failed');
        expect(mainWorkflow.error).toContain('Audio generation service unavailable');

        // Verify partial completion
        const completedSteps = mainWorkflow.steps.filter(s => s.status === 'completed');
        const failedStep = mainWorkflow.steps.find(s => s.status === 'failed');
        
        expect(completedSteps.length).toBeGreaterThan(0);
        expect(failedStep.name).toBe('generateAudio');

        // Verify cleanup workflow triggered
        const cleanupWorkflows = workflows.filter(w => w.name === 'creditRefundWorkflow');
        expect(cleanupWorkflows.length).toBeGreaterThan(0);
      });
    });
  });

  describe('AIVID_INT_IW_004: Workflow State Persistence and Recovery', () => {
    test('should persist workflow state and recover from interruptions', async () => {
      await testBase.runInTransaction(async (tx) => {
        // Arrange
        const testUser = await testBase.createTestUser(tx);
        const testVideo = await testBase.createTestVideo(tx, {
          clerkId: testUser.clerkId
        });

        const workflowEvent = {
          name: 'video/generation.requested',
          data: {
            videoId: testVideo.id,
            userId: testUser.clerkId,
            workflowType: 'AI_VIDEO'
          }
        };

        // Act - Start workflow
        await testBase.mocks.inngest.send(workflowEvent);

        // Simulate workflow interruption after 3 steps
        await testBase.waitForAsyncOperations(150);

        const workflows = testBase.mocks.inngest.getWorkflows();
        const workflow = workflows[0];

        // Verify partial completion
        expect(workflow.steps.length).toBeGreaterThan(0);
        expect(workflow.status).toBe('running');

        // Simulate workflow recovery
        await testBase.waitForAsyncOperations(350);

        // Assert - Workflow should complete
        const completedWorkflow = testBase.mocks.inngest.getWorkflow(workflow.id);
        expect(completedWorkflow.status).toBe('completed');
        expect(completedWorkflow.steps).toHaveLength(7); // All steps completed

        // Verify state persistence across interruption
        expect(completedWorkflow.data).toMatchObject({
          videoId: testVideo.id,
          userId: testUser.clerkId,
          validated: true,
          script: expect.any(String),
          videoUrl: expect.any(String)
        });
      });
    });

    test('should handle workflow timeout and cleanup', async () => {
      await testBase.runInTransaction(async (tx) => {
        // Arrange
        const testUser = await testBase.createTestUser(tx);
        const testVideo = await testBase.createTestVideo(tx, {
          clerkId: testUser.clerkId
        });

        // Configure workflow to simulate long-running operation
        const originalExecuteStep = testBase.mocks.inngest.executeWorkflowStep;
        testBase.mocks.inngest.executeWorkflowStep = async function(stepDef, workflowData) {
          if (stepDef.name === 'compileVideo') {
            // Simulate timeout
            await new Promise(resolve => setTimeout(resolve, 2000));
            throw new Error('Workflow timeout');
          }
          return originalExecuteStep.call(this, stepDef, workflowData);
        };

        const workflowEvent = {
          name: 'video/generation.requested',
          data: {
            videoId: testVideo.id,
            userId: testUser.clerkId,
            workflowType: 'AI_VIDEO'
          }
        };

        // Act
        await testBase.mocks.inngest.send(workflowEvent);
        await testBase.waitForAsyncOperations(500);

        // Assert
        const workflows = testBase.mocks.inngest.getWorkflows();
        const workflow = workflows.find(w => w.name === 'videoGenerationWorkflow');

        expect(workflow.status).toBe('failed');
        expect(workflow.error).toContain('Workflow timeout');

        // Verify cleanup was triggered
        const cleanupWorkflows = workflows.filter(w => w.name === 'creditRefundWorkflow');
        expect(cleanupWorkflows.length).toBeGreaterThan(0);
      });
    });
  });

  describe('AIVID_INT_IW_005: Event Payload Validation and Processing', () => {
    test('should validate event payload structure and data types', async () => {
      // Test cases for event validation
      const testCases = [
        {
          name: 'missing videoId',
          event: {
            name: 'video/generation.requested',
            data: { userId: 'test_user', workflowType: 'AI_VIDEO' }
          },
          expectedError: 'Missing videoId'
        },
        {
          name: 'invalid workflowType',
          event: {
            name: 'video/generation.requested',
            data: {
              videoId: 'video_123',
              userId: 'test_user',
              workflowType: 'INVALID_TYPE'
            }
          },
          expectedError: 'Invalid workflow type'
        },
        {
          name: 'missing formData',
          event: {
            name: 'video/generation.requested',
            data: {
              videoId: 'video_123',
              userId: 'test_user',
              workflowType: 'AI_VIDEO'
            }
          },
          expectedError: 'Missing formData'
        }
      ];

      for (const testCase of testCases) {
        // Act
        await testBase.mocks.inngest.send(testCase.event);
        await testBase.waitForAsyncOperations(100);

        // Assert
        const workflows = testBase.mocks.inngest.getWorkflowsByName('videoGenerationWorkflow');
        const latestWorkflow = workflows[workflows.length - 1];

        expect(latestWorkflow.status).toBe('failed');
        expect(latestWorkflow.error).toContain(testCase.expectedError);
      }
    });

    test('should transform and enrich event data during processing', async () => {
      await testBase.runInTransaction(async (tx) => {
        // Arrange
        const testUser = await testBase.createTestUser(tx, {
          clerkId: 'test_user_transform'
        });

        const testVideo = await testBase.createTestVideo(tx, {
          clerkId: testUser.clerkId
        });

        const rawEvent = {
          name: 'video/generation.requested',
          data: {
            videoId: testVideo.id,
            userId: testUser.clerkId,
            workflowType: 'AI_VIDEO',
            formData: {
              projectTitle: 'Raw Title',
              topic: 'raw topic',
              videoStyle: 'professional'
            }
          }
        };

        // Act
        await testBase.mocks.inngest.send(rawEvent);
        await testBase.waitForAsyncOperations(300);

        // Assert
        const workflow = testBase.mocks.inngest.getWorkflows()[0];

        // Verify data transformation occurred
        expect(workflow.data).toMatchObject({
          videoId: testVideo.id,
          userId: testUser.clerkId,
          workflowType: 'AI_VIDEO',

          // Enriched data from workflow steps
          validated: true,
          userPlan: expect.any(String),
          userPreferences: expect.any(Object),

          // Processed form data
          script: expect.any(String),
          audioUrl: expect.any(String),
          videoUrl: expect.any(String)
        });

        // Verify data enrichment steps
        const enrichStep = workflow.steps.find(s => s.name === 'enrichUserData');
        expect(enrichStep.output).toMatchObject({
          userPlan: 'free',
          userPreferences: { quality: 'standard' }
        });
      });
    });

    test('should handle malformed event data gracefully', async () => {
      // Test malformed JSON and data types
      const malformedEvents = [
        {
          name: 'video/generation.requested',
          data: null
        },
        {
          name: 'video/generation.requested',
          data: 'invalid_string_data'
        },
        {
          name: 'video/generation.requested',
          data: {
            videoId: 123, // Should be string
            userId: null,
            workflowType: ['AI_VIDEO'] // Should be string
          }
        }
      ];

      for (const malformedEvent of malformedEvents) {
        // Act
        await testBase.mocks.inngest.send(malformedEvent);
        await testBase.waitForAsyncOperations(50);

        // Assert
        const workflows = testBase.mocks.inngest.getWorkflows();
        const latestWorkflow = workflows[workflows.length - 1];

        expect(latestWorkflow.status).toBe('failed');
        expect(latestWorkflow.error).toMatch(/validation|invalid|malformed/i);
      }
    });
  });
});

// Helper function to simulate API route calls
async function simulateApiRoute(endpoint, requestData, tx) {
  // Simulate the API route logic for video generation initiation
  if (endpoint === '/api/video/initiate-generation') {
    const { userId, workflowType, formData } = requestData;
    
    // This would call the actual atomicVideoGeneration function
    const result = await atomicVideoGeneration(userId, workflowType, formData, tx);
    
    if (!result.success) {
      throw new Error(result.error);
    }

    return {
      success: true,
      videoId: result.video.id,
      eventId: result.inngestEventId || 'mock_event_id',
      newCreditBalance: result.newBalance
    };
  }
  
  throw new Error(`Unknown endpoint: ${endpoint}`);
}

// Mock atomicVideoGeneration function for testing
async function atomicVideoGeneration(userId, workflowType, formData, tx) {
  // Simplified version for testing
  const user = await tx
    .select()
    .from(users)
    .where(eq(users.clerkId, userId))
    .limit(1);

  if (!user[0]) {
    throw new Error('User not found');
  }

  const cost = workflowType === 'AI_VIDEO' ? 5 : 2;
  
  if (user[0].credits < cost) {
    throw new Error(`Insufficient credits. Required: ${cost}, Available: ${user[0].credits}`);
  }

  const newBalance = user[0].credits - cost;

  // Update user balance
  await tx
    .update(users)
    .set({ credits: newBalance, currentCreditBalance: newBalance })
    .where(eq(users.clerkId, userId));

  // Create video record
  const video = await tx.insert(videoData).values({
    clerkId: userId,
    title: formData.projectTitle || 'Test Video',
    workflowType,
    status: 'Pending',
    costInCredits: cost
  }).returning();

  // Create transaction record
  await tx.insert(creditTransactions).values({
    userId,
    type: 'DEBIT',
    amount: -cost,
    balanceBefore: user[0].credits,
    balanceAfter: newBalance,
    description: `${workflowType} Generation`,
    videoId: video[0].id
  });

  return {
    success: true,
    video: video[0],
    newBalance,
    inngestEventId: 'mock_event_id'
  };
}
