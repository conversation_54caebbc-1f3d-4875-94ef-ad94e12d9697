/**
 * AIVID_INT_DB_001 through AIVID_INT_DB_005
 * Atomic Credit System Integration Tests
 * 
 * Tests critical database interactions, transaction integrity,
 * and credit system reliability under various scenarios.
 */

// Mock the database and external dependencies first
jest.mock('@/configs/db', () => ({
  db: {
    select: jest.fn().mockReturnThis(),
    from: jest.fn().mockReturnThis(),
    where: jest.fn().mockReturnThis(),
    limit: jest.fn().mockReturnThis(),
    insert: jest.fn().mockReturnThis(),
    values: jest.fn().mockReturnThis(),
    returning: jest.fn().mockReturnThis(),
    update: jest.fn().mockReturnThis(),
    set: jest.fn().mockReturnThis(),
  },
}));

jest.mock('@/app/inngest/client', () => ({
  inngest: { send: jest.fn() },
}));

import { atomicVideoGeneration } from '@/lib/atomicCreditSystem';
import { db } from '@/configs/db';

describe('Atomic Credit System Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('AIVID_INT_DB_001: Credit Debit with Video Creation Success', () => {
    test('should successfully debit credits and create video record', async () => {
      // Arrange
      const testUser = {
        clerkId: 'test_user_123',
        email: '<EMAIL>',
        credits: 20,
        currentCreditBalance: 20
      };

      const formData = {
        projectTitle: 'Integration Test Video',
        topic: 'Testing atomic credit operations',
        videoStyle: 'Professional',
        aspectRatio: '16:9'
      };

      // Mock database responses
      db.select.mockReturnValue({
        from: jest.fn().mockReturnValue({
          where: jest.fn().mockReturnValue({
            limit: jest.fn().mockResolvedValue([testUser])
          })
        })
      });

      db.update.mockReturnValue({
        set: jest.fn().mockReturnValue({
          where: jest.fn().mockReturnValue({
            returning: jest.fn().mockResolvedValue([{ ...testUser, credits: 15 }])
          })
        })
      });

      db.insert.mockReturnValue({
        values: jest.fn().mockReturnValue({
          returning: jest.fn().mockResolvedValue([{
            id: 'video_123',
            clerkId: testUser.clerkId,
            title: formData.projectTitle,
            status: 'Pending',
            workflowType: 'AI_VIDEO',
            costInCredits: 5
          }])
        })
      });

      // Act
      const result = await atomicVideoGeneration(
        testUser.clerkId,
        'AI_VIDEO',
        formData
      );

      // Assert
      expect(result).toMatchObject({
        video: expect.objectContaining({
          id: 'video_123',
          clerkId: testUser.clerkId,
          status: 'Pending',
          workflowType: 'AI_VIDEO'
        }),
        newBalance: 15
      });

      // Verify database operations were called correctly
      expect(db.select).toHaveBeenCalled();
      expect(db.update).toHaveBeenCalled();
      expect(db.insert).toHaveBeenCalled();
    });

    test('should handle different workflow types with correct costs', async () => {
      // Test MEME_VIDEO workflow (2 credits)
      const testUser = {
        clerkId: 'test_user_meme',
        credits: 10,
        currentCreditBalance: 10
      };

      // Mock database responses for MEME_VIDEO
      db.select.mockReturnValue({
        from: jest.fn().mockReturnValue({
          where: jest.fn().mockReturnValue({
            limit: jest.fn().mockResolvedValue([testUser])
          })
        })
      });

      db.update.mockReturnValue({
        set: jest.fn().mockReturnValue({
          where: jest.fn().mockReturnValue({
            returning: jest.fn().mockResolvedValue([{ ...testUser, credits: 8 }])
          })
        })
      });

      db.insert.mockReturnValue({
        values: jest.fn().mockReturnValue({
          returning: jest.fn().mockResolvedValue([{
            id: 'video_meme_123',
            clerkId: testUser.clerkId,
            workflowType: 'MEME_VIDEO',
            status: 'Pending'
          }])
        })
      });

      const result = await atomicVideoGeneration(
        testUser.clerkId,
        'MEME_VIDEO',
        { projectTitle: 'Test Meme', topic: 'Funny meme' }
      );

      expect(result.newBalance).toBe(8); // 10 - 2
      expect(result.video.workflowType).toBe('MEME_VIDEO');
    });
  });

  describe('AIVID_INT_DB_002: Credit Debit Failure with Rollback', () => {
    test('should reject video generation with insufficient credits', async () => {
      // Arrange
      const testUser = {
        clerkId: 'test_user_123',
        email: '<EMAIL>',
        credits: 3,
        currentCreditBalance: 3
      };

      const formData = {
        projectTitle: 'Test Video',
        topic: 'Test topic',
        videoStyle: 'Professional'
      };

      // Mock database to return user with insufficient credits
      db.select.mockReturnValue({
        from: jest.fn().mockReturnValue({
          where: jest.fn().mockReturnValue({
            limit: jest.fn().mockResolvedValue([testUser])
          })
        })
      });

      // Act & Assert
      await expect(
        atomicVideoGeneration(testUser.clerkId, 'AI_VIDEO', formData)
      ).rejects.toThrow('Insufficient credits. Required: 5, Available: 3');

      // Verify no update or insert operations were attempted
      expect(db.update).not.toHaveBeenCalled();
      expect(db.insert).not.toHaveBeenCalled();
    });

    test('should handle user not found scenario', async () => {
      // Mock database to return empty array (user not found)
      db.select.mockReturnValue({
        from: jest.fn().mockReturnValue({
          where: jest.fn().mockReturnValue({
            limit: jest.fn().mockResolvedValue([]) // Empty array = user not found
          })
        })
      });

      // Act & Assert
      await expect(
        atomicVideoGeneration('nonexistent_user', 'AI_VIDEO', {})
      ).rejects.toThrow('User not found');

      // Verify no update or insert operations were attempted
      expect(db.update).not.toHaveBeenCalled();
      expect(db.insert).not.toHaveBeenCalled();
    });
  });
});

// Simplified test for the actual function behavior
describe('Atomic Credit System Function Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('AIVID_INT_DB_003: Credit Cost Calculation', () => {
    test('should calculate correct costs for different workflow types', () => {
      const { getCostForWorkflow } = require('@/lib/atomicCreditSystem');

      // Test different workflow costs
      expect(getCostForWorkflow('AI_VIDEO')).toBe(5);
      expect(getCostForWorkflow('MEME_VIDEO')).toBe(2);
      expect(getCostForWorkflow('UGC_VIDEO')).toBe(8);
      expect(getCostForWorkflow('NARRATOR_VIDEO')).toBe(5); // 4 base + 1 for duration
      expect(getCostForWorkflow('UNKNOWN_WORKFLOW')).toBe(5); // Default
    });

    test('should adjust costs based on estimated duration', () => {
      const { getCostForWorkflow } = require('@/lib/atomicCreditSystem');

      // Test duration-based cost adjustment
      const longVideoConfig = { topic: 'A very long topic that would generate a longer video with more content and details' };
      const shortVideoConfig = { topic: 'Short' };

      const longCost = getCostForWorkflow('AI_VIDEO', longVideoConfig);
      const shortCost = getCostForWorkflow('AI_VIDEO', shortVideoConfig);

      expect(longCost).toBeGreaterThanOrEqual(shortCost);
    });
  });

  describe('AIVID_INT_DB_004: Error Handling', () => {
    test('should handle database errors gracefully', async () => {
      // Mock database error
      db.select.mockReturnValue({
        from: jest.fn().mockReturnValue({
          where: jest.fn().mockReturnValue({
            limit: jest.fn().mockRejectedValue(new Error('Database connection failed'))
          })
        })
      });

      // Act & Assert
      await expect(
        atomicVideoGeneration('test_user', 'AI_VIDEO', {})
      ).rejects.toThrow('Database connection failed');
    });

    test('should handle video creation failure with rollback', async () => {
      const testUser = {
        clerkId: 'test_user_rollback',
        credits: 20,
        currentCreditBalance: 20
      };

      // Mock successful user fetch and update, but failed video creation
      db.select.mockReturnValue({
        from: jest.fn().mockReturnValue({
          where: jest.fn().mockReturnValue({
            limit: jest.fn().mockResolvedValue([testUser])
          })
        })
      });

      db.update.mockReturnValue({
        set: jest.fn().mockReturnValue({
          where: jest.fn().mockReturnValue({
            returning: jest.fn().mockResolvedValue([{ ...testUser, credits: 15 }])
          })
        })
      });

      db.insert.mockReturnValue({
        values: jest.fn().mockReturnValue({
          returning: jest.fn().mockRejectedValue(new Error('Video creation failed'))
        })
      });

      // Act & Assert
      await expect(
        atomicVideoGeneration(testUser.clerkId, 'AI_VIDEO', {})
      ).rejects.toThrow('Video creation failed');

      // Verify rollback was attempted (update called twice - once for debit, once for rollback)
      expect(db.update).toHaveBeenCalledTimes(2);
    });
  });
});
