/**
 * AIVID_INT_002: Test Credit Check and Debit Integration
 * 
 * Tests the atomic credit system integration including credit checks,
 * debit operations, and transaction recording for video generation.
 */

// Mock the database and external dependencies first
jest.mock('@/configs/db', () => ({
  db: {
    select: jest.fn().mockReturnThis(),
    from: jest.fn().mockReturnThis(),
    where: jest.fn().mockReturnThis(),
    limit: jest.fn().mockReturnThis(),
    insert: jest.fn().mockReturnThis(),
    values: jest.fn().mockReturnThis(),
    returning: jest.fn().mockReturnThis(),
    update: jest.fn().mockReturnThis(),
    set: jest.fn().mockReturnThis(),
  },
}));

jest.mock('@/configs/schema', () => ({
  users: {
    clerkId: 'clerkId',
    credits: 'credits',
    currentCreditBalance: 'currentCreditBalance',
  },
  creditTransactions: {
    userId: 'userId',
    transactionType: 'transactionType',
    amount: 'amount',
  },
  videoData: {
    clerkId: 'clerkId',
    title: 'title',
    status: 'status',
  },
}));

jest.mock('drizzle-orm', () => ({
  eq: jest.fn((field, value) => ({ field, value, type: 'eq' })),
  sql: jest.fn((strings, ...values) => ({ strings, values, type: 'sql' })),
}));

jest.mock('@/app/inngest/client', () => ({
  inngest: {
    send: jest.fn(),
  },
}));

import { atomicVideoGeneration } from '@/lib/atomicCreditSystem';
import { testUsers, testFormData, testCreditTransactions } from '../fixtures/testData';

describe('AIVID_INT_002: Credit Check and Debit Integration', () => {
  let mockDb;

  beforeEach(() => {
    jest.clearAllMocks();

    // Get the mocked db instance
    const { db } = require('@/configs/db');
    mockDb = db;

    // Reset all mock implementations
    Object.keys(mockDb).forEach(key => {
      if (typeof mockDb[key].mockReset === 'function') {
        mockDb[key].mockReset();
        mockDb[key].mockReturnThis();
      }
    });
  });

  describe('Sufficient credits scenario', () => {
    beforeEach(() => {
      // Mock successful user lookup
      mockDb.limit.mockResolvedValue([testUsers.sufficientCredits]);

      // Mock successful user update
      mockDb.returning.mockResolvedValue([{
        ...testUsers.sufficientCredits,
        credits: 15
      }]);
    });

    test('should successfully debit credits for AI video generation', async () => {
      const result = await atomicVideoGeneration(
        'test_user_123',
        'AI_VIDEO',
        testFormData.valid
      );

      expect(result).toEqual({
        video: expect.objectContaining({
          clerkId: 'test_user_123',
          title: expect.any(String),
          status: 'Pending',
        }),
        creditTransaction: null, // May be null if table doesn't exist
        newBalance: 15,
      });
    });

    test('should create proper credit transaction record', async () => {
      setupDatabaseMocks('transaction_success');
      
      await atomicVideoGeneration(
        'test_user_123',
        'AI_VIDEO',
        testFormData.valid
      );

      // Verify transaction was created
      expect(mockDb.insert).toHaveBeenCalled();
      expect(mockDb.values).toHaveBeenCalledWith(
        expect.objectContaining({
          userId: 'test_user_123',
          transactionType: 'DEBIT',
          amount: -5,
          balanceBefore: 20,
          balanceAfter: 15,
        })
      );
    });

    test('should update user balance correctly', async () => {
      setupDatabaseMocks('transaction_success');
      
      await atomicVideoGeneration(
        'test_user_123',
        'AI_VIDEO',
        testFormData.valid
      );

      // Verify user balance was updated
      expect(mockDb.update).toHaveBeenCalled();
      expect(mockDb.set).toHaveBeenCalledWith(
        expect.objectContaining({
          credits: 15, // New balance
          updatedAt: expect.any(Date),
        })
      );
    });

    test('should create video record with correct data', async () => {
      setupDatabaseMocks('transaction_success');
      
      const result = await atomicVideoGeneration(
        'test_user_123',
        'AI_VIDEO',
        testFormData.valid
      );

      // Verify video record was created
      expect(mockDb.insert).toHaveBeenCalled();
      expect(mockDb.values).toHaveBeenCalledWith(
        expect.objectContaining({
          clerkId: 'test_user_123',
          title: testFormData.valid.projectTitle,
          topic: testFormData.valid.topic,
          script: testFormData.valid.script,
          videoStyle: testFormData.valid.videoStyle,
          aspectRatio: testFormData.valid.aspectRatio,
          costInCredits: 5,
          status: 'Pending',
          workflow_data: expect.objectContaining({
            workflowType: 'AI_VIDEO',
          }),
        })
      );
    });
  });

  describe('Insufficient credits scenario', () => {
    beforeEach(() => {
      setupDatabaseMocks('insufficient_credits');
    });

    test('should reject video generation with insufficient credits', async () => {
      await expect(
        atomicVideoGeneration(
          'test_user_insufficient',
          'AI_VIDEO',
          testFormData.valid
        )
      ).rejects.toThrow('Insufficient credits. Required: 5, Available: 3');
    });

    test('should not modify user balance when credits insufficient', async () => {
      try {
        await atomicVideoGeneration(
          'test_user_insufficient',
          'AI_VIDEO',
          testFormData.valid
        );
      } catch (error) {
        // Expected to throw
      }

      // Verify no update operations were called
      expect(mockDb.update).not.toHaveBeenCalled();
    });

    test('should not create transaction when credits insufficient', async () => {
      try {
        await atomicVideoGeneration(
          'test_user_insufficient',
          'AI_VIDEO',
          testFormData.valid
        );
      } catch (error) {
        // Expected to throw
      }

      // Verify no insert operations were called
      expect(mockDb.insert).not.toHaveBeenCalled();
    });
  });

  describe('User not found scenario', () => {
    beforeEach(() => {
      setupDatabaseMocks('user_not_found');
    });

    test('should reject video generation for non-existent user', async () => {
      await expect(
        atomicVideoGeneration(
          'non_existent_user',
          'AI_VIDEO',
          testFormData.valid
        )
      ).rejects.toThrow('User not found');
    });
  });

  describe('Different workflow types', () => {
    beforeEach(() => {
      setupDatabaseMocks('transaction_success');
    });

    test('should handle MEME_VIDEO workflow with correct cost', async () => {
      const result = await atomicVideoGeneration(
        'test_user_123',
        'MEME_VIDEO',
        { projectTitle: 'Test Meme' }
      );

      expect(result.creditsRemaining).toBe(18); // 20 - 2
    });

    test('should handle UGC_VIDEO workflow with correct cost', async () => {
      const result = await atomicVideoGeneration(
        'test_user_123',
        'UGC_VIDEO',
        { projectTitle: 'Test UGC' }
      );

      expect(result.creditsRemaining).toBe(12); // 20 - 8
    });

    test('should handle unknown workflow with default cost', async () => {
      const result = await atomicVideoGeneration(
        'test_user_123',
        'UNKNOWN_WORKFLOW',
        { projectTitle: 'Test Unknown' }
      );

      expect(result.creditsRemaining).toBe(15); // 20 - 5 (default)
    });
  });

  describe('Duration-based cost adjustments', () => {
    beforeEach(() => {
      setupDatabaseMocks('transaction_success');
    });

    test('should apply extra cost for long videos', async () => {
      const longVideoConfig = {
        ...testFormData.valid,
        estimatedDurationSeconds: 120, // 2 minutes
      };

      const result = await atomicVideoGeneration(
        'test_user_123',
        'AI_VIDEO',
        longVideoConfig
      );

      // Base 5 + 2 extra for duration = 7 total
      expect(result.creditsRemaining).toBe(13); // 20 - 7
    });

    test('should not apply extra cost for short videos', async () => {
      const shortVideoConfig = {
        ...testFormData.valid,
        estimatedDurationSeconds: 45,
      };

      const result = await atomicVideoGeneration(
        'test_user_123',
        'AI_VIDEO',
        shortVideoConfig
      );

      expect(result.creditsRemaining).toBe(15); // 20 - 5 (no extra cost)
    });
  });

  describe('Error handling and edge cases', () => {
    test('should handle database update failure', async () => {
      setupDatabaseMocks('sufficient_credits');
      
      // Mock update to fail
      mockDb.update.mockImplementation(() => ({
        set: () => ({
          where: () => Promise.resolve([]) // Empty result indicates failure
        })
      }));

      await expect(
        atomicVideoGeneration(
          'test_user_123',
          'AI_VIDEO',
          testFormData.valid
        )
      ).rejects.toThrow('Failed to update user credits');
    });

    test('should handle transaction creation failure', async () => {
      setupDatabaseMocks('sufficient_credits');
      
      // Mock successful user update but failed transaction creation
      mockDb.update.mockImplementation(() => ({
        set: () => ({
          where: () => Promise.resolve([{ credits: 15 }])
        })
      }));
      
      mockDb.insert.mockImplementation(() => ({
        values: () => ({
          returning: () => Promise.resolve([]) // Empty result indicates failure
        })
      }));

      await expect(
        atomicVideoGeneration(
          'test_user_123',
          'AI_VIDEO',
          testFormData.valid
        )
      ).rejects.toThrow('Failed to create credit transaction');
    });

    test('should handle zero credit balance', async () => {
      const zeroCreditsUser = {
        ...testUsers.sufficientCredits,
        credits: 0,
        currentCreditBalance: 0,
      };
      
      mockDb.select.mockImplementation(() => ({
        from: () => ({
          where: () => ({
            limit: () => Promise.resolve([zeroCreditsUser])
          })
        })
      }));

      await expect(
        atomicVideoGeneration(
          'test_user_123',
          'AI_VIDEO',
          testFormData.valid
        )
      ).rejects.toThrow('Insufficient credits. Required: 5, Available: 0');
    });
  });

  describe('Concurrent access scenarios', () => {
    test('should handle multiple simultaneous requests', async () => {
      setupDatabaseMocks('transaction_success');
      
      // Simulate multiple concurrent requests
      const promises = Array.from({ length: 3 }, () =>
        atomicVideoGeneration(
          'test_user_123',
          'AI_VIDEO',
          testFormData.valid
        )
      );

      const results = await Promise.all(promises);
      
      // All should succeed (in real scenario, database locking would prevent issues)
      results.forEach(result => {
        expect(result.success).toBe(true);
      });
    });
  });
});
