/**
 * AIVID_UNIT_001: Validate Project Title Input Component
 * 
 * Tests the project title input validation and user interaction
 * to ensure proper form handling and user feedback.
 */

import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { renderWithProviders } from '../utils/testHelpers';
import React from 'react';

// Mock the hook to test component behavior
const mockUseAIVideoForm = jest.fn();

// Simple test component that uses the form hook
function ProjectTitleInputTest() {
  const {
    projectTitle,
    updateField,
    getFieldError,
    handleFieldBlur,
  } = mockUseAIVideoForm();

  return (
    <div>
      <label htmlFor="project-title">Project Title</label>
      <input
        id="project-title"
        data-testid="project-title-input"
        type="text"
        value={projectTitle}
        onChange={(e) => updateField('projectTitle', e.target.value)}
        onBlur={() => handleFieldBlur('projectTitle')}
        placeholder="Enter project title"
      />
      {getFieldError('projectTitle') && (
        <div data-testid="project-title-error" role="alert">
          {getFieldError('projectTitle')}
        </div>
      )}
    </div>
  );
}

describe('AIVID_UNIT_001: Project Title Input Component', () => {
  let mockFormHook;
  const user = userEvent.setup();

  beforeEach(() => {
    mockFormHook = {
      projectTitle: '',
      updateField: jest.fn(),
      getFieldError: jest.fn().mockReturnValue(null),
      handleFieldBlur: jest.fn(),
    };
    mockUseAIVideoForm.mockReturnValue(mockFormHook);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Basic rendering and interaction', () => {
    test('should render project title input field', () => {
      renderWithProviders(<ProjectTitleInputTest />);
      
      const input = screen.getByTestId('project-title-input');
      expect(input).toBeInTheDocument();
      expect(input).toHaveAttribute('type', 'text');
      expect(input).toHaveAttribute('placeholder', 'Enter project title');
    });

    test('should display current project title value', () => {
      mockFormHook.projectTitle = 'My AI Video Project';
      
      renderWithProviders(<ProjectTitleInputTest />);
      
      const input = screen.getByTestId('project-title-input');
      expect(input).toHaveValue('My AI Video Project');
    });

    test('should call updateField when user types', async () => {
      renderWithProviders(<ProjectTitleInputTest />);

      const input = screen.getByTestId('project-title-input');
      await user.type(input, 'Test Project');

      // Check that updateField was called with the right field name
      expect(mockFormHook.updateField).toHaveBeenCalledWith('projectTitle', expect.any(String));
      // Should be called for each character typed (12 characters in 'Test Project')
      expect(mockFormHook.updateField).toHaveBeenCalledTimes(12);

      // Check that the last call was with the last character
      const calls = mockFormHook.updateField.mock.calls;
      expect(calls[calls.length - 1]).toEqual(['projectTitle', 't']);
    });

    test('should call handleFieldBlur when input loses focus', async () => {
      renderWithProviders(<ProjectTitleInputTest />);
      
      const input = screen.getByTestId('project-title-input');
      await user.click(input);
      await user.tab(); // Move focus away
      
      expect(mockFormHook.handleFieldBlur).toHaveBeenCalledWith('projectTitle');
    });
  });

  describe('Validation error display', () => {
    test('should not show error when field is valid', () => {
      mockFormHook.getFieldError.mockReturnValue(null);
      
      renderWithProviders(<ProjectTitleInputTest />);
      
      const errorElement = screen.queryByTestId('project-title-error');
      expect(errorElement).not.toBeInTheDocument();
    });

    test('should show error message when field is invalid', () => {
      const errorMessage = 'Project title is required';
      mockFormHook.getFieldError.mockReturnValue(errorMessage);
      
      renderWithProviders(<ProjectTitleInputTest />);
      
      const errorElement = screen.getByTestId('project-title-error');
      expect(errorElement).toBeInTheDocument();
      expect(errorElement).toHaveTextContent(errorMessage);
      expect(errorElement).toHaveAttribute('role', 'alert');
    });

    test('should show length validation error', () => {
      const errorMessage = 'Project title must be between 3-100 characters';
      mockFormHook.getFieldError.mockReturnValue(errorMessage);
      
      renderWithProviders(<ProjectTitleInputTest />);
      
      const errorElement = screen.getByTestId('project-title-error');
      expect(errorElement).toHaveTextContent(errorMessage);
    });
  });

  describe('User interaction scenarios', () => {
    test('should handle rapid typing correctly', async () => {
      renderWithProviders(<ProjectTitleInputTest />);

      const input = screen.getByTestId('project-title-input');

      // Simulate rapid typing
      const testText = 'Quick typing test';
      await user.type(input, testText, { delay: 1 });

      // Should call updateField for each character
      expect(mockFormHook.updateField).toHaveBeenCalledTimes(testText.length);

      // Last call should have the last character
      const lastCall = mockFormHook.updateField.mock.calls[mockFormHook.updateField.mock.calls.length - 1];
      expect(lastCall[0]).toBe('projectTitle');
      expect(lastCall[1]).toBe('t');
    });

    test('should handle copy and paste', async () => {
      renderWithProviders(<ProjectTitleInputTest />);
      
      const input = screen.getByTestId('project-title-input');
      const pastedText = 'Pasted Project Title';
      
      await user.click(input);
      await user.paste(pastedText);
      
      // Should call updateField with the pasted text
      expect(mockFormHook.updateField).toHaveBeenCalledWith('projectTitle', pastedText);
    });

    test('should handle clearing the field', async () => {
      mockFormHook.projectTitle = 'Initial Title';
      
      renderWithProviders(<ProjectTitleInputTest />);
      
      const input = screen.getByTestId('project-title-input');
      await user.clear(input);
      
      // Should call updateField with empty string
      expect(mockFormHook.updateField).toHaveBeenCalledWith('projectTitle', '');
    });

    test('should handle focus and blur events', async () => {
      renderWithProviders(<ProjectTitleInputTest />);
      
      const input = screen.getByTestId('project-title-input');
      
      // Focus the input
      await user.click(input);
      expect(input).toHaveFocus();
      
      // Blur the input
      await user.tab();
      expect(input).not.toHaveFocus();
      expect(mockFormHook.handleFieldBlur).toHaveBeenCalledWith('projectTitle');
    });
  });

  describe('Accessibility', () => {
    test('should have proper label association', () => {
      renderWithProviders(<ProjectTitleInputTest />);
      
      const input = screen.getByTestId('project-title-input');
      const label = screen.getByLabelText('Project Title');
      
      expect(label).toBe(input);
    });

    test('should have accessible error message', () => {
      const errorMessage = 'Project title is required';
      mockFormHook.getFieldError.mockReturnValue(errorMessage);
      
      renderWithProviders(<ProjectTitleInputTest />);
      
      const errorElement = screen.getByTestId('project-title-error');
      expect(errorElement).toHaveAttribute('role', 'alert');
    });

    test('should be keyboard navigable', async () => {
      renderWithProviders(<ProjectTitleInputTest />);
      
      const input = screen.getByTestId('project-title-input');
      
      // Should be focusable with keyboard
      await user.tab();
      expect(input).toHaveFocus();
      
      // Should be able to type
      await user.keyboard('Test Title');
      expect(mockFormHook.updateField).toHaveBeenCalled();
    });
  });

  describe('Edge cases', () => {
    test('should handle special characters', async () => {
      renderWithProviders(<ProjectTitleInputTest />);
      
      const input = screen.getByTestId('project-title-input');
      const specialText = 'Project @#$% & Title!';
      
      await user.type(input, specialText);
      
      // Should accept special characters
      expect(mockFormHook.updateField).toHaveBeenCalledTimes(specialText.length);
    });

    test('should handle unicode characters', async () => {
      renderWithProviders(<ProjectTitleInputTest />);
      
      const input = screen.getByTestId('project-title-input');
      const unicodeText = 'Projet Vidéo IA 🎬';
      
      await user.type(input, unicodeText);
      
      expect(mockFormHook.updateField).toHaveBeenCalledTimes(unicodeText.length);
    });

    test('should handle very long input', async () => {
      renderWithProviders(<ProjectTitleInputTest />);
      
      const input = screen.getByTestId('project-title-input');
      const longText = 'A'.repeat(150); // Longer than max length
      
      await user.type(input, longText);
      
      // Should still call updateField for each character
      expect(mockFormHook.updateField).toHaveBeenCalledTimes(150);
    });
  });
});
