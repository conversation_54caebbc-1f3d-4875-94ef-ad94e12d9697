/**
 * Test Setup Validation
 * 
 * This test file validates that our testing infrastructure is properly configured
 * and all mocks and utilities are working correctly.
 */

import { renderWithProviders, creditTestHelpers } from '../utils/testHelpers';
import { testUsers, testFormData } from '../fixtures/testData';

describe('Test Setup Validation', () => {
  describe('Test utilities', () => {
    test('should have working test helpers', () => {
      expect(renderWithProviders).toBeDefined();
      expect(creditTestHelpers).toBeDefined();
      expect(creditTestHelpers.createUserWithCredits).toBeDefined();
      expect(creditTestHelpers.calculateExpectedCost).toBeDefined();
    });

    test('should create mock users correctly', () => {
      const user = creditTestHelpers.createUserWithCredits(20);
      expect(user).toEqual({
        id: 'test_user_123',
        clerkId: 'test_user_123',
        credits: 20,
        currentCreditBalance: 20,
        email: '<EMAIL>',
      });
    });

    test('should calculate costs correctly', () => {
      const cost = creditTestHelpers.calculateExpectedCost('AI_VIDEO');
      expect(cost).toBe(5);
      
      const costWithDuration = creditTestHelpers.calculateExpectedCost('AI_VIDEO', {
        estimatedDurationSeconds: 90,
      });
      expect(costWithDuration).toBe(6); // 5 + 1 for extra 30 seconds
    });
  });

  describe('Test fixtures', () => {
    test('should have valid test users', () => {
      expect(testUsers.sufficientCredits).toBeDefined();
      expect(testUsers.sufficientCredits.credits).toBe(20);
      expect(testUsers.insufficientCredits.credits).toBe(3);
    });

    test('should have valid form data', () => {
      expect(testFormData.valid).toBeDefined();
      expect(testFormData.valid.projectTitle).toBeTruthy();
      expect(testFormData.valid.topic).toBeTruthy();
      expect(testFormData.valid.script).toBeTruthy();
    });

    test('should have invalid form data for testing', () => {
      expect(testFormData.invalid.emptyTitle.projectTitle).toBe('');
      expect(testFormData.invalid.shortTopic.topic).toBe('AI');
      expect(testFormData.invalid.invalidAudioSpeed.audioSpeed).toBe(3.0);
    });
  });

  describe('Mock setup', () => {
    test('should have Clerk authentication mocked', () => {
      const { useUser } = require('@clerk/nextjs');
      const mockUser = useUser();
      
      expect(mockUser.isLoaded).toBe(true);
      expect(mockUser.isSignedIn).toBe(true);
      expect(mockUser.user.id).toBe('test_user_123');
    });

    test('should have UserDetailContext mocked', () => {
      // Test that the context module is properly mocked
      const UserDetailContextModule = require('@/context/UserDetailContext');

      expect(UserDetailContextModule.UserDetailContext).toBeDefined();
      expect(UserDetailContextModule.UserDetailProvider).toBeDefined();
      expect(typeof UserDetailContextModule.UserDetailProvider).toBe('function');
    });

    test('should have toast notifications mocked', () => {
      const { toast } = require('sonner');
      
      expect(toast.success).toBeDefined();
      expect(toast.error).toBeDefined();
      expect(typeof toast.success).toBe('function');
      expect(typeof toast.error).toBe('function');
    });
  });

  describe('Environment setup', () => {
    test('should be in test environment', () => {
      expect(process.env.NODE_ENV).toBe('test');
    });

    test('should have test environment variables', () => {
      expect(process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY).toBe('test_clerk_key');
    });
  });

  describe('Global test utilities', () => {
    test('should have global testUtils available', () => {
      expect(global.testUtils).toBeDefined();
      expect(global.testUtils.createMockUser).toBeDefined();
      expect(global.testUtils.createMockFormData).toBeDefined();
      expect(global.testUtils.createMockVideoData).toBeDefined();
    });

    test('should create mock user with defaults', () => {
      const user = global.testUtils.createMockUser();
      expect(user.id).toBe('test_user_123');
      expect(user.credits).toBe(20);
    });

    test('should create mock user with overrides', () => {
      const user = global.testUtils.createMockUser({ credits: 50 });
      expect(user.credits).toBe(50);
      expect(user.id).toBe('test_user_123'); // Default preserved
    });

    test('should create mock form data', () => {
      const formData = global.testUtils.createMockFormData();
      expect(formData.projectTitle).toBeTruthy();
      expect(formData.topic).toBeTruthy();
      expect(formData.videoStyle).toBeTruthy();
    });

    test('should create mock video data', () => {
      const videoData = global.testUtils.createMockVideoData();
      expect(videoData.id).toBe('video_123');
      expect(videoData.clerkId).toBe('test_user_123');
      expect(videoData.status).toBe('Pending');
    });
  });

  describe('Jest configuration', () => {
    test('should have proper timeout configured', () => {
      // This test should complete within the configured timeout
      return new Promise(resolve => {
        setTimeout(resolve, 100);
      });
    });

    test('should support async/await', async () => {
      const result = await Promise.resolve('test');
      expect(result).toBe('test');
    });

    test('should support modern JavaScript features', () => {
      // Test arrow functions
      const arrow = () => 'arrow';
      expect(arrow()).toBe('arrow');

      // Test destructuring
      const { projectTitle } = testFormData.valid;
      expect(projectTitle).toBeTruthy();

      // Test template literals
      const template = `Hello ${projectTitle}`;
      expect(template).toContain(projectTitle);

      // Test spread operator
      const spread = { ...testFormData.valid, extra: 'value' };
      expect(spread.extra).toBe('value');
      expect(spread.projectTitle).toBe(testFormData.valid.projectTitle);
    });
  });

  describe('Error handling', () => {
    test('should handle thrown errors correctly', () => {
      expect(() => {
        throw new Error('Test error');
      }).toThrow('Test error');
    });

    test('should handle async errors correctly', async () => {
      await expect(async () => {
        throw new Error('Async test error');
      }).rejects.toThrow('Async test error');
    });
  });

  describe('Performance', () => {
    test('should complete setup quickly', () => {
      const start = performance.now();
      
      // Simulate test setup operations
      const user = global.testUtils.createMockUser();
      const formData = global.testUtils.createMockFormData();
      const cost = creditTestHelpers.calculateExpectedCost('AI_VIDEO');
      
      const end = performance.now();
      const duration = end - start;
      
      // Setup should be very fast
      expect(duration).toBeLessThan(10);
      expect(user).toBeDefined();
      expect(formData).toBeDefined();
      expect(cost).toBe(5);
    });
  });
});
