/**
 * AIVID_UNIT_005: Test Credit Cost Calculation Utility
 * 
 * Tests the credit cost calculation logic for AI video generation
 * to ensure accurate billing and prevent revenue loss.
 */

// Mock the database and external dependencies first
jest.mock('@/configs/db', () => ({
  db: {
    select: jest.fn(),
    from: jest.fn(),
    where: jest.fn(),
    limit: jest.fn(),
  },
}));

jest.mock('@/configs/schema', () => ({
  users: {},
  creditTransactions: {},
  videoData: {},
}));

jest.mock('@/app/inngest/client', () => ({
  inngest: {
    send: jest.fn(),
  },
}));

import { getCostForWorkflow } from '@/lib/atomicCreditSystem';
import { testWorkflowCosts, testFormData } from '../fixtures/testData';

describe('AIVID_UNIT_005: Credit Cost Calculation Utility', () => {
  describe('Basic cost calculation', () => {
    test('should return correct base cost for AI_VIDEO workflow', () => {
      const cost = getCostForWorkflow('AI_VIDEO');
      expect(cost).toBe(testWorkflowCosts.AI_VIDEO);
    });

    test('should return correct base cost for all workflow types', () => {
      Object.entries(testWorkflowCosts).forEach(([workflowType, expectedCost]) => {
        const cost = getCostForWorkflow(workflowType);
        expect(cost).toBe(expectedCost);
      });
    });

    test('should return default cost for unknown workflow type', () => {
      const cost = getCostForWorkflow('UNKNOWN_WORKFLOW');
      expect(cost).toBe(5); // Default cost
    });
  });

  describe('Duration-based cost adjustments', () => {
    test('should not add extra cost for short topics (AI_VIDEO)', () => {
      const config = { topic: 'AI' }; // Very short topic
      const cost = getCostForWorkflow('AI_VIDEO', config);
      expect(cost).toBe(testWorkflowCosts.AI_VIDEO); // Should be base cost only
    });

    test('should add extra cost for long topics (AI_VIDEO)', () => {
      // Create a very long topic that will result in estimated duration > 60 seconds
      const longTopic = 'A'.repeat(1000); // Very long topic
      const config = { topic: longTopic };
      const cost = getCostForWorkflow('AI_VIDEO', config);
      expect(cost).toBeGreaterThan(testWorkflowCosts.AI_VIDEO);
    });

    test('should handle MEME_VIDEO duration correctly', () => {
      const config = {
        videoStartTime: 0,
        videoEndTime: 90 // 90 second meme video
      };
      const cost = getCostForWorkflow('MEME_VIDEO', config);
      expect(cost).toBeGreaterThan(testWorkflowCosts.MEME_VIDEO);
    });

    test('should handle UGC_VIDEO with long script', () => {
      const longScript = 'A'.repeat(2000); // Very long script
      const config = { userScript: longScript };
      const cost = getCostForWorkflow('UGC_VIDEO', config);
      expect(cost).toBeGreaterThan(testWorkflowCosts.UGC_VIDEO);
    });

    test('should handle NARRATOR_VIDEO with standard duration', () => {
      const config = {};
      const cost = getCostForWorkflow('NARRATOR_VIDEO', config);
      // NARRATOR_VIDEO has fixed 90 second duration, already included in testWorkflowCosts
      expect(cost).toBe(testWorkflowCosts.NARRATOR_VIDEO);
    });
  });

  describe('Edge cases', () => {
    test('should handle zero duration', () => {
      const config = { estimatedDurationSeconds: 0 };
      const cost = getCostForWorkflow('AI_VIDEO', config);
      expect(cost).toBe(testWorkflowCosts.AI_VIDEO);
    });

    test('should handle negative duration', () => {
      const config = { estimatedDurationSeconds: -10 };
      const cost = getCostForWorkflow('AI_VIDEO', config);
      expect(cost).toBe(testWorkflowCosts.AI_VIDEO);
    });

    test('should handle undefined config', () => {
      const cost = getCostForWorkflow('AI_VIDEO', undefined);
      expect(cost).toBe(testWorkflowCosts.AI_VIDEO);
    });

    test('should handle empty config object', () => {
      const cost = getCostForWorkflow('AI_VIDEO', {});
      expect(cost).toBe(testWorkflowCosts.AI_VIDEO);
    });

    test('should handle config without duration', () => {
      const config = { projectTitle: 'Test Video' };
      const cost = getCostForWorkflow('AI_VIDEO', config);
      expect(cost).toBe(testWorkflowCosts.AI_VIDEO);
    });
  });

  describe('Real-world scenarios', () => {
    test('should calculate cost for typical AI video configuration', () => {
      const config = {
        ...testFormData.valid,
        topic: testFormData.valid.topic, // Use the actual topic from test data
      };
      const cost = getCostForWorkflow('AI_VIDEO', config);
      expect(cost).toBeGreaterThanOrEqual(testWorkflowCosts.AI_VIDEO);
    });

    test('should calculate cost for short meme video', () => {
      const config = {
        videoStartTime: 0,
        videoEndTime: 30 // 30 second video
      };
      const cost = getCostForWorkflow('MEME_VIDEO', config);
      expect(cost).toBe(testWorkflowCosts.MEME_VIDEO);
    });

    test('should calculate cost for UGC video with script', () => {
      const config = {
        userScript: testFormData.valid.script // Use script from test data
      };
      const cost = getCostForWorkflow('UGC_VIDEO', config);
      expect(cost).toBeGreaterThanOrEqual(testWorkflowCosts.UGC_VIDEO);
    });
  });

  describe('Cost calculation consistency', () => {
    test('should return same cost for identical inputs', () => {
      const config = { topic: 'Consistent test topic' };
      const cost1 = getCostForWorkflow('AI_VIDEO', config);
      const cost2 = getCostForWorkflow('AI_VIDEO', config);
      expect(cost1).toBe(cost2);
    });

    test('should be deterministic across multiple calls', () => {
      const config = { topic: 'Deterministic test topic' };
      const costs = Array.from({ length: 10 }, () =>
        getCostForWorkflow('AI_VIDEO', config)
      );

      const uniqueCosts = [...new Set(costs)];
      expect(uniqueCosts).toHaveLength(1);
      expect(uniqueCosts[0]).toBeGreaterThanOrEqual(testWorkflowCosts.AI_VIDEO);
    });
  });

  describe('Performance', () => {
    test('should calculate cost quickly for large numbers of calls', () => {
      const config = { topic: 'Performance test topic' };
      const startTime = performance.now();

      for (let i = 0; i < 1000; i++) {
        getCostForWorkflow('AI_VIDEO', config);
      }

      const endTime = performance.now();
      const duration = endTime - startTime;

      // Should complete 1000 calculations in under 100ms
      expect(duration).toBeLessThan(100);
    });
  });
});
