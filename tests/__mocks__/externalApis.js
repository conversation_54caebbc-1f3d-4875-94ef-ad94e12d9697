/**
 * External API mocks for testing
 * Provides consistent mock implementations for external services
 */

// Mock Google AI responses
export const mockGoogleAIResponses = {
  scriptGeneration: {
    success: "In 2024, artificial intelligence is revolutionizing healthcare through predictive diagnostics, personalized treatment plans, and automated medical imaging analysis. AI-powered systems can now detect diseases earlier than traditional methods, leading to better patient outcomes and reduced healthcare costs.",
    failure: null,
  },
  imagePrompts: {
    success: [
      "A futuristic hospital with AI-powered diagnostic equipment",
      "Doctor using AI tablet for patient diagnosis",
      "Medical AI analyzing brain scans on multiple monitors",
      "Robotic surgery assistant in modern operating room"
    ],
    failure: null,
  },
};

// Mock ElevenLabs responses
export const mockElevenLabsResponses = {
  audioGeneration: {
    success: {
      audioUrl: 'https://mock-audio-url.com/generated-audio.mp3',
      duration: 45,
    },
    failure: null,
  },
};

// Mock Runware/Pexels/Pixabay responses
export const mockImageResponses = {
  runware: {
    success: [
      { url: 'https://mock-runware.com/image1.jpg', id: 'rw_001' },
      { url: 'https://mock-runware.com/image2.jpg', id: 'rw_002' },
    ],
    failure: null,
  },
  pexels: {
    success: [
      { url: 'https://mock-pexels.com/image1.jpg', id: 'px_001' },
      { url: 'https://mock-pexels.com/image2.jpg', id: 'px_002' },
    ],
    failure: null,
  },
  pixabay: {
    success: [
      { url: 'https://mock-pixabay.com/image1.jpg', id: 'pb_001' },
      { url: 'https://mock-pixabay.com/image2.jpg', id: 'pb_002' },
    ],
    failure: null,
  },
};

// Mock Inngest client
export const mockInngest = {
  send: jest.fn().mockResolvedValue({ id: 'inngest_job_123' }),
};

// Mock circuit breaker responses
export const mockCircuitBreakerResponses = {
  closed: { state: 'CLOSED', failures: 0 },
  open: { state: 'OPEN', failures: 5 },
  halfOpen: { state: 'HALF_OPEN', failures: 3 },
};

// Helper to setup API mocks for specific scenarios
export function setupApiMocks(scenario = 'all_success') {
  switch (scenario) {
    case 'all_success':
      // All APIs return successful responses
      mockGoogleAIResponses.scriptGeneration.failure = null;
      mockGoogleAIResponses.imagePrompts.failure = null;
      mockElevenLabsResponses.audioGeneration.failure = null;
      mockImageResponses.runware.failure = null;
      break;
      
    case 'google_ai_failure':
      // Google AI fails, fallbacks should be used
      mockGoogleAIResponses.scriptGeneration.failure = new Error('Google AI API unavailable');
      mockGoogleAIResponses.imagePrompts.failure = new Error('Google AI API unavailable');
      break;
      
    case 'image_api_cascade_failure':
      // Runware fails, should fallback to Pexels, then Pixabay
      mockImageResponses.runware.failure = new Error('Runware API unavailable');
      mockImageResponses.pexels.failure = null;
      mockImageResponses.pixabay.failure = null;
      break;
      
    case 'all_image_apis_failure':
      // All image APIs fail
      mockImageResponses.runware.failure = new Error('Runware API unavailable');
      mockImageResponses.pexels.failure = new Error('Pexels API unavailable');
      mockImageResponses.pixabay.failure = new Error('Pixabay API unavailable');
      break;
      
    case 'circuit_breaker_open':
      // Circuit breakers are open due to failures
      mockCircuitBreakerResponses.closed.state = 'OPEN';
      break;
      
    default:
      // Reset to success state
      Object.keys(mockGoogleAIResponses).forEach(key => {
        mockGoogleAIResponses[key].failure = null;
      });
      Object.keys(mockElevenLabsResponses).forEach(key => {
        mockElevenLabsResponses[key].failure = null;
      });
      Object.keys(mockImageResponses).forEach(api => {
        mockImageResponses[api].failure = null;
      });
  }
}

// Mock implementations
jest.mock('@/src/lib/aiUtils', () => ({
  generateScriptFromAI: jest.fn().mockImplementation(() => {
    if (mockGoogleAIResponses.scriptGeneration.failure) {
      throw mockGoogleAIResponses.scriptGeneration.failure;
    }
    return Promise.resolve(mockGoogleAIResponses.scriptGeneration.success);
  }),
}));

jest.mock('@/lib/inngestApiHelpers', () => ({
  resilientGoogleAI: jest.fn().mockImplementation(() => {
    if (mockGoogleAIResponses.scriptGeneration.failure) {
      throw mockGoogleAIResponses.scriptGeneration.failure;
    }
    return Promise.resolve(mockGoogleAIResponses.scriptGeneration.success);
  }),
  resilientElevenLabs: jest.fn().mockImplementation(() => {
    if (mockElevenLabsResponses.audioGeneration.failure) {
      throw mockElevenLabsResponses.audioGeneration.failure;
    }
    return Promise.resolve(mockElevenLabsResponses.audioGeneration.success);
  }),
  resilientPexels: jest.fn().mockImplementation(() => {
    if (mockImageResponses.pexels.failure) {
      throw mockImageResponses.pexels.failure;
    }
    return Promise.resolve(mockImageResponses.pexels.success);
  }),
  resilientPixabay: jest.fn().mockImplementation(() => {
    if (mockImageResponses.pixabay.failure) {
      throw mockImageResponses.pixabay.failure;
    }
    return Promise.resolve(mockImageResponses.pixabay.success);
  }),
  createEmergencyFallbacks: jest.fn().mockReturnValue({
    scriptFallback: jest.fn().mockResolvedValue('Emergency fallback script about the requested topic.'),
    imageFallback: jest.fn().mockResolvedValue(['https://fallback-image.com/default.jpg']),
  }),
  checkAPIHealth: jest.fn().mockResolvedValue({ healthy: true }),
}));

jest.mock('@/lib/circuitBreaker', () => ({
  withCircuitBreaker: jest.fn().mockImplementation((apiName, callback) => {
    // Simulate circuit breaker behavior
    if (mockCircuitBreakerResponses.closed.state === 'OPEN') {
      throw new Error(`Circuit breaker is open for ${apiName}`);
    }
    return callback();
  }),
  getCircuitBreakerStatus: jest.fn().mockReturnValue(mockCircuitBreakerResponses.closed),
}));

jest.mock('@/app/inngest/client', () => ({
  inngest: mockInngest,
}));
