/**
 * Database mocks for testing
 * Provides consistent mock implementations for database operations
 */

// Mock database responses
export const mockUsers = {
  test_user_123: {
    id: 1,
    clerkId: 'test_user_123',
    credits: 20,
    currentCreditBalance: 20,
    email: '<EMAIL>',
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
  },
  test_user_insufficient: {
    id: 2,
    clerkId: 'test_user_insufficient',
    credits: 3,
    currentCreditBalance: 3,
    email: '<EMAIL>',
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
  },
};

export const mockCreditTransactions = [];
export const mockVideoData = [];

// Mock database operations
export const mockDb = {
  select: jest.fn().mockReturnThis(),
  from: jest.fn().mockReturnThis(),
  where: jest.fn().mockReturnThis(),
  limit: jest.fn().mockReturnThis(),
  insert: jest.fn().mockReturnThis(),
  values: jest.fn().mockReturnThis(),
  returning: jest.fn().mockReturnThis(),
  update: jest.fn().mockReturnThis(),
  set: jest.fn().mockReturnThis(),
  for: jest.fn().mockReturnThis(),
  transaction: jest.fn(),
};

// Helper to setup database mocks for specific scenarios
export function setupDatabaseMocks(scenario = 'default') {
  switch (scenario) {
    case 'sufficient_credits':
      mockDb.select.mockImplementation(() => ({
        from: () => ({
          where: () => ({
            limit: () => Promise.resolve([mockUsers.test_user_123])
          })
        })
      }));
      break;
      
    case 'insufficient_credits':
      mockDb.select.mockImplementation(() => ({
        from: () => ({
          where: () => ({
            limit: () => Promise.resolve([mockUsers.test_user_insufficient])
          })
        })
      }));
      break;
      
    case 'user_not_found':
      mockDb.select.mockImplementation(() => ({
        from: () => ({
          where: () => ({
            limit: () => Promise.resolve([])
          })
        })
      }));
      break;
      
    case 'transaction_success':
      mockDb.transaction.mockImplementation(async (callback) => {
        const mockTx = {
          select: () => ({
            from: () => ({
              where: () => ({
                for: () => Promise.resolve([mockUsers.test_user_123])
              })
            })
          }),
          update: () => ({
            set: () => ({
              where: () => Promise.resolve([{ ...mockUsers.test_user_123, credits: 15 }])
            })
          }),
          insert: () => ({
            values: () => ({
              returning: () => Promise.resolve([{
                id: 'transaction_123',
                userId: 'test_user_123',
                transactionType: 'DEBIT',
                amount: -5,
                balanceBefore: 20,
                balanceAfter: 15,
              }])
            })
          }),
        };
        return await callback(mockTx);
      });
      break;
      
    default:
      // Reset all mocks to default behavior
      Object.keys(mockDb).forEach(key => {
        if (typeof mockDb[key].mockReset === 'function') {
          mockDb[key].mockReset();
        }
      });
  }
}

// Mock Drizzle ORM
jest.mock('@/configs/db', () => ({
  db: mockDb,
}));

jest.mock('@/configs/schema', () => ({
  users: {
    clerkId: 'clerkId',
    credits: 'credits',
    currentCreditBalance: 'currentCreditBalance',
  },
  creditTransactions: {
    userId: 'userId',
    transactionType: 'transactionType',
    amount: 'amount',
  },
  videoData: {
    clerkId: 'clerkId',
    title: 'title',
    status: 'status',
  },
}));

jest.mock('drizzle-orm', () => ({
  eq: jest.fn((field, value) => ({ field, value, type: 'eq' })),
  sql: jest.fn((strings, ...values) => ({ strings, values, type: 'sql' })),
}));
