/**
 * Enhanced Circuit Breaker Mock for Integration Testing
 * 
 * Simulates circuit breaker behavior for API failure testing
 * and fallback mechanism validation.
 */

export class MockCircuitBreaker {
  constructor(options = {}) {
    this.state = 'CLOSED'; // CLOSED, OPEN, HALF_OPEN
    this.failureCount = 0;
    this.successCount = 0;
    this.lastFailureTime = null;
    this.lastSuccessTime = null;
    
    // Configuration
    this.failureThreshold = options.failureThreshold || 3;
    this.timeout = options.timeout || 60000; // 1 minute
    this.resetTimeout = options.resetTimeout || 30000; // 30 seconds
    
    // Tracking
    this.callHistory = [];
    this.stateHistory = [];
  }

  /**
   * Execute a function with circuit breaker protection
   */
  async execute(fn, fallbackFn = null) {
    const callStart = Date.now();
    
    // Record the call attempt
    this.callHistory.push({
      timestamp: new Date(),
      state: this.state,
      attempt: this.callHistory.length + 1
    });

    try {
      // Check if circuit breaker should open
      if (this.shouldOpen()) {
        this.open();
      }

      // Check if circuit breaker should transition to half-open
      if (this.shouldAttemptReset()) {
        this.halfOpen();
      }

      // If circuit is open, either use fallback or throw error
      if (this.state === 'OPEN') {
        if (fallbackFn) {
          console.log('[CircuitBreaker] Circuit OPEN, using fallback');
          const result = await fallbackFn();
          this.recordCall('fallback', callStart, true);
          return result;
        } else {
          const error = new Error(`Circuit breaker is open for ${this.name || 'service'}`);
          this.recordCall('blocked', callStart, false);
          throw error;
        }
      }

      // Execute the main function
      const result = await fn();
      
      // Record success
      this.onSuccess();
      this.recordCall('success', callStart, true);
      
      return result;

    } catch (error) {
      // Record failure
      this.onFailure();
      this.recordCall('failure', callStart, false, error.message);
      
      // If we're in half-open state and failed, go back to open
      if (this.state === 'HALF_OPEN') {
        this.open();
      }

      // Try fallback if available and circuit is now open
      if (this.state === 'OPEN' && fallbackFn) {
        console.log('[CircuitBreaker] Primary failed, circuit OPEN, using fallback');
        try {
          const result = await fallbackFn();
          this.recordCall('fallback_after_failure', callStart, true);
          return result;
        } catch (fallbackError) {
          this.recordCall('fallback_failed', callStart, false, fallbackError.message);
          throw fallbackError;
        }
      }

      throw error;
    }
  }

  /**
   * Record success
   */
  onSuccess() {
    this.successCount++;
    this.lastSuccessTime = Date.now();
    
    // If we're in half-open state and succeeded, close the circuit
    if (this.state === 'HALF_OPEN') {
      this.close();
    }
  }

  /**
   * Record failure
   */
  onFailure() {
    this.failureCount++;
    this.lastFailureTime = Date.now();
  }

  /**
   * Check if circuit breaker should open
   */
  shouldOpen() {
    return this.state === 'CLOSED' && this.failureCount >= this.failureThreshold;
  }

  /**
   * Check if circuit breaker should attempt reset
   */
  shouldAttemptReset() {
    return this.state === 'OPEN' && 
           this.lastFailureTime && 
           (Date.now() - this.lastFailureTime) >= this.resetTimeout;
  }

  /**
   * Open the circuit breaker
   */
  open() {
    if (this.state !== 'OPEN') {
      this.state = 'OPEN';
      this.recordStateChange('OPEN');
      console.log('[CircuitBreaker] Circuit breaker OPENED');
    }
  }

  /**
   * Close the circuit breaker
   */
  close() {
    if (this.state !== 'CLOSED') {
      this.state = 'CLOSED';
      this.failureCount = 0; // Reset failure count
      this.recordStateChange('CLOSED');
      console.log('[CircuitBreaker] Circuit breaker CLOSED');
    }
  }

  /**
   * Set circuit breaker to half-open state
   */
  halfOpen() {
    if (this.state !== 'HALF_OPEN') {
      this.state = 'HALF_OPEN';
      this.recordStateChange('HALF_OPEN');
      console.log('[CircuitBreaker] Circuit breaker HALF_OPEN');
    }
  }

  /**
   * Get current state
   */
  getState() {
    return this.state;
  }

  /**
   * Get failure count
   */
  getFailureCount() {
    return this.failureCount;
  }

  /**
   * Get success count
   */
  getSuccessCount() {
    return this.successCount;
  }

  /**
   * Record state change
   */
  recordStateChange(newState) {
    this.stateHistory.push({
      state: newState,
      timestamp: new Date(),
      failureCount: this.failureCount,
      successCount: this.successCount
    });
  }

  /**
   * Record function call details
   */
  recordCall(type, startTime, success, error = null) {
    const callRecord = this.callHistory[this.callHistory.length - 1];
    if (callRecord) {
      callRecord.type = type;
      callRecord.duration = Date.now() - startTime;
      callRecord.success = success;
      callRecord.error = error;
      callRecord.endState = this.state;
    }
  }

  /**
   * Get call history
   */
  getCallHistory() {
    return [...this.callHistory];
  }

  /**
   * Get state history
   */
  getStateHistory() {
    return [...this.stateHistory];
  }

  /**
   * Reset circuit breaker to initial state
   */
  reset() {
    this.state = 'CLOSED';
    this.failureCount = 0;
    this.successCount = 0;
    this.lastFailureTime = null;
    this.lastSuccessTime = null;
    this.callHistory = [];
    this.stateHistory = [];
  }

  /**
   * Force circuit breaker to specific state (for testing)
   */
  forceState(state) {
    this.state = state;
    this.recordStateChange(state);
  }

  /**
   * Simulate multiple failures to trigger circuit breaker
   */
  simulateFailures(count) {
    for (let i = 0; i < count; i++) {
      this.onFailure();
    }
    
    if (this.shouldOpen()) {
      this.open();
    }
  }

  /**
   * Get statistics
   */
  getStats() {
    const totalCalls = this.callHistory.length;
    const successfulCalls = this.callHistory.filter(call => call.success).length;
    const failedCalls = this.callHistory.filter(call => !call.success).length;
    const fallbackCalls = this.callHistory.filter(call => 
      call.type && call.type.includes('fallback')
    ).length;

    return {
      state: this.state,
      totalCalls,
      successfulCalls,
      failedCalls,
      fallbackCalls,
      failureCount: this.failureCount,
      successCount: this.successCount,
      successRate: totalCalls > 0 ? (successfulCalls / totalCalls) * 100 : 0,
      stateChanges: this.stateHistory.length,
      lastFailureTime: this.lastFailureTime,
      lastSuccessTime: this.lastSuccessTime
    };
  }
}

/**
 * Circuit Breaker Manager for multiple services
 */
export class MockCircuitBreakerManager {
  constructor() {
    this.breakers = new Map();
  }

  /**
   * Get or create a circuit breaker for a service
   */
  getBreaker(serviceName, options = {}) {
    if (!this.breakers.has(serviceName)) {
      const breaker = new MockCircuitBreaker(options);
      breaker.name = serviceName;
      this.breakers.set(serviceName, breaker);
    }
    
    return this.breakers.get(serviceName);
  }

  /**
   * Execute function with circuit breaker protection
   */
  async execute(serviceName, fn, fallbackFn = null, options = {}) {
    const breaker = this.getBreaker(serviceName, options);
    return await breaker.execute(fn, fallbackFn);
  }

  /**
   * Get all circuit breaker states
   */
  getAllStates() {
    const states = {};
    for (const [serviceName, breaker] of this.breakers) {
      states[serviceName] = breaker.getState();
    }
    return states;
  }

  /**
   * Reset all circuit breakers
   */
  resetAll() {
    for (const breaker of this.breakers.values()) {
      breaker.reset();
    }
  }

  /**
   * Get statistics for all circuit breakers
   */
  getAllStats() {
    const stats = {};
    for (const [serviceName, breaker] of this.breakers) {
      stats[serviceName] = breaker.getStats();
    }
    return stats;
  }
}

// Export singleton instances
export const mockCircuitBreaker = new MockCircuitBreaker();
export const mockCircuitBreakerManager = new MockCircuitBreakerManager();

// Export factory functions
export function createMockCircuitBreaker(options = {}) {
  return new MockCircuitBreaker(options);
}

export function createMockCircuitBreakerManager() {
  return new MockCircuitBreakerManager();
}
