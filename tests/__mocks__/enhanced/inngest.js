/**
 * Enhanced Inngest Mock for Integration Testing
 * 
 * Provides workflow simulation capabilities and event tracking
 * for comprehensive integration testing of async workflows.
 */

export class MockInngestClient {
  constructor() {
    this.events = [];
    this.workflows = new Map();
    this.isEnabled = true;
    this.simulateFailures = false;
    this.failureRate = 0;
  }

  /**
   * Send an event to Inngest (mocked)
   */
  async send(event) {
    if (!this.isEnabled) {
      throw new Error('Inngest client is disabled');
    }

    // Simulate random failures if configured
    if (this.simulateFailures && Math.random() < this.failureRate) {
      throw new Error('Simulated Inngest failure');
    }

    const eventWithMetadata = {
      ...event,
      id: `event_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date().toISOString(),
      status: 'sent'
    };

    this.events.push(eventWithMetadata);

    // Simulate workflow triggering based on event name
    if (event.name === 'video/generation.requested') {
      await this.triggerWorkflow('videoGenerationWorkflow', event.data, eventWithMetadata.id);
    } else if (event.name === 'user/created') {
      await this.triggerWorkflow('userInitializationWorkflow', event.data, eventWithMetadata.id);
    } else if (event.name === 'credit/refund.requested') {
      await this.triggerWorkflow('creditRefundWorkflow', event.data, eventWithMetadata.id);
    }

    return { 
      id: eventWithMetadata.id,
      status: 'sent',
      timestamp: eventWithMetadata.timestamp
    };
  }

  /**
   * Trigger a workflow simulation
   */
  async triggerWorkflow(workflowName, data, eventId) {
    const workflow = {
      id: `workflow_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      name: workflowName,
      eventId,
      status: 'running',
      data,
      steps: [],
      startTime: new Date(),
      endTime: null,
      error: null
    };

    this.workflows.set(workflow.id, workflow);

    try {
      // Simulate workflow steps based on workflow type
      await this.simulateWorkflowSteps(workflow);
      workflow.status = 'completed';
      workflow.endTime = new Date();
    } catch (error) {
      workflow.status = 'failed';
      workflow.error = error.message;
      workflow.endTime = new Date();
    }

    return workflow;
  }

  /**
   * Simulate workflow steps execution
   */
  async simulateWorkflowSteps(workflow) {
    const stepDefinitions = this.getWorkflowSteps(workflow.name);

    for (const stepDef of stepDefinitions) {
      const step = {
        name: stepDef.name,
        status: 'running',
        startTime: new Date(),
        endTime: null,
        output: null,
        error: null
      };

      workflow.steps.push(step);

      try {
        // Simulate step processing time
        await new Promise(resolve => setTimeout(resolve, stepDef.duration || 10));

        // Simulate step execution
        step.output = await this.executeWorkflowStep(stepDef, workflow.data);
        step.status = 'completed';
        step.endTime = new Date();

        // Update workflow data with step output
        if (step.output && typeof step.output === 'object') {
          workflow.data = { ...workflow.data, ...step.output };
        }

      } catch (error) {
        step.status = 'failed';
        step.error = error.message;
        step.endTime = new Date();
        throw error;
      }
    }
  }

  /**
   * Get workflow step definitions
   */
  getWorkflowSteps(workflowName) {
    const stepDefinitions = {
      videoGenerationWorkflow: [
        { name: 'validateInput', duration: 5 },
        { name: 'enrichUserData', duration: 10 },
        { name: 'generateScript', duration: 50 },
        { name: 'generateAudio', duration: 100 },
        { name: 'generateImages', duration: 150 },
        { name: 'compileVideo', duration: 200 },
        { name: 'updateVideoStatus', duration: 5 }
      ],
      userInitializationWorkflow: [
        { name: 'validateClerkData', duration: 5 },
        { name: 'createUserRecord', duration: 10 },
        { name: 'allocateWelcomeCredits', duration: 5 },
        { name: 'sendWelcomeEmail', duration: 20 }
      ],
      creditRefundWorkflow: [
        { name: 'validateRefundRequest', duration: 5 },
        { name: 'processRefund', duration: 15 },
        { name: 'updateVideoStatus', duration: 5 },
        { name: 'notifyUser', duration: 10 }
      ]
    };

    return stepDefinitions[workflowName] || [];
  }

  /**
   * Execute a single workflow step
   */
  async executeWorkflowStep(stepDef, workflowData) {
    // Simulate step-specific logic
    switch (stepDef.name) {
      case 'validateInput':
        if (!workflowData.videoId) {
          throw new Error('Missing videoId in workflow data');
        }
        return { validated: true };

      case 'enrichUserData':
        return { 
          userPlan: 'free',
          userPreferences: { quality: 'standard' }
        };

      case 'generateScript':
        return { 
          script: 'Generated script content...',
          wordCount: 150,
          estimatedDuration: 60
        };

      case 'generateAudio':
        return {
          audioUrl: 'https://mock-audio-url.com/audio.mp3',
          duration: 62
        };

      case 'generateImages':
        return {
          images: [
            'https://mock-image-url.com/image1.jpg',
            'https://mock-image-url.com/image2.jpg'
          ]
        };

      case 'compileVideo':
        return {
          videoUrl: 'https://mock-video-url.com/video.mp4',
          thumbnailUrl: 'https://mock-video-url.com/thumbnail.jpg',
          duration: 62
        };

      case 'updateVideoStatus':
        return { statusUpdated: true };

      case 'createUserRecord':
        return { userCreated: true };

      case 'allocateWelcomeCredits':
        return { creditsAllocated: 10 };

      case 'processRefund':
        return { refundProcessed: true, amount: workflowData.refundAmount };

      default:
        return { stepCompleted: true };
    }
  }

  /**
   * Get all events sent to Inngest
   */
  getEvents() {
    return [...this.events];
  }

  /**
   * Get all workflows triggered
   */
  getWorkflows() {
    return Array.from(this.workflows.values());
  }

  /**
   * Get a specific workflow by ID
   */
  getWorkflow(workflowId) {
    return this.workflows.get(workflowId);
  }

  /**
   * Get events by name
   */
  getEventsByName(eventName) {
    return this.events.filter(event => event.name === eventName);
  }

  /**
   * Get workflows by name
   */
  getWorkflowsByName(workflowName) {
    return Array.from(this.workflows.values())
      .filter(workflow => workflow.name === workflowName);
  }

  /**
   * Configure failure simulation
   */
  setFailureSimulation(enabled, failureRate = 0.1) {
    this.simulateFailures = enabled;
    this.failureRate = failureRate;
  }

  /**
   * Enable/disable the Inngest client
   */
  setEnabled(enabled) {
    this.isEnabled = enabled;
  }

  /**
   * Clear all events and workflows
   */
  clear() {
    this.events = [];
    this.workflows.clear();
  }

  /**
   * Get summary statistics
   */
  getStats() {
    const workflows = Array.from(this.workflows.values());
    
    return {
      totalEvents: this.events.length,
      totalWorkflows: workflows.length,
      completedWorkflows: workflows.filter(w => w.status === 'completed').length,
      failedWorkflows: workflows.filter(w => w.status === 'failed').length,
      runningWorkflows: workflows.filter(w => w.status === 'running').length,
      averageWorkflowDuration: this.calculateAverageWorkflowDuration(workflows)
    };
  }

  /**
   * Calculate average workflow duration
   */
  calculateAverageWorkflowDuration(workflows) {
    const completedWorkflows = workflows.filter(w => w.endTime && w.startTime);
    
    if (completedWorkflows.length === 0) {
      return 0;
    }

    const totalDuration = completedWorkflows.reduce((sum, workflow) => {
      return sum + (workflow.endTime.getTime() - workflow.startTime.getTime());
    }, 0);

    return Math.round(totalDuration / completedWorkflows.length);
  }
}

// Export singleton instance
export const mockInngestClient = new MockInngestClient();

// Export factory function for creating new instances
export function createMockInngestClient() {
  return new MockInngestClient();
}
