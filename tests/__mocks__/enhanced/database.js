/**
 * Enhanced Database Mocks for Integration Testing
 * 
 * Extends existing database mocks with constraint violation simulation,
 * transaction behavior, and realistic error scenarios.
 */

import { existingDatabaseMocks } from '../database.js';

export class MockDatabaseTransaction {
  constructor(shouldFail = false, failureType = 'generic') {
    this.shouldFail = shouldFail;
    this.failureType = failureType;
    this.operations = [];
    this.isRolledBack = false;
    this.isCommitted = false;
  }

  select() {
    this.operations.push({ type: 'select', table: null });
    return this;
  }

  from(table) {
    if (this.operations.length > 0) {
      this.operations[this.operations.length - 1].table = table;
    }
    return this;
  }

  where(condition) {
    this.operations.push({ type: 'where', condition });
    return this;
  }

  insert(table) {
    this.operations.push({ type: 'insert', table });
    return this;
  }

  update(table) {
    this.operations.push({ type: 'update', table });
    return this;
  }

  delete(table) {
    this.operations.push({ type: 'delete', table });
    return this;
  }

  values(data) {
    this.operations.push({ type: 'values', data });
    return this;
  }

  set(data) {
    this.operations.push({ type: 'set', data });
    return this;
  }

  returning() {
    if (this.shouldFail) {
      this.simulateFailure();
    }

    // Return mock data based on operation type
    const lastOp = this.operations[this.operations.length - 1];
    if (lastOp?.type === 'insert' && lastOp?.data) {
      return [{ id: `mock_id_${Date.now()}`, ...lastOp.data }];
    }

    return [{ id: 'mock_id', success: true }];
  }

  limit(count) {
    this.operations.push({ type: 'limit', count });
    return this;
  }

  simulateFailure() {
    switch (this.failureType) {
      case 'constraint_violation':
        throw new Error('Constraint violation: unique_constraint_violation');
      case 'foreign_key_violation':
        throw new Error('Foreign key constraint violation');
      case 'check_constraint':
        throw new Error('Check constraint violation: credits cannot be negative');
      case 'lock_timeout':
        throw new Error('Lock timeout: could not obtain lock on table');
      case 'connection_error':
        throw new Error('Connection error: database unavailable');
      default:
        throw new Error('Database operation failed');
    }
  }

  rollback() {
    this.isRolledBack = true;
  }

  commit() {
    this.isCommitted = true;
  }
}

export const enhancedDatabaseMocks = {
  ...existingDatabaseMocks,

  /**
   * Simulate database transaction with automatic rollback
   */
  simulateTransaction: (callback, shouldFail = false, failureType = 'generic') => {
    const mockTx = new MockDatabaseTransaction(shouldFail, failureType);
    
    try {
      const result = callback(mockTx);
      
      // If callback returns a promise, handle it appropriately
      if (result && typeof result.then === 'function') {
        return result.catch(error => {
          mockTx.rollback();
          throw error;
        });
      }
      
      return result;
    } catch (error) {
      mockTx.rollback();
      throw error;
    }
  },

  /**
   * Simulate constraint violations
   */
  simulateConstraintViolation: (constraintName, violationType = 'unique') => {
    const errorMessages = {
      unique: `Unique constraint violation: ${constraintName}`,
      foreign_key: `Foreign key constraint violation: ${constraintName}`,
      check: `Check constraint violation: ${constraintName}`,
      not_null: `Not null constraint violation: ${constraintName}`
    };

    throw new Error(errorMessages[violationType] || `Constraint violation: ${constraintName}`);
  },

  /**
   * Simulate database lock timeout
   */
  simulateLockTimeout: (tableName = 'unknown') => {
    throw new Error(`Lock timeout: could not obtain lock on table "${tableName}"`);
  },

  /**
   * Simulate connection errors
   */
  simulateConnectionError: (errorType = 'unavailable') => {
    const errorMessages = {
      unavailable: 'Database connection unavailable',
      timeout: 'Database connection timeout',
      refused: 'Database connection refused',
      pool_exhausted: 'Database connection pool exhausted'
    };

    throw new Error(errorMessages[errorType] || 'Database connection error');
  },

  /**
   * Create mock user data
   */
  createMockUser: (overrides = {}) => {
    return {
      id: `user_${Date.now()}`,
      clerkId: `clerk_${Date.now()}`,
      email: `test${Date.now()}@example.com`,
      credits: 20,
      currentCreditBalance: 20,
      createdAt: new Date(),
      updatedAt: new Date(),
      ...overrides
    };
  },

  /**
   * Create mock credit transaction data
   */
  createMockCreditTransaction: (overrides = {}) => {
    return {
      id: `transaction_${Date.now()}`,
      userId: 'test_user_123',
      type: 'DEBIT',
      amount: -5,
      balanceBefore: 20,
      balanceAfter: 15,
      description: 'Test transaction',
      createdAt: new Date(),
      ...overrides
    };
  },

  /**
   * Create mock video data
   */
  createMockVideo: (overrides = {}) => {
    return {
      id: `video_${Date.now()}`,
      clerkId: 'test_user_123',
      title: 'Test Video',
      workflowType: 'AI_VIDEO',
      status: 'Pending',
      costInCredits: 5,
      createdAt: new Date(),
      updatedAt: new Date(),
      ...overrides
    };
  },

  /**
   * Mock database query results
   */
  mockQueryResults: {
    users: {
      findByClerkId: (clerkId, credits = 20) => [{
        id: 'user_123',
        clerkId,
        email: `${clerkId}@example.com`,
        credits,
        currentCreditBalance: credits,
        createdAt: new Date('2024-01-01T00:00:00Z'),
        updatedAt: new Date('2024-01-01T00:00:00Z')
      }],
      
      insufficientCredits: (clerkId, credits = 2) => [{
        id: 'user_123',
        clerkId,
        email: `${clerkId}@example.com`,
        credits,
        currentCreditBalance: credits,
        createdAt: new Date('2024-01-01T00:00:00Z'),
        updatedAt: new Date('2024-01-01T00:00:00Z')
      }],
      
      notFound: () => []
    },

    creditTransactions: {
      successful: (userId, amount, balanceBefore, balanceAfter) => [{
        id: 'transaction_123',
        userId,
        type: amount < 0 ? 'DEBIT' : 'CREDIT',
        amount,
        balanceBefore,
        balanceAfter,
        description: amount < 0 ? 'Video Generation' : 'Credit Purchase',
        createdAt: new Date(),
        updatedAt: new Date()
      }]
    },

    videoData: {
      pending: (clerkId, workflowType = 'AI_VIDEO') => [{
        id: 'video_123',
        clerkId,
        title: 'Test Video',
        workflowType,
        status: 'Pending',
        costInCredits: 5,
        createdAt: new Date(),
        updatedAt: new Date()
      }],
      
      processing: (clerkId, workflowType = 'AI_VIDEO') => [{
        id: 'video_123',
        clerkId,
        title: 'Test Video',
        workflowType,
        status: 'Processing',
        costInCredits: 5,
        createdAt: new Date(),
        updatedAt: new Date()
      }]
    }
  },

  /**
   * Setup mock database responses for specific scenarios
   */
  setupScenario: (scenarioName) => {
    const scenarios = {
      sufficient_credits: () => {
        // Mock user with sufficient credits
        return {
          user: enhancedDatabaseMocks.mockQueryResults.users.findByClerkId('test_user_123', 20),
          expectedOperations: ['select', 'update', 'insert', 'insert'] // user lookup, credit update, transaction, video
        };
      },

      insufficient_credits: () => {
        // Mock user with insufficient credits
        return {
          user: enhancedDatabaseMocks.mockQueryResults.users.insufficientCredits('test_user_123', 2),
          expectedOperations: ['select'] // only user lookup, then early return
        };
      },

      user_not_found: () => {
        // Mock user not found scenario
        return {
          user: enhancedDatabaseMocks.mockQueryResults.users.notFound(),
          expectedOperations: ['select'] // only user lookup, then error
        };
      },

      transaction_success: () => {
        // Mock successful transaction scenario
        return {
          user: enhancedDatabaseMocks.mockQueryResults.users.findByClerkId('test_user_123', 20),
          transaction: enhancedDatabaseMocks.mockQueryResults.creditTransactions.successful('test_user_123', -5, 20, 15),
          video: enhancedDatabaseMocks.mockQueryResults.videoData.pending('test_user_123'),
          expectedOperations: ['select', 'update', 'insert', 'insert']
        };
      }
    };

    return scenarios[scenarioName] ? scenarios[scenarioName]() : null;
  }
};

// Export individual mock functions for convenience
export const {
  simulateTransaction,
  simulateConstraintViolation,
  simulateLockTimeout,
  simulateConnectionError,
  createMockUser,
  createMockCreditTransaction,
  createMockVideo,
  mockQueryResults,
  setupScenario
} = enhancedDatabaseMocks;
