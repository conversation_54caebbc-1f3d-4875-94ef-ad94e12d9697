/**
 * Security Middleware Integration Tests
 * 
 * Tests the comprehensive security middleware implementation
 * including rate limiting, input validation, and security headers.
 */

// Mock Next.js request/response
const mockRequest = (options = {}) => ({
  nextUrl: {
    pathname: options.pathname || '/api/test',
    search: options.search || ''
  },
  method: options.method || 'GET',
  headers: new Map(Object.entries(options.headers || {})),
  ...options
});

const mockResponse = () => ({
  headers: new Map(),
  status: 200,
  json: jest.fn(),
  set: function(key, value) { this.headers.set(key, value); }
});

// Mock NextResponse
jest.mock('next/server', () => ({
  NextResponse: {
    next: () => mockResponse(),
    json: (data, options) => ({
      ...mockResponse(),
      status: options?.status || 200,
      data
    })
  }
}));

import { 
  securityMiddleware, 
  sanitizeInput, 
  validateEmail, 
  validateUrl, 
  validateFileType,
  getSecurityMetrics,
  clearSecurityEvents
} from '@/lib/securityMiddleware';

describe('Security Middleware Integration Tests', () => {
  beforeEach(() => {
    // Clear security events before each test
    clearSecurityEvents(0);
  });

  describe('SEC_015: Rate Limiting Integration Tests', () => {
    test('should enforce rate limits for API endpoints', async () => {
      const clientIP = '*************';
      const pathname = '/api/generate-script';
      
      // Make requests up to the limit (10 per minute for generate-script)
      for (let i = 0; i < 10; i++) {
        const request = mockRequest({
          pathname,
          headers: { 'x-forwarded-for': clientIP }
        });
        
        const response = securityMiddleware(request);
        
        // Should not be blocked
        expect(response.status).not.toBe(429);
      }
      
      // 11th request should be blocked
      const blockedRequest = mockRequest({
        pathname,
        headers: { 'x-forwarded-for': clientIP }
      });
      
      const blockedResponse = securityMiddleware(blockedRequest);
      expect(blockedResponse.status).toBe(429);
      expect(blockedResponse.data.error).toBe('Rate limit exceeded');
      
      console.log('Rate Limiting - API endpoint rate limit enforced');
    });

    test('should apply different rate limits for different endpoints', async () => {
      const clientIP = '*************';
      
      const endpointTests = [
        { pathname: '/api/generate-image', limit: 20 },
        { pathname: '/api/generate-audio', limit: 5 },
        { pathname: '/api/get-credits', limit: 100 }
      ];
      
      for (const test of endpointTests) {
        // Test up to limit
        for (let i = 0; i < test.limit; i++) {
          const request = mockRequest({
            pathname: test.pathname,
            headers: { 'x-forwarded-for': clientIP }
          });
          
          const response = securityMiddleware(request);
          expect(response.status).not.toBe(429);
        }
        
        // Test exceeding limit
        const exceededRequest = mockRequest({
          pathname: test.pathname,
          headers: { 'x-forwarded-for': clientIP }
        });
        
        const exceededResponse = securityMiddleware(exceededRequest);
        expect(exceededResponse.status).toBe(429);
        
        console.log(`Rate Limiting - ${test.pathname}: ${test.limit} requests/minute enforced`);
      }
    });
  });

  describe('SEC_016: Security Headers Integration Tests', () => {
    test('should add comprehensive security headers', () => {
      const request = mockRequest({
        pathname: '/api/test'
      });
      
      const response = securityMiddleware(request);
      
      // Check for security headers
      expect(response.headers.get('Content-Security-Policy')).toContain("default-src 'self'");
      expect(response.headers.get('X-Frame-Options')).toBe('DENY');
      expect(response.headers.get('X-Content-Type-Options')).toBe('nosniff');
      expect(response.headers.get('Referrer-Policy')).toBe('strict-origin-when-cross-origin');
      expect(response.headers.get('Permissions-Policy')).toContain('camera=()');
      
      console.log('Security Headers - Comprehensive headers applied');
    });

    test('should block requests from unauthorized origins', () => {
      const request = mockRequest({
        pathname: '/api/test',
        headers: { 'origin': 'https://malicious-site.com' }
      });
      
      const response = securityMiddleware(request);
      
      expect(response.status).toBe(403);
      expect(response.data.error).toBe('Origin not allowed');
      
      console.log('CORS Protection - Unauthorized origin blocked');
    });

    test('should allow requests from authorized origins', () => {
      const request = mockRequest({
        pathname: '/api/test',
        headers: { 'origin': 'http://localhost:3000' }
      });
      
      const response = securityMiddleware(request);
      
      expect(response.status).not.toBe(403);
      
      console.log('CORS Protection - Authorized origin allowed');
    });
  });

  describe('SEC_017: Input Validation Integration Tests', () => {
    test('should detect and block XSS attempts', () => {
      const xssPayloads = [
        '<script>alert("XSS")</script>',
        'javascript:alert("XSS")',
        '<img src=x onerror=alert("XSS")>',
        '<svg onload=alert("XSS")>'
      ];
      
      for (const payload of xssPayloads) {
        const request = mockRequest({
          pathname: '/api/test',
          search: `?input=${encodeURIComponent(payload)}`,
          headers: { 'x-forwarded-for': '*************' }
        });
        
        const response = securityMiddleware(request);
        
        expect(response.status).toBe(403);
        expect(response.data.error).toBe('Suspicious request pattern detected');
        
        console.log(`XSS Protection - Blocked payload: ${payload}`);
      }
    });

    test('should detect and block SQL injection attempts', () => {
      const sqlPayloads = [
        "'; DROP TABLE users; --",
        "' UNION SELECT * FROM users --",
        "' OR '1'='1",
        "'; INSERT INTO users VALUES ('hacker'); --"
      ];
      
      for (const payload of sqlPayloads) {
        const request = mockRequest({
          pathname: '/api/test',
          search: `?query=${encodeURIComponent(payload)}`,
          headers: { 'x-forwarded-for': '*************' }
        });
        
        const response = securityMiddleware(request);
        
        expect(response.status).toBe(403);
        expect(response.data.error).toBe('Suspicious request pattern detected');
        
        console.log(`SQL Injection Protection - Blocked payload: ${payload}`);
      }
    });

    test('should sanitize input correctly', () => {
      const testCases = [
        {
          input: '<script>alert("XSS")</script>Normal text',
          expected: 'Normal text'
        },
        {
          input: 'javascript:alert("XSS")Clean text',
          expected: 'Clean text'
        },
        {
          input: '<img src=x onerror=alert("XSS")>Safe content',
          expected: 'Safe content'
        },
        {
          input: 'Normal text with <b>bold</b> tags',
          expected: 'Normal text with bold tags'
        }
      ];
      
      for (const testCase of testCases) {
        const sanitized = sanitizeInput(testCase.input);
        expect(sanitized).toBe(testCase.expected);
        
        console.log(`Input Sanitization - "${testCase.input}" → "${sanitized}"`);
      }
    });
  });

  describe('SEC_018: User Agent and Bot Detection Tests', () => {
    test('should block malicious bots', () => {
      const maliciousBots = [
        'curl/7.68.0',
        'python-requests/2.25.1',
        'scrapy/2.5.0',
        'bot-scanner/1.0'
      ];
      
      for (const userAgent of maliciousBots) {
        const request = mockRequest({
          pathname: '/api/test',
          headers: { 
            'user-agent': userAgent,
            'x-forwarded-for': '192.168.1.104'
          }
        });
        
        const response = securityMiddleware(request);
        
        expect(response.status).toBe(403);
        expect(response.data.error).toBe('Blocked user agent');
        
        console.log(`Bot Detection - Blocked: ${userAgent}`);
      }
    });

    test('should allow legitimate bots', () => {
      const legitimateBots = [
        'Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)',
        'Mozilla/5.0 (compatible; bingbot/2.0; +http://www.bing.com/bingbot.htm)',
        'facebookexternalhit/1.1'
      ];
      
      for (const userAgent of legitimateBots) {
        const request = mockRequest({
          pathname: '/api/test',
          headers: { 
            'user-agent': userAgent,
            'x-forwarded-for': '192.168.1.105'
          }
        });
        
        const response = securityMiddleware(request);
        
        expect(response.status).not.toBe(403);
        
        console.log(`Bot Detection - Allowed: ${userAgent}`);
      }
    });
  });

  describe('SEC_019: CSRF Protection Tests', () => {
    test('should require CSRF protection for POST requests', () => {
      const request = mockRequest({
        pathname: '/api/test',
        method: 'POST',
        headers: { 'x-forwarded-for': '192.168.1.106' }
      });
      
      const response = securityMiddleware(request);
      
      expect(response.status).toBe(403);
      expect(response.data.error).toBe('CSRF protection required');
      
      console.log('CSRF Protection - POST request without CSRF token blocked');
    });

    test('should allow POST requests with proper CSRF protection', () => {
      const request = mockRequest({
        pathname: '/api/test',
        method: 'POST',
        headers: { 
          'x-csrf-token': 'valid-csrf-token',
          'x-forwarded-for': '*************'
        }
      });
      
      const response = securityMiddleware(request);
      
      expect(response.status).not.toBe(403);
      
      console.log('CSRF Protection - POST request with CSRF token allowed');
    });

    test('should skip CSRF check for public endpoints', () => {
      const publicEndpoints = [
        '/api/webhook',
        '/api/inngest',
        '/api/lemonsqueezy-webhook'
      ];
      
      for (const endpoint of publicEndpoints) {
        const request = mockRequest({
          pathname: endpoint,
          method: 'POST',
          headers: { 'x-forwarded-for': '*************' }
        });
        
        const response = securityMiddleware(request);
        
        expect(response.status).not.toBe(403);
        
        console.log(`CSRF Protection - Public endpoint ${endpoint} allowed without CSRF`);
      }
    });
  });

  describe('SEC_020: Utility Function Tests', () => {
    test('should validate email addresses correctly', () => {
      const emailTests = [
        { email: '<EMAIL>', valid: true },
        { email: '<EMAIL>', valid: true },
        { email: 'invalid.email', valid: false },
        { email: '@domain.com', valid: false },
        { email: 'user@', valid: false },
        { email: '', valid: false }
      ];
      
      for (const test of emailTests) {
        const isValid = validateEmail(test.email);
        expect(isValid).toBe(test.valid);
        
        console.log(`Email Validation - ${test.email}: ${isValid ? 'VALID' : 'INVALID'}`);
      }
    });

    test('should validate URLs correctly', () => {
      const urlTests = [
        { url: 'https://example.com', valid: true },
        { url: 'http://localhost:3000', valid: true },
        { url: 'javascript:alert("XSS")', valid: false },
        { url: 'data:text/html,<script>alert("XSS")</script>', valid: false },
        { url: 'ftp://example.com', valid: false },
        { url: 'invalid-url', valid: false }
      ];
      
      for (const test of urlTests) {
        const isValid = validateUrl(test.url);
        expect(isValid).toBe(test.valid);
        
        console.log(`URL Validation - ${test.url}: ${isValid ? 'VALID' : 'INVALID'}`);
      }
    });

    test('should validate file types correctly', () => {
      const fileTests = [
        { filename: 'video.mp4', allowedTypes: ['mp4', 'mov'], valid: true },
        { filename: 'image.jpg', allowedTypes: ['jpg', 'png'], valid: true },
        { filename: 'script.js', allowedTypes: ['mp4', 'jpg'], valid: false },
        { filename: 'malware.exe', allowedTypes: ['mp4', 'jpg'], valid: false }
      ];
      
      for (const test of fileTests) {
        const isValid = validateFileType(test.filename, test.allowedTypes);
        expect(isValid).toBe(test.valid);
        
        console.log(`File Type Validation - ${test.filename}: ${isValid ? 'VALID' : 'INVALID'}`);
      }
    });
  });

  describe('SEC_021: Security Monitoring Tests', () => {
    test('should track security metrics', () => {
      // Generate some security events
      const clientIP = '*************';
      
      // Rate limit violations
      for (let i = 0; i < 15; i++) {
        const request = mockRequest({
          pathname: '/api/generate-script',
          headers: { 'x-forwarded-for': clientIP }
        });
        securityMiddleware(request);
      }
      
      // Suspicious patterns
      const xssRequest = mockRequest({
        pathname: '/api/test',
        search: '?input=<script>alert("XSS")</script>',
        headers: { 'x-forwarded-for': clientIP }
      });
      securityMiddleware(xssRequest);
      
      const metrics = getSecurityMetrics();
      
      expect(metrics.rateLimitViolations).toBeGreaterThan(0);
      expect(metrics.suspiciousPatterns).toBeGreaterThan(0);
      
      console.log('Security Monitoring - Metrics tracked:', metrics);
    });

    test('should clear old security events', () => {
      // Generate events
      const request = mockRequest({
        pathname: '/api/test',
        search: '?input=<script>alert("XSS")</script>',
        headers: { 'x-forwarded-for': '*************' }
      });
      securityMiddleware(request);
      
      // Clear events older than 0ms (all events)
      clearSecurityEvents(0);
      
      const metrics = getSecurityMetrics();
      expect(metrics.suspiciousPatterns).toBe(0);
      
      console.log('Security Monitoring - Old events cleared');
    });
  });

  describe('SEC_022: Request Size Validation Tests', () => {
    test('should block oversized requests', () => {
      const request = mockRequest({
        pathname: '/api/test',
        headers: { 
          'content-length': '30000000', // 30MB (over 25MB limit)
          'x-forwarded-for': '*************'
        }
      });
      
      const response = securityMiddleware(request);
      
      expect(response.status).toBe(413);
      expect(response.data.error).toBe('Request too large');
      
      console.log('Request Size Validation - Oversized request blocked');
    });

    test('should allow normal-sized requests', () => {
      const request = mockRequest({
        pathname: '/api/test',
        headers: { 
          'content-length': '1000000', // 1MB (under limit)
          'x-forwarded-for': '192.168.1.112'
        }
      });
      
      const response = securityMiddleware(request);
      
      expect(response.status).not.toBe(413);
      
      console.log('Request Size Validation - Normal-sized request allowed');
    });
  });
});
