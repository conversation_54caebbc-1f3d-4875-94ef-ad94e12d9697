/**
 * Integration Test Base Class
 * 
 * Provides transaction management, mock setup utilities, and ensures
 * compatibility with existing test infrastructure.
 */

import { TestDatabaseManager } from '../setup/database.setup.js';
import { MockInngestClient } from '../__mocks__/enhanced/inngest.js';
import { MockCircuitBreakerManager } from '../__mocks__/enhanced/circuitBreaker.js';
import { enhancedDatabaseMocks } from '../__mocks__/enhanced/database.js';

export class IntegrationTestBase {
  constructor() {
    this.dbManager = null;
    this.mocks = {};
    this.testStartTime = null;
    this.testEndTime = null;
  }

  /**
   * Setup method to be called before each test
   */
  async beforeEach() {
    this.testStartTime = Date.now();
    
    try {
      // Initialize database manager
      this.dbManager = new TestDatabaseManager();
      await this.dbManager.setup();

      // Setup enhanced mocks
      await this.setupEnhancedMocks();

      // Clear all existing mocks
      jest.clearAllMocks();

      console.log('[IntegrationTest] Setup completed');
    } catch (error) {
      console.error('[IntegrationTest] Setup failed:', error);
      throw error;
    }
  }

  /**
   * Cleanup method to be called after each test
   */
  async afterEach() {
    this.testEndTime = Date.now();
    const duration = this.testEndTime - this.testStartTime;

    try {
      // Cleanup database connections
      if (this.dbManager) {
        await this.dbManager.cleanup();
      }

      // Restore mocks
      this.restoreMocks();

      console.log(`[IntegrationTest] Cleanup completed (${duration}ms)`);
    } catch (error) {
      console.error('[IntegrationTest] Cleanup failed:', error);
      throw error;
    }
  }

  /**
   * Setup enhanced mocks for integration testing
   */
  async setupEnhancedMocks() {
    // Inngest mock
    this.mocks.inngest = new MockInngestClient();
    
    // Circuit breaker mock
    this.mocks.circuitBreakerManager = new MockCircuitBreakerManager();
    
    // Database mocks
    this.mocks.database = enhancedDatabaseMocks;

    // External API mocks
    this.mocks.googleAI = {
      generateScript: jest.fn(),
      isAvailable: jest.fn().mockReturnValue(true)
    };

    this.mocks.elevenLabs = {
      generateAudio: jest.fn(),
      isAvailable: jest.fn().mockReturnValue(true)
    };

    this.mocks.runware = {
      generateImage: jest.fn(),
      isAvailable: jest.fn().mockReturnValue(true)
    };

    this.mocks.pexels = {
      searchImages: jest.fn(),
      isAvailable: jest.fn().mockReturnValue(true)
    };

    this.mocks.pixabay = {
      searchImages: jest.fn(),
      isAvailable: jest.fn().mockReturnValue(true)
    };

    // Fallback script generator mock
    this.mocks.fallbackScriptGenerator = {
      generate: jest.fn()
    };

    // Clerk authentication mock (extend existing)
    this.mocks.clerk = {
      getUser: jest.fn(),
      verifyToken: jest.fn(),
      createUser: jest.fn()
    };

    // Mock Inngest client globally
    jest.doMock('@/app/inngest/client', () => ({
      inngest: this.mocks.inngest
    }));

    // Mock circuit breaker globally
    jest.doMock('@/lib/circuitBreaker', () => ({
      withCircuitBreaker: this.mocks.circuitBreakerManager.execute.bind(this.mocks.circuitBreakerManager),
      getCircuitBreakerStatus: (serviceName) => this.mocks.circuitBreakerManager.getBreaker(serviceName).getState()
    }));

    console.log('[IntegrationTest] Enhanced mocks setup completed');
  }

  /**
   * Restore all mocks to their original state
   */
  restoreMocks() {
    // Clear Inngest mock
    if (this.mocks.inngest) {
      this.mocks.inngest.clear();
    }

    // Reset circuit breakers
    if (this.mocks.circuitBreakerManager) {
      this.mocks.circuitBreakerManager.resetAll();
    }

    // Clear all Jest mocks
    jest.clearAllMocks();
    jest.restoreAllMocks();
  }

  /**
   * Run test within a database transaction that automatically rolls back
   */
  async runInTransaction(testCallback) {
    if (!this.dbManager) {
      throw new Error('Database manager not initialized. Call beforeEach() first.');
    }

    return await this.dbManager.withTransaction(testCallback);
  }

  /**
   * Get database instance for direct queries
   */
  getDatabase() {
    if (!this.dbManager) {
      throw new Error('Database manager not initialized. Call beforeEach() first.');
    }
    
    return this.dbManager.getDatabase();
  }

  /**
   * Create a test user within the current transaction
   */
  async createTestUser(tx, userData = {}) {
    return await this.dbManager.createTestUser(tx, userData);
  }

  /**
   * Create a test video within the current transaction
   */
  async createTestVideo(tx, videoData = {}) {
    return await this.dbManager.createTestVideo(tx, videoData);
  }

  /**
   * Setup mock responses for external APIs
   */
  setupApiMocks(scenario) {
    switch (scenario) {
      case 'all_success':
        this.mocks.googleAI.generateScript.mockResolvedValue({
          script: 'Generated script content...',
          metadata: { wordCount: 150, estimatedDuration: 60 }
        });
        
        this.mocks.elevenLabs.generateAudio.mockResolvedValue({
          audioUrl: 'https://mock-audio.com/audio.mp3',
          duration: 62
        });
        
        this.mocks.runware.generateImage.mockResolvedValue({
          imageUrl: 'https://mock-image.com/image.jpg'
        });
        break;

      case 'google_ai_failure':
        this.mocks.googleAI.generateScript.mockRejectedValue(
          new Error('Google AI API unavailable')
        );
        
        this.mocks.fallbackScriptGenerator.generate.mockResolvedValue({
          script: 'Fallback script content...',
          metadata: { source: 'fallback', wordCount: 100 }
        });
        break;

      case 'circuit_breaker_open':
        // Simulate circuit breaker in open state
        const breaker = this.mocks.circuitBreakerManager.getBreaker('GOOGLE_AI');
        breaker.forceState('OPEN');
        break;

      case 'api_timeout':
        this.mocks.googleAI.generateScript.mockImplementation(() => 
          new Promise((_, reject) => 
            setTimeout(() => reject(new Error('Request timeout')), 100)
          )
        );
        break;

      default:
        console.warn(`[IntegrationTest] Unknown API mock scenario: ${scenario}`);
    }
  }

  /**
   * Verify Inngest events were triggered correctly
   */
  verifyInngestEvents(expectedEvents) {
    const actualEvents = this.mocks.inngest.getEvents();
    
    expect(actualEvents).toHaveLength(expectedEvents.length);
    
    expectedEvents.forEach((expectedEvent, index) => {
      expect(actualEvents[index]).toMatchObject({
        name: expectedEvent.name,
        data: expect.objectContaining(expectedEvent.data)
      });
    });
  }

  /**
   * Verify Inngest workflows were triggered correctly
   */
  verifyInngestWorkflows(expectedWorkflows) {
    const actualWorkflows = this.mocks.inngest.getWorkflows();
    
    expect(actualWorkflows).toHaveLength(expectedWorkflows.length);
    
    expectedWorkflows.forEach((expectedWorkflow, index) => {
      expect(actualWorkflows[index]).toMatchObject({
        name: expectedWorkflow.name,
        status: expectedWorkflow.status || 'completed'
      });
    });
  }

  /**
   * Verify circuit breaker behavior
   */
  verifyCircuitBreakerState(serviceName, expectedState) {
    const breaker = this.mocks.circuitBreakerManager.getBreaker(serviceName);
    expect(breaker.getState()).toBe(expectedState);
  }

  /**
   * Verify database state matches expectations
   */
  async verifyDatabaseState(tx, expectations) {
    return await this.dbManager.verifyDatabaseState(tx, expectations);
  }

  /**
   * Get test execution statistics
   */
  getTestStats() {
    return {
      duration: this.testEndTime - this.testStartTime,
      inngestStats: this.mocks.inngest ? this.mocks.inngest.getStats() : null,
      circuitBreakerStats: this.mocks.circuitBreakerManager ? 
        this.mocks.circuitBreakerManager.getAllStats() : null
    };
  }

  /**
   * Wait for async operations to complete
   */
  async waitForAsyncOperations(timeout = 1000) {
    return new Promise(resolve => setTimeout(resolve, timeout));
  }

  /**
   * Assert that no unexpected errors occurred during test
   */
  assertNoUnexpectedErrors() {
    // Check for any unhandled promise rejections or errors
    // This can be extended based on specific error tracking needs
    
    const inngestStats = this.mocks.inngest.getStats();
    if (inngestStats.failedWorkflows > 0) {
      const failedWorkflows = this.mocks.inngest.getWorkflows()
        .filter(w => w.status === 'failed');
      
      console.warn('[IntegrationTest] Failed workflows detected:', failedWorkflows);
    }
  }

  /**
   * Helper method to simulate time passage for circuit breaker testing
   */
  simulateTimePassage(milliseconds) {
    const originalNow = Date.now;
    const mockNow = jest.fn(() => originalNow() + milliseconds);
    Date.now = mockNow;
    
    // Return cleanup function
    return () => {
      Date.now = originalNow;
    };
  }
}

// Export helper function for quick setup
export async function setupIntegrationTest() {
  const testBase = new IntegrationTestBase();
  await testBase.beforeEach();
  return testBase;
}

// Export helper function for quick cleanup
export async function cleanupIntegrationTest(testBase) {
  if (testBase) {
    await testBase.afterEach();
  }
}
