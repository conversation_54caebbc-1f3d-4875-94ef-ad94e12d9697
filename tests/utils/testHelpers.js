/**
 * Test utilities and helpers
 * Provides reusable functions for setting up test scenarios
 */

import { render } from '@testing-library/react';
import React from 'react';

/**
 * Custom render function with providers
 */
export function renderWithProviders(ui, options = {}) {
  const {
    userDetail = global.testUtils?.createMockUser() || {
      id: 'test_user_123',
      credits: 20,
      currentCreditBalance: 20,
    },
    ...renderOptions
  } = options;

  const mockContextValue = {
    userDetail,
    setUserDetail: jest.fn(),
    invalidateVideoCache: jest.fn(),
  };

  // Create a simple wrapper that provides the context value
  function Wrapper({ children }) {
    // Mock the context provider
    return React.createElement('div', { 'data-testid': 'test-wrapper' }, children);
  }

  return {
    ...render(ui, { wrapper: Wrapper, ...renderOptions }),
    mockContextValue,
  };
}

/**
 * Create mock form data with validation
 */
export function createValidFormData(overrides = {}) {
  return {
    projectTitle: 'Test AI Video Project',
    topic: 'The Future of Artificial Intelligence in Healthcare',
    videoStyle: 'Professional',
    aspectRatio: '16:9',
    script: 'This is a comprehensive test script for AI video generation that meets the minimum length requirements and provides engaging content.',
    voice: 'en-US-Wavenet-D',
    audioSpeed: 1.0,
    backgroundMusic: 'upbeat',
    caption: 'enabled',
    templateId: 'AI_VIDEO_TEMPLATE',
    ...overrides,
  };
}

/**
 * Create invalid form data for testing validation
 */
export function createInvalidFormData(invalidField) {
  const baseData = createValidFormData();
  
  switch (invalidField) {
    case 'projectTitle':
      return { ...baseData, projectTitle: '' };
    case 'topic':
      return { ...baseData, topic: 'AI' }; // Too short
    case 'script':
      return { ...baseData, script: 'Short' }; // Too short
    case 'audioSpeed':
      return { ...baseData, audioSpeed: 3.0 }; // Out of range
    default:
      return { ...baseData, [invalidField]: '' };
  }
}

/**
 * Mock server action responses
 */
export function mockServerActionSuccess(data = {}) {
  return {
    success: true,
    eventId: 'video_123',
    creditsRemaining: 15,
    ...data,
  };
}

export function mockServerActionError(error = 'Unknown error') {
  return {
    success: false,
    error,
  };
}

/**
 * Mock API route responses
 */
export function mockApiResponse(data, status = 200) {
  return {
    ok: status >= 200 && status < 300,
    status,
    json: () => Promise.resolve(data),
  };
}

/**
 * Credit system test helpers
 */
export const creditTestHelpers = {
  createUserWithCredits: (credits) => ({
    id: 'test_user_123',
    clerkId: 'test_user_123',
    credits,
    currentCreditBalance: credits,
    email: '<EMAIL>',
  }),
  
  createCreditTransaction: (type, amount, userId = 'test_user_123') => ({
    id: 'transaction_123',
    userId,
    transactionType: type,
    amount: type === 'DEBIT' ? -Math.abs(amount) : Math.abs(amount),
    balanceBefore: 20,
    balanceAfter: type === 'DEBIT' ? 20 - Math.abs(amount) : 20 + Math.abs(amount),
    createdAt: new Date(),
  }),
  
  calculateExpectedCost: (workflowType, config = {}) => {
    const baseCosts = {
      'AI_VIDEO': 5,
      'MEME_VIDEO': 2,
      'UGC_VIDEO': 8,
      'NARRATOR_VIDEO': 4,
      'REDDIT_VIDEO': 3,
      'TWITTER_VIDEO': 3,
      'STOCK_MEDIA_VIDEO': 6,
    };
    
    let cost = baseCosts[workflowType] || 5;
    
    // Add duration-based adjustments
    if (config.estimatedDurationSeconds > 60) {
      cost += Math.ceil((config.estimatedDurationSeconds - 60) / 30);
    }
    
    return cost;
  },
};

/**
 * Form validation test helpers
 */
export const validationTestHelpers = {
  testRequiredField: async (component, fieldName, userEvent) => {
    const input = component.getByTestId(`${fieldName}-input`);
    await userEvent.clear(input);
    await userEvent.tab(); // Trigger blur
    
    expect(component.getByText(/required/i)).toBeInTheDocument();
  },
  
  testFieldLength: async (component, fieldName, minLength, maxLength, userEvent) => {
    const input = component.getByTestId(`${fieldName}-input`);
    
    // Test minimum length
    await userEvent.clear(input);
    await userEvent.type(input, 'a'.repeat(minLength - 1));
    await userEvent.tab();
    
    expect(component.getByText(new RegExp(`at least ${minLength}`, 'i'))).toBeInTheDocument();
    
    // Test maximum length
    await userEvent.clear(input);
    await userEvent.type(input, 'a'.repeat(maxLength + 1));
    await userEvent.tab();
    
    expect(component.getByText(new RegExp(`no more than ${maxLength}`, 'i'))).toBeInTheDocument();
  },
};

/**
 * Async test helpers
 */
export const asyncTestHelpers = {
  waitForLoadingToFinish: async (component) => {
    await component.findByText(/loading/i, {}, { timeout: 5000 });
    await component.waitForElementToBeRemoved(() => component.queryByText(/loading/i));
  },
  
  waitForSuccessMessage: async (component, message = /success/i) => {
    return await component.findByText(message, {}, { timeout: 5000 });
  },
  
  waitForErrorMessage: async (component, message = /error/i) => {
    return await component.findByText(message, {}, { timeout: 5000 });
  },
};

/**
 * E2E test helpers (for Playwright)
 */
export const e2eHelpers = {
  selectors: {
    projectTitleInput: '[data-testid="project-title-input"]',
    topicInput: '[data-testid="topic-input"]',
    videoStyleSelect: '[data-testid="video-style-select"]',
    aspectRatioSelect: '[data-testid="aspect-ratio-select"]',
    scriptEditor: '[data-testid="script-editor"]',
    voiceSelect: '[data-testid="voice-select"]',
    audioSpeedSlider: '[data-testid="audio-speed-slider"]',
    generateButton: '[data-testid="generate-video-button"]',
    creditsDisplay: '[data-testid="credits-display"]',
    errorMessage: '[data-testid="error-message"]',
    successMessage: '[data-testid="success-message"]',
  },
  
  urls: {
    aiVideoCreation: '/dashboard/create-new-short/create-ai-video',
    dashboard: '/dashboard',
    signIn: '/sign-in',
  },
  
  testData: {
    validProject: {
      title: 'E2E Test Video',
      topic: 'Testing AI video generation with Playwright',
      style: 'Professional',
      aspectRatio: '16:9',
    },
    
    invalidProject: {
      title: '', // Invalid - empty
      topic: 'AI', // Invalid - too short
      style: 'Professional',
      aspectRatio: '16:9',
    },
  },
};
