/**
 * Example: AI Video Generation with Resilient API Calls
 * This shows how to integrate the resilient API system with your existing Inngest functions
 */

import { inngest } from "../client";
import { db } from "@/configs/db";
import { users, videoData } from "@/configs/schema";
import { eq } from 'drizzle-orm';

// Import resilient API helpers
import { 
  resilientGoogleAI, 
  resilientElevenLabs, 
  resilientPexels,
  resilientPixabay,
  createEmergencyFallbacks,
  checkAPIHealth,
  rateLimitAwareBatch
} from '@/lib/inngestApiHelpers';

import { triggerCreditRefund } from '@/lib/atomicCreditSystem';
import { withCircuitBreaker } from '@/lib/circuitBreaker';

export const resilientAIVideoGeneration = inngest.createFunction(
  { id: "resilient-ai-video-generation" },
  { event: "app/ai-video.generate" },
  async ({ event, step }) => {
    const { userId, videoId, workflowType, ...videoConfig } = event.data;
    const { projectTitle, topic, videoStyle, aspectRatio, caption, templateId, voice, audioSpeed, backgroundMusic } = videoConfig;

    // Step 0: Check API Health
    const healthStatus = await checkAPIHealth(step);
    console.log('[Resilient AI Video] API Health Status:', healthStatus);

    // Step 1: Verify Video Record (same as before)
    const videoRecord = await step.run("verify-video-record", async () => {
        console.log(`[Resilient AI Video] Verifying video record for ID: ${videoId}...`);
        
        const [video] = await db
            .select()
            .from(videoData)
            .where(eq(videoData.id, videoId))
            .limit(1);

        if (!video) {
            throw new Error(`Video record not found for ID: ${videoId}`);
        }

        if (video.clerkId !== userId) {
            throw new Error(`Video record does not belong to user: ${userId}`);
        }

        return video;
    });

    // Step 2: Generate Script with Resilient Google AI
    const generatedScript = await step.run("generate-script-resilient", async () => {
        console.log("[Resilient AI Video] Generating script with resilient API...");
        
        try {
            return await withCircuitBreaker('GOOGLE_AI', async () => {
                const prompt = `Create a ${videoStyle} video script about: ${topic}. 
                Make it engaging, informative, and under 150 words. 
                Focus on key points that would work well with visual content.`;
                
                const response = await resilientGoogleAI(step, prompt);
                
                if (!response || !response.candidates || !response.candidates[0]) {
                    throw new Error("Invalid response from Google AI");
                }
                
                const script = response.candidates[0].content.parts[0].text;
                
                if (!script || script.trim().length < 10) {
                    throw new Error("Generated script is too short or empty");
                }
                
                return script.trim();
            });
        } catch (error) {
            console.log("[Resilient AI Video] Google AI failed, using emergency fallback");
            const fallbacks = createEmergencyFallbacks();
            return await fallbacks.scriptFallback(topic, videoStyle);
        }
    });

    // Step 3: Generate Audio with Resilient ElevenLabs
    const generatedAudioUrl = await step.run("generate-audio-resilient", async () => {
        console.log("[Resilient AI Video] Generating audio with resilient API...");
        
        try {
            return await withCircuitBreaker('ELEVENLABS', async () => {
                const voiceId = 'pNInz6obpgDQGcFmaJgB'; // Default voice
                const response = await resilientElevenLabs(step, generatedScript, voiceId, {
                    voice_settings: {
                        stability: 0.5,
                        similarity_boost: 0.5,
                        speed: parseFloat(audioSpeed) || 1.0
                    }
                });
                
                // Upload to your storage and return URL
                // This would integrate with your existing Firebase storage logic
                return await uploadAudioToStorage(response);
            });
        } catch (error) {
            console.log("[Resilient AI Video] ElevenLabs failed, using emergency fallback");
            const fallbacks = createEmergencyFallbacks();
            return await fallbacks.ttsFallback(generatedScript);
        }
    });

    // Step 4: Generate Images with Fallback Strategy
    const generatedImages = await step.run("generate-images-resilient", async () => {
        console.log("[Resilient AI Video] Generating images with fallback strategy...");
        
        // First, generate image prompts
        const imagePrompts = await generateImagePrompts(generatedScript, videoStyle);
        
        try {
            // Try Pexels first
            return await withCircuitBreaker('PEXELS', async () => {
                const searchTerms = imagePrompts.slice(0, 3); // Limit to avoid rate limits
                const images = [];
                
                for (const prompt of searchTerms) {
                    try {
                        const response = await resilientPexels(step, prompt, { perPage: 3 });
                        if (response.photos && response.photos.length > 0) {
                            images.push(response.photos[0].src.large);
                        }
                    } catch (error) {
                        console.log(`[Resilient AI Video] Failed to get image for prompt: ${prompt}`);
                        images.push(null);
                    }
                }
                
                return images.filter(img => img !== null);
            });
        } catch (pexelsError) {
            console.log("[Resilient AI Video] Pexels failed, trying Pixabay fallback");
            
            try {
                return await withCircuitBreaker('PIXABAY', async () => {
                    const searchTerms = imagePrompts.slice(0, 5); // Pixabay has higher limits
                    const images = [];
                    
                    for (const prompt of searchTerms) {
                        try {
                            const response = await resilientPixabay(step, prompt, { perPage: 5 });
                            if (response.hits && response.hits.length > 0) {
                                images.push(response.hits[0].largeImageURL);
                            }
                        } catch (error) {
                            console.log(`[Resilient AI Video] Failed to get image for prompt: ${prompt}`);
                            images.push(null);
                        }
                    }
                    
                    return images.filter(img => img !== null);
                });
            } catch (pixabayError) {
                console.log("[Resilient AI Video] Both image APIs failed, using emergency fallback");
                const fallbacks = createEmergencyFallbacks();
                return await fallbacks.imageFallback(imagePrompts);
            }
        }
    });

    // Step 5: Generate Captions (with resilient audio processing)
    const generatedCaptions = await step.run("generate-captions-resilient", async () => {
        console.log("[Resilient AI Video] Generating captions...");
        
        try {
            // Use your existing Deepgram integration with retry logic
            return await withCircuitBreaker('DEEPGRAM', async () => {
                // Your existing caption generation logic here
                // but wrapped with circuit breaker protection
                return await generateCaptionsFromAudio(generatedAudioUrl);
            });
        } catch (error) {
            console.log("[Resilient AI Video] Caption generation failed, creating fallback captions");
            // Create simple word-based captions as fallback
            return createFallbackCaptions(generatedScript);
        }
    });

    // Step 6: Update Video Record
    const savedVideoId = await step.run("update-video-data", async () => {
        console.log("[Resilient AI Video] Updating video record...");

        const updatePayload = {
            script: generatedScript,
            audioUrl: generatedAudioUrl,
            captionJson: generatedCaptions,
            images: generatedImages,
            status: 'Ready for Rendering',
            updatedAt: new Date(),
            workflow_data: {
                ...videoRecord.workflow_data,
                generatedContent: {
                    script: generatedScript,
                    audioUrl: generatedAudioUrl,
                    captions: generatedCaptions,
                    images: generatedImages,
                    apiHealthStatus: healthStatus
                }
            }
        };

        const result = await db
            .update(videoData)
            .set(updatePayload)
            .where(eq(videoData.id, videoId))
            .returning({ id: videoData.id });

        if (!result || result.length === 0) {
            throw new Error("Failed to update video data in the database.");
        }

        return videoId;
    });

    return {
      message: "Resilient AI Video Generation workflow completed!",
      videoId: savedVideoId,
      healthStatus: healthStatus.overallStatus
    };
  },
  {
    // Enhanced error handling with circuit breaker awareness
    onFailure: async ({ event, error }) => {
      console.error(`[Resilient AI Video] Generation failed for video ${event.data.videoId}:`, error);
      
      // Check if failure was due to circuit breaker
      if (error.message.includes('circuit breaker')) {
        console.log(`[Resilient AI Video] Failure due to circuit breaker - service degradation detected`);
      }
      
      // Trigger credit refund
      await triggerCreditRefund(event.data.videoId, `Resilient AI Video Generation failed: ${error.message}`);
    }
  }
);

// Helper functions (you would implement these based on your existing logic)
async function generateImagePrompts(script, style) {
  // Your existing image prompt generation logic
  return [`${style} scene`, `${style} background`, `${style} visual`];
}

async function uploadAudioToStorage(audioResponse) {
  // Your existing Firebase storage upload logic
  return 'https://example.com/audio.mp3';
}

async function generateCaptionsFromAudio(audioUrl) {
  // Your existing Deepgram caption generation logic
  return [];
}

function createFallbackCaptions(script) {
  // Create simple word-based captions as fallback
  const words = script.split(' ');
  return words.map((word, index) => ({
    text: word,
    start: index * 500, // 500ms per word
    end: (index + 1) * 500
  }));
}
