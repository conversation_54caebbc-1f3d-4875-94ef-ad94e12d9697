'use client';

import React, { createContext, useContext, useRef } from 'react';

/**
 * Context for managing global video cache invalidation
 * This allows components across the app to invalidate the video cache
 * when new videos are created or when cache needs to be refreshed
 */
const VideoCacheContext = createContext(null);

export function VideoCacheProvider({ children }) {
  // Store cache invalidation callbacks
  const invalidationCallbacksRef = useRef(new Set());

  /**
   * Register a cache invalidation callback
   * This is called by the useVideoCache hook to register its invalidation function
   */
  const registerInvalidationCallback = (callback) => {
    invalidationCallbacksRef.current.add(callback);
    
    // Return cleanup function
    return () => {
      invalidationCallbacksRef.current.delete(callback);
    };
  };

  /**
   * Invalidate all registered video caches
   * This can be called from anywhere in the app when new videos are created
   */
  const invalidateAllCaches = () => {
    console.log('[VideoCacheContext] Invalidating all video caches');
    invalidationCallbacksRef.current.forEach(callback => {
      try {
        callback();
      } catch (error) {
        console.error('[VideoCacheContext] Error calling invalidation callback:', error);
      }
    });
  };

  /**
   * Get the number of registered cache instances
   * Useful for debugging
   */
  const getRegisteredCacheCount = () => {
    return invalidationCallbacksRef.current.size;
  };

  const value = {
    registerInvalidationCallback,
    invalidateAllCaches,
    getRegisteredCacheCount
  };

  return (
    <VideoCacheContext.Provider value={value}>
      {children}
    </VideoCacheContext.Provider>
  );
}

/**
 * Hook to access video cache context
 * Provides functions to invalidate caches globally
 */
export function useVideoCacheContext() {
  const context = useContext(VideoCacheContext);
  
  if (!context) {
    throw new Error('useVideoCacheContext must be used within a VideoCacheProvider');
  }
  
  return context;
}

/**
 * Hook for components that need to trigger cache invalidation
 * This is a simpler interface for components that just need to invalidate cache
 */
export function useVideoCacheInvalidation() {
  const { invalidateAllCaches } = useVideoCacheContext();
  
  return {
    invalidateVideoCache: invalidateAllCaches
  };
}
