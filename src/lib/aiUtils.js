// src/lib/aiUtils.js
import { GoogleGenerativeAI } from "@google/generative-ai";

// Ensure you have GEMINI_API_KEY in your .env file
if (!process.env.GEMINI_API_KEY) {
    console.error("FATAL ERROR: GEMINI_API_KEY environment variable is not set.");
    // In a production environment, you might want to throw an error or exit
    // throw new Error("GEMINI_API_KEY is not configured.");
}

// Initialize the Google Generative AI client
// It's generally better to initialize this once and reuse it.
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);

// Choose the generative model
// Consider making the model name configurable if needed
const model = genAI.getGenerativeModel({ model: "gemini-2.0-flash-lite" });

/**
 * Calls the configured Generative AI model to generate a video script based on a topic.
 * 
 * @param {string} topic - The topic for the video script.
 * @returns {Promise<string>} - A promise that resolves with the generated script text.
 * @throws {Error} - Throws an error if the AI call fails or returns an invalid/empty response.
 */
export async function generateScriptFromAI(topic) {
    console.log(`[AI Utility] Calling AI to generate script for topic: "${topic}"`);

    // Construct the prompt for the AI model
    const prompt = `Generate a video narrator script (around 100-150 words) about the following topic: ${topic}. The script should be engaging, concise, and suitable for a TikTok or Instagram Reels. Focus on clear, simple language. Avoid complex jargon unless the topic demands it. Respond ONLY with the script text. Do not include scene descriptions, background music suggestions, or any other meta-text, as the output will be directly used for text-to-speech generation.`;

    try {
        console.log(`[AI Utility] Sending prompt to AI: "${prompt.substring(0, 100)}..."`); // Log truncated prompt
        const result = await model.generateContent(prompt);

        // Validate the AI response structure
        if (!result || !result.response || typeof result.response.text !== 'function') {
            console.error("[AI Utility] Invalid AI response structure:", result);
            throw new Error("AI script generation failed: Invalid response structure from AI.");
        }

        const response = result.response;
        const text = await response.text();

        // Validate the generated text
        if (!text || text.trim() === '') {
            console.warn("[AI Utility] AI returned an empty script.");
            throw new Error("AI script generation failed: AI returned an empty response.");
        }

        console.log("[AI Utility] AI Script generated successfully.");
        return text.trim(); // Return the cleaned script text

    } catch (error) {
        console.error("[AI Utility] AI generation failed:", error);
        // Re-throw a more specific error for upstream handling
        throw new Error(`AI script generation failed: ${error.message || 'Unknown AI error'}`);
    }
}