// src/lib/videoGenerationUtils.js
import axios from 'axios';

/**
 * Step 1: Generate Video Script and Image Prompts.
 * If a script is provided in formData, uses it.
 * Otherwise, calls the '/api/generate-script' endpoint with the topic.
 * Then, calls '/api/generate-image-prompt' with the script text.
 * 
 * @param {object} formData - The form data containing topic and potentially a script.
 * @returns {Promise<Array<{contentText: string, imagePrompts: string[]}>>} - Structured script with image prompts.
 * @throws {Error} If script or image prompt generation fails.
 */
export async function getVideoScriptAndPrompts(formData) {
    let plainTextScript;

    if (formData.script && typeof formData.script === 'string' && formData.script.trim() !== '') {
        console.log("[Video Utils - Step 1] Using provided script.");
        plainTextScript = formData.script.trim();
    } else {
        console.log("[Video Utils - Step 1] Generating script from topic:", formData.topic);
        if (!formData.topic) {
            throw new Error("Topic is required to generate a video script.");
        }
        try {
            const scriptResponse = await axios.post("/api/generate-script", { topic: formData.topic });
            plainTextScript = scriptResponse.data?.script;
            if (!plainTextScript || typeof plainTextScript !== 'string' || plainTextScript.trim() === '') {
                throw new Error("API did not return a valid script text.");
            }
            console.log("[Video Utils - Step 1] Plain text script received from API.");
        } catch (error) {
            console.error("[Video Utils - Step 1] Error getting video script via API:", error);
            const errorMessage = error.response?.data?.error || error.message || "Unknown error generating script";
            throw new Error(`Failed to generate video script: ${errorMessage}`);
        }
    }

    console.log("[Video Utils - Step 1] Generating image prompts from script...");
    try {
        const promptResponse = await axios.post("/api/generate-image-prompt", { scriptText: plainTextScript });
        const imagePrompts = promptResponse.data?.imagePrompts;

        if (!Array.isArray(imagePrompts) || imagePrompts.length === 0) {
            console.warn("[Video Utils - Step 1] API did not return valid image prompts. Using fallback.");
            return [{
                contentText: plainTextScript,
                imagePrompts: [`A visual representation illustrating: ${formData.topic || 'the script'}`]
            }];
        }

        console.log("[Video Utils - Step 1] Image prompts received.");
        // Assuming a single scene structure for simplicity based on original code
        return [{
            contentText: plainTextScript,
            imagePrompts: imagePrompts
        }];
    } catch (error) {
        console.error("[Video Utils - Step 1] Error generating image prompts via API:", error);
        const errorMessage = error.response?.data?.error || error.message || "Unknown error generating prompts";
        throw new Error(`Failed to generate image prompts: ${errorMessage}`);
    }
}

/**
 * Step 2: Generate Audio File.
 * Calls the '/api/generate-audio' endpoint with the script text.
 * 
 * @param {Array<{contentText: string}>} videoScript - The structured script.
 * @returns {Promise<string>} - The URL of the generated audio file.
 * @throws {Error} If audio generation fails.
 */
export async function generateAudioFile(videoScript) {
    console.log("[Video Utils - Step 2] Generating audio file...");
    const fullScriptText = videoScript.map(scene => scene.contentText).join(" ");
    if (!fullScriptText) {
        throw new Error("Cannot generate audio from empty script.");
    }

    try {
        const response = await axios.post("/api/generate-audio", { scriptText: fullScriptText });
        if (!response.data?.result) {
            throw new Error("Audio generation API did not return a URL.");
        }
        console.log("[Video Utils - Step 2] Audio URL received:", response.data.result);
        return response.data.result;
    } catch (error) {
        console.error("[Video Utils - Step 2] Error generating audio file:", error);
        throw new Error(`Failed to generate audio: ${error.response?.data?.error || error.message}`);
    }
}

/**
 * Step 3: Generate Audio Captions.
 * Calls the '/api/generate-caption' endpoint with the audio file URL.
 * 
 * @param {string} audioUrl - The URL of the audio file.
 * @returns {Promise<Array<object>>} - Array of caption word objects.
 * @throws {Error} If caption generation fails.
 */
export async function generateAudioCaption(audioUrl) {
    console.log("[Video Utils - Step 3] Generating captions for audio:", audioUrl);
    try {
        const response = await axios.post("/api/generate-caption", { audioUrl });
        if (!Array.isArray(response.data?.result)) {
            throw new Error("Caption generation API did not return a valid word array.");
        }
        console.log("[Video Utils - Step 3] Captions received.");
        return response.data.result;
    } catch (error) {
        console.error("[Video Utils - Step 3] Error generating captions:", error);
        throw new Error(`Failed to generate captions: ${error.response?.data?.error || error.message}`);
    }
}

/**
 * Step 4: Generate Images.
 * Calls the '/api/generate-image' endpoint for each image prompt in the script.
 * 
 * @param {Array<{imagePrompts: string[]}>} videoScript - The structured script with image prompts.
 * @returns {Promise<Array<Array<string|null>>>} - Nested array of image data/URLs (or null on failure).
 */
export async function generateImages(videoScript) {
    console.log("[Video Utils - Step 4] Generating images for scenes...");
    const imageList = [];
    for (const scene of videoScript) {
        if (!scene.imagePrompts || scene.imagePrompts.length === 0) {
            console.warn("[Video Utils - Step 4] Skipping image generation for scene due to missing prompts.");
            imageList.push([]);
            continue;
        }
        const sceneImages = [];
        for (const prompt of scene.imagePrompts) {
            console.log(`[Video Utils - Step 4] Generating image for prompt: ${prompt}`);
            try {
                const response = await axios.post("/api/generate-image", { imagePrompt: prompt });
                if (!response.data?.image) {
                    throw new Error(`API did not return image data for prompt: ${prompt}`);
                }
                sceneImages.push(response.data.image);
            } catch (error) {
                console.error(`[Video Utils - Step 4] Error generating image for prompt "${prompt}":`, error);
                sceneImages.push(null); // Add null placeholder on error
            }
        }
        imageList.push(sceneImages);
    }
    console.log("[Video Utils - Step 4] All image data processed.");
    return imageList;
}

/**
 * Step 5: Save Video Data.
 * Calls the '/api/save-video-data' endpoint to save the generated video assets and metadata.
 * 
 * @param {object} videoData - Object containing script, audio URL, captions, images, title, style.
 * @param {string} userEmail - The email of the user creating the video.
 * @returns {Promise<number>} - The ID of the newly saved video record.
 * @throws {Error} If saving data fails.
 */
export async function savevideoData(videoData, userEmail) {
    console.log("[Video Utils - Step 5] Saving video data to database...");
    if (!videoData || !userEmail) {
        throw new Error("Incomplete data provided for saving video.");
    }
    try {
        const videoData = {
            ...videoData, // Includes videoScript, audioUrl, captions, imageList, title, style
            createdBy: userEmail, // User identifier (assuming email is used as clerkId here)
        };
        const response = await axios.post("/api/save-video-data", videoData);
        if (!response.data?.result?.id) {
            throw new Error("Failed to save video data to database.");
        }
        console.log("[Video Utils - Step 5] Video data saved with ID:", response.data.result.id);
        return response.data.result.id;
    } catch (error) {
        console.error("[Video Utils - Step 5] Error saving video data:", error);
        throw new Error(`Failed to save video: ${error.response?.data?.error || error.message}`);
    }
}

/**
 * Step 6: Update User Credits.
 * Calls the '/api/update-credits' endpoint to deduct credits from the user's account.
 * 
 * @param {string} userEmail - The user's email.
 * @param {number} creditsToDeduct - The number of credits to deduct.
 * @returns {Promise<boolean>} - True if credits were updated successfully (according to API response), false otherwise.
 */
export async function updateUserCredits( credits= 10,Deduct ) {
    console.log(`[Video Utils - Step 6] Deducting ${creditsToDeduct} credits for user ${userEmail}...`);
    try {
        const response = await axios.post("/api/update-credits", {
            credits,
            Deduct,
        });
        if (!response.data?.success) {
            console.error("[Video Utils - Step 6] Failed to update user credits on the backend.");
            // Consider logging this failure but not blocking user flow
        }
        console.log("[Video Utils - Step 6] Credits update API call Completed.");
        return response.data?.success || false;
    } catch (error) {
        console.error("[Video Utils - Step 6] Error calling update credits API:", error);
        // Don't throw here to avoid breaking UX if video is generated, but log it.
        return false;
    }
}