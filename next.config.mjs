/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    // Configure server actions body size limit for file uploads (e.g., videos)
    serverActions: {
      bodySizeLimit: '25mb', // Increase limit to 25MB, adjust as needed
    },
  },
  webpack: (config, { isServer }) => {
    config.resolve.alias = {
      ...config.resolve.alias,
      'node:async_hooks': 'async_hooks',
    };
    // Provide a fallback for the 'async_hooks' module in the browser environment
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        async_hooks: false,
      };
    }

    return config;
  },
};

export default nextConfig;
