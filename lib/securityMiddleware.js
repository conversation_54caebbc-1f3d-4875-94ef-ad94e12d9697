/**
 * Security Middleware for AI Video Generation Platform
 * 
 * Implements comprehensive security measures including:
 * - Rate limiting
 * - Input sanitization
 * - Security headers
 * - CSRF protection
 * - XSS protection
 */

import { NextResponse } from 'next/server';
import crypto from 'crypto';

// Rate limiting store (in production, use Redis or similar)
const rateLimitStore = new Map();
const securityEventStore = new Map();

/**
 * Security configuration
 */
const SECURITY_CONFIG = {
  rateLimiting: {
    // API endpoints with their specific limits
    '/api/generate-script': { requests: 10, windowMs: 60000 }, // 10 per minute
    '/api/generate-image': { requests: 20, windowMs: 60000 },  // 20 per minute
    '/api/generate-audio': { requests: 5, windowMs: 60000 },   // 5 per minute
    '/api/save-video-data': { requests: 50, windowMs: 60000 }, // 50 per minute
    '/api/get-credits': { requests: 100, windowMs: 60000 },    // 100 per minute
    default: { requests: 30, windowMs: 60000 } // Default limit
  },
  security: {
    maxRequestSize: 25 * 1024 * 1024, // 25MB
    allowedOrigins: [
      'http://localhost:3000',
      'https://your-domain.com',
      'https://your-staging-domain.com'
    ],
    blockedUserAgents: [
      /bot/i,
      /crawler/i,
      /spider/i,
      /scraper/i
    ]
  }
};

/**
 * Main security middleware function
 */
export function securityMiddleware(request) {
  const { pathname } = request.nextUrl;
  const clientIP = getClientIP(request);
  const userAgent = request.headers.get('user-agent') || '';

  // Apply security checks
  const securityChecks = [
    checkRateLimit(request, clientIP, pathname),
    checkSecurityHeaders(request),
    checkUserAgent(userAgent),
    checkRequestSize(request),
    checkCSRF(request),
    checkSuspiciousPatterns(request, clientIP)
  ];

  // Execute all security checks
  for (const check of securityChecks) {
    const result = check;
    if (result && result.block) {
      return createSecurityResponse(result);
    }
  }

  // Add security headers to response
  return addSecurityHeaders(NextResponse.next());
}

/**
 * Rate limiting implementation
 */
function checkRateLimit(request, clientIP, pathname) {
  const config = SECURITY_CONFIG.rateLimiting[pathname] || SECURITY_CONFIG.rateLimiting.default;
  const key = `${clientIP}:${pathname}`;
  const now = Date.now();
  const windowStart = now - config.windowMs;

  // Get or create rate limit data
  if (!rateLimitStore.has(key)) {
    rateLimitStore.set(key, []);
  }

  const requests = rateLimitStore.get(key);
  
  // Remove old requests outside the window
  const validRequests = requests.filter(timestamp => timestamp > windowStart);
  
  // Check if limit exceeded
  if (validRequests.length >= config.requests) {
    recordSecurityEvent(clientIP, 'RATE_LIMIT_EXCEEDED', { pathname, requests: validRequests.length });
    
    return {
      block: true,
      status: 429,
      message: 'Rate limit exceeded',
      headers: {
        'Retry-After': Math.ceil(config.windowMs / 1000),
        'X-RateLimit-Limit': config.requests,
        'X-RateLimit-Remaining': 0,
        'X-RateLimit-Reset': new Date(now + config.windowMs).toISOString()
      }
    };
  }

  // Add current request
  validRequests.push(now);
  rateLimitStore.set(key, validRequests);

  return null;
}

/**
 * Security headers validation
 */
function checkSecurityHeaders(request) {
  const origin = request.headers.get('origin');
  const referer = request.headers.get('referer');
  
  // Check origin for CORS
  if (origin && !SECURITY_CONFIG.security.allowedOrigins.includes(origin)) {
    return {
      block: true,
      status: 403,
      message: 'Origin not allowed'
    };
  }

  return null;
}

/**
 * User agent validation
 */
function checkUserAgent(userAgent) {
  // Block known bot user agents (except legitimate ones)
  const isBlockedBot = SECURITY_CONFIG.security.blockedUserAgents.some(pattern => 
    pattern.test(userAgent)
  );

  if (isBlockedBot && !isLegitimateBot(userAgent)) {
    return {
      block: true,
      status: 403,
      message: 'Blocked user agent'
    };
  }

  return null;
}

/**
 * Request size validation
 */
function checkRequestSize(request) {
  const contentLength = request.headers.get('content-length');
  
  if (contentLength && parseInt(contentLength) > SECURITY_CONFIG.security.maxRequestSize) {
    return {
      block: true,
      status: 413,
      message: 'Request too large'
    };
  }

  return null;
}

/**
 * CSRF protection
 */
function checkCSRF(request) {
  // Skip CSRF check for GET requests and public endpoints
  if (request.method === 'GET' || isPublicEndpoint(request.nextUrl.pathname)) {
    return null;
  }

  const csrfToken = request.headers.get('x-csrf-token');
  const origin = request.headers.get('origin');
  const referer = request.headers.get('referer');

  // Check if request has proper CSRF protection
  if (!csrfToken && !origin && !referer) {
    return {
      block: true,
      status: 403,
      message: 'CSRF protection required'
    };
  }

  return null;
}

/**
 * Suspicious pattern detection
 */
function checkSuspiciousPatterns(request, clientIP) {
  const pathname = request.nextUrl.pathname;
  const userAgent = request.headers.get('user-agent') || '';
  
  // Check for suspicious patterns
  const suspiciousPatterns = [
    // SQL injection attempts
    /(\bUNION\b|\bSELECT\b|\bINSERT\b|\bDELETE\b|\bDROP\b)/i,
    // XSS attempts
    /<script|javascript:|vbscript:|onload=|onerror=/i,
    // Path traversal
    /\.\.\//,
    // Command injection
    /[;&|`$]/
  ];

  const queryString = request.nextUrl.search;
  const testString = `${pathname}${queryString}${userAgent}`;

  for (const pattern of suspiciousPatterns) {
    if (pattern.test(testString)) {
      recordSecurityEvent(clientIP, 'SUSPICIOUS_PATTERN', { 
        pattern: pattern.toString(), 
        pathname,
        userAgent 
      });
      
      return {
        block: true,
        status: 403,
        message: 'Suspicious request pattern detected'
      };
    }
  }

  return null;
}

/**
 * Add security headers to response
 */
function addSecurityHeaders(response) {
  // Content Security Policy
  response.headers.set(
    'Content-Security-Policy',
    "default-src 'self'; " +
    "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://clerk.com https://*.clerk.com; " +
    "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; " +
    "font-src 'self' https://fonts.gstatic.com; " +
    "img-src 'self' data: https: blob:; " +
    "media-src 'self' https: blob:; " +
    "connect-src 'self' https: wss:; " +
    "frame-src 'self' https://clerk.com https://*.clerk.com; " +
    "object-src 'none'; " +
    "base-uri 'self';"
  );

  // Other security headers
  response.headers.set('X-Frame-Options', 'DENY');
  response.headers.set('X-Content-Type-Options', 'nosniff');
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
  response.headers.set('Permissions-Policy', 'camera=(), microphone=(), geolocation=()');
  
  // HSTS (only in production with HTTPS)
  if (process.env.NODE_ENV === 'production') {
    response.headers.set('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');
  }

  return response;
}

/**
 * Create security response for blocked requests
 */
function createSecurityResponse(securityResult) {
  const response = NextResponse.json(
    { 
      error: securityResult.message,
      code: 'SECURITY_VIOLATION'
    },
    { status: securityResult.status }
  );

  // Add any specific headers
  if (securityResult.headers) {
    Object.entries(securityResult.headers).forEach(([key, value]) => {
      response.headers.set(key, value);
    });
  }

  return addSecurityHeaders(response);
}

/**
 * Utility functions
 */
function getClientIP(request) {
  return (
    request.headers.get('x-forwarded-for')?.split(',')[0] ||
    request.headers.get('x-real-ip') ||
    request.headers.get('cf-connecting-ip') ||
    '127.0.0.1'
  );
}

function isLegitimateBot(userAgent) {
  const legitimateBots = [
    /googlebot/i,
    /bingbot/i,
    /slurp/i,
    /duckduckbot/i,
    /baiduspider/i,
    /yandexbot/i,
    /facebookexternalhit/i,
    /twitterbot/i,
    /linkedinbot/i
  ];

  return legitimateBots.some(pattern => pattern.test(userAgent));
}

function isPublicEndpoint(pathname) {
  const publicEndpoints = [
    '/api/webhook',
    '/api/inngest',
    '/api/lemonsqueezy-webhook',
    '/api/health'
  ];

  return publicEndpoints.some(endpoint => pathname.startsWith(endpoint));
}

function recordSecurityEvent(clientIP, eventType, details) {
  const key = `${clientIP}:${eventType}`;
  const now = Date.now();
  
  if (!securityEventStore.has(key)) {
    securityEventStore.set(key, []);
  }
  
  const events = securityEventStore.get(key);
  events.push({ timestamp: now, details });
  
  // Keep only last 100 events per IP/type
  if (events.length > 100) {
    events.splice(0, events.length - 100);
  }
  
  securityEventStore.set(key, events);
  
  // Log security event
  console.warn(`[SECURITY EVENT] ${eventType} from ${clientIP}:`, details);
}

/**
 * Input sanitization utilities
 */
export function sanitizeInput(input) {
  if (typeof input !== 'string') return '';
  
  return input
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
    .replace(/<[^>]*>/g, '')
    .replace(/javascript:/gi, '')
    .replace(/vbscript:/gi, '')
    .replace(/data:/gi, '')
    .replace(/on\w+\s*=/gi, '')
    .trim();
}

export function validateEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

export function validateUrl(url) {
  try {
    const parsedUrl = new URL(url);
    const allowedProtocols = ['http:', 'https:'];
    return allowedProtocols.includes(parsedUrl.protocol);
  } catch {
    return false;
  }
}

export function validateFileType(filename, allowedTypes) {
  const extension = filename.toLowerCase().split('.').pop();
  return allowedTypes.includes(extension);
}

/**
 * Security monitoring utilities
 */
export function getSecurityMetrics() {
  const metrics = {
    rateLimitViolations: 0,
    suspiciousPatterns: 0,
    blockedRequests: 0,
    topOffendingIPs: []
  };

  // Aggregate security events
  for (const [key, events] of securityEventStore) {
    const [ip, eventType] = key.split(':');
    
    switch (eventType) {
      case 'RATE_LIMIT_EXCEEDED':
        metrics.rateLimitViolations += events.length;
        break;
      case 'SUSPICIOUS_PATTERN':
        metrics.suspiciousPatterns += events.length;
        break;
    }
  }

  return metrics;
}

export function clearSecurityEvents(olderThanMs = 24 * 60 * 60 * 1000) {
  const cutoff = Date.now() - olderThanMs;
  
  for (const [key, events] of securityEventStore) {
    const recentEvents = events.filter(event => event.timestamp > cutoff);
    
    if (recentEvents.length === 0) {
      securityEventStore.delete(key);
    } else {
      securityEventStore.set(key, recentEvents);
    }
  }
}
