import { db } from "@/configs/db";
import { users, creditTransactions } from "@/configs/schema";
import { eq, sql } from "drizzle-orm";

// Define the cost for video generation. This could also come from an environment variable or a more central config.
export const VIDEO_GENERATION_COST = 10;
export const UGC_VIDEO_GENERATION_COST = 8;

/**
 * Checks if a user has sufficient credits for an operation using the new atomic credit system.
 * @param {string} userId - The Clerk user ID.
 * @param {number} costRequired - The amount of credits required for the operation.
 * @returns {Promise<boolean>} - True if credits are sufficient, false otherwise.
 */
export async function hasSufficientCredits(userId, costRequired) {
  if (!userId) {
    console.error("[Credit Check] User ID is required.");
    return false;
  }
  try {
    const userResult = await db
      .select({
        currentCreditBalance: users.currentCreditBalance,
        // Keep legacy credits for backward compatibility
        legacyCredits: users.credits,
      })
      .from(users)
      .where(eq(users.clerkId, userId))
      .limit(1);

    if (userResult.length === 0) {
      console.warn(`[Credit Check] User not found with ID: ${userId}`);
      return false; // User not found, so they don't have credits
    }

    const userCredits = userResult[0].currentCreditBalance;
    return userCredits >= costRequired;
  } catch (error) {
    console.error(`[Credit Check] Error fetching credits for user ${userId}:`, error);
    return false; // Default to false on error to be safe
  }
}

/**
 * Get current credit balance for a user using the new atomic system.
 * @param {string} userId - The Clerk user ID.
 * @returns {Promise<number|null>} - Current credit balance or null if user not found.
 */
export async function getCurrentCreditBalance(userId) {
  if (!userId) {
    console.error("[Credit Balance] User ID is required.");
    return null;
  }

  try {
    const userResult = await db
      .select({
        currentCreditBalance: users.currentCreditBalance,
      })
      .from(users)
      .where(eq(users.clerkId, userId))
      .limit(1);

    if (userResult.length === 0) {
      console.warn(`[Credit Balance] User not found with ID: ${userId}`);
      return null;
    }

    return userResult[0].currentCreditBalance;
  } catch (error) {
    console.error(`[Credit Balance] Error fetching balance for user ${userId}:`, error);
    return null;
  }
}

/**
 * Reconcile user credits by comparing current balance with transaction history.
 * @param {string} userId - The Clerk user ID.
 * @returns {Promise<Object>} - Reconciliation result with consistency check.
 */
export async function reconcileUserCredits(userId) {
  try {
    const result = await db
      .select({
        currentBalance: users.currentCreditBalance,
        transactionSum: sql<number>`COALESCE(SUM(${creditTransactions.amount}), 0)`,
        initialBalance: sql<number>`10` // Default starting balance
      })
      .from(users)
      .leftJoin(creditTransactions, eq(users.clerkId, creditTransactions.userId))
      .where(eq(users.clerkId, userId))
      .groupBy(users.clerkId, users.currentCreditBalance);

    if (result.length === 0) {
      return {
        userId,
        error: 'User not found'
      };
    }

    const [data] = result;
    const expectedBalance = data.initialBalance + data.transactionSum;
    const isConsistent = data.currentBalance === expectedBalance;

    return {
      userId,
      currentBalance: data.currentBalance,
      expectedBalance,
      isConsistent,
      discrepancy: data.currentBalance - expectedBalance
    };
  } catch (error) {
    console.error(`[Credit Reconciliation] Error for user ${userId}:`, error);
    return {
      userId,
      error: error.message
    };
  }
}