import { google } from 'googleapis';

// Load environment variables
const GO<PERSON><PERSON><PERSON>_CLIENT_ID = process.env.GOOGLE_CLIENT_ID;
const GOOGLE_CLIENT_SECRET = process.env.GOOGLE_CLIENT_SECRET;
const GOOGLE_REDIRECT_URI = process.env.GOOGLE_REDIRECT_URI; // This should point to your callback route

const oauth2Client = new google.auth.OAuth2(
  GOOGLE_CLIENT_ID,
  GOOGLE_CLIENT_SECRET,
  GOOGLE_REDIRECT_URI
);

// Define the scopes needed for YouTube upload and channel info
const scopes = [
  'https://www.googleapis.com/auth/youtube.upload',
  'https://www.googleapis.com/auth/youtube.readonly', // To get channel info
];

/**
 * Generates the YouTube OAuth authorization URL.
 * This function can be called directly from server actions or API routes.
 * @param {string} userId - The Clerk user ID to include in the state parameter.
 * @returns {string} The authorization URL.
 */
export function generateYouTubeAuthUrl(userId) {
  const authorizationUrl = oauth2Client.generateAuthUrl({
    access_type: 'offline', // Request a refresh token
    scope: scopes,
    prompt: 'consent', // Always ask for consent to ensure refresh token is issued
    state: userId, // Pass Clerk userId as state to link back
  });
  return authorizationUrl;
}

export { oauth2Client };
