/**
 * Circuit Breaker Pattern Implementation for External APIs
 * Prevents cascading failures and provides fast failure when APIs are down
 */

// Circuit breaker states
const CIRCUIT_STATES = {
  CLOSED: 'CLOSED',     // Normal operation
  OPEN: 'OPEN',         // Circuit is open, failing fast
  HALF_OPEN: 'HALF_OPEN' // Testing if service has recovered
};

// In-memory store for circuit breaker state (consider Redis for production)
const circuitBreakerStore = new Map();

/**
 * Circuit breaker configuration for different APIs
 */
const CIRCUIT_BREAKER_CONFIGS = {
  GOOGLE_AI: {
    failureThreshold: 5,        // Open circuit after 5 consecutive failures
    recoveryTimeout: 60000,     // Wait 1 minute before trying half-open
    monitoringPeriod: 300000,   // 5 minute monitoring window
    volumeThreshold: 10,        // Minimum requests before considering failure rate
    errorThresholdPercentage: 50 // Open if 50% of requests fail
  },
  ELEVENLABS: {
    failureThreshold: 3,
    recoveryTimeout: 30000,     // 30 seconds
    monitoringPeriod: 180000,   // 3 minutes
    volumeThreshold: 5,
    errorThresholdPercentage: 60
  },
  CAPTIONS_AI: {
    failureThreshold: 2,        // More sensitive due to long processing times
    recoveryTimeout: 120000,    // 2 minutes
    monitoringPeriod: 600000,   // 10 minutes
    volumeThreshold: 3,
    errorThresholdPercentage: 50
  },
  PEXELS: {
    failureThreshold: 3,
    recoveryTimeout: 1800000,   // 30 minutes (rate limit reset)
    monitoringPeriod: 3600000,  // 1 hour
    volumeThreshold: 5,
    errorThresholdPercentage: 40
  }
};

/**
 * Circuit breaker state management
 */
class CircuitBreaker {
  constructor(apiName, config) {
    this.apiName = apiName;
    this.config = config;
    this.state = CIRCUIT_STATES.CLOSED;
    this.failureCount = 0;
    this.lastFailureTime = null;
    this.successCount = 0;
    this.requestCount = 0;
    this.lastResetTime = Date.now();
  }

  /**
   * Execute a function with circuit breaker protection
   */
  async execute(fn) {
    // Check if we should reset the monitoring period
    this.checkMonitoringPeriodReset();

    // Handle different circuit states
    switch (this.state) {
      case CIRCUIT_STATES.OPEN:
        return this.handleOpenState(fn);
      
      case CIRCUIT_STATES.HALF_OPEN:
        return this.handleHalfOpenState(fn);
      
      case CIRCUIT_STATES.CLOSED:
      default:
        return this.handleClosedState(fn);
    }
  }

  /**
   * Handle execution when circuit is closed (normal operation)
   */
  async handleClosedState(fn) {
    try {
      this.requestCount++;
      const result = await fn();
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure();
      
      // Check if we should open the circuit
      if (this.shouldOpenCircuit()) {
        this.openCircuit();
      }
      
      throw error;
    }
  }

  /**
   * Handle execution when circuit is open (failing fast)
   */
  async handleOpenState(fn) {
    // Check if enough time has passed to try half-open
    if (this.shouldAttemptReset()) {
      this.state = CIRCUIT_STATES.HALF_OPEN;
      console.log(`[Circuit Breaker] ${this.apiName}: Moving to HALF_OPEN state`);
      return this.handleHalfOpenState(fn);
    }

    // Fail fast
    const error = new Error(`Circuit breaker is OPEN for ${this.apiName}. Service is currently unavailable.`);
    error.circuitBreakerOpen = true;
    throw error;
  }

  /**
   * Handle execution when circuit is half-open (testing recovery)
   */
  async handleHalfOpenState(fn) {
    try {
      this.requestCount++;
      const result = await fn();
      this.onSuccess();
      
      // If successful, close the circuit
      this.closeCircuit();
      return result;
    } catch (error) {
      this.onFailure();
      
      // If it fails in half-open, go back to open
      this.openCircuit();
      throw error;
    }
  }

  /**
   * Record a successful execution
   */
  onSuccess() {
    this.successCount++;
    this.failureCount = 0; // Reset consecutive failure count
    console.log(`[Circuit Breaker] ${this.apiName}: Success recorded. State: ${this.state}`);
  }

  /**
   * Record a failed execution
   */
  onFailure() {
    this.failureCount++;
    this.lastFailureTime = Date.now();
    console.log(`[Circuit Breaker] ${this.apiName}: Failure recorded. Count: ${this.failureCount}, State: ${this.state}`);
  }

  /**
   * Check if circuit should be opened
   */
  shouldOpenCircuit() {
    // Check consecutive failures
    if (this.failureCount >= this.config.failureThreshold) {
      return true;
    }

    // Check failure rate over monitoring period
    if (this.requestCount >= this.config.volumeThreshold) {
      const failureRate = ((this.requestCount - this.successCount) / this.requestCount) * 100;
      return failureRate >= this.config.errorThresholdPercentage;
    }

    return false;
  }

  /**
   * Check if we should attempt to reset from open to half-open
   */
  shouldAttemptReset() {
    return this.lastFailureTime && 
           (Date.now() - this.lastFailureTime) >= this.config.recoveryTimeout;
  }

  /**
   * Open the circuit
   */
  openCircuit() {
    this.state = CIRCUIT_STATES.OPEN;
    this.lastFailureTime = Date.now();
    console.log(`[Circuit Breaker] ${this.apiName}: Circuit OPENED due to failures`);
    
    // Trigger alert/notification
    this.notifyCircuitOpened();
  }

  /**
   * Close the circuit
   */
  closeCircuit() {
    this.state = CIRCUIT_STATES.CLOSED;
    this.failureCount = 0;
    this.lastFailureTime = null;
    console.log(`[Circuit Breaker] ${this.apiName}: Circuit CLOSED - service recovered`);
    
    // Trigger recovery notification
    this.notifyCircuitClosed();
  }

  /**
   * Reset monitoring period statistics
   */
  checkMonitoringPeriodReset() {
    const now = Date.now();
    if (now - this.lastResetTime >= this.config.monitoringPeriod) {
      this.requestCount = 0;
      this.successCount = 0;
      this.lastResetTime = now;
      console.log(`[Circuit Breaker] ${this.apiName}: Monitoring period reset`);
    }
  }

  /**
   * Notify when circuit opens (implement alerting here)
   */
  notifyCircuitOpened() {
    // TODO: Implement alerting (email, Slack, monitoring system)
    console.error(`🚨 ALERT: Circuit breaker OPENED for ${this.apiName}`);
  }

  /**
   * Notify when circuit closes (recovery)
   */
  notifyCircuitClosed() {
    // TODO: Implement recovery notification
    console.log(`✅ RECOVERY: Circuit breaker CLOSED for ${this.apiName}`);
  }

  /**
   * Get current circuit breaker status
   */
  getStatus() {
    return {
      apiName: this.apiName,
      state: this.state,
      failureCount: this.failureCount,
      successCount: this.successCount,
      requestCount: this.requestCount,
      lastFailureTime: this.lastFailureTime,
      lastResetTime: this.lastResetTime
    };
  }
}

/**
 * Get or create circuit breaker for an API
 */
function getCircuitBreaker(apiName) {
  if (!circuitBreakerStore.has(apiName)) {
    const config = CIRCUIT_BREAKER_CONFIGS[apiName] || CIRCUIT_BREAKER_CONFIGS.GOOGLE_AI;
    circuitBreakerStore.set(apiName, new CircuitBreaker(apiName, config));
  }
  return circuitBreakerStore.get(apiName);
}

/**
 * Execute function with circuit breaker protection
 */
export async function withCircuitBreaker(apiName, fn) {
  const circuitBreaker = getCircuitBreaker(apiName);
  return circuitBreaker.execute(fn);
}

/**
 * Get status of all circuit breakers
 */
export function getCircuitBreakerStatus() {
  const status = {};
  for (const [apiName, breaker] of circuitBreakerStore.entries()) {
    status[apiName] = breaker.getStatus();
  }
  return status;
}

/**
 * Reset a specific circuit breaker
 */
export function resetCircuitBreaker(apiName) {
  const breaker = circuitBreakerStore.get(apiName);
  if (breaker) {
    breaker.closeCircuit();
    console.log(`[Circuit Breaker] ${apiName}: Manually reset`);
  }
}

/**
 * Enhanced API call with both retry logic and circuit breaker
 */
export async function resilientApiCall(apiName, fn) {
  return withCircuitBreaker(apiName, async () => {
    const { resilientFetch } = await import('./apiResilience.js');
    return fn(resilientFetch);
  });
}
