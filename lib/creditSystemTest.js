/**
 * Credit System Test Utilities
 * Use these functions to test the new atomic credit system
 */

import { db } from '@/configs/db';
import { users, creditTransactions, videoData } from '@/configs/schema';
import { eq, sql } from 'drizzle-orm';

/**
 * Test function to verify credit system integrity
 */
export async function testCreditSystemIntegrity(userId) {
  try {
    console.log(`Testing credit system integrity for user: ${userId}`);
    
    // 1. Get current user balance
    const [user] = await db
      .select({
        currentCreditBalance: users.currentCreditBalance,
        legacyCredits: users.credits
      })
      .from(users)
      .where(eq(users.clerkId, userId))
      .limit(1);

    if (!user) {
      throw new Error('User not found');
    }

    console.log(`Current balance: ${user.currentCreditBalance}, Legacy: ${user.legacyCredits}`);

    // 2. Get all transactions for this user
    const transactions = await db
      .select()
      .from(creditTransactions)
      .where(eq(creditTransactions.userId, userId))
      .orderBy(creditTransactions.createdAt);

    console.log(`Found ${transactions.length} transactions`);

    // 3. Calculate expected balance
    const initialBalance = 10; // Default starting balance
    let expectedBalance = initialBalance;
    
    for (const transaction of transactions) {
      expectedBalance += transaction.amount;
      console.log(`Transaction: ${transaction.transactionType}, Amount: ${transaction.amount}, Expected Balance: ${expectedBalance}`);
    }

    // 4. Check consistency
    const isConsistent = user.currentCreditBalance === expectedBalance;
    
    console.log(`Expected balance: ${expectedBalance}`);
    console.log(`Actual balance: ${user.currentCreditBalance}`);
    console.log(`System is consistent: ${isConsistent}`);

    return {
      userId,
      currentBalance: user.currentCreditBalance,
      expectedBalance,
      isConsistent,
      discrepancy: user.currentCreditBalance - expectedBalance,
      transactionCount: transactions.length
    };

  } catch (error) {
    console.error('Credit system test failed:', error);
    return {
      userId,
      error: error.message
    };
  }
}

/**
 * Test function to simulate a video generation workflow
 */
export async function testVideoGenerationWorkflow(userId, workflowType = 'AI_VIDEO') {
  try {
    console.log(`Testing video generation workflow for user: ${userId}`);
    
    // 1. Check initial balance
    const initialBalance = await getCurrentBalance(userId);
    console.log(`Initial balance: ${initialBalance}`);

    // 2. Simulate video generation cost
    const cost = getCostForWorkflow(workflowType);
    console.log(`Video generation cost: ${cost}`);

    if (initialBalance < cost) {
      throw new Error('Insufficient credits for test');
    }

    // 3. Simulate atomic debit transaction
    const result = await db.transaction(async (tx) => {
      // Fetch user with lock
      const [user] = await tx
        .select()
        .from(users)
        .where(eq(users.clerkId, userId))
        .for('update');

      if (!user) {
        throw new Error('User not found');
      }

      const newBalance = user.currentCreditBalance - cost;

      // Update balance
      await tx
        .update(users)
        .set({ 
          currentCreditBalance: newBalance,
          updatedAt: new Date()
        })
        .where(eq(users.clerkId, userId));

      // Insert transaction
      const [transaction] = await tx
        .insert(creditTransactions)
        .values({
          userId,
          transactionType: 'DEBIT',
          amount: -cost,
          balanceBefore: user.currentCreditBalance,
          balanceAfter: newBalance,
          relatedEntityType: 'VIDEO',
          notes: `Test video generation: ${workflowType}`,
          metadata: { workflowType, test: true }
        })
        .returning();

      return { transaction, newBalance };
    });

    console.log(`Transaction completed. New balance: ${result.newBalance}`);

    // 4. Verify integrity after transaction
    const integrityCheck = await testCreditSystemIntegrity(userId);
    
    return {
      success: true,
      initialBalance,
      cost,
      newBalance: result.newBalance,
      transactionId: result.transaction.id,
      integrityCheck
    };

  } catch (error) {
    console.error('Video generation workflow test failed:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Helper function to get current balance
 */
async function getCurrentBalance(userId) {
  const [user] = await db
    .select({
      currentCreditBalance: users.currentCreditBalance
    })
    .from(users)
    .where(eq(users.clerkId, userId))
    .limit(1);

  return user?.currentCreditBalance || 0;
}

/**
 * Helper function to get cost for workflow
 */
function getCostForWorkflow(workflowType) {
  const baseCosts = {
    'AI_VIDEO': 5,
    'MEME_VIDEO': 2,
    'UGC_VIDEO': 8,
    'NARRATOR_VIDEO': 4,
    'REDDIT_VIDEO': 3,
    'TWITTER_VIDEO': 3,
    'STOCK_MEDIA_VIDEO': 6
  };
  
  return baseCosts[workflowType] || 5;
}

/**
 * Test function to simulate credit refund
 */
export async function testCreditRefund(userId, amount, reason = 'Test refund') {
  try {
    console.log(`Testing credit refund for user: ${userId}, amount: ${amount}`);
    
    const initialBalance = await getCurrentBalance(userId);
    
    const result = await db.transaction(async (tx) => {
      const [user] = await tx
        .select()
        .from(users)
        .where(eq(users.clerkId, userId))
        .for('update');

      if (!user) {
        throw new Error('User not found');
      }

      const newBalance = user.currentCreditBalance + amount;

      await tx
        .update(users)
        .set({ 
          currentCreditBalance: newBalance,
          updatedAt: new Date()
        })
        .where(eq(users.clerkId, userId));

      const [transaction] = await tx
        .insert(creditTransactions)
        .values({
          userId,
          transactionType: 'REFUND',
          amount: amount,
          balanceBefore: user.currentCreditBalance,
          balanceAfter: newBalance,
          relatedEntityType: 'VIDEO',
          notes: reason,
          metadata: { test: true }
        })
        .returning();

      return { transaction, newBalance };
    });

    console.log(`Refund completed. New balance: ${result.newBalance}`);

    return {
      success: true,
      initialBalance,
      refundAmount: amount,
      newBalance: result.newBalance,
      transactionId: result.transaction.id
    };

  } catch (error) {
    console.error('Credit refund test failed:', error);
    return {
      success: false,
      error: error.message
    };
  }
}
