import { google } from 'googleapis';
import { db } from '@/configs/db';
import { userSocialAccounts, scheduledPosts } from '@/configs/schema';
import { eq } from 'drizzle-orm';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { S3Client, GetObjectCommand } from '@aws-sdk/client-s3';
import { Readable } from 'stream';

const GOOGLE_CLIENT_ID = process.env.GOOGLE_CLIENT_ID;
const GOOGLE_CLIENT_SECRET = process.env.GOOGLE_CLIENT_SECRET;
const GOOGLE_REDIRECT_URI = process.env.GOOGLE_REDIRECT_URI; // Ensure this matches your callback URI

const s3Client = new S3Client({
  region: process.env.AWS_REGION,
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
  },
});

const S3_BUCKET_NAME = process.env.S3_BUCKET_NAME; // Your S3 bucket name

class YouTubePublisher {
  constructor(socialAccountId, clerkId) {
    this.socialAccountId = socialAccountId;
    this.clerkId = clerkId;
    this.oauth2Client = new google.auth.OAuth2(
      GOOGLE_CLIENT_ID,
      GOOGLE_CLIENT_SECRET,
      GOOGLE_REDIRECT_URI
    );
  }

  async #refreshAccessToken(account) {
    this.oauth2Client.setCredentials({
      refresh_token: account.refreshToken,
    });

    try {
      const { tokens } = await this.oauth2Client.refreshAccessToken();
      this.oauth2Client.setCredentials(tokens);

      // Update the database with new tokens and expiry
      const tokenExpiresAt = tokens.expiry_date ? new Date(tokens.expiry_date) : null;
      await db.update(userSocialAccounts)
        .set({
          accessToken: tokens.access_token,
          tokenExpiresAt: tokenExpiresAt,
          updatedAt: new Date(),
        })
        .where(eq(userSocialAccounts.id, this.socialAccountId));

      return tokens.access_token;
    } catch (error) {
      console.error('Error refreshing YouTube access token:', error);
      throw new Error('Failed to refresh YouTube access token.');
    }
  }

  async #getAuthenticatedClient() {
    const account = await db.query.userSocialAccounts.findFirst({
      where: eq(userSocialAccounts.id, this.socialAccountId) && eq(userSocialAccounts.clerkId, this.clerkId),
    });

    if (!account) {
      throw new Error('Social account not found or unauthorized.');
    }

    let currentAccessToken = account.accessToken;

    // Check if token is expired or about to expire (e.g., within 5 minutes)
    const fiveMinutesFromNow = new Date(Date.now() + 5 * 60 * 1000);
    if (!account.tokenExpiresAt || account.tokenExpiresAt <= fiveMinutesFromNow) {
      if (!account.refreshToken) {
        throw new Error('Refresh token not available. User needs to re-authenticate.');
      }
      currentAccessToken = await this.#refreshAccessToken(account);
    }

    this.oauth2Client.setCredentials({
      access_token: currentAccessToken,
      refresh_token: account.refreshToken, // Keep refresh token set
      expiry_date: account.tokenExpiresAt?.getTime(),
    });

    return google.youtube({
      version: 'v3',
      auth: this.oauth2Client,
    });
  }

  async #downloadFileFromS3(s3Key) {
    if (!S3_BUCKET_NAME) {
      throw new Error('S3_BUCKET_NAME environment variable is not set.');
    }
    const command = new GetObjectCommand({
      Bucket: S3_BUCKET_NAME,
      Key: s3Key,
    });
    const signedUrl = await getSignedUrl(s3Client, command, { expiresIn: 3600 }); // URL valid for 1 hour

    console.log(`Attempting to download file from S3 via signed URL: ${signedUrl}`);
    const response = await fetch(signedUrl);
    if (!response.ok) {
      throw new Error(`Failed to download file from S3: ${response.statusText}`);
    }
    // Return as a Readable stream
    return Readable.fromWeb(response.body);
  }

  async publish(postDetails) {
    const youtube = await this.#getAuthenticatedClient();

    const {
      title,
      description,
      tags,
      privacyStatus,
      categoryId,
      madeForKids,
    } = postDetails.platformSpecificData;

    const videoS3Key = postDetails.videoFilePathOnServer; // Now it's an S3 key
    const thumbnailS3Key = postDetails.thumbnailFilePathOnServer; // Now it's an S3 key

    if (!videoS3Key) {
      throw new Error('Video S3 key is missing for YouTube upload.');
    }

    let videoStream;
    try {
      videoStream = await this.#downloadFileFromS3(videoS3Key);
    } catch (downloadError) {
      console.error('Failed to get video stream from S3:', downloadError);
      throw new Error(`Failed to access video file from S3: ${downloadError.message}`);
    }

    const videoMetadata = {
      snippet: {
        title: title,
        description: description,
        tags: tags,
        categoryId: categoryId,
      },
      status: {
        privacyStatus: privacyStatus,
        selfDeclaredMadeForKids: madeForKids,
      },
    };

    try {
      // Initiate resumable upload
      const videoInsertResponse = await youtube.videos.insert({
        part: 'snippet,status',
        requestBody: videoMetadata,
        media: {
          body: videoStream,
        },
      });

      const youtubeVideoId = videoInsertResponse.data.id;
      console.log(`Video uploaded to YouTube. ID: ${youtubeVideoId}`);

      // Upload thumbnail if provided
      if (thumbnailS3Key) {
        let thumbnailStream;
        try {
          thumbnailStream = await this.#downloadFileFromS3(thumbnailS3Key);
        } catch (thumbDownloadError) {
          console.warn(`Could not download thumbnail from S3: ${thumbDownloadError.message}. Proceeding without thumbnail.`);
        }

        if (thumbnailStream) {
          try {
            await youtube.thumbnails.set({
              videoId: youtubeVideoId,
              media: {
                body: thumbnailStream,
              },
            });
            console.log(`Thumbnail set for video ID: ${youtubeVideoId}`);
          } catch (thumbnailError) {
            console.error(`Failed to set thumbnail for video ID ${youtubeVideoId}:`, thumbnailError);
            // Do not re-throw, as video upload was successful
          }
        }
      }

      return { success: true, platformPostId: youtubeVideoId, message: 'Video uploaded to YouTube successfully.' };

    } catch (error) {
      console.error('Error uploading video to YouTube:', error.response ? error.response.data : error.message);
      const errorMessage = error.response?.data?.error?.message || error.message;
      throw new Error(`YouTube upload failed: ${errorMessage}`);
    }
  }
}

export default YouTubePublisher;
