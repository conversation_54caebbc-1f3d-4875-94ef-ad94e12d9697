/**
 * Inngest API Helper Functions with Resilience
 * Integrates retry logic and circuit breakers with Inngest workflows
 */

import { resilientApiCall, withCircuitBreaker } from './circuitBreaker.js';
import { callGoogleAI, callElevenLabs, callPexels, callPixabay, callCaptionsAI } from './apiResilience.js';

/**
 * Enhanced Inngest step wrapper for API calls
 * Provides automatic retries, circuit breaking, and error handling
 */
export function createResilientApiStep(stepName, apiName, apiFunction) {
  return async (step, ...args) => {
    return step.run(stepName, async () => {
      try {
        return await resilientApiCall(apiName, async () => {
          return await apiFunction(...args);
        });
      } catch (error) {
        // Enhanced error handling for Inngest
        if (error.circuitBreakerOpen) {
          // Circuit breaker is open - this is a fast failure
          console.log(`[Inngest] ${stepName}: Circuit breaker open for ${apiName}, failing fast`);
          throw new Error(`Service ${apiName} is currently unavailable (circuit breaker open)`);
        }

        // Check if this is a rate limit error that we should handle gracefully
        if (error.message.includes('429') || error.message.includes('rate limit')) {
          console.log(`[Inngest] ${stepName}: Rate limit hit for ${apiName}`);
          
          // For certain APIs, we might want to pause the entire workflow
          if (apiName === 'PEXELS' || apiName === 'ELEVENLABS') {
            throw new Error(`Rate limit exceeded for ${apiName}. Workflow will retry later.`);
          }
        }

        // Re-throw the error for Inngest to handle
        throw error;
      }
    });
  };
}

/**
 * Google AI API call with resilience for Inngest
 */
export const resilientGoogleAI = createResilientApiStep(
  'call-google-ai',
  'GOOGLE_AI',
  callGoogleAI
);

/**
 * ElevenLabs TTS API call with resilience for Inngest
 */
export const resilientElevenLabs = createResilientApiStep(
  'call-elevenlabs-tts',
  'ELEVENLABS',
  callElevenLabs
);

/**
 * Pexels API call with resilience for Inngest
 */
export const resilientPexels = createResilientApiStep(
  'call-pexels-api',
  'PEXELS',
  callPexels
);

/**
 * Pixabay API call with resilience for Inngest
 */
export const resilientPixabay = createResilientApiStep(
  'call-pixabay-api',
  'PIXABAY',
  callPixabay
);

/**
 * Captions AI API call with resilience for Inngest
 */
export const resilientCaptionsAI = createResilientApiStep(
  'call-captions-ai',
  'CAPTIONS_AI',
  callCaptionsAI
);

/**
 * Example: Enhanced AI Video Generation with Resilient APIs
 */
export function createResilientAIVideoSteps() {
  return {
    // Step 1: Generate script with Google AI
    generateScript: async (step, topic, style) => {
      return resilientGoogleAI(step, 
        `Create a ${style} video script about: ${topic}. Make it engaging and under 150 words.`
      );
    },

    // Step 2: Generate TTS with ElevenLabs
    generateTTS: async (step, script, voiceId) => {
      return resilientElevenLabs(step, script, voiceId, {
        voice_settings: {
          stability: 0.5,
          similarity_boost: 0.5
        }
      });
    },

    // Step 3: Get images with fallback between Pexels and Pixabay
    getImages: async (step, searchTerms) => {
      return step.run('get-images-with-fallback', async () => {
        try {
          // Try Pexels first
          return await withCircuitBreaker('PEXELS', async () => {
            return await callPexels(searchTerms.join(' '), { perPage: 10 });
          });
        } catch (pexelsError) {
          console.log('[Inngest] Pexels failed, trying Pixabay fallback');
          
          // Fallback to Pixabay
          try {
            return await withCircuitBreaker('PIXABAY', async () => {
              return await callPixabay(searchTerms.join(' '), { perPage: 20 });
            });
          } catch (pixabayError) {
            console.error('[Inngest] Both image APIs failed:', { pexelsError, pixabayError });
            throw new Error('All image APIs are currently unavailable');
          }
        }
      });
    },

    // Step 4: Generate UGC video with Captions AI
    generateUGCVideo: async (step, script, avatarId) => {
      return resilientCaptionsAI(step, 'generate', {
        script,
        avatar_id: avatarId,
        voice_settings: {
          speed: 1.0,
          pitch: 1.0
        }
      });
    }
  };
}

/**
 * Rate limit aware batch processing
 * Processes items in batches with respect to API rate limits
 */
export async function rateLimitAwareBatch(step, items, apiName, processFn, batchConfig = {}) {
  const {
    batchSize = 5,
    delayBetweenBatches = 1000,
    maxConcurrent = 2
  } = batchConfig;

  return step.run(`batch-process-${apiName.toLowerCase()}`, async () => {
    const results = [];
    
    // Process items in batches
    for (let i = 0; i < items.length; i += batchSize) {
      const batch = items.slice(i, i + batchSize);
      
      console.log(`[Batch Processing] Processing batch ${Math.floor(i / batchSize) + 1} for ${apiName}`);
      
      // Process batch with limited concurrency
      const batchPromises = batch.map(async (item, index) => {
        // Stagger requests to avoid hitting rate limits
        if (index > 0) {
          await new Promise(resolve => setTimeout(resolve, index * 200));
        }
        
        return withCircuitBreaker(apiName, async () => {
          return await processFn(item);
        });
      });

      // Wait for batch to complete
      const batchResults = await Promise.allSettled(batchPromises);
      
      // Collect successful results and log failures
      batchResults.forEach((result, index) => {
        if (result.status === 'fulfilled') {
          results.push(result.value);
        } else {
          console.error(`[Batch Processing] Item ${i + index} failed:`, result.reason);
        }
      });

      // Delay between batches (except for the last batch)
      if (i + batchSize < items.length) {
        console.log(`[Batch Processing] Waiting ${delayBetweenBatches}ms before next batch`);
        await new Promise(resolve => setTimeout(resolve, delayBetweenBatches));
      }
    }

    return results;
  });
}

/**
 * API health check for monitoring
 */
export async function checkAPIHealth(step) {
  return step.run('api-health-check', async () => {
    const { getCircuitBreakerStatus } = await import('./circuitBreaker.js');
    
    const healthStatus = {
      timestamp: new Date().toISOString(),
      circuitBreakers: getCircuitBreakerStatus(),
      overallHealth: 'healthy'
    };

    // Determine overall health
    const openCircuits = Object.values(healthStatus.circuitBreakers)
      .filter(cb => cb.state === 'OPEN');

    if (openCircuits.length > 0) {
      healthStatus.overallHealth = 'degraded';
      healthStatus.openCircuits = openCircuits.map(cb => cb.apiName);
    }

    // Log health status
    console.log('[API Health Check]', healthStatus);

    return healthStatus;
  });
}

/**
 * Emergency fallback for critical workflows
 * Provides alternative paths when primary APIs are down
 */
export function createEmergencyFallbacks() {
  return {
    // Fallback for script generation
    scriptFallback: async (topic, style) => {
      const templates = {
        professional: `Professional video about ${topic}. Key points: Introduction, main content, conclusion.`,
        casual: `Hey everyone! Today we're talking about ${topic}. This is really interesting because...`,
        educational: `In this video, we'll learn about ${topic}. Let's start with the basics...`
      };
      
      return templates[style] || templates.professional;
    },

    // Fallback for images (use default/placeholder images)
    imageFallback: async (searchTerms) => {
      return {
        photos: [
          { src: { large: '/api/placeholder/1080x1920' } },
          { src: { large: '/api/placeholder/1080x1920' } },
          { src: { large: '/api/placeholder/1080x1920' } }
        ]
      };
    },

    // Fallback for TTS (use system TTS or pre-recorded audio)
    ttsFallback: async (text) => {
      // Could integrate with system TTS or return pre-recorded segments
      return {
        audioUrl: '/api/fallback-tts',
        duration: Math.ceil(text.length / 10) // Rough estimate
      };
    }
  };
}
