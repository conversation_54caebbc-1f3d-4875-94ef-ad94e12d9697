/**
 * Centralized Video Template Configurations
 * Eliminates duplicate template data across components
 * Used by: VideoTemplatesWidget, VideoCreationBentoGrid, FloatingActionButton
 */

import { 
  Bot, 
  User, 
  Images, 
  Mic, 
  FileText, 
  Film,
  Video,
  TrendingUp,
  MessageSquare,
  Camera,
  Music
} from 'lucide-react';

/**
 * Complete video template configurations
 * Centralized source of truth for all video creation options
 */
export const VIDEO_TEMPLATES = [
  {
    id: 'ai-video',
    name: 'AI Video',
    description: 'Generate from script or topic',
    href: '/dashboard/create-new-short/create-ai-video',
    icon: Bot,
    color: 'bg-blue-500',
    textColor: 'text-blue-700',
    bgColor: 'bg-blue-50',
    borderColor: 'border-blue-200',
    credits: 10,
    duration: '2-4 min',
    badge: 'Popular',
    badgeColor: 'bg-blue-100 text-blue-700',
    popular: true,
    category: 'ai-powered',
    meta: 'AI Script',
    tags: ['AI', 'Script', 'Automated'],
    cta: 'Create AI Video →'
  },
  {
    id: 'ai-ugc-video',
    name: 'AI UGC Video',
    description: 'AI avatar with backgrounds',
    href: '/dashboard/create-new-short/create-ai-ugc-video',
    icon: User,
    color: 'bg-green-500',
    textColor: 'text-green-700',
    bgColor: 'bg-green-50',
    borderColor: 'border-green-200',
    credits: 15,
    duration: '1-3 min',
    badge: 'New',
    badgeColor: 'bg-green-100 text-green-700',
    popular: true,
    category: 'ai-powered',
    meta: 'AI Avatar',
    tags: ['UGC', 'Avatar', 'Personal'],
    cta: 'Create UGC →'
  },
  {
    id: 'meme-video',
    name: 'Meme Video',
    description: 'Viral content creation',
    href: '/dashboard/create-new-short/create-meme-video',
    icon: Images,
    color: 'bg-purple-500',
    textColor: 'text-purple-700',
    bgColor: 'bg-purple-50',
    borderColor: 'border-purple-200',
    credits: 5,
    duration: '1-2 min',
    badge: 'Quick',
    badgeColor: 'bg-purple-100 text-purple-700',
    popular: true,
    category: 'social-media',
    meta: 'Viral Content',
    tags: ['Meme', 'Viral', 'Social'],
    cta: 'Make Meme →'
  },
  {
    id: 'podcast-clipper',
    name: 'Podcast Clipper',
    description: 'Extract highlights',
    href: '/dashboard/create-new-short/create-podcast-clipper',
    icon: Mic,
    color: 'bg-orange-500',
    textColor: 'text-orange-700',
    bgColor: 'bg-orange-50',
    borderColor: 'border-orange-200',
    credits: 8,
    duration: '30s-2min',
    badge: 'Pro',
    badgeColor: 'bg-orange-100 text-orange-700',
    popular: true,
    category: 'content-editing',
    meta: 'Audio Clips',
    tags: ['Podcast', 'Audio', 'Clips'],
    cta: 'Clip Podcast →'
  },
  {
    id: 'reddit-post-video',
    name: 'Reddit Post Video',
    description: 'Reddit posts to video',
    href: '/dashboard/create-new-short/create-reddit-post-video',
    icon: MessageSquare,
    color: 'bg-red-500',
    textColor: 'text-red-700',
    bgColor: 'bg-red-50',
    borderColor: 'border-red-200',
    credits: 6,
    duration: '30s-1min',
    badge: 'Trending',
    badgeColor: 'bg-red-100 text-red-700',
    popular: false,
    category: 'social-media',
    meta: 'Social Posts',
    tags: ['Reddit', 'Social', 'Posts'],
    cta: 'Create Reddit Video →'
  },
  {
    id: 'twitter-post-video',
    name: 'Twitter Post Video',
    description: 'Tweets to video format',
    href: '/dashboard/create-new-short/create-twitter-post-video',
    icon: MessageSquare,
    color: 'bg-sky-500',
    textColor: 'text-sky-700',
    bgColor: 'bg-sky-50',
    borderColor: 'border-sky-200',
    credits: 6,
    duration: '30s-1min',
    badge: 'Social',
    badgeColor: 'bg-sky-100 text-sky-700',
    popular: false,
    category: 'social-media',
    meta: 'Social Posts',
    tags: ['Twitter', 'Social', 'Posts'],
    cta: 'Create Tweet Video →'
  },
  {
    id: 'stock-media-video',
    name: 'Stock Media Video',
    description: 'Professional stock footage',
    href: '/dashboard/create-new-short/create-stock-media-video',
    icon: Camera,
    color: 'bg-indigo-500',
    textColor: 'text-indigo-700',
    bgColor: 'bg-indigo-50',
    borderColor: 'border-indigo-200',
    credits: 12,
    duration: '1-3 min',
    badge: 'Pro',
    badgeColor: 'bg-indigo-100 text-indigo-700',
    popular: false,
    category: 'professional',
    meta: 'Stock Footage',
    tags: ['Stock', 'Professional', 'Media'],
    cta: 'Use Stock Media →'
  },
  {
    id: 'narrator-video',
    name: 'Narrator Videos',
    description: 'Add AI-powered narration and voiceovers to your content',
    href: '/dashboard/create-new-short/create-new-narrator-short',
    icon: Music,
    color: 'bg-indigo-500',
    textColor: 'text-indigo-700',
    bgColor: 'bg-indigo-50',
    borderColor: 'border-indigo-200',
    credits: 10,
    duration: '2-5 min',
    badge: 'Voice AI',
    badgeColor: 'bg-indigo-100 text-indigo-700',
    popular: false,
    category: 'ai-powered',
    meta: 'Voice AI',
    tags: ['Voice', 'Narration', 'AI'],
    cta: 'Add Voice →'
  }
];

/**
 * Filter templates by category
 */
export const getTemplatesByCategory = (category) => {
  return VIDEO_TEMPLATES.filter(template => template.category === category);
};

/**
 * Get popular templates
 */
export const getPopularTemplates = () => {
  return VIDEO_TEMPLATES.filter(template => template.popular);
};

/**
 * Get template by ID
 */
export const getTemplateById = (id) => {
  return VIDEO_TEMPLATES.find(template => template.id === id);
};

/**
 * Get templates for floating action button (simplified structure)
 */
export const getFloatingActionTemplates = () => {
  return VIDEO_TEMPLATES.map(template => ({
    label: template.name,
    href: template.href,
    Icon: template.icon,
    description: template.description
  }));
};

/**
 * Template categories for organization
 */
export const TEMPLATE_CATEGORIES = {
  'ai-powered': {
    name: 'AI-Powered',
    description: 'Videos generated using artificial intelligence',
    icon: Bot
  },
  'social-media': {
    name: 'Social Media',
    description: 'Content optimized for social platforms',
    icon: MessageSquare
  },
  'content-editing': {
    name: 'Content Editing',
    description: 'Tools for editing and enhancing existing content',
    icon: Film
  },
  'professional': {
    name: 'Professional',
    description: 'High-quality content for business use',
    icon: Camera
  }
};

/**
 * Default video generation cost (can be overridden per template)
 */
export const DEFAULT_VIDEO_GENERATION_COST = 10;
