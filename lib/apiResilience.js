/**
 * API Resilience Utilities for External API Calls
 * Handles rate limits, retries, and circuit breaker patterns
 */

/**
 * Retry configuration for different APIs
 */
const API_RETRY_CONFIGS = {
  GOOGLE_AI: {
    maxRetries: 5,
    baseDelay: 1000,
    maxDelay: 30000,
    retryableStatusCodes: [429, 500, 502, 503, 504],
    useRetryAfterHeader: true
  },
  ELEVENLABS: {
    maxRetries: 3,
    baseDelay: 2000,
    maxDelay: 60000,
    retryableStatusCodes: [429, 500, 502, 503, 504],
    useRetryAfterHeader: true
  },
  PEXELS: {
    maxRetries: 3,
    baseDelay: 1800000, // 30 minutes for rate limit reset
    maxDelay: 3600000,  // 1 hour max
    retryableStatusCodes: [429, 500, 502, 503, 504],
    useRetryAfterHeader: false
  },
  PIXABAY: {
    maxRetries: 3,
    baseDelay: 1000,
    maxDelay: 10000,
    retryableStatusCodes: [429, 500, 502, 503, 504],
    useRetryAfterHeader: false
  },
  CAPTIONS_AI: {
    maxRetries: 2,
    baseDelay: 5000,
    maxDelay: 30000,
    retryableStatusCodes: [429, 500, 502, 503, 504],
    useRetryAfterHeader: true
  },
  REMOTION_LAMBDA: {
    maxRetries: 3,
    baseDelay: 2000,
    maxDelay: 15000,
    retryableStatusCodes: [429, 500, 502, 503, 504],
    useRetryAfterHeader: true
  }
};

/**
 * Enhanced fetch with retry logic and exponential backoff
 */
export async function resilientFetch(url, options = {}, apiType = 'DEFAULT') {
  const config = API_RETRY_CONFIGS[apiType] || API_RETRY_CONFIGS.GOOGLE_AI;
  let lastError;

  for (let attempt = 0; attempt <= config.maxRetries; attempt++) {
    try {
      console.log(`[API Resilience] Attempt ${attempt + 1}/${config.maxRetries + 1} for ${apiType}: ${url}`);
      
      const response = await fetch(url, {
        ...options,
        headers: {
          'User-Agent': 'AI-Video-Platform/1.0',
          ...options.headers
        }
      });

      // Success case
      if (response.ok) {
        console.log(`[API Resilience] Success for ${apiType} after ${attempt + 1} attempts`);
        return response;
      }

      // Check if this is a retryable error
      if (!config.retryableStatusCodes.includes(response.status)) {
        console.log(`[API Resilience] Non-retryable error ${response.status} for ${apiType}`);
        return response; // Return non-retryable errors immediately
      }

      // Handle rate limiting
      if (response.status === 429) {
        const retryAfter = response.headers.get('Retry-After');
        const rateLimitReset = response.headers.get('X-Ratelimit-Reset');
        
        let delay = calculateBackoffDelay(attempt, config);
        
        if (config.useRetryAfterHeader && retryAfter) {
          delay = parseInt(retryAfter) * 1000; // Convert to milliseconds
        } else if (rateLimitReset) {
          const resetTime = parseInt(rateLimitReset) * 1000;
          const currentTime = Date.now();
          delay = Math.max(resetTime - currentTime, delay);
        }

        console.log(`[API Resilience] Rate limited for ${apiType}. Waiting ${delay}ms before retry`);
        
        if (attempt < config.maxRetries) {
          await sleep(delay);
          continue;
        }
      }

      // For other retryable errors, use exponential backoff
      if (attempt < config.maxRetries) {
        const delay = calculateBackoffDelay(attempt, config);
        console.log(`[API Resilience] Error ${response.status} for ${apiType}. Retrying in ${delay}ms`);
        await sleep(delay);
        continue;
      }

      // Last attempt failed
      lastError = new Error(`API call failed after ${config.maxRetries + 1} attempts. Status: ${response.status}`);
      lastError.response = response;
      throw lastError;

    } catch (error) {
      lastError = error;
      
      // Network errors are always retryable
      if (attempt < config.maxRetries && (error.code === 'ECONNRESET' || error.code === 'ETIMEDOUT' || error.name === 'FetchError')) {
        const delay = calculateBackoffDelay(attempt, config);
        console.log(`[API Resilience] Network error for ${apiType}. Retrying in ${delay}ms`);
        await sleep(delay);
        continue;
      }
      
      // Non-retryable error or max attempts reached
      break;
    }
  }

  console.error(`[API Resilience] All retry attempts failed for ${apiType}:`, lastError);
  throw lastError;
}

/**
 * Calculate exponential backoff delay with jitter
 */
function calculateBackoffDelay(attempt, config) {
  const exponentialDelay = config.baseDelay * Math.pow(2, attempt);
  const jitter = Math.random() * 0.1 * exponentialDelay; // 10% jitter
  const delay = Math.min(exponentialDelay + jitter, config.maxDelay);
  return Math.floor(delay);
}

/**
 * Sleep utility
 */
function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Wrapper for Google AI API calls
 */
export async function callGoogleAI(prompt, options = {}) {
  const url = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent';
  
  const response = await resilientFetch(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'x-goog-api-key': process.env.GOOGLE_AI_API_KEY
    },
    body: JSON.stringify({
      contents: [{ parts: [{ text: prompt }] }],
      ...options
    })
  }, 'GOOGLE_AI');

  if (!response.ok) {
    throw new Error(`Google AI API error: ${response.status} ${response.statusText}`);
  }

  return response.json();
}

/**
 * Wrapper for ElevenLabs TTS API calls
 */
export async function callElevenLabs(text, voiceId, options = {}) {
  const url = `https://api.elevenlabs.io/v1/text-to-speech/${voiceId}`;
  
  const response = await resilientFetch(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'xi-api-key': process.env.ELEVENLABS_API_KEY
    },
    body: JSON.stringify({
      text,
      model_id: 'eleven_monolingual_v1',
      ...options
    })
  }, 'ELEVENLABS');

  if (!response.ok) {
    throw new Error(`ElevenLabs API error: ${response.status} ${response.statusText}`);
  }

  return response;
}

/**
 * Wrapper for Pexels API calls
 */
export async function callPexels(query, options = {}) {
  const params = new URLSearchParams({
    query,
    per_page: options.perPage || 15,
    page: options.page || 1,
    ...options
  });
  
  const url = `https://api.pexels.com/v1/search?${params}`;
  
  const response = await resilientFetch(url, {
    headers: {
      'Authorization': process.env.PEXELS_API_KEY
    }
  }, 'PEXELS');

  if (!response.ok) {
    throw new Error(`Pexels API error: ${response.status} ${response.statusText}`);
  }

  return response.json();
}

/**
 * Wrapper for Pixabay API calls
 */
export async function callPixabay(query, options = {}) {
  const params = new URLSearchParams({
    key: process.env.PIXABAY_API_KEY,
    q: query,
    image_type: 'photo',
    per_page: options.perPage || 20,
    ...options
  });
  
  const url = `https://pixabay.com/api/?${params}`;
  
  const response = await resilientFetch(url, {}, 'PIXABAY');

  if (!response.ok) {
    throw new Error(`Pixabay API error: ${response.status} ${response.statusText}`);
  }

  return response.json();
}

/**
 * Wrapper for Captions AI API calls
 */
export async function callCaptionsAI(endpoint, data, options = {}) {
  const url = `https://api.captions.ai/api/${endpoint}`;
  
  const response = await resilientFetch(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'x-api-key': process.env.CAPTIONS_API_KEY,
      ...options.headers
    },
    body: JSON.stringify(data)
  }, 'CAPTIONS_AI');

  if (!response.ok) {
    throw new Error(`Captions AI API error: ${response.status} ${response.statusText}`);
  }

  return response.json();
}
