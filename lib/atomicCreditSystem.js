/**
 * Shared Atomic Credit System Utilities
 * Used by all video generation workflows for consistent credit handling
 */

import { db } from '@/configs/db';
import { users, creditTransactions, videoData } from '@/configs/schema';
import { eq } from 'drizzle-orm';
import { inngest } from '@/app/inngest/client';

/**
 * Cost calculation for different workflow types
 */
export function getCostForWorkflow(workflowType, config = {}) {
  const baseCosts = {
    'AI_VIDEO': 5,
    'MEME_VIDEO': 2,
    'UGC_VIDEO': 8,
    'NARRATOR_VIDEO': 4,
    'REDDIT_VIDEO': 3,
    'TWITTER_VIDEO': 3,
    'STOCK_MEDIA_VIDEO': 6
  };
  
  let cost = baseCosts[workflowType] || 5;
  
  // Adjust based on configuration
  const estimatedDuration = estimateVideoDuration(config, workflowType);
  if (estimatedDuration > 60) {
    cost += Math.ceil((estimatedDuration - 60) / 30);
  }
  
  return cost;
}

/**
 * Estimate video duration based on configuration and workflow type
 */
function estimateVideoDuration(config, workflowType) {
  switch (workflowType) {
    case 'AI_VIDEO':
      const topicLength = config.topic?.length || 0;
      const estimatedWords = Math.max(50, topicLength / 5);
      return Math.min(Math.max(Math.ceil((estimatedWords / 150) * 60) + 10, 30), 180);
    
    case 'MEME_VIDEO':
      const duration = config.videoEndTime - config.videoStartTime;
      return Math.min(Math.max(duration || 30, 15), 120);
    
    case 'UGC_VIDEO':
      const scriptLength = (config.userScript || config.scriptTopic || '').length;
      return Math.min(Math.max(Math.ceil(scriptLength / 10), 30), 180);
    
    case 'NARRATOR_VIDEO':
      return 90; // Typically longer videos
    
    case 'REDDIT_VIDEO':
    case 'TWITTER_VIDEO':
      return 60; // Standard social media length
    
    case 'STOCK_MEDIA_VIDEO':
      return 120; // Typically longer curated videos
    
    default:
      return 60;
  }
}

/**
 * Get the correct Inngest event name for each workflow type
 */
export function getInngestEventName(workflowType) {
  const eventMap = {
    'AI_VIDEO': 'app/ai-video.generate',
    'MEME_VIDEO': 'app/meme-video.generate',
    'UGC_VIDEO': 'app/ai-ugc-video.generate',
    'NARRATOR_VIDEO': 'app/narrator.video.generate',
    'REDDIT_VIDEO': 'app/reddit-video.generate',
    'TWITTER_VIDEO': 'app/twitter-video.generate',
    'STOCK_MEDIA_VIDEO': 'app/stock-media-video.generate'
  };
  
  return eventMap[workflowType] || 'app/ai-video.generate';
}

/**
 * Atomic credit debit and video creation
 * Used by all server actions for consistent credit handling
 */
export async function atomicVideoGeneration(userId, workflowType, videoConfig) {
  // Determine cost
  const costInCredits = getCostForWorkflow(workflowType, videoConfig);

  // Non-transaction approach for Neon HTTP driver compatibility
  // Step 1: Fetch user and verify credits
  const [user] = await db
    .select()
    .from(users)
    .where(eq(users.clerkId, userId))
    .limit(1);

  if (!user) {
    throw new Error('User not found');
  }

  // Use credits field as fallback if currentCreditBalance doesn't exist
  const currentBalance = user.currentCreditBalance ?? user.credits ?? 0;

  // Verify sufficient credits
  if (currentBalance < costInCredits) {
    throw new Error(`Insufficient credits. Required: ${costInCredits}, Available: ${currentBalance}`);
  }

  // Calculate new balance
  const newBalance = currentBalance - costInCredits;

  try {
    // Step 2: Update user balance (using credits field for compatibility)
    const updateResult = await db
      .update(users)
      .set({
        credits: newBalance, // Use credits field for now
        updatedAt: new Date()
      })
      .where(eq(users.clerkId, userId))
      .returning();

    if (!updateResult || updateResult.length === 0) {
      throw new Error('Failed to update user credits');
    }

    // Step 3: Insert video record first (without credit transaction reference)
    console.log(`[Atomic Credit] Creating video record for user ${userId}, workflow: ${workflowType}`);

    const videoInsertData = {
      clerkId: userId,
      title: getVideoTitle(workflowType, videoConfig),
      topic: getVideoTopic(workflowType, videoConfig),
      script: videoConfig.script || videoConfig.userScript || '',
      videoStyle: getVideoStyle(workflowType, videoConfig),
      aspectRatio: videoConfig.aspectRatio || '9:16',
      templateId: videoConfig.templateId || workflowType,
      voice: videoConfig.voice || '',
      captionName: videoConfig.captionName || '',
      audioSpeed: videoConfig.audioSpeed || 1.0,
      backgroundMusic: videoConfig.backgroundMusic || '',
      estimatedDurationSeconds: estimateVideoDuration(videoConfig, workflowType),
      status: 'Pending',
      workflowType, // Use the promoted field
      originalPrompt: videoConfig.topic || videoConfig.userPrompt || '',
      workflow_data: {
        workflowType,
        ...videoConfig
      }
    };

    console.log(`[Atomic Credit] Video insert data:`, {
      clerkId: videoInsertData.clerkId,
      title: videoInsertData.title,
      workflowType: videoInsertData.workflowType,
      status: videoInsertData.status
    });

    const videoInsertResult = await db
      .insert(videoData)
      .values(videoInsertData)
      .returning();

    console.log(`[Atomic Credit] Video insert result:`, videoInsertResult);

    if (!videoInsertResult || videoInsertResult.length === 0) {
      throw new Error('Failed to create video record - no result returned');
    }

    const [video] = videoInsertResult;

    if (!video || !video.id) {
      throw new Error('Failed to create video record - invalid video object returned');
    }

    console.log(`[Atomic Credit] Video created successfully with ID: ${video.id}`);

    // Step 4: Create credit transaction record (if creditTransactions table exists)
    let creditTransaction = null;
    try {
      [creditTransaction] = await db
        .insert(creditTransactions)
        .values({
          userId,
          transactionType: 'DEBIT',
          amount: -costInCredits,
          balanceBefore: currentBalance,
          balanceAfter: newBalance,
          relatedEntityId: video.id.toString(),
          relatedEntityType: 'VIDEO',
          notes: `Video generation: ${workflowType}`,
          metadata: { workflowType, videoConfig }
        })
        .returning();
    } catch (creditTxError) {
      console.warn('[Atomic Credit] Credit transaction table not available, skipping:', creditTxError.message);
    }

    // If we get here, everything succeeded
    const result = { video, creditTransaction, newBalance };
    return result;
  } catch (error) {
    // If any step fails, try to rollback the credit update
    console.error('[Atomic Credit] Error during video generation, attempting rollback:', error);
    try {
      await db
        .update(users)
        .set({
          credits: currentBalance, // Restore original balance
          updatedAt: new Date()
        })
        .where(eq(users.clerkId, userId));
      console.log('[Atomic Credit] Successfully rolled back credit deduction');
    } catch (rollbackError) {
      console.error('[Atomic Credit] Failed to rollback credits:', rollbackError);
    }
    throw error;
  }
}




/**
 * Helper functions to extract video metadata
 */
function getVideoTitle(workflowType, config) {
  if (config.projectTitle) return config.projectTitle;
  if (config.title) return config.title;
  
  switch (workflowType) {
    case 'AI_VIDEO':
      return `AI Video: ${config.topic?.substring(0, 40) || 'Generated'}...`;
    case 'MEME_VIDEO':
      return `Meme Video: ${config.memeText?.substring(0, 40) || 'Generated'}...`;
    case 'UGC_VIDEO':
      return `UGC Video: ${config.scriptTopic?.substring(0, 40) || 'Generated'}...`;
    case 'NARRATOR_VIDEO':
      return `Narrator Video: ${config.title || 'Generated'}`;
    case 'REDDIT_VIDEO':
      return `Reddit Video: ${config.redditPost?.title?.substring(0, 40) || 'Generated'}...`;
    case 'TWITTER_VIDEO':
      return `Twitter Video: ${config.twitterPost?.text?.substring(0, 40) || 'Generated'}...`;
    case 'STOCK_MEDIA_VIDEO':
      return `Stock Video: ${config.userPrompt?.substring(0, 40) || 'Generated'}...`;
    default:
      return `${workflowType} Video`;
  }
}

function getVideoTopic(workflowType, config) {
  switch (workflowType) {
    case 'AI_VIDEO':
      return config.topic || '';
    case 'UGC_VIDEO':
      return config.scriptTopic || 'User Generated Content';
    case 'REDDIT_VIDEO':
      return config.redditPost?.title || 'Reddit Content';
    case 'TWITTER_VIDEO':
      return config.twitterPost?.text || 'Twitter Content';
    case 'STOCK_MEDIA_VIDEO':
      return config.userPrompt || 'Stock Media Content';
    default:
      return '';
  }
}

function getVideoStyle(workflowType, config) {
  switch (workflowType) {
    case 'AI_VIDEO':
      return config.videoStyle || 'AI Generated';
    case 'MEME_VIDEO':
      return 'Meme';
    case 'UGC_VIDEO':
      return 'AI UGC Talking Head';
    case 'NARRATOR_VIDEO':
      return 'Narrator';
    case 'REDDIT_VIDEO':
      return 'Reddit Story';
    case 'TWITTER_VIDEO':
      return 'Twitter Story';
    case 'STOCK_MEDIA_VIDEO':
      return 'Stock Media Compilation';
    default:
      return workflowType;
  }
}

/**
 * Trigger credit refund for failed video generation
 * Used by Inngest functions on failure
 */
export async function triggerCreditRefund(videoId, reason) {
  try {
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';
    const response = await fetch(`${baseUrl}/api/video/refund-credits`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.INNGEST_SIGNING_KEY}`
      },
      body: JSON.stringify({
        videoId,
        reason
      })
    });

    if (response.ok) {
      console.log(`[Credit Refund] Successfully triggered refund for video ${videoId}`);
      return true;
    } else {
      console.error(`[Credit Refund] Failed to trigger refund for video ${videoId}: ${response.status}`);
      return false;
    }
  } catch (error) {
    console.error(`[Credit Refund] Error triggering refund for video ${videoId}:`, error);
    return false;
  }
}

/**
 * Standard error handler for Inngest functions
 * Automatically triggers credit refunds on failure
 */
export function createInngestErrorHandler(workflowType) {
  return {
    onFailure: async ({ event, error }) => {
      console.error(`[Inngest] ${workflowType} failed for video ${event.data.videoId}:`, error);
      await triggerCreditRefund(event.data.videoId, `${workflowType} failed: ${error.message}`);
    }
  };
}

/**
 * Standard video record verification step for Inngest functions
 */
export async function verifyVideoRecord(db, videoData, eq, videoId, userId) {
  console.log(`[Inngest] Verifying video record exists for video ID: ${videoId}...`);

  try {
    // Select only essential fields to avoid missing column errors
    const [video] = await db
      .select({
        id: videoData.id,
        clerkId: videoData.clerkId,
        title: videoData.title,
        status: videoData.status,
        workflowType: videoData.workflowType,
        createdAt: videoData.createdAt,
        updatedAt: videoData.updatedAt
      })
      .from(videoData)
      .where(eq(videoData.id, videoId))
      .limit(1);

    if (!video) {
      throw new Error(`Video record not found for ID: ${videoId}`);
    }

    if (video.clerkId !== userId) {
      throw new Error(`Video record does not belong to user: ${userId}`);
    }

    console.log(`[Inngest] Video record verified for ID: ${videoId}`);
    return video;
  } catch (error) {
    console.error(`[Inngest] Error verifying video record for ID: ${videoId}:`, error);
    throw error;
  }
}
