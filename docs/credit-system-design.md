# Credit System Design - AI Video Generation Platform

## Overview

This document formalizes the credit system design with zero tolerance for calculation errors, ensuring high reliability and complete auditability for all credit operations.

## Core Principles

- **Atomicity**: All credit operations must be atomic (all-or-nothing)
- **Consistency**: Credit balances must always be accurate and reconcilable
- **Isolation**: Concurrent operations must not interfere with each other
- **Durability**: All transactions must be permanently recorded
- **Auditability**: Complete transaction history for reconciliation

---

## 1. Data Model (Drizzle Schema)

### 1.1 Users Table Enhancement

```typescript
// configs/schema.js - Enhanced Users table
import { pgTable, varchar, integer, timestamp, check } from 'drizzle-orm/pg-core';

export const users = pgTable('users', {
  clerkId: varchar('clerk_id').primaryKey(),
  name: varchar('name'),
  email: varchar('email').notNull().unique(),
  imageUrl: varchar('image_url'),
  currentCreditBalance: integer('current_credit_balance')
    .default(10)
    .notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
}, (table) => ({
  // Ensure credit balance is never negative
  creditBalanceCheck: check('credit_balance_non_negative', 
    sql`${table.currentCreditBalance} >= 0`),
}));
```

### 1.2 Credit Transactions Table

```typescript
// configs/schema.js - New CreditTransactions table
export const creditTransactions = pgTable('credit_transactions', {
  id: serial('id').primaryKey(),
  userId: varchar('user_id')
    .notNull()
    .references(() => users.clerkId, { onDelete: 'cascade' }),
  transactionType: varchar('transaction_type', { length: 50 })
    .notNull(), // 'DEBIT', 'CREDIT', 'REFUND', 'PURCHASE', 'ADJUSTMENT'
  amount: integer('amount').notNull(), // Positive for credits, negative for debits
  balanceBefore: integer('balance_before').notNull(),
  balanceAfter: integer('balance_after').notNull(),
  relatedEntityId: varchar('related_entity_id'), // videoId, orderId, etc.
  relatedEntityType: varchar('related_entity_type'), // 'VIDEO', 'ORDER', 'MANUAL'
  notes: text('notes'),
  metadata: jsonb('metadata'), // Additional context (workflow type, cost breakdown)
  createdAt: timestamp('created_at').defaultNow().notNull(),
}, (table) => ({
  // Indexes for performance
  userIdIdx: index('credit_transactions_user_id_idx').on(table.userId),
  transactionTypeIdx: index('credit_transactions_type_idx').on(table.transactionType),
  relatedEntityIdx: index('credit_transactions_entity_idx')
    .on(table.relatedEntityId, table.relatedEntityType),
  createdAtIdx: index('credit_transactions_created_at_idx').on(table.createdAt),
  
  // Ensure balance calculations are consistent
  balanceConsistencyCheck: check('balance_consistency', 
    sql`${table.balanceBefore} + ${table.amount} = ${table.balanceAfter}`),
}));
```

### 1.3 Enhanced Video Data Table

```typescript
// configs/schema.js - Enhanced videoData table
export const videoData = pgTable('video_data', {
  // ... existing fields ...
  costInCredits: integer('cost_in_credits').notNull().default(0),
  creditTransactionId: integer('credit_transaction_id')
    .references(() => creditTransactions.id),
  refundTransactionId: integer('refund_transaction_id')
    .references(() => creditTransactions.id),
  // ... rest of existing fields ...
});
```

---

## 2. API Endpoint Logic

### 2.1 Video Generation Initiation (Credit Debit)

```typescript
// app/api/video/initiate-generation/route.js
import { db } from '@/configs/db';
import { users, creditTransactions, videoData } from '@/configs/schema';
import { auth } from '@clerk/nextjs';
import { eq, sql } from 'drizzle-orm';

export async function POST(request) {
  try {
    // 1. Authentication
    const { userId } = auth();
    if (!userId) {
      return Response.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // 2. Input validation
    const { videoConfig, workflowType } = await request.json();
    if (!videoConfig || !workflowType) {
      return Response.json({ error: 'Invalid input' }, { status: 400 });
    }

    // 3. Determine cost
    const costInCredits = getCostForWorkflow(workflowType, videoConfig);

    // 4. Atomic database transaction
    const result = await db.transaction(async (tx) => {
      // 4a. Fetch user with pessimistic lock
      const [user] = await tx
        .select()
        .from(users)
        .where(eq(users.clerkId, userId))
        .for('update'); // Pessimistic lock

      if (!user) {
        throw new Error('User not found');
      }

      // 4b. Verify sufficient credits
      if (user.currentCreditBalance < costInCredits) {
        throw new Error('Insufficient credits');
      }

      // 4c. Calculate new balance
      const newBalance = user.currentCreditBalance - costInCredits;

      // 4d. Update user balance
      await tx
        .update(users)
        .set({ 
          currentCreditBalance: newBalance,
          updatedAt: new Date()
        })
        .where(eq(users.clerkId, userId));

      // 4e. Insert credit transaction
      const [creditTransaction] = await tx
        .insert(creditTransactions)
        .values({
          userId,
          transactionType: 'DEBIT',
          amount: -costInCredits,
          balanceBefore: user.currentCreditBalance,
          balanceAfter: newBalance,
          relatedEntityType: 'VIDEO',
          notes: `Video generation: ${workflowType}`,
          metadata: { workflowType, videoConfig }
        })
        .returning();

      // 4f. Insert video record
      const [video] = await tx
        .insert(videoData)
        .values({
          clerkId: userId,
          ...videoConfig,
          costInCredits,
          creditTransactionId: creditTransaction.id,
          status: 'Pending'
        })
        .returning();

      return { video, creditTransaction };
    });

    // 5. Enqueue Inngest job
    await inngest.send({
      name: 'video.generate',
      data: {
        videoId: result.video.id,
        userId,
        workflowType,
        ...videoConfig
      }
    });

    return Response.json({
      success: true,
      videoId: result.video.id,
      creditsRemaining: result.video.balanceAfter
    }, { status: 202 });

  } catch (error) {
    console.error('Video generation initiation failed:', error);
    
    if (error.message === 'Insufficient credits') {
      return Response.json({ error: 'Insufficient credits' }, { status: 402 });
    }
    
    return Response.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// Helper function to determine cost
function getCostForWorkflow(workflowType, config) {
  const baseCosts = {
    'AI_VIDEO': 5,
    'MEME_VIDEO': 2,
    'UGC_VIDEO': 8,
    'NARRATOR_VIDEO': 4
  };
  
  let cost = baseCosts[workflowType] || 5;
  
  // Adjust based on configuration
  if (config.estimatedDurationSeconds > 60) {
    cost += Math.ceil((config.estimatedDurationSeconds - 60) / 30);
  }
  
  return cost;
}
```

### 2.2 Credit Refund for Failed Generation

```typescript
// app/api/video/refund-credits/route.js
export async function POST(request) {
  try {
    // 1. Internal authentication (from Inngest)
    const authHeader = request.headers.get('authorization');
    if (authHeader !== `Bearer ${process.env.INNGEST_SIGNING_KEY}`) {
      return Response.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // 2. Input validation
    const { videoId, reason } = await request.json();
    if (!videoId || !reason) {
      return Response.json({ error: 'Invalid input' }, { status: 400 });
    }

    // 3. Atomic refund transaction
    const result = await db.transaction(async (tx) => {
      // 3a. Fetch video record
      const [video] = await tx
        .select()
        .from(videoData)
        .where(eq(videoData.id, videoId));

      if (!video || video.status === 'failed_refunded') {
        throw new Error('Video not found or already refunded');
      }

      // 3b. Fetch user with pessimistic lock
      const [user] = await tx
        .select()
        .from(users)
        .where(eq(users.clerkId, video.clerkId))
        .for('update');

      if (!user) {
        throw new Error('User not found');
      }

      // 3c. Calculate new balance
      const refundAmount = video.costInCredits;
      const newBalance = user.currentCreditBalance + refundAmount;

      // 3d. Update user balance
      await tx
        .update(users)
        .set({ 
          currentCreditBalance: newBalance,
          updatedAt: new Date()
        })
        .where(eq(users.clerkId, video.clerkId));

      // 3e. Insert refund transaction
      const [refundTransaction] = await tx
        .insert(creditTransactions)
        .values({
          userId: video.clerkId,
          transactionType: 'REFUND',
          amount: refundAmount,
          balanceBefore: user.currentCreditBalance,
          balanceAfter: newBalance,
          relatedEntityId: videoId.toString(),
          relatedEntityType: 'VIDEO',
          notes: `Refund for failed video generation: ${reason}`,
          metadata: { originalTransactionId: video.creditTransactionId, reason }
        })
        .returning();

      // 3f. Update video status
      await tx
        .update(videoData)
        .set({ 
          status: 'failed_refunded',
          refundTransactionId: refundTransaction.id,
          updatedAt: new Date()
        })
        .where(eq(videoData.id, videoId));

      return { refundTransaction, newBalance };
    });

    return Response.json({
      success: true,
      refundAmount: result.refundTransaction.amount,
      newBalance: result.newBalance
    });

  } catch (error) {
    console.error('Credit refund failed:', error);
    return Response.json({ error: 'Internal server error' }, { status: 500 });
  }
}
```

### 2.3 LemonSqueezy Webhook (Credit Purchase)

```typescript
// app/api/lemonsqueezy-webhook/route.js
import crypto from 'crypto';

export async function POST(request) {
  try {
    // 1. Webhook signature verification
    const body = await request.text();
    const signature = request.headers.get('x-signature');

    const expectedSignature = crypto
      .createHmac('sha256', process.env.LEMONSQUEEZY_WEBHOOK_SECRET)
      .update(body)
      .digest('hex');

    if (signature !== expectedSignature) {
      return Response.json({ error: 'Invalid signature' }, { status: 401 });
    }

    // 2. Parse webhook payload
    const payload = JSON.parse(body);
    const { event_name, data } = payload;

    if (event_name !== 'order_created') {
      return Response.json({ message: 'Event ignored' }, { status: 200 });
    }

    // 3. Extract purchase information
    const {
      id: orderId,
      attributes: {
        user_email,
        custom_data: { clerk_user_id: userId, credits_purchased }
      }
    } = data;

    // 4. Idempotency check
    const existingTransaction = await db
      .select()
      .from(creditTransactions)
      .where(
        and(
          eq(creditTransactions.relatedEntityId, orderId.toString()),
          eq(creditTransactions.relatedEntityType, 'ORDER')
        )
      )
      .limit(1);

    if (existingTransaction.length > 0) {
      return Response.json({ message: 'Already processed' }, { status: 200 });
    }

    // 5. Atomic credit purchase transaction
    const result = await db.transaction(async (tx) => {
      // 5a. Fetch user with pessimistic lock
      const [user] = await tx
        .select()
        .from(users)
        .where(eq(users.clerkId, userId))
        .for('update');

      if (!user) {
        throw new Error('User not found');
      }

      // 5b. Calculate new balance
      const creditAmount = parseInt(credits_purchased);
      const newBalance = user.currentCreditBalance + creditAmount;

      // 5c. Update user balance
      await tx
        .update(users)
        .set({
          currentCreditBalance: newBalance,
          updatedAt: new Date()
        })
        .where(eq(users.clerkId, userId));

      // 5d. Insert purchase transaction
      const [purchaseTransaction] = await tx
        .insert(creditTransactions)
        .values({
          userId,
          transactionType: 'PURCHASE',
          amount: creditAmount,
          balanceBefore: user.currentCreditBalance,
          balanceAfter: newBalance,
          relatedEntityId: orderId.toString(),
          relatedEntityType: 'ORDER',
          notes: `Credit purchase via LemonSqueezy`,
          metadata: {
            orderId,
            userEmail: user_email,
            purchaseAmount: creditAmount
          }
        })
        .returning();

      return { purchaseTransaction, newBalance };
    });

    // 6. Log successful purchase
    console.log(`Credits purchased: ${userId} received ${credits_purchased} credits`);

    return Response.json({
      success: true,
      creditsAdded: credits_purchased,
      newBalance: result.newBalance
    }, { status: 200 });

  } catch (error) {
    console.error('LemonSqueezy webhook processing failed:', error);

    // Return 500 to trigger LemonSqueezy retry
    return Response.json({ error: 'Processing failed' }, { status: 500 });
  }
}
```

---

## 3. Concurrency and Atomicity

### Why Pessimistic Locking?

```sql
-- Pessimistic locking prevents race conditions
SELECT * FROM users WHERE clerk_id = $1 FOR UPDATE;
```

**Benefits:**
- Prevents concurrent modifications to user balance
- Ensures atomic read-modify-write operations
- Eliminates race conditions in high-concurrency scenarios
- Guarantees consistency in credit calculations

**Database Transaction Importance:**
- **Atomicity**: All operations succeed or all fail
- **Consistency**: Database constraints are maintained
- **Isolation**: Concurrent transactions don't interfere
- **Durability**: Committed changes are permanent

---

## 4. Auditing and Reconciliation

### 4.1 Reconciliation Query

```typescript
// Reconciliation check for a specific user
const reconcileUserCredits = async (userId) => {
  const result = await db
    .select({
      currentBalance: users.currentCreditBalance,
      transactionSum: sql<number>`COALESCE(SUM(${creditTransactions.amount}), 0)`,
      initialBalance: sql<number>`10` // Default starting balance
    })
    .from(users)
    .leftJoin(creditTransactions, eq(users.clerkId, creditTransactions.userId))
    .where(eq(users.clerkId, userId))
    .groupBy(users.clerkId, users.currentCreditBalance);

  const [data] = result;
  const expectedBalance = data.initialBalance + data.transactionSum;
  const isConsistent = data.currentBalance === expectedBalance;

  return {
    userId,
    currentBalance: data.currentBalance,
    expectedBalance,
    isConsistent,
    discrepancy: data.currentBalance - expectedBalance
  };
};
```

### 4.2 System Health Metrics

**Key Monitoring Metrics:**
- Credit transaction success rate (target: 99.99%)
- Balance reconciliation consistency (target: 100%)
- Transaction processing latency (target: <500ms)
- Failed transaction count and reasons
- Credit refund rate and patterns
- User balance distribution and trends

---

## 5. Edge Cases & Critical Questions

### 5.1 Payment Processing Edge Cases

**Q1: What happens if LemonSqueezy payment is confirmed but our credit application fails?**
- **Solution**: Implement idempotent webhook processing with retry mechanism
- Store webhook events in separate table for replay
- Use LemonSqueezy event ID for deduplication

**Q2: How are credit costs managed and updated for different video types?**
- **Solution**: Implement dynamic pricing configuration
- Store pricing rules in database or configuration service
- Version pricing changes for historical accuracy

### 5.2 Data Integrity Edge Cases

**Q3: What if a user's balance becomes negative despite constraints?**
- **Solution**: Implement automated detection and alerting
- Daily reconciliation jobs to identify discrepancies
- Manual adjustment procedures with full audit trail

**Q4: How do we handle partial failures in video generation workflows?**
- **Solution**: Implement granular refund policies
- Track resource consumption for partial refunds
- Clear escalation procedures for edge cases

**Q5: What's the disaster recovery plan for credit system data?**
- **Solution**: Implement point-in-time recovery capabilities
- Regular backup verification and restoration testing
- Cross-region replication for critical credit data

---

## Next Steps

1. **Implement schemas** in existing codebase
2. **Create API endpoints** with proper error handling
3. **Set up monitoring** for credit system health
4. **Implement reconciliation** jobs and alerting
5. **Test edge cases** thoroughly before production deployment

---

*Document Version: 1.0*  
*Last Updated: [Current Date]*  
*Status: Detailed Design Ready for Implementation*
