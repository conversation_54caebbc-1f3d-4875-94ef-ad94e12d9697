# Implementation Guide with Code Examples
## Atomic Design Refactoring - Practical Implementation

### Phase 1A: Critical Atoms Implementation

#### 1. Enhanced TextInput Atom

```jsx
// components/atoms/inputs/TextInput.jsx
import React from 'react';
import { Input } from '@/components/ui/input';
import { cn } from '@/lib/utils';

/**
 * Standardized text input atom
 * Consistent styling and behavior across all forms
 */
export default function TextInput({
  className,
  error,
  disabled,
  loading,
  variant = 'default',
  size = 'md',
  ...props
}) {
  return (
    <Input
      className={cn(
        // Base styles
        'transition-all duration-200',
        
        // Size variants
        {
          'h-8 text-sm': size === 'sm',
          'h-10 text-base': size === 'md',
          'h-12 text-lg': size === 'lg',
        },
        
        // State styles
        {
          'border-destructive focus-visible:ring-destructive': error,
          'opacity-50 cursor-not-allowed': disabled,
          'animate-pulse': loading,
        },
        
        className
      )}
      disabled={disabled || loading}
      {...props}
    />
  );
}

// Specialized variants
export function ProjectTitleInput(props) {
  return (
    <TextInput
      placeholder="Enter your project title..."
      size="lg"
      {...props}
    />
  );
}

export function ScriptInput(props) {
  return (
    <TextInput
      placeholder="Describe your video topic or paste your script..."
      {...props}
    />
  );
}
```

#### 2. Enhanced GenerationButton (Extend Existing)

```jsx
// components/atoms/buttons/GenerationButton.jsx (Enhanced)
import React from 'react';
import { Button } from '@/components/ui/button';
import { Loader2, Sparkles, Bot, Film, Scissors, User } from 'lucide-react';
import { cn } from '@/lib/utils';

/**
 * Enhanced generation button with video type variants
 */
export default function GenerationButton({
  isGenerating = false,
  disabled = false,
  generationMessage = "Generating...",
  defaultText = "Generate Video",
  onClick,
  className,
  size = "lg",
  variant = "default",
  videoType = "default",
  credits,
  requiredCredits,
  title,
  ...props
}) {
  const isDisabled = disabled || isGenerating;
  const hasSufficientCredits = credits >= requiredCredits;
  
  // Video type specific icons and styling
  const videoTypeConfig = {
    'ai-video': { icon: Bot, gradient: 'from-blue-500 to-purple-500' },
    'meme-video': { icon: Film, gradient: 'from-purple-500 to-pink-500' },
    'podcast-clipper': { icon: Scissors, gradient: 'from-green-500 to-blue-500' },
    'ai-ugc-video': { icon: User, gradient: 'from-emerald-500 to-teal-500' },
    'default': { icon: Sparkles, gradient: 'from-indigo-500 to-purple-500' }
  };
  
  const config = videoTypeConfig[videoType] || videoTypeConfig.default;
  const IconComponent = config.icon;
  
  return (
    <Button
      onClick={onClick}
      disabled={isDisabled || !hasSufficientCredits}
      className={cn(
        "w-full transition-all duration-300",
        `bg-gradient-to-r ${config.gradient}`,
        "hover:shadow-lg hover:scale-[1.02]",
        "disabled:opacity-50 disabled:cursor-not-allowed",
        !hasSufficientCredits && "opacity-60",
        className
      )}
      size={size}
      variant={variant}
      title={title || (!hasSufficientCredits ? `Insufficient credits (Need ${requiredCredits})` : undefined)}
      {...props}
    >
      {isGenerating ? (
        <Loader2 className="mr-2 h-5 w-5 animate-spin" />
      ) : (
        <IconComponent className="mr-2 h-5 w-5" />
      )}
      {isGenerating ? generationMessage : defaultText}
    </Button>
  );
}

// Specialized variants for each video type
export function AIVideoGenerationButton(props) {
  return (
    <GenerationButton
      videoType="ai-video"
      defaultText="Generate AI Video"
      generationMessage="Creating AI Video..."
      requiredCredits={10}
      {...props}
    />
  );
}

export function MemeVideoGenerationButton(props) {
  return (
    <GenerationButton
      videoType="meme-video"
      defaultText="Generate Meme Video"
      generationMessage="Creating Meme Video..."
      requiredCredits={5}
      {...props}
    />
  );
}

export function PodcastClipperButton(props) {
  return (
    <GenerationButton
      videoType="podcast-clipper"
      defaultText="Clip Podcast"
      generationMessage="Processing Podcast..."
      requiredCredits={8}
      {...props}
    />
  );
}

export function AIUGCGenerationButton(props) {
  return (
    <GenerationButton
      videoType="ai-ugc-video"
      defaultText="Generate UGC Video"
      generationMessage="Creating UGC Video..."
      requiredCredits={15}
      {...props}
    />
  );
}
```

#### 3. StatusBadge Atom

```jsx
// components/atoms/indicators/StatusBadge.jsx
import React from 'react';
import { Badge } from '@/components/ui/badge';
import { 
  Clock, 
  CheckCircle, 
  XCircle, 
  AlertTriangle, 
  Loader2,
  Play,
  Pause
} from 'lucide-react';
import { cn } from '@/lib/utils';

/**
 * Status badge for video generation and processing states
 */
export default function StatusBadge({
  status = 'pending',
  showIcon = true,
  className,
  size = 'md',
  ...props
}) {
  const statusConfig = {
    pending: {
      icon: Clock,
      label: 'Pending',
      variant: 'secondary',
      className: 'bg-yellow-100 text-yellow-800 border-yellow-200'
    },
    processing: {
      icon: Loader2,
      label: 'Processing',
      variant: 'default',
      className: 'bg-blue-100 text-blue-800 border-blue-200',
      animate: true
    },
    completed: {
      icon: CheckCircle,
      label: 'Completed',
      variant: 'default',
      className: 'bg-green-100 text-green-800 border-green-200'
    },
    failed: {
      icon: XCircle,
      label: 'Failed',
      variant: 'destructive',
      className: 'bg-red-100 text-red-800 border-red-200'
    },
    warning: {
      icon: AlertTriangle,
      label: 'Warning',
      variant: 'secondary',
      className: 'bg-orange-100 text-orange-800 border-orange-200'
    },
    playing: {
      icon: Play,
      label: 'Playing',
      variant: 'default',
      className: 'bg-green-100 text-green-800 border-green-200'
    },
    paused: {
      icon: Pause,
      label: 'Paused',
      variant: 'secondary',
      className: 'bg-gray-100 text-gray-800 border-gray-200'
    }
  };
  
  const config = statusConfig[status] || statusConfig.pending;
  const IconComponent = config.icon;
  
  return (
    <Badge
      className={cn(
        'flex items-center gap-1.5 font-medium',
        {
          'text-xs px-2 py-1': size === 'sm',
          'text-sm px-2.5 py-1.5': size === 'md',
          'text-base px-3 py-2': size === 'lg',
        },
        config.className,
        className
      )}
      {...props}
    >
      {showIcon && (
        <IconComponent 
          className={cn(
            {
              'h-3 w-3': size === 'sm',
              'h-4 w-4': size === 'md',
              'h-5 w-5': size === 'lg',
            },
            config.animate && 'animate-spin'
          )} 
        />
      )}
      {config.label}
    </Badge>
  );
}

// Specialized status badges
export function VideoGenerationStatus({ status, ...props }) {
  const statusMap = {
    'queued': 'pending',
    'generating': 'processing',
    'rendering': 'processing',
    'completed': 'completed',
    'failed': 'failed',
    'cancelled': 'warning'
  };
  
  return (
    <StatusBadge 
      status={statusMap[status] || status} 
      {...props} 
    />
  );
}

export function UploadStatus({ status, ...props }) {
  const statusMap = {
    'uploading': 'processing',
    'uploaded': 'completed',
    'upload-failed': 'failed'
  };
  
  return (
    <StatusBadge 
      status={statusMap[status] || status} 
      {...props} 
    />
  );
}
```

### Phase 1B: Core Molecules Implementation

#### 1. ProjectDetailsForm Molecule

```jsx
// components/molecules/forms/ProjectDetailsForm.jsx
import React from 'react';
import { InputFormField } from '@/components/molecules/forms/FormField';
import { SectionHeader } from '@/components/atoms/layout/SectionHeader';
import { cn } from '@/lib/utils';

/**
 * Reusable project details form molecule
 * Used across all video creation workflows
 */
export default function ProjectDetailsForm({
  projectTitle,
  projectDescription,
  onProjectTitleChange,
  onProjectDescriptionChange,
  errors = {},
  className,
  showDescription = true,
  titlePlaceholder = "Enter your project title...",
  descriptionPlaceholder = "Describe your project (optional)...",
  ...props
}) {
  return (
    <div className={cn("space-y-6", className)} {...props}>
      <SectionHeader
        title="Project Details"
        description="Set up your video project information"
      />
      
      <div className="space-y-4">
        <InputFormField
          label="Project Title"
          required
          value={projectTitle}
          onChange={(e) => onProjectTitleChange(e.target.value)}
          placeholder={titlePlaceholder}
          error={errors.projectTitle}
          className="w-full"
        />
        
        {showDescription && (
          <InputFormField
            label="Project Description"
            value={projectDescription}
            onChange={(e) => onProjectDescriptionChange(e.target.value)}
            placeholder={descriptionPlaceholder}
            error={errors.projectDescription}
            className="w-full"
          />
        )}
      </div>
    </div>
  );
}

// Specialized variants for different video types
export function AIVideoProjectDetails(props) {
  return (
    <ProjectDetailsForm
      titlePlaceholder="Name your AI video project..."
      descriptionPlaceholder="Describe the video content you want to create..."
      {...props}
    />
  );
}

export function MemeVideoProjectDetails(props) {
  return (
    <ProjectDetailsForm
      titlePlaceholder="Name your meme video..."
      descriptionPlaceholder="Describe your meme concept..."
      showDescription={false}
      {...props}
    />
  );
}

export function PodcastClipperProjectDetails(props) {
  return (
    <ProjectDetailsForm
      titlePlaceholder="Name your podcast clips project..."
      descriptionPlaceholder="Describe the podcast content..."
      {...props}
    />
  );
}
```

#### 2. VideoPreviewCard Molecule

```jsx
// components/molecules/preview/VideoPreviewCard.jsx
import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { VideoThumbnail } from '@/components/atoms/media/VideoThumbnail';
import { StatusBadge } from '@/components/atoms/indicators/StatusBadge';
import { CreditBadge } from '@/components/atoms/indicators/CreditBadge';
import { cn } from '@/lib/utils';

/**
 * Reusable video preview card molecule
 * Shows video preview, status, and metadata
 */
export default function VideoPreviewCard({
  title = "Video Preview",
  videoUrl,
  thumbnailUrl,
  status = 'pending',
  duration,
  aspectRatio = '16:9',
  credits,
  requiredCredits,
  className,
  showCredits = true,
  showStatus = true,
  children,
  ...props
}) {
  return (
    <Card className={cn("overflow-hidden", className)} {...props}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg">{title}</CardTitle>
          {showStatus && <StatusBadge status={status} size="sm" />}
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Video Preview */}
        <div className={cn(
          "relative rounded-lg overflow-hidden bg-muted",
          {
            'aspect-video': aspectRatio === '16:9',
            'aspect-square': aspectRatio === '1:1',
            'aspect-[9/16]': aspectRatio === '9:16',
          }
        )}>
          <VideoThumbnail
            videoUrl={videoUrl}
            thumbnailUrl={thumbnailUrl}
            aspectRatio={aspectRatio}
            className="w-full h-full object-cover"
          />
        </div>
        
        {/* Metadata */}
        <div className="flex items-center justify-between text-sm text-muted-foreground">
          {duration && <span>Duration: {duration}</span>}
          {showCredits && (
            <CreditBadge
              credits={credits}
              requiredCredits={requiredCredits}
              size="sm"
            />
          )}
        </div>
        
        {/* Additional content */}
        {children}
      </CardContent>
    </Card>
  );
}

// Specialized variants
export function AIVideoPreviewCard(props) {
  return (
    <VideoPreviewCard
      title="AI Video Preview"
      requiredCredits={10}
      {...props}
    />
  );
}

export function MemeVideoPreviewCard(props) {
  return (
    <VideoPreviewCard
      title="Meme Video Preview"
      requiredCredits={5}
      {...props}
    />
  );
}
```

### Migration Example: AI Video Page

#### Before (Current Structure)
```
app/dashboard/create-new-short/create-ai-video/
├── page.jsx                           # Server component
├── components/
│   ├── ai-video-creation-client.jsx   # Large client component
│   ├── VideoConfigurationForm.jsx     # Complex form
│   ├── VideoPreviewPanel.jsx          # Preview panel
│   └── sections/
│       ├── ProjectDetailsSection.jsx  # Duplicate pattern
│       └── ScriptSection.jsx          # Duplicate pattern
```

#### After (Atomic Structure)
```
app/dashboard/create-new-short/create-ai-video/
├── page.jsx                           # Server component (unchanged)
└── components/
    └── ai-video-creation-client.jsx   # Simplified orchestrator

# Components now use atomic structure:
components/
├── molecules/forms/ProjectDetailsForm.jsx    # Reused across all video types
├── molecules/forms/ScriptInputForm.jsx       # Reused across AI video types
├── organisms/panels/VideoPreviewPanel.jsx   # Reused across all video types
└── templates/video-creation/VideoCreationLayout.jsx  # Standard layout
```

### Next Steps for Implementation

1. **Start with Phase 1A atoms** - Create the foundation components
2. **Test each component in isolation** - Ensure they work independently
3. **Build molecules from atoms** - Compose larger components
4. **Migrate one page at a time** - Start with AI Video (most complex)
5. **Update imports gradually** - Replace old components with new atomic ones
6. **Remove duplicate components** - Clean up after migration

This approach ensures minimal disruption while systematically improving the codebase architecture.
