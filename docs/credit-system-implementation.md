# Credit System Implementation Guide

## Overview

This document describes the implementation of the new atomic credit system for the AI Video Generation Platform. The system ensures zero tolerance for credit calculation errors through atomic transactions, pessimistic locking, and comprehensive audit trails.

## Key Features

- **Atomic Transactions**: All credit operations are atomic (all-or-nothing)
- **Pessimistic Locking**: Prevents race conditions in concurrent operations
- **Complete Audit Trail**: Every credit change is logged with full context
- **Automatic Refunds**: Failed video generations trigger automatic credit refunds
- **Reconciliation**: Built-in tools to verify system integrity

## Database Schema Changes

### New Tables

#### `creditTransactions`
```sql
CREATE TABLE credit_transactions (
  id SERIAL PRIMARY KEY,
  user_id VARCHAR NOT NULL REFERENCES users(clerk_id) ON DELETE CASCADE,
  transaction_type VARCHAR(50) NOT NULL, -- 'DEBIT', 'CREDIT', 'REFUND', 'PURCHASE', 'ADJUSTMENT'
  amount INTEGER NOT NULL, -- Positive for credits, negative for debits
  balance_before INTEGER NOT NULL,
  balance_after INTEGER NOT NULL,
  related_entity_id VARCHAR, -- videoId, orderId, etc.
  related_entity_type VARCHAR, -- 'VIDEO', 'ORDER', 'MANUAL'
  notes TEXT,
  metadata JSONB, -- Additional context
  created_at TIMESTAMP DEFAULT NOW()
);
```

### Modified Tables

#### `users` table
- Added `currentCreditBalance` field (new atomic balance)
- Kept `credits` field for backward compatibility during migration
- Added constraint: `currentCreditBalance >= 0`

#### `videoData` table
- Added `costInCredits` field
- Added `creditTransactionId` reference
- Added `refundTransactionId` reference

## API Endpoints

### 1. Video Generation Initiation
**POST** `/api/video/initiate-generation`

Atomically debits credits and creates video record.

```javascript
// Request
{
  "workflowType": "AI_VIDEO",
  "videoConfig": {
    "projectTitle": "My Video",
    "topic": "AI Technology",
    "videoStyle": "Professional",
    // ... other config
  }
}

// Response
{
  "success": true,
  "videoId": 123,
  "creditsRemaining": 45
}
```

### 2. Credit Refund
**POST** `/api/video/refund-credits`

Refunds credits for failed video generation (internal use).

```javascript
// Request
{
  "videoId": 123,
  "reason": "Video generation failed: API timeout"
}

// Response
{
  "success": true,
  "refundAmount": 5,
  "newBalance": 50
}
```

### 3. LemonSqueezy Webhook
**POST** `/api/lemonsqueezy-webhook`

Processes credit purchases from LemonSqueezy.

## Integration with Existing Workflows

### Server Actions
Updated server actions now use the atomic credit system:

```javascript
// actions/aiVideoGeneration.js
export async function triggerAIVideoGeneration(formData) {
  // Atomic credit debit and video creation
  const result = await db.transaction(async (tx) => {
    // ... atomic operations
  });
  
  // Trigger Inngest workflow
  await inngest.send({
    name: 'app/ai-video.generate',
    data: { videoId: result.video.id, ... }
  });
}
```

### Inngest Functions
Updated to handle pre-created video records and automatic refunds:

```javascript
export const aiVideoGeneration = inngest.createFunction(
  { event: "app/ai-video.generate" },
  async ({ event, step }) => {
    // Verify video record exists
    const video = await verifyVideoRecord(event.data.videoId);
    
    // Generate content and update record
    // ... workflow steps
  },
  {
    // Automatic refund on failure
    onFailure: async ({ event, error }) => {
      await triggerCreditRefund(event.data.videoId, error.message);
    }
  }
);
```

## Cost Calculation

Dynamic cost calculation based on workflow type and configuration:

```javascript
function getCostForWorkflow(workflowType, config) {
  const baseCosts = {
    'AI_VIDEO': 5,
    'MEME_VIDEO': 2,
    'UGC_VIDEO': 8,
    'NARRATOR_VIDEO': 4,
    'REDDIT_VIDEO': 3,
    'TWITTER_VIDEO': 3,
    'STOCK_MEDIA_VIDEO': 6
  };
  
  let cost = baseCosts[workflowType] || 5;
  
  // Adjust based on video duration
  if (config.estimatedDurationSeconds > 60) {
    cost += Math.ceil((config.estimatedDurationSeconds - 60) / 30);
  }
  
  return cost;
}
```

## Migration Process

### 1. Run Migration Script
```bash
node scripts/migrateCreditSystem.js
```

### 2. Verify Migration
```bash
node scripts/migrateCreditSystem.js verify
```

### 3. Rollback (if needed)
```bash
node scripts/migrateCreditSystem.js rollback
```

## Testing

### Credit System Integrity Test
```javascript
import { testCreditSystemIntegrity } from '@/lib/creditSystemTest';

const result = await testCreditSystemIntegrity('user_123');
console.log(result.isConsistent); // Should be true
```

### Video Generation Workflow Test
```javascript
import { testVideoGenerationWorkflow } from '@/lib/creditSystemTest';

const result = await testVideoGenerationWorkflow('user_123', 'AI_VIDEO');
console.log(result.success); // Should be true
```

## Monitoring and Alerts

### Key Metrics to Monitor

1. **Credit Transaction Success Rate**: Should be 99.99%
2. **Balance Reconciliation**: Should be 100% consistent
3. **Failed Video Generation Rate**: Should trigger refunds
4. **Credit Purchase Processing**: Should be idempotent

### Reconciliation Queries

```javascript
// Check user balance consistency
const reconciliation = await reconcileUserCredits(userId);
if (!reconciliation.isConsistent) {
  // Alert: Credit system inconsistency detected
}

// Daily reconciliation check
const allUsers = await db.select().from(users);
for (const user of allUsers) {
  const check = await reconcileUserCredits(user.clerkId);
  if (!check.isConsistent) {
    console.error(`Inconsistency for user ${user.clerkId}:`, check);
  }
}
```

## Error Handling

### Insufficient Credits
```javascript
if (error.message === 'Insufficient credits') {
  return { success: false, error: "Insufficient credits for video generation." };
}
```

### Failed Transactions
```javascript
// Automatic rollback in database transactions
await db.transaction(async (tx) => {
  // If any operation fails, entire transaction is rolled back
});
```

### Failed Video Generation
```javascript
// Automatic refund triggered by Inngest onFailure
onFailure: async ({ event, error }) => {
  await fetch('/api/video/refund-credits', {
    method: 'POST',
    body: JSON.stringify({
      videoId: event.data.videoId,
      reason: error.message
    })
  });
}
```

## Security Considerations

1. **Pessimistic Locking**: Prevents concurrent credit modifications
2. **Database Constraints**: Ensures credits never go negative
3. **Audit Trail**: Complete transaction history for forensics
4. **Idempotent Operations**: Webhook processing prevents duplicate charges
5. **Internal Authentication**: Refund endpoint requires internal auth

## Performance Considerations

1. **Indexes**: Added on frequently queried fields
2. **Transaction Scope**: Minimized to reduce lock time
3. **Batch Operations**: For bulk credit operations
4. **Connection Pooling**: Efficient database connection usage

## Deployment Checklist

- [ ] Run database migration
- [ ] Update environment variables (INNGEST_SIGNING_KEY, LEMONSQUEEZY_WEBHOOK_SECRET)
- [ ] Deploy new API endpoints
- [ ] Update Inngest functions
- [ ] Run integration tests
- [ ] Monitor credit system metrics
- [ ] Verify reconciliation reports

## Support and Troubleshooting

### Common Issues

1. **Credit Inconsistency**: Run reconciliation check and manual adjustment
2. **Failed Refunds**: Check Inngest function logs and retry manually
3. **Webhook Failures**: Verify signature and replay webhook events
4. **Migration Issues**: Use rollback script and investigate data

### Manual Credit Adjustment

```javascript
// For emergency credit adjustments
await db.transaction(async (tx) => {
  const [user] = await tx.select().from(users).where(eq(users.clerkId, userId)).for('update');
  const newBalance = user.currentCreditBalance + adjustmentAmount;
  
  await tx.update(users).set({ currentCreditBalance: newBalance }).where(eq(users.clerkId, userId));
  await tx.insert(creditTransactions).values({
    userId,
    transactionType: 'ADJUSTMENT',
    amount: adjustmentAmount,
    balanceBefore: user.currentCreditBalance,
    balanceAfter: newBalance,
    notes: 'Manual adjustment: [reason]'
  });
});
```

---

*Last Updated: [Current Date]*  
*Version: 1.0*
