"use server";

import { PutObjectCommand, S3Client } from "@aws-sdk/client-s3";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";
import { auth } from "@clerk/nextjs/server";
import { v4 as uuidv4 } from "uuid";

/**
 * Generate a pre-signed URL for uploading UGC assets (images/videos) to S3
 * @param {Object} fileInfo - File information including filename and contentType
 * @returns {Promise<Object>} - Result object with success status and upload data
 */
export async function generateUgcAssetUploadUrl(fileInfo) {
    console.log("[UGC Upload] generateUgcAssetUploadUrl: Called with fileInfo:", fileInfo);

    const { userId } = await auth();
    console.log("[UGC Upload] generateUgcAssetUploadUrl: User ID:", userId);

    if (!userId) {
        console.warn("[UGC Upload Error] Unauthorized access attempt.");
        return { success: false, error: "Unauthorized" };
    }

    try {
        // Validate file info
        if (!fileInfo.filename || !fileInfo.contentType) {
            return { success: false, error: "Missing filename or content type" };
        }

        // Validate file type
        const isImage = fileInfo.contentType.startsWith('image/');
        const isVideo = fileInfo.contentType.startsWith('video/');
        
        if (!isImage && !isVideo) {
            return { success: false, error: "Only image and video files are allowed" };
        }

        // Validate file size (passed as fileSize in bytes)
        const maxSize = 50 * 1024 * 1024; // 50MB
        if (fileInfo.fileSize && fileInfo.fileSize > maxSize) {
            return { success: false, error: "File size exceeds 50MB limit" };
        }

        console.log("[UGC Upload] Initializing S3 Client.");
        const s3Client = new S3Client({
            region: process.env.AWS_REGION,
            credentials: {
                accessKeyId: process.env.AWS_ACCESS_KEY_ID,
                secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
            },
        });

        const fileExtension = fileInfo.filename.split(".").pop() ?? "";
        const uniqueId = uuidv4();
        const key = `ugc-assets/${userId}/${uniqueId}.${fileExtension}`;
        
        console.log("[UGC Upload] Generated S3 Key:", key);

        const command = new PutObjectCommand({
            Bucket: process.env.S3_BUCKET_NAME,
            Key: key,
            ContentType: fileInfo.contentType,
        });

        console.log("[UGC Upload] Getting signed URL...");
        const signedUrl = await getSignedUrl(s3Client, command, { expiresIn: 600 }); // 10 minutes

        // Generate the final URL that will be accessible after upload
        const finalUrl = `https://${process.env.S3_BUCKET_NAME}.s3.${process.env.AWS_REGION}.amazonaws.com/${key}`;

        console.log("[UGC Upload] Returning success response.");
        return {
            success: true,
            data: {
                signedUrl,
                key,
                finalUrl,
                uploadId: uniqueId
            }
        };

    } catch (error) {
        console.error("[UGC Upload Error] Failed to generate upload URL:", error);
        return { success: false, error: error.message || "Failed to generate upload URL." };
    }
}

/**
 * Generate multiple pre-signed URLs for batch upload
 * @param {Array} filesInfo - Array of file information objects
 * @returns {Promise<Object>} - Result object with success status and upload data array
 */
export async function generateMultipleUgcAssetUploadUrls(filesInfo) {
    console.log("[UGC Upload] generateMultipleUgcAssetUploadUrls: Called with", filesInfo.length, "files");

    const { userId } = await auth();

    if (!userId) {
        return { success: false, error: "Unauthorized" };
    }

    if (!Array.isArray(filesInfo) || filesInfo.length === 0) {
        return { success: false, error: "No files provided" };
    }

    if (filesInfo.length > 10) {
        return { success: false, error: "Maximum 10 files allowed per batch" };
    }

    try {
        const uploadPromises = filesInfo.map(fileInfo => generateUgcAssetUploadUrl(fileInfo));
        const results = await Promise.all(uploadPromises);

        // Check if any uploads failed
        const failedUploads = results.filter(result => !result.success);
        if (failedUploads.length > 0) {
            return { 
                success: false, 
                error: `Failed to generate upload URLs for ${failedUploads.length} files`,
                details: failedUploads
            };
        }

        return {
            success: true,
            data: results.map(result => result.data)
        };

    } catch (error) {
        console.error("[UGC Upload Error] Failed to generate multiple upload URLs:", error);
        return { success: false, error: "Failed to generate upload URLs" };
    }
}
