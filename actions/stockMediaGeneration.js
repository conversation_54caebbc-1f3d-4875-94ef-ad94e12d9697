"use server";

import { inngest } from "@/app/inngest/client";
import { auth } from "@clerk/nextjs/server";

export async function triggerStockMediaVideoGeneration({ userPrompt, videoConfig }) {
  const { userId } = await auth();
  if (!userId) {
    console.warn(
      "[Server Action Error] actions/stockMediaGeneration/triggerStockMediaVideoGeneration: Unauthorized access attempt."
    );
    return { success: false, error: "Unauthorized" };
  }

  if (!userPrompt) {
     console.warn(
      "[Server Action Error] actions/stockMediaGeneration/triggerStockMediaVideoGeneration: Missing userPrompt."
    );
    return { success: false, error: "Missing user prompt." };
  }

  try {
    console.log(`[Server Action] actions/stockMediaGeneration/triggerStockMediaVideoGeneration: Sending Inngest event for user ${userId}.`);
    await inngest.send({
      name: "generate-stock-media-video-event",
      data: {
        userId: userId,
        userPrompt: userPrompt,
        videoConfig: videoConfig, // Pass video configuration if needed
      },
    });

    console.log(`[Server Action] actions/stockMediaGeneration/triggerStockMediaVideoGeneration: Inngest event sent.`);
    return { success: true, data: { message: "Stock media video generation initiated." } };

  } catch (error) {
    console.error(`[Server Action Error] actions/stockMediaGeneration/triggerStockMediaVideoGeneration: Failed to trigger stock media video generation for user ${userId}:`, error);
    return { success: false, error: error.message || "Failed to initiate stock media video generation." };
  }
}
