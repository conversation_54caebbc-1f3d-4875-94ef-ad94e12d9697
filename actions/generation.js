"use server";

import { GetObjectCommand, S3Client } from "@aws-sdk/client-s3";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";
import { revalidatePath } from "next/cache";
// import { env } from "~/env"; // Assuming this path is correct based on other files, despite lint - Removed as using process.env
import { inngest } from "@/app/inngest/client"; // Assuming this path is correct despite lint
import { auth } from "@clerk/nextjs/server"; // Corrected import path based on context
import { db } from "@/configs/db"; // Corrected import path based on context
import { eq, and } from 'drizzle-orm'; // Add this import
import { uploadedFile, clip } from "@/configs/schema"; // Add this import

// Updated signature based on file_context_0
export async function processVideo({ source, userPrompt, numberOfClips, requestId }) {
  const { userId } = await auth();
  if (!userId) {
    console.warn(
      "[Server Action Error] actions/generation/processVideo: Unauthorized access attempt."
    );
    return { success: false, error: "Unauthorized" };
  }

  // Assuming source contains uploadedFileId, based on file_context_3
  const uploadedFileId = source?.uploadedFileId;

  if (!uploadedFileId) {
     console.warn(
      "[Server Action Error] actions/generation/processVideo: Missing uploadedFileId in source."
    );
    return { success: false, error: "Missing file information." };
  }


  try {
    const uploadedVideo = await db.query.uploadedFile.findFirst({
      where: and(eq(uploadedFile.id, uploadedFileId), eq(uploadedFile.userId, userId)),
      columns: {
        uploaded: true,
        id: true,
        userId: true,
      },
    });

    if (!uploadedVideo) {
      throw new Error(`Uploaded file with ID ${uploadedFileId} not found for user ${userId}.`);
    }

    if (uploadedVideo.uploaded) {
      console.log(`[Server Action] actions/generation/processVideo: File ${uploadedFileId} already processed.`);
      return { success: true, data: { message: "File already processed." } };
    }

    console.log(`[Server Action] actions/generation/processVideo: Sending Inngest event for file ${uploadedFileId}.`);
    // Updated Inngest event data to include new parameters
    await inngest.send({
      name: "process-video-events",
      data: {
        uploadedFileId: uploadedVideo.id,
        userId: uploadedVideo.userId,
        userPrompt: userPrompt,
        numberOfClips: numberOfClips,
        requestId: requestId,
        // Include other source data if needed by the Inngest function
        source: source,
      },
    });

    console.log(`[Server Action] actions/generation/processVideo: Updating file status for ${uploadedFileId}.`);
    await db.update(uploadedFile)
      .set({ uploaded: true })
      .where(and(eq(uploadedFile.id, uploadedFileId), eq(uploadedFile.userId, userId)));

    console.log(`[Server Action] actions/generation/processVideo: Revalidating dashboard path.`);
    revalidatePath("/dashboard");

    return { success: true, data: { message: "Video processing initiated." } };

  } catch (error) {
    console.error(`[Server Action Error] actions/generation/processVideo: Failed to process video ${uploadedFileId}:`, error);
    // More specific error handling could be added here based on error types
    return { success: false, error: error.message || "Failed to initiate video processing." };
  }
}

export async function getClipPlayUrl(
  clipId
) {
  const { userId } = await auth();
  if (!userId) {
    console.warn(
      "[Server Action Error] actions/generation/getClipPlayUrl: Unauthorized access attempt."
    );
    return { success: false, error: "Unauthorized" };
  }

  try {
    const clip = await db.query.clip.findFirst({
      where: and(eq(clip.id, clipId), eq(clip.userId, userId)),
    });

    if (!clip) {
      throw new Error(`Clip with ID ${clipId} not found for user ${userId}.`);
    }

    const s3Client = new S3Client({
      region: process.env.AWS_REGION,
      credentials: {
        accessKeyId: process.env.AWS_ACCESS_KEY_ID,
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
      },
    });

    const command = new GetObjectCommand({
      Bucket: process.env.S3_BUCKET_NAME,
      Key: clip.s3Key,
    });

    const signedUrl = await getSignedUrl(s3Client, command, {
      expiresIn: 3600, // URL expires in 1 hour
    });

    console.log(`[Server Action] actions/generation/getClipPlayUrl: Generated signed URL for clip ${clipId}.`);
    return { success: true, data: { url: signedUrl } };
  } catch (error) {
    console.error(`[Server Action Error] actions/generation/getClipPlayUrl: Failed to generate play URL for clip ${clipId}:`, error);
    // Check if the error is due to clip not found or not owned by user
    if (error instanceof Error && error.message.includes("No Clip found")) {
       return { success: false, error: "Clip not found or not accessible." };
    }
    return { success: false, error: error.message || "Failed to generate play URL." };
  }
}
