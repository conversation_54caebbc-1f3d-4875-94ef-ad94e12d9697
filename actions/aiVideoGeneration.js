"use server";

import { auth } from "@clerk/nextjs/server";

export async function triggerAIVideoGeneration(formData) {
  const { userId } = await auth();

  if (!userId) {
    return { success: false, error: "Unauthorized" };
  }

  // Basic validation (should also be done on frontend and in Inngest function)
  const { topic, videoStyle, aspectRatio, templateId, voice, audioSpeed, backgroundMusic } = formData;
   if (!topic || !videoStyle || !aspectRatio || !templateId || !voice || audioSpeed === null || audioSpeed === undefined || !backgroundMusic) {
         console.error("[Server Action] Missing required formData fields:", { userId, formData });
         return { success: false, error: "Missing required input data." };
    }
     if (!topic?.trim()) {
        return { success: false, error: "Topic cannot be empty." };
    }
     if (topic.length > 200) { // Limit topic length
        return { success: false, error: 'Topic is too long (max 200 characters)' };
    }
    const parsedAudioSpeed = parseFloat(audioSpeed);
    if (isNaN(parsedAudioSpeed) || parsedAudioSpeed < 0.5 || parsedAudioSpeed > 2.0) {
      return { success: false, error: "Invalid 'audioSpeed' value. Must be a number between 0.5 and 2.0." };
    }

  try {
    // Use the new atomic credit system directly in the server action
    const { db } = await import('@/configs/db');
    const { users, creditTransactions, videoData } = await import('@/configs/schema');
    const { eq } = await import('drizzle-orm');
    const { inngest } = await import('@/app/inngest/client');

    // Determine cost
    const costInCredits = getCostForWorkflow('AI_VIDEO', formData);

    // Atomic database transaction
    const result = await db.transaction(async (tx) => {
      // Fetch user with pessimistic lock
      const [user] = await tx
        .select()
        .from(users)
        .where(eq(users.clerkId, userId))
        .for('update');

      if (!user) {
        throw new Error('User not found');
      }

      // Verify sufficient credits
      if (user.currentCreditBalance < costInCredits) {
        throw new Error('Insufficient credits');
      }

      // Calculate new balance
      const newBalance = user.currentCreditBalance - costInCredits;

      // Update user balance
      await tx
        .update(users)
        .set({
          currentCreditBalance: newBalance,
          updatedAt: new Date()
        })
        .where(eq(users.clerkId, userId));

      // Insert credit transaction
      const [creditTransaction] = await tx
        .insert(creditTransactions)
        .values({
          userId,
          transactionType: 'DEBIT',
          amount: -costInCredits,
          balanceBefore: user.currentCreditBalance,
          balanceAfter: newBalance,
          relatedEntityType: 'VIDEO',
          notes: `Video generation: AI_VIDEO`,
          metadata: { workflowType: 'AI_VIDEO', videoConfig: formData }
        })
        .returning();

      // Insert video record
      const [video] = await tx
        .insert(videoData)
        .values({
          clerkId: userId,
          title: formData.projectTitle || `AI Video`,
          topic: formData.topic || '',
          script: formData.script || '',
          videoStyle: formData.videoStyle || '',
          aspectRatio: formData.aspectRatio || '9:16',
          templateId: formData.templateId || 'AI_VIDEO',
          voice: formData.voice || '',
          captionName: formData.captionName || '',
          captionStyleJson: formData.captionStyleJson || null,
          captionContainerStyle: formData.captionContainerStyle || null,
          audioSpeed: formData.audioSpeed || 1.0,
          backgroundMusic: formData.backgroundMusic || '',
          estimatedDurationSeconds: estimateVideoDuration(formData),
          costInCredits,
          creditTransactionId: creditTransaction.id,
          status: 'Pending',
          workflow_data: {
            workflowType: 'AI_VIDEO',
            ...formData
          }
        })
        .returning();

      return { video, creditTransaction, newBalance };
    });

    // Enqueue Inngest job
    await inngest.send({
      name: 'app/ai-video.generate',
      data: {
        videoId: result.video.id,
        userId,
        workflowType: 'AI_VIDEO',
        ...formData
      }
    });

    console.log(`[Server Action] Video generation initiated successfully: ${result.video.id}`);

    return {
      success: true,
      eventId: result.video.id,
      creditsRemaining: result.newBalance
    };
  } catch (error) {
    console.error("[Server Action Error] Failed to initiate video generation:", error);

    if (error.message === 'Insufficient credits') {
      return { success: false, error: "Insufficient credits for video generation." };
    }

    return { success: false, error: error.message || "Failed to initiate video generation process." };
  }
}

/**
 * Helper function to determine cost based on workflow type and configuration
 */
function getCostForWorkflow(workflowType, config) {
  const baseCosts = {
    'AI_VIDEO': 5,
    'MEME_VIDEO': 2,
    'UGC_VIDEO': 8,
    'NARRATOR_VIDEO': 4,
    'REDDIT_VIDEO': 3,
    'TWITTER_VIDEO': 3,
    'STOCK_MEDIA_VIDEO': 6
  };

  let cost = baseCosts[workflowType] || 5;

  // Adjust based on configuration
  const estimatedDuration = estimateVideoDuration(config);
  if (estimatedDuration > 60) {
    cost += Math.ceil((estimatedDuration - 60) / 30);
  }

  return cost;
}

/**
 * Helper function to estimate video duration based on form data
 */
function estimateVideoDuration(formData) {
  // Basic estimation: assume 150 words per minute for TTS
  // and add some buffer for transitions and music
  const topicLength = formData.topic?.length || 0;
  const estimatedWords = Math.max(50, topicLength / 5); // Rough word count estimation
  const estimatedSeconds = Math.ceil((estimatedWords / 150) * 60) + 10; // Add 10s buffer

  return Math.min(Math.max(estimatedSeconds, 30), 180); // Between 30s and 3 minutes
}
