'use server';

import { currentUser } from '@clerk/nextjs/server';
import { atomicVideoGeneration } from '@/lib/atomicCreditSystem';

export async function generateNarratorVideo({
  videoUrl,
  title,
  aspectRatio,
  templateId,
  voice,
  captionName,
  captionStyleJson,
  captionContainerStyle,
  audioSpeed,
  backgroundMusic,
}) {
  const user = await currentUser();

  if (!user) {
    throw new Error('User not authenticated.');
  }

  const clerkId = user.id;

  try {
    // Use the new atomic credit system
    const videoConfig = {
      videoUrl,
      title,
      aspectRatio,
      templateId,
      voice,
      captionName,
      captionStyleJson,
      captionContainerStyle,
      audioSpeed,
      backgroundMusic,
    };

    const result = await atomicVideoGeneration(clerkId, 'NARRATOR_VIDEO', videoConfig);

    console.log(`[Server Action] Narrator video generation initiated successfully: ${result.videoId}`);

    return {
      success: true,
      message: 'Narrator video generation initiated.',
      videoId: result.videoId,
      creditsRemaining: result.creditsRemaining
    };
  } catch (error) {
    console.error('Error initiating narrator video generation:', error);

    if (error.message === 'Insufficient credits') {
      return { success: false, message: "Insufficient credits for video generation." };
    }

    return { success: false, message: error.message || 'Failed to initiate narrator video generation.' };
  }
}
