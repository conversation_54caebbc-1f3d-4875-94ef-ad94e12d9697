"use server";

import { auth } from "@clerk/nextjs/server";
import { atomicVideoGeneration } from '@/lib/atomicCreditSystem';

export async function triggerMemeVideoGeneration(formData) {
  const { userId } = await auth();

  if (!userId) {
    return { success: false, error: "Unauthorized" };
  }

  // Basic validation (should also be done on frontend and in Inngest function)
  const { projectTitle, videoSource, memeText, font, fontSize, textColor, textOutline, outlineColor, outlineThickness, textShadow, backgroundColor, textPosition, aspectRatio, videoStartTime, videoEndTime, useOriginalAudio, backgroundMusic, originalAudioVolume, backgroundMusicVolume } = formData;
   if (!projectTitle || !videoSource || !memeText || !font || !fontSize || !textColor || textOutline === null || outlineColor === null || outlineThickness === null || textShadow === null || textPosition === null || aspectRatio === null || videoStartTime === null || useOriginalAudio === null || originalAudioVolume === null || backgroundMusicVolume === null) {
         console.error("[Server Action] Missing required formData fields:", { userId, formData });
         return { success: false, error: "Missing required input data." };
    }
    // Add more specific validation for videoSource (URL or uploaded file ID)


  try {
    // Use the new atomic credit system
    const result = await atomicVideoGeneration(userId, 'MEME_VIDEO', formData);

    console.log(`[Server Action] Meme video generation initiated successfully: ${result.videoId}`);

    return result;
  } catch (error) {
    console.error("[Server Action Error] Failed to initiate meme video generation:", error);

    if (error.message === 'Insufficient credits') {
      return { success: false, error: "Insufficient credits for video generation." };
    }

    return { success: false, error: error.message || "Failed to initiate meme video generation process." };
  }
}
