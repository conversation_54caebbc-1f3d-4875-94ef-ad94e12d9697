"use server";

import { auth } from "@clerk/nextjs/server";
import { atomicVideoGeneration } from '@/lib/atomicCreditSystem';

export async function triggerTwitterVideoGeneration(formData) {
  const { userId } = await auth();

  if (!userId) {
    return { success: false, error: "Unauthorized" };
  }

  // Basic validation (should also be done on frontend and in Inngest function)
  const { projectTitle, twitterPost, backgroundVideoUrls, templateId, aspectRatio } = formData;
   if (!projectTitle || !twitterPost || !backgroundVideoUrls || !templateId || aspectRatio === null || aspectRatio === undefined) {
         console.error("[Server Action] Missing required formData fields:", { userId, formData });
         return { success: false, error: "Missing required input data." };
    }
    // Add more specific validation for twitterPost object and backgroundVideoUrls array
    if (!twitterPost.username || !twitterPost.tweetContent) {
         return { success: false, error: "Missing required Twitter post data (username or content)." };
    }
     if (!Array.isArray(backgroundVideoUrls) || (backgroundVideoUrls.every(url => url.trim() === '') && !formData.selectedDefaultVideo)) {
         return { success: false, error: "At least one background video URL or a default video must be provided." };
     }


  try {
    // Use the new atomic credit system
    const result = await atomicVideoGeneration(userId, 'TWITTER_VIDEO', formData);

    console.log(`[Server Action] Twitter video generation initiated successfully: ${result.videoId}`);

    return result;
  } catch (error) {
    console.error("[Server Action Error] Failed to initiate twitter video generation:", error);

    if (error.message === 'Insufficient credits') {
      return { success: false, error: "Insufficient credits for video generation." };
    }

    return { success: false, error: error.message || "Failed to initiate twitter video generation process." };
  }
}
