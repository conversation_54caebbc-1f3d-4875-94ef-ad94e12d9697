'use server';

import { auth } from '@clerk/nextjs/server';
import { db } from '@/configs/db';
import { scheduledPosts, userSocialAccounts } from '@/configs/schema';
import { eq } from 'drizzle-orm';
import { inngest } from '@/app/inngest/client';
import { S3Client, PutObjectCommand } from '@aws-sdk/client-s3'; // Import S3 SDK
import { nanoid } from 'nanoid'; // For generating unique file names

const S3_BUCKET_NAME = process.env.S3_BUCKET_NAME; // Your S3 bucket name
const AWS_REGION = process.env.AWS_REGION; // Your AWS region

const s3Client = new S3Client({
  region: AWS_REGION,
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
  },
});

export async function createScheduledYouTubePost(formData) {
  const { userId } = await auth();
  if (!userId) {
    return { success: false, message: 'Unauthorized' };
  }

  try {
    const socialAccountId = formData.get('socialAccountId');
    const videoFile = formData.get('videoFile');
    const thumbnailFile = formData.get('thumbnailFile');
    const title = formData.get('title');
    const description = formData.get('description');
    const tags = formData.get('tags');
    const privacyStatus = formData.get('privacyStatus');
    const categoryId = formData.get('categoryId');
    const madeForKids = formData.get('madeForKids') === 'true';
    const scheduledAtString = formData.get('scheduledAt');
    const publishNow = formData.get('publishNow') === 'true';

    if (!socialAccountId || !videoFile || !title || !categoryId) {
      return { success: false, message: 'Missing required fields.' };
    }

    // 1. Upload video and thumbnail files to S3
    let videoS3Key = null;
    let thumbnailS3Key = null;

    if (videoFile) {
      const videoFileName = `videos/${userId}/${nanoid()}-${videoFile.name}`;
      try {
        await s3Client.send(new PutObjectCommand({
          Bucket: S3_BUCKET_NAME,
          Key: videoFileName,
          Body: Buffer.from(await videoFile.arrayBuffer()), // Convert File to Buffer
          ContentType: videoFile.type,
        }));
        videoS3Key = videoFileName;
      } catch (s3Error) {
        console.error('S3 video upload failed:', s3Error);
        return { success: false, message: `Failed to upload video file to S3: ${s3Error.message}` };
      }
    }

    if (thumbnailFile) {
      const thumbnailFileName = `thumbnails/${userId}/${nanoid()}-${thumbnailFile.name}`;
      try {
        await s3Client.send(new PutObjectCommand({
          Bucket: S3_BUCKET_NAME,
          Key: thumbnailFileName,
          Body: Buffer.from(await thumbnailFile.arrayBuffer()), // Convert File to Buffer
          ContentType: thumbnailFile.type,
        }));
        thumbnailS3Key = thumbnailFileName;
      } catch (s3Error) {
        console.error('S3 thumbnail upload failed:', s3Error);
        return { success: false, message: `Failed to upload thumbnail file to S3: ${s3Error.message}` };
      }
    }

    const scheduledAt = publishNow ? new Date() : (scheduledAtString ? new Date(scheduledAtString) : null);
    const status = publishNow ? 'uploading' : 'pending';

    const platformSpecificData = {
      title,
      description,
      tags: tags.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0),
      privacyStatus,
      categoryId,
      madeForKids,
    };

    // 2. Save post details to the database
    const [newPost] = await db.insert(scheduledPosts).values({
      clerkId: userId,
      socialAccountId: parseInt(socialAccountId),
      postType: 'youtube_video',
      contentText: description, // Using description as contentText for YouTube
      videoFilePathOnServer: videoS3Key, // Store S3 key
      thumbnailFilePathOnServer: thumbnailS3Key, // Store S3 key
      scheduledAt: scheduledAt,
      status: status,
      platformSpecificData: platformSpecificData,
      createdAt: new Date(),
      updatedAt: new Date(),
    }).returning();

    if (!newPost) {
      return { success: false, message: 'Failed to save post to database.' };
    }

    // 3. If "publish now" or scheduled for immediate future, trigger Inngest event
    if (publishNow || (scheduledAt && scheduledAt <= new Date())) {
      await inngest.send({
        name: 'social.post.publish',
        data: {
          postId: newPost.id,
          socialAccountId: newPost.socialAccountId,
          clerkId: userId,
        },
      });
    }

    return { success: true, message: 'Video post saved and scheduled successfully!', postId: newPost.id };
  } catch (error) {
    console.error('Error creating scheduled YouTube post:', error);
    return { success: false, message: error.message || 'Failed to create scheduled post.' };
  }
}

import { generateYouTubeAuthUrl } from '@/lib/youtube-oauth'; // Import the helper

export async function initiateYouTubeOAuth() {
  const { userId } = await auth();
  if (!userId) {
    return { success: false, message: 'Unauthorized' };
  }

  try {
    // Directly call the helper function to generate the URL
    const authorizationUrl = generateYouTubeAuthUrl(userId);
    return { success: true, redirectUrl: authorizationUrl };
  } catch (error) {
    console.error('Error in initiateYouTubeOAuth server action:', error);
    return { success: false, message: error.message || 'Failed to initiate YouTube OAuth.' };
  }
}

export async function getScheduledYouTubePosts() {
  const { userId } = await auth();
  if (!userId) {
    return { success: false, message: 'Unauthorized', posts: [] };
  }

  try {
    const posts = await db.query.scheduledPosts.findMany({
      where: eq(scheduledPosts.clerkId, userId),
      with: {
        socialAccount: true, // Fetch related social account details
      },
      orderBy: (posts, { desc }) => [desc(posts.createdAt)],
    });
    return { success: true, posts: posts };
  } catch (error) {
    console.error('Error fetching scheduled YouTube posts:', error);
    return { success: false, message: error.message || 'Failed to fetch scheduled posts.', posts: [] };
  }
}

export async function getConnectedYouTubeAccounts() {
  const { userId } = await auth();
  if (!userId) {
    return { success: false, message: 'Unauthorized', accounts: [] };
  }

  try {
    const accounts = await db.query.userSocialAccounts.findMany({
      where: eq(userSocialAccounts.clerkId, userId) && eq(userSocialAccounts.platformName, 'youtube'),
    });
    return { success: true, accounts: accounts };
  } catch (error) {
    console.error('Error fetching connected YouTube accounts:', error);
    return { success: false, message: error.message || 'Failed to fetch connected accounts.', accounts: [] };
  }
}
