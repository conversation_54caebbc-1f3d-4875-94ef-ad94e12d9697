"use server";

import { auth } from "@clerk/nextjs/server";
import { redirect } from "next/navigation";
import { atomicVideoGeneration } from '@/lib/atomicCreditSystem';

/**
 * Server action to trigger AI UGC video generation
 * @param {Object} formData - The form data containing video generation parameters
 * @returns {Promise<Object>} - Result object with success status and video ID or error message
 */
export async function generateAiUgcVideo(formData) {
    try {
        // Get the authenticated user
        const { userId } = await auth();
        
        if (!userId) {
            console.error("[AI UGC Action] User not authenticated");
            return { 
                success: false, 
                error: "User not authenticated. Please log in and try again." 
            };
        }

        console.log(`[AI UGC Action] Starting AI UGC video generation for user: ${userId}`);

        // Extract and validate form data
        const {
            projectTitle,
            scriptTopic,
            userScript,
            avatarChoice,
            voiceChoice,
            userAssetUrls,
            aspectRatio = '9:16'
        } = formData;

        // Validation
        if (!avatarChoice) {
            return { 
                success: false, 
                error: "Please select an AI avatar." 
            };
        }

        if (!voiceChoice) {
            return { 
                success: false, 
                error: "Please select a voice option." 
            };
        }

        if (!scriptTopic && !userScript) {
            return { 
                success: false, 
                error: "Please provide either a script topic or write your own script." 
            };
        }

        if (userScript && userScript.length > 5000) {
            return { 
                success: false, 
                error: "Script is too long. Please keep it under 5000 characters." 
            };
        }

        if (userAssetUrls && userAssetUrls.length > 10) {
            return { 
                success: false, 
                error: "Too many background assets. Please limit to 10 images/videos." 
            };
        }

        // Use the new atomic credit system
        const videoConfig = {
            projectTitle: projectTitle || '',
            scriptTopic: scriptTopic || '',
            userScript: userScript || '',
            avatarChoice,
            voiceChoice,
            userAssetUrls: userAssetUrls || [],
            aspectRatio,
            templateId: 'AI_UGC_TALKING_HEAD'
        };

        console.log(`[AI UGC Action] Initiating UGC video generation with atomic credit system:`, {
            userId,
            projectTitle,
            hasScriptTopic: !!scriptTopic,
            hasUserScript: !!userScript,
            avatarChoice,
            userAssetCount: userAssetUrls?.length || 0,
            aspectRatio
        });

        const result = await atomicVideoGeneration(userId, 'UGC_VIDEO', videoConfig);

        console.log(`[AI UGC Action] UGC video generation initiated successfully: ${result.videoId}`);

        return {
            success: true,
            message: "AI UGC video generation started successfully! You will be redirected to your dashboard.",
            videoId: result.videoId,
            creditsRemaining: result.creditsRemaining
        };

    } catch (error) {
        console.error("[AI UGC Action] Error in generateAiUgcVideo:", error);
        
        // Return user-friendly error messages
        if (error.message === 'Insufficient credits') {
            return {
                success: false,
                error: "You don't have enough credits to generate this video. Please purchase more credits."
            };
        }
        
        if (error.message.includes("API key")) {
            return { 
                success: false, 
                error: "Service temporarily unavailable. Please try again later." 
            };
        }

        return { 
            success: false, 
            error: "An unexpected error occurred. Please try again later." 
        };
    }
}

/**
 * Server action to get available AI creators from Captions API
 * @returns {Promise<Object>} - Result object with creators list or error
 */
export async function getAvailableAvatars() {
    try {
        const { userId } = await auth();

        if (!userId) {
            return {
                success: false,
                error: "User not authenticated"
            };
        }

        const CAPTIONS_API_KEY = process.env.CAPTIONS_API_KEY;
        if (!CAPTIONS_API_KEY) {
            console.error("[AI UGC Action] CAPTIONS_API_KEY not configured");
            return {
                success: false,
                error: "Creator service not configured"
            };
        }

        const response = await fetch("https://api.captions.ai/api/ads/list-creators", {
            method: 'POST',
            headers: {
                'x-api-key': CAPTIONS_API_KEY,
                'Content-Type': 'application/json',
            },
        });

        if (!response.ok) {
            throw new Error(`Failed to fetch creators: ${response.statusText}`);
        }

        const data = await response.json();

        // Transform the creator data for frontend use with proper thumbnail handling
        const creators = data.supportedCreators.map((creatorName) => ({
            id: creatorName,
            name: creatorName,
            thumbnail: data.thumbnails[creatorName]?.imageUrl || null,
            previewVideo: data.thumbnails[creatorName]?.videoUrl || null,
            // Extract gender/type from name for better categorization
            category: creatorName.includes('-') ? creatorName.split('-')[0] : creatorName
        }));

        console.log(`[AI UGC Action] Retrieved ${creators.length} creators for user ${userId}`);
        console.log(`[AI UGC Action] Sample creator data:`, creators.slice(0, 2));

        return {
            success: true,
            avatars: creators // Keep the same property name for frontend compatibility
        };

    } catch (error) {
        console.error("[AI UGC Action] Error fetching creators:", error);
        return {
            success: false,
            error: "Failed to load available creators. Please try again later."
        };
    }
}

// Note: getAvailableVoices function removed - Captions API handles voice internally based on selected creator
