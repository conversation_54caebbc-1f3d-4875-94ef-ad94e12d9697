"use server";

import { PutObjectCommand, S3Client } from "@aws-sdk/client-s3";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";
// import { env } from "~/env"; // Removed import
import { auth } from "@clerk/nextjs/server";
import { v4 as uuidv4 } from "uuid";
import { db } from "@/configs/db";
// Assuming 'uploadedFile' schema is defined in configs/schema.js
// You might need to add the schema definition if it doesn't exist.
import { uploadedFile } from "@/configs/schema";
import { eq } from "drizzle-orm"; // Import eq if needed for future queries, not strictly needed for insert

export async function generateUploadUrl(fileInfo) {
  console.log("[Server Action] generateUploadUrl: Called with fileInfo:", fileInfo);

  const { userId } = await auth();
  console.log("[Server Action] generateUploadUrl: User ID:", userId);

  if (!userId) {
    console.warn(
      "[Server Action Error] actions/s3/generateUploadUrl: Unauthorized access attempt."
    );
    return { success: false, error: "Unauthorized" };
  }

  try {
    console.log("[Server Action] generateUploadUrl: Initializing S3 Client.");
    const s3Client = new S3Client({
      region: process.env.AWS_REGION,
      credentials: {
        accessKeyId: process.env.AWS_ACCESS_KEY_ID,
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
      },
    });
    console.log("[Server Action] generateUploadUrl: S3 Client initialized.");

    const fileExtension = fileInfo.filename.split(".").pop() ?? "";

    const uniqueId = uuidv4();
    const key = `${uniqueId}/original.${fileExtension}`;
    console.log("[Server Action] generateUploadUrl: Generated S3 Key:", key);

    const command = new PutObjectCommand({
      Bucket: process.env.S3_BUCKET_NAME,
      Key: key,
      ContentType: fileInfo.contentType,
    });
    console.log("[Server Action] generateUploadUrl: Created PutObjectCommand.");

    console.log("[Server Action] generateUploadUrl: Getting signed URL...");
    const signedUrl = await getSignedUrl(s3Client, command, { expiresIn: 600 });
    console.log("[Server Action] generateUploadUrl: Received signed URL (truncated):", signedUrl.substring(0, 100) + '...');

    console.log("[Server Action] generateUploadUrl: Creating database record for uploaded file...");
    const result = await db.insert(uploadedFile).values({
      userId: userId,
      s3Key: key,
      displayName: fileInfo.filename,
      uploaded: false,
    }).returning({
      id: uploadedFile.id,
    });

    const uploadedFileDbRecord = result[0];

    if (!uploadedFileDbRecord) {
        console.error("[Server Action Error] generateUploadUrl: Failed to create database record for uploaded file.");
        return { success: false, error: "Failed to create database record" };
    }

    console.log("[Server Action] generateUploadUrl: Database record created with ID:", uploadedFileDbRecord.id);

    console.log("[Server Action] generateUploadUrl: Returning success response.");
    return {
      success: true,
      data: {
        signedUrl,
        key,
        uploadedFileId: uploadedFileDbRecord.id,
      }
    };

  } catch (error) {
    console.error("[Server Action Error] generateUploadUrl: Failed to generate upload URL:", error);
    // More specific error handling could be added here based on error types
    return { success: false, error: error.message || "Failed to generate upload URL." };
  }
}
