"use server";

import { auth } from "@clerk/nextjs/server";
import { atomicVideoGeneration } from '@/lib/atomicCreditSystem';
import { inngest } from '@/app/inngest/client';

export async function triggerRedditVideoGeneration(formData) {
  const { userId } = await auth();

  if (!userId) {
    return { success: false, error: "Unauthorized" };
  }

  // Basic validation (should also be done on frontend and in Inngest function)
  const { projectTitle, redditPost, backgroundVideoUrls, templateId, aspectRatio } = formData;
   if (!projectTitle || !redditPost || !backgroundVideoUrls || !templateId || aspectRatio === null || aspectRatio === undefined) {
         console.error("[Server Action] Missing required formData fields:", { userId, formData });
         return { success: false, error: "Missing required input data." };
    }
    // Add more specific validation for redditPost object and backgroundVideoUrls array
    if (!redditPost.title || !redditPost.postContent) {
         return { success: false, error: "Missing required Reddit post data (title or content)." };
    }
     if (!Array.isArray(backgroundVideoUrls) || (backgroundVideoUrls.every(url => url.trim() === '') && !formData.selectedDefaultVideo)) {
         return { success: false, error: "At least one background video URL or a default video must be provided." };
     }


  try {
    // Use the new atomic credit system
    const result = await atomicVideoGeneration(userId, 'REDDIT_VIDEO', formData);

    if (!result || !result.video || !result.video.id) {
      throw new Error('Invalid result from atomicVideoGeneration');
    }

    console.log(`[Server Action] Video record created with ID: ${result.video.id}`);

    // Enqueue Inngest job
    try {
      await inngest.send({
        name: 'app/reddit-video.generate',
        data: {
          videoId: result.video.id,
          userId,
          workflowType: 'REDDIT_VIDEO',
          ...formData
        }
      });
      console.log(`[Server Action] Inngest job enqueued successfully for video ${result.video.id}`);
    } catch (inngestError) {
      console.error(`[Server Action] Failed to enqueue Inngest job:`, inngestError);
      // Don't throw here - video was created successfully, just log the error
    }

    const finalResult = {
      success: true,
      videoId: result.video.id,
      creditsRemaining: result.newBalance,
      eventId: result.video.id
    };

    console.log(`[Server Action] Reddit video generation initiated successfully: ${finalResult.videoId}`);
    return finalResult;
  } catch (error) {
    console.error("[Server Action Error] Failed to initiate reddit video generation:", error);

    if (error.message === 'Insufficient credits') {
      return { success: false, error: "Insufficient credits for video generation." };
    }

    return { success: false, error: error.message || "Failed to initiate reddit video generation process." };
  }
}
