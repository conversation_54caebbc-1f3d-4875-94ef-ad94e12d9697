# AI UGC Video Setup Guide

This guide covers the setup required for the new AI UGC (User Generated Content) video feature that allows users to create videos with AI avatars speaking over custom backgrounds using Captions API.

## Environment Variables Required

Add these environment variables to your `.env.local` file:

```bash
# Captions API Configuration
CAPTIONS_API_KEY=e3a4a635-be50-4e64-b5fb-90a49923285f

# Existing variables (ensure these are set)
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
S3_BUCKET_NAME=your_s3_bucket_name
```

## Getting Captions API Key

1. Visit [Captions API Dashboard](https://desktop.captions.ai/api)
2. Sign up for an account
3. Accept the Captions API Terms
4. Generate your API key
5. Purchase credits (minimum 100 credits at $0.05 each)
6. Add the API key to your environment variables

## Features Implemented

### 1. AI UGC Video Generation

- **Location**: `/dashboard/create-new-short/create-ai-ugc-video`
- **Functionality**:
  - Users can provide a script topic (AI generates script) OR write their own script
  - Select from available AI creators (fetched from Captions API)
  - Upload background images/videos (stored in S3)
  - AI generates UGC-style videos with creators speaking over custom backgrounds
  - Built-in voice handling (no separate voice selection needed)

### 2. Backend Architecture

#### Inngest Function

- **File**: `app/inngest/functions/aiUgcVideoGeneration.js`
- **Event**: `app/ai-ugc-video.generate`
- **Process**:
  1. Check user credits (cost based on video length)
  2. Generate or use provided script
  3. Create UGC video using Captions AI Ads API
  4. Poll for video completion
  5. Save to database with completed video URL
  6. Deduct user credits

#### Server Actions

- **File**: `actions/aiUgcVideoGeneration.js`
- **Functions**:
  - `generateAiUgcVideo()`: Triggers the Inngest workflow
  - `getAvailableAvatars()`: Fetches creators from Captions API

#### File Upload
- **File**: `actions/ugcAssetUpload.js`
- **Functions**:
  - `generateUgcAssetUploadUrl()`: Single file upload to S3
  - `generateMultipleUgcAssetUploadUrls()`: Batch file upload

### 3. Database Schema

The existing `video_data` table is used with the following `workflow_data` structure for AI UGC videos:

```json
{
  "type": "ai-ugc-talking-head",
  "userScript": "User provided script (if any)",
  "scriptTopic": "Topic for AI script generation (if any)",
  "avatarChoice": "avatar_id|from",
  "voiceChoice": "voice_id",
  "userAssetUrls": ["s3://bucket/path1", "s3://bucket/path2"],
  "aiGeneratedAudioUrl": "s3://bucket/audio.mp3",
  "akoolVideoUrl": "https://akool-cdn/video.mp4",
  "akoolVideoModelId": "akool_model_id"
}
```

### 4. Frontend Components

#### Main Creation Page

- **File**: `app/dashboard/create-new-short/create-ai-ugc-video/page.jsx`
- **Features**:
  - Tabbed interface for script input (AI generated vs custom)
  - Creator selection dropdown (populated from Captions API)
  - File upload with drag & drop support
  - Real-time upload progress
  - Form validation

## Current Limitations & Future Enhancements

### Current Limitations

1. **Multiple Backgrounds**: Captions AI Ads API supports multiple media URLs for dynamic backgrounds
2. **No Remotion Rendering**: Videos are generated directly by Captions (bypasses Remotion pipeline)
3. **Voice Customization**: Voice is handled internally by Captions based on selected creator
4. **No Background Music**: Feature not yet implemented

### Planned Enhancements

1. **Multiple Background Support**: Utilize Captions' support for multiple media URLs
2. **Remotion Integration**: Create Remotion composition for more control over video layout
3. **Creator Customization**: Explore Captions AI Twin API for custom avatars
4. **Background Music**: Add music tracks to videos
5. **Text Overlays**: Add captions or text elements
6. **Transitions**: Smooth transitions between background assets

## Testing the Feature

1. Ensure all environment variables are set
2. Start your development server
3. Navigate to `/dashboard/create-new-short`
4. Click "Create AI UGC Video"
5. Fill out the form:
   - Add a project title
   - Choose script mode (AI generated or custom)
   - Select an avatar
   - Choose a voice
   - Upload background images/videos (optional)
6. Submit and monitor the Inngest dashboard for progress

## Troubleshooting

### Common Issues

1. **"Avatar service not configured"**
   - Check that `AKOOL_API_KEY` is set in environment variables
   - Verify the API key is valid

2. **"Failed to upload files"**
   - Check AWS S3 credentials and permissions
   - Ensure S3 bucket exists and is accessible

3. **"Insufficient credits"**
   - User needs at least 15 credits to generate AI UGC video
   - Check user's credit balance in database

4. **"Video generation timeout"**
   - Akool API can take several minutes to generate videos
   - Check Akool dashboard for video status
   - Consider increasing timeout in Inngest function

### Monitoring

- **Inngest Dashboard**: Monitor function execution and errors
- **Akool Dashboard**: Check video generation status and usage
- **AWS S3**: Monitor file uploads and storage usage
- **Database**: Check `video_data` table for saved records

## Cost Considerations

- **Akool API**: Charges per video generation (check their pricing)
- **Google TTS**: Charges per character synthesized
- **AWS S3**: Storage and transfer costs for uploaded assets
- **Credits**: Each AI UGC video costs 15 user credits

## Security Notes

- File uploads are validated for type and size
- Pre-signed URLs expire after 10 minutes
- User authentication required for all operations
- Files are stored in user-specific S3 paths
