'use server';

import { db } from "@/configs/db";
import { users } from "@/configs/schema";
import { eq } from "drizzle-orm";

/**
 * Server action to initialize a new user in the database
 * 
 * This replaces the client-side database operations that were previously
 * performed in the Provider component. By moving this to a server action,
 * we improve security and enable better error handling.
 * 
 * @param clerkUser - The Clerk user object containing user details
 * @returns Promise<{ success: boolean; error?: string }>
 */
export async function initializeUser(clerkUser: {
  id: string;
  fullName?: string | null;
  primaryEmailAddress?: {
    emailAddress: string;
  } | null;
  imageUrl?: string;
}) {
  try {
    // Validate required user data
    if (!clerkUser?.primaryEmailAddress?.emailAddress) {
      console.warn('[User Initialization] No email address provided for user:', clerkUser?.id);
      return { success: false, error: 'No email address provided' };
    }

    if (!clerkUser.id) {
      console.warn('[User Initialization] No user ID provided');
      return { success: false, error: 'No user ID provided' };
    }

    // Check if user already exists
    const existingUser = await db
      .select()
      .from(users)
      .where(eq(users.email, clerkUser.primaryEmailAddress.emailAddress))
      .limit(1);

    // If user doesn't exist, create them
    if (!existingUser[0]) {
      console.log('[User Initialization] Creating new user:', clerkUser.primaryEmailAddress.emailAddress);
      
      const newUser = {
        clerkId: clerkUser.id,
        email: clerkUser.primaryEmailAddress.emailAddress,
        ...(clerkUser.fullName && { name: clerkUser.fullName }),
        ...(clerkUser.imageUrl && { imageUrl: clerkUser.imageUrl }),
        // credits will default to 10 as per schema
        // currentCreditBalance will default to 10 as per schema
      };

      await db.insert(users).values(newUser);

      console.log('[User Initialization] ✅ New user created successfully:', clerkUser.primaryEmailAddress.emailAddress);
      return { success: true };
    } else {
      // User already exists, no action needed
      console.log('[User Initialization] User already exists:', clerkUser.primaryEmailAddress.emailAddress);
      return { success: true };
    }
  } catch (error) {
    console.error('[User Initialization] ❌ Error initializing user:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error occurred' 
    };
  }
}

/**
 * Server action to get user credits by Clerk ID
 * 
 * This can be used by server components to fetch user credit information
 * without requiring client-side database access.
 * 
 * @param clerkId - The Clerk user ID
 * @returns Promise<{ credits: number; currentCreditBalance: number } | null>
 */
export async function getUserCredits(clerkId: string) {
  try {
    if (!clerkId) {
      console.warn('[Get User Credits] No clerk ID provided');
      return null;
    }

    // Try to select credits, handle missing columns gracefully
    let userResult;
    try {
      userResult = await db
        .select({
          credits: users.credits,
        })
        .from(users)
        .where(eq(users.clerkId, clerkId))
        .limit(1);
    } catch (dbError) {
      console.warn('[Get User Credits] Database query failed, trying fallback:', dbError.message);
      // Fallback: try with minimal query
      try {
        userResult = await db
          .select()
          .from(users)
          .where(eq(users.clerkId, clerkId))
          .limit(1);
      } catch (fallbackError) {
        console.error('[Get User Credits] Fallback query also failed:', fallbackError.message);
        return null;
      }
    }

    if (!userResult[0]) {
      console.warn('[Get User Credits] User not found:', clerkId);
      return null;
    }

    return {
      credits: userResult[0].credits || 0,
      currentCreditBalance: userResult[0].credits || 0, // Use credits as fallback
    };
  } catch (error) {
    console.error('[Get User Credits] ❌ Error fetching user credits:', error);
    return null;
  }
}
