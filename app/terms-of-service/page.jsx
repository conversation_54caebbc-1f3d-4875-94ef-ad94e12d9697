import React from 'react';

export const metadata = {
  title: "Terms of Service - AI Shorts Studio",
  description: "Read the terms of service for AI Shorts Studio.",
};

export default function TermsOfServicePage() {
  return (
    <div className="container mx-auto px-4 py-16 md:py-24">
      <h1 className="text-4xl font-bold mb-8 text-center">Terms of Service</h1>
      <div className="prose dark:prose-invert mx-auto max-w-3xl">
        <p className="mb-6 text-muted-foreground">Last Updated: [Insert Date]</p>

        <p className="mb-6">
          Please read these Terms of Service carefully before using the AI Shorts Studio website and services operated by [Your Company Name] ("us," "we," or "our").
        </p>

        <h2 className="mt-8 mb-4 text-2xl font-semibold">1. Acceptance of Terms</h2>
        <p className="mb-6">[Insert details about user agreement to the terms by using the service.]</p>

        <h2 className="mt-8 mb-4 text-2xl font-semibold">2. Changes to Terms</h2>
        <p className="mb-6">[Explain that terms may be updated and how users will be notified.]</p>

        <h2 className="mt-8 mb-4 text-2xl font-semibold">3. Account Registration</h2>
        <p className="mb-6">[Details about account creation, responsibilities, and security.]</p>

        <h2 className="mt-8 mb-4 text-2xl font-semibold">4. User Content</h2>
        <p className="mb-6">[Policies regarding content created or uploaded by users, ownership, licenses, etc.]</p>

        <h2 className="mt-8 mb-4 text-2xl font-semibold">5. Prohibited Conduct</h2>
        <p className="mb-6">[List of actions or content that are not allowed on the platform.]</p>

        <h2 className="mt-8 mb-4 text-2xl font-semibold">6. Intellectual Property</h2>
        <p className="mb-6">[Information about the ownership of the platform's content and user content.]</p>

        <h2 className="mt-8 mb-4 text-2xl font-semibold">7. Termination</h2>
        <p className="mb-6">[Conditions under which accounts or access to the service can be terminated.]</p>

        <h2 className="mt-8 mb-4 text-2xl font-semibold">8. Disclaimer of Warranties</h2>
        <p className="mb-6">[Standard disclaimer about the service being provided "as is".]</p>

        <h2 className="mt-8 mb-4 text-2xl font-semibold">9. Limitation of Liability</h2>
        <p className="mb-6">[Limits on the company's liability for damages.]</p>

        <h2 className="mt-8 mb-4 text-2xl font-semibold">10. Governing Law</h2>
        <p className="mb-6">[Specify the jurisdiction whose laws will govern the terms.]</p>

        <h2 className="mt-8 mb-4 text-2xl font-semibold">11. Contact Information</h2>
        <p className="mb-6">
          If you have any questions about these Terms of Service, please contact us at [Insert Contact Email Address].
        </p>
      </div>
    </div>
  );
}