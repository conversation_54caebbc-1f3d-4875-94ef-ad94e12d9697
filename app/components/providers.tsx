'use client';

import React from 'react';
import { ThemeProvider } from 'next-themes';
import { Clerk<PERSON>rovider } from '@clerk/nextjs';
import { Toaster } from 'sonner';
import { dark } from '@clerk/themes';
import Provider from '../provider';

/**
 * Client-side providers component that wraps the entire application
 * with authentication, theming, and context providers.
 *
 * This component is extracted from the root layout to enable server-side
 * rendering while maintaining all client-side provider functionality.
 */
export function Providers({ children }) {
  return (
    <ClerkProvider
      appearance={{
        baseTheme: dark,
      }}
    >
      <ThemeProvider
        attribute="class"
        defaultTheme="dark"
        enableSystem
        disableTransitionOnChange
      >
        <Provider>
          {children}
          <Toaster richColors position="top-right" />
        </Provider>
      </ThemeProvider>
    </ClerkProvider>
  );
}
