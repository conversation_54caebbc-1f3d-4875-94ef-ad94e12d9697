import React from 'react';

export const metadata = {
  title: "Privacy Policy - AI Shorts Studio",
  description: "Learn about our privacy policy at AI Shorts Studio.",
};

export default function PrivacyPolicyPage() {
  return (
    <div className="container mx-auto px-4 py-16 md:py-24">
      <h1 className="text-4xl font-bold mb-8 text-center">Privacy Policy</h1>
      <div className="prose dark:prose-invert mx-auto max-w-3xl">
        <p className="mb-6 text-muted-foreground">Last Updated: [Insert Date]</p>

        <p className="mb-6">
          This Privacy Policy describes how AI Shorts Studio ("we," "us," or "our") collects, uses, and discloses your personal information when you use our website and services.
        </p>

        <h2 className="mt-8 mb-4 text-2xl font-semibold">1. Information We Collect</h2>
        <p className="mb-6">[Insert details about the types of information collected, e.g., personal identification information, usage data, etc.]</p>

        <h2 className="mt-8 mb-4 text-2xl font-semibold">2. How We Use Your Information</h2>
        <p className="mb-6">[Insert details about how the collected information is used, e.g., to provide and maintain the service, improve the service, communicate with you, etc.]</p>

        <h2 className="mt-8 mb-4 text-2xl font-semibold">3. Sharing Your Information</h2>
        <p className="mb-6">[Insert details about if and how information is shared with third parties, e.g., service providers, legal requirements, etc.]</p>

        <h2 className="mt-8 mb-4 text-2xl font-semibold">4. Data Security</h2>
        <p className="mb-6">[Insert details about the security measures taken to protect user data.]</p>

        <h2 className="mt-8 mb-4 text-2xl font-semibold">5. Your Rights</h2>
        <p className="mb-6">[Insert details about user rights regarding their data, e.g., access, correction, deletion, etc.]</p>

        <h2 className="mt-8 mb-4 text-2xl font-semibold">6. Third-Party Links</h2>
        <p className="mb-6">[Mention if your site contains links to third-party websites and that you are not responsible for their privacy practices.]</p>

        <h2 className="mt-8 mb-4 text-2xl font-semibold">7. Changes to This Privacy Policy</h2>
        <p className="mb-6">[Explain how users will be notified of changes to the policy.]</p>

        <h2 className="mt-8 mb-4 text-2xl font-semibold">8. Contact Us</h2>
        <p className="mb-6">
          If you have any questions about this Privacy Policy, please contact us at [Insert Contact Email Address].
        </p>
      </div>
    </div>
  );
}