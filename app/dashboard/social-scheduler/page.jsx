'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { format } from 'date-fns';
import { CalendarIcon, UploadCloud, Youtube } from 'lucide-react';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';

import { createScheduledYouTubePost, getScheduledYouTubePosts, getConnectedYouTubeAccounts, initiateYouTubeOAuth } from '@/actions/youtubeScheduler';

const SocialSchedulerPage = () => {
  const [connectedAccounts, setConnectedAccounts] = useState([]); // To store connected YouTube channels
  const [selectedAccount, setSelectedAccount] = useState('');
  const [videoFile, setVideoFile] = useState(null);
  const [thumbnailFile, setThumbnailFile] = useState(null);
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [tags, setTags] = useState('');
  const [privacyStatus, setPrivacyStatus] = useState('private'); // Default to private
  const [categoryId, setCategoryId] = useState('');
  const [madeForKids, setMadeForKids] = useState(false);
  const [scheduledDate, setScheduledDate] = useState(null);
  const [scheduledTime, setScheduledTime] = useState(''); // e.g., "14:30"
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [scheduledPosts, setScheduledPosts] = useState([]); // To display list of posts

  // Dummy YouTube categories for now
  const youtubeCategories = [
    { id: '1', snippet: { title: 'Film & Animation' } },
    { id: '2', snippet: { title: 'Autos & Vehicles' } },
    { id: '10', snippet: { title: 'Music' } },
    { id: '15', snippet: { title: 'Pets & Animals' } },
    { id: '17', snippet: { title: 'Sports' } },
    { id: '19', snippet: { title: 'Travel & Events' } },
    { id: '20', snippet: { title: 'Gaming' } },
    { id: '22', snippet: { title: 'People & Blogs' } },
    { id: '23', snippet: { title: 'Comedy' } },
    { id: '24', snippet: { title: 'Entertainment' } },
    { id: '25', snippet: { title: 'News & Politics' } },
    { id: '26', snippet: { title: 'Howto & Style' } },
    { id: '27', snippet: { title: 'Education' } },
    { id: '28', snippet: { title: 'Science & Technology' } },
  ];

  useEffect(() => {
    const fetchData = async () => {
      const accountsResult = await getConnectedYouTubeAccounts();
      if (accountsResult.success) {
        setConnectedAccounts(accountsResult.accounts);
        if (accountsResult.accounts.length > 0) {
          setSelectedAccount(accountsResult.accounts[0].id); // Auto-select first account
        }
      } else {
        toast.error(accountsResult.message || 'Failed to fetch connected accounts.');
      }

      const postsResult = await getScheduledYouTubePosts();
      if (postsResult.success) {
        setScheduledPosts(postsResult.posts);
      } else {
        toast.error(postsResult.message || 'Failed to fetch scheduled posts.');
      }
    };
    fetchData();
  }, []);

  const handleFileChange = (e, type) => {
    if (type === 'video') {
      setVideoFile(e.target.files[0]);
    } else if (type === 'thumbnail') {
      setThumbnailFile(e.target.files[0]);
    }
  };

  const handleInitiateYouTubeOAuth = async () => {
    toast.info('Redirecting to YouTube for authorization...');
    try {
      const result = await initiateYouTubeOAuth();
      if (result.success && result.redirectUrl) {
        window.location.href = result.redirectUrl; // Redirect the current window
        // Alternatively, to open in a new tab: window.open(result.redirectUrl, '_blank');
      } else {
        toast.error(result.message || 'Failed to initiate YouTube OAuth.');
      }
    } catch (error) {
      console.error('Error initiating YouTube OAuth from frontend:', error);
      toast.error('An unexpected error occurred during OAuth initiation.');
    }
  };

  const handleSubmit = async (e, publishNow = false) => {
    e.preventDefault();

    if (!selectedAccount) {
      toast.error('Please select a connected YouTube account.');
      return;
    }
    if (!videoFile) {
      toast.error('Please upload a video file.');
      return;
    }
    if (!title.trim()) {
      toast.error('Please enter a video title.');
      return;
    }
    if (!categoryId) {
      toast.error('Please select a video category.');
      return;
    }
    if (!publishNow && (!scheduledDate || !scheduledTime)) {
      toast.error('Please select a scheduled date and time, or choose "Upload Now".');
      return;
    }

    setIsSubmitting(true);
    toast.info(publishNow ? 'Uploading video now...' : 'Scheduling video...');

    const formData = new FormData();
    formData.append('socialAccountId', selectedAccount);
    formData.append('videoFile', videoFile);
    if (thumbnailFile) {
      formData.append('thumbnailFile', thumbnailFile);
    }
    formData.append('title', title);
    formData.append('description', description);
    formData.append('tags', tags);
    formData.append('privacyStatus', privacyStatus);
    formData.append('categoryId', categoryId);
    formData.append('madeForKids', madeForKids);
    formData.append('publishNow', publishNow);

    if (!publishNow) {
      const combinedDateTime = new Date(
        `${format(scheduledDate, 'yyyy-MM-dd')}T${scheduledTime}:00`
      );
      formData.append('scheduledAt', combinedDateTime.toISOString());
    }

    try {
      const result = await createScheduledYouTubePost(formData);

      if (result.success) {
        toast.success(result.message);
        // Reset form
        setSelectedAccount(connectedAccounts.length > 0 ? connectedAccounts[0].id : ''); // Reset to first account or empty
        setVideoFile(null);
        setThumbnailFile(null);
        setTitle('');
        setDescription('');
        setTags('');
        setPrivacyStatus('private');
        setCategoryId('');
        setMadeForKids(false);
        setScheduledDate(null);
        setScheduledTime('');
        // Refresh scheduled posts list
        const updatedPostsResult = await getScheduledYouTubePosts();
        if (updatedPostsResult.success) {
          setScheduledPosts(updatedPostsResult.posts);
        } else {
          toast.error(updatedPostsResult.message || 'Failed to refresh scheduled posts.');
        }
      } else {
        toast.error(result.message);
      }
    } catch (error) {
      console.error('Error scheduling/uploading video:', error);
      toast.error('An unexpected error occurred.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-8 max-w-7xl mx-auto">
      {/* Header Section */}
      <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-red-50 via-orange-50 to-red-50 dark:from-red-950/20 dark:via-orange-950/20 dark:to-red-950/20 p-8 border border-border/50">
        <div className="relative z-10">
          <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-6">
            <div className="space-y-3">
              <h1 className="text-heading-1 text-gradient">
                Social Media Scheduler 📅
              </h1>
              <p className="text-body-large text-muted-foreground max-w-2xl">
                Schedule and manage your YouTube content with ease. Connect your channels and automate your posting workflow.
              </p>
              <div className="flex items-center gap-4 text-body-small text-muted-foreground">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                  <span>YouTube API connected</span>
                </div>
                <div className="flex items-center gap-2">
                  <span>🎬</span>
                  <span>{scheduledPosts.length} scheduled posts</span>
                </div>
              </div>
            </div>
            <div className="flex flex-col sm:flex-row gap-3">
              <Button asChild size="lg" className="bg-gradient-primary hover:opacity-90 text-white border-0 shadow-medium interactive-scale">
                <a href="#composer">
                  <UploadCloud className="h-5 w-5 mr-2" />
                  Schedule Video
                </a>
              </Button>
            </div>
          </div>
        </div>
        {/* Background decoration */}
        <div className="absolute top-0 right-0 w-64 h-64 bg-gradient-to-br from-red-400/10 to-orange-400/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 left-0 w-48 h-48 bg-gradient-to-tr from-orange-400/10 to-red-400/10 rounded-full blur-3xl"></div>
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 xl:grid-cols-3 gap-8">
        {/* Left Column - Main Content */}
        <div className="xl:col-span-2 space-y-8">
          {/* Connect Account Section */}
          <div className="card-modern p-6">
            <div className="flex items-center gap-3 mb-6">
              <div className="p-2 bg-gradient-to-br from-red-500 to-orange-500 rounded-lg">
                <Youtube className="h-6 w-6 text-white" />
              </div>
              <div>
                <h2 className="text-heading-3">YouTube Connection</h2>
                <p className="text-body-small text-muted-foreground">Connect and manage your YouTube channels</p>
              </div>
            </div>

            {connectedAccounts.length === 0 ? (
              <div className="text-center py-8">
                <div className="relative mb-4">
                  <div className="text-6xl mb-2">🔗</div>
                  <div className="absolute inset-0 bg-gradient-to-br from-red-400/20 to-orange-400/20 rounded-full blur-2xl"></div>
                </div>
                <p className="text-body text-muted-foreground mb-6">
                  Connect your YouTube channel to start scheduling videos
                </p>
                <Button
                  onClick={handleInitiateYouTubeOAuth}
                  disabled={isSubmitting}
                  className="bg-gradient-to-r from-red-500 to-orange-500 hover:opacity-90 text-white border-0 interactive-scale"
                  size="lg"
                >
                  <Youtube className="h-5 w-5 mr-2" />
                  Connect YouTube Channel
                </Button>
              </div>
            ) : (
              <div className="space-y-4">
                <div className="flex items-center gap-2 mb-3">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span className="text-body font-medium">Connected Accounts</span>
                </div>
                <Select onValueChange={setSelectedAccount} value={selectedAccount} disabled={isSubmitting}>
                  <SelectTrigger className="w-full h-12">
                    <SelectValue placeholder="Select a YouTube Channel" />
                  </SelectTrigger>
                  <SelectContent>
                    {connectedAccounts.map(account => (
                      <SelectItem key={account.id} value={account.id}>
                        <div className="flex items-center gap-2">
                          <Youtube className="h-4 w-4 text-red-500" />
                          {account.accountDisplayName}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}
          </div>

          {/* Post Composer Form */}
          <div id="composer" className="card-modern p-6">
            <div className="flex items-center gap-3 mb-6">
              <div className="p-2 bg-gradient-accent rounded-lg">
                <UploadCloud className="h-6 w-6 text-white" />
              </div>
              <div>
                <h2 className="text-heading-3">Compose New Video Post</h2>
                <p className="text-body-small text-muted-foreground">Upload and schedule your YouTube content</p>
              </div>
            </div>

            <form onSubmit={(e) => handleSubmit(e, false)} className="space-y-6">

              {/* File Upload Section */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-3">
                  <label htmlFor="videoFile" className="block text-body font-medium">
                    Video File <span className="text-red-500">*</span>
                  </label>
                  <div className="relative">
                    <Input
                      id="videoFile"
                      type="file"
                      accept="video/*"
                      onChange={(e) => handleFileChange(e, 'video')}
                      disabled={isSubmitting}
                      className="file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-caption file:font-semibold file:bg-gradient-to-r file:from-blue-50 file:to-purple-50 file:text-blue-700 hover:file:from-blue-100 hover:file:to-purple-100 h-12"
                    />
                    {videoFile && (
                      <div className="mt-2 p-2 bg-blue-50 dark:bg-blue-950/30 rounded-lg">
                        <p className="text-body-small text-blue-700 dark:text-blue-300 flex items-center gap-2">
                          <span>🎬</span>
                          {videoFile.name}
                        </p>
                      </div>
                    )}
                  </div>
                </div>
                <div className="space-y-3">
                  <label htmlFor="thumbnailFile" className="block text-body font-medium">
                    Custom Thumbnail <span className="text-body-small text-muted-foreground">(Optional)</span>
                  </label>
                  <div className="relative">
                    <Input
                      id="thumbnailFile"
                      type="file"
                      accept="image/*"
                      onChange={(e) => handleFileChange(e, 'thumbnail')}
                      disabled={isSubmitting}
                      className="file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-caption file:font-semibold file:bg-gradient-to-r file:from-emerald-50 file:to-green-50 file:text-emerald-700 hover:file:from-emerald-100 hover:file:to-green-100 h-12"
                    />
                    {thumbnailFile && (
                      <div className="mt-2 p-2 bg-emerald-50 dark:bg-emerald-950/30 rounded-lg">
                        <p className="text-body-small text-emerald-700 dark:text-emerald-300 flex items-center gap-2">
                          <span>🖼️</span>
                          {thumbnailFile.name}
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Video Details Section */}
              <div className="space-y-6">
                <div className="space-y-3">
                  <label htmlFor="title" className="block text-body font-medium">
                    Video Title <span className="text-red-500">*</span>
                  </label>
                  <Input
                    id="title"
                    type="text"
                    value={title}
                    onChange={(e) => setTitle(e.target.value)}
                    placeholder="Enter an engaging video title..."
                    disabled={isSubmitting}
                    className="h-12 text-body"
                  />
                </div>

                <div className="space-y-3">
                  <label htmlFor="description" className="block text-body font-medium">
                    Description
                  </label>
                  <Textarea
                    id="description"
                    value={description}
                    onChange={(e) => setDescription(e.target.value)}
                    placeholder="Describe your video content, include relevant keywords..."
                    rows="4"
                    disabled={isSubmitting}
                    className="text-body resize-none"
                  />
                </div>

                <div className="space-y-3">
                  <label htmlFor="tags" className="block text-body font-medium">
                    Tags <span className="text-body-small text-muted-foreground">(comma-separated)</span>
                  </label>
                  <Input
                    id="tags"
                    type="text"
                    value={tags}
                    onChange={(e) => setTags(e.target.value)}
                    placeholder="gaming, tutorial, entertainment, tech..."
                    disabled={isSubmitting}
                    className="h-12 text-body"
                  />
                </div>
              </div>

              {/* Settings Section */}
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-3">
                    <label htmlFor="privacyStatus" className="block text-body font-medium">
                      Privacy Status <span className="text-red-500">*</span>
                    </label>
                    <Select onValueChange={setPrivacyStatus} value={privacyStatus} disabled={isSubmitting}>
                      <SelectTrigger className="w-full h-12">
                        <SelectValue placeholder="Select privacy status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="public">
                          <div className="flex items-center gap-2">
                            <span>🌍</span>
                            Public
                          </div>
                        </SelectItem>
                        <SelectItem value="private">
                          <div className="flex items-center gap-2">
                            <span>🔒</span>
                            Private
                          </div>
                        </SelectItem>
                        <SelectItem value="unlisted">
                          <div className="flex items-center gap-2">
                            <span>🔗</span>
                            Unlisted
                          </div>
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-3">
                    <label htmlFor="categoryId" className="block text-body font-medium">
                      Category <span className="text-red-500">*</span>
                    </label>
                    <Select onValueChange={setCategoryId} value={categoryId} disabled={isSubmitting}>
                      <SelectTrigger className="w-full h-12">
                        <SelectValue placeholder="Select category" />
                      </SelectTrigger>
                      <SelectContent>
                        {youtubeCategories.map(cat => (
                          <SelectItem key={cat.id} value={cat.id}>
                            {cat.snippet.title}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="flex items-center space-x-3 p-4 bg-muted/30 rounded-lg">
                  <input
                    type="checkbox"
                    id="madeForKids"
                    checked={madeForKids}
                    onChange={(e) => setMadeForKids(e.target.checked)}
                    disabled={isSubmitting}
                    className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                  />
                  <label htmlFor="madeForKids" className="text-body font-medium flex items-center gap-2">
                    <span>👶</span>
                    Made for Kids
                  </label>
                </div>
              </div>

              {/* Scheduling Section */}
              <div className="space-y-6">
                <div className="flex items-center gap-2 mb-4">
                  <CalendarIcon className="h-5 w-5 text-blue-500" />
                  <h3 className="text-heading-3">Scheduling Options</h3>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-3">
                    <label htmlFor="scheduledDate" className="block text-body font-medium">
                      Scheduled Date <span className="text-body-small text-muted-foreground">(Optional)</span>
                    </label>
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant={"outline"}
                          className={cn(
                            "w-full justify-start text-left font-normal h-12",
                            !scheduledDate && "text-muted-foreground"
                          )}
                          disabled={isSubmitting}
                        >
                          <CalendarIcon className="mr-2 h-4 w-4" />
                          {scheduledDate ? format(scheduledDate, "PPP") : <span>Pick a date</span>}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0">
                        <Calendar
                          mode="single"
                          selected={scheduledDate}
                          onSelect={setScheduledDate}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                  </div>
                  <div className="space-y-3">
                    <label htmlFor="scheduledTime" className="block text-body font-medium">
                      Scheduled Time <span className="text-body-small text-muted-foreground">(Optional)</span>
                    </label>
                    <Input
                      id="scheduledTime"
                      type="time"
                      value={scheduledTime}
                      onChange={(e) => setScheduledTime(e.target.value)}
                      disabled={isSubmitting}
                      className="h-12 text-body"
                    />
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex flex-col md:flex-row gap-4 pt-4 border-t border-border/50">
                <Button
                  type="submit"
                  disabled={isSubmitting || !selectedAccount || !videoFile || !title || !categoryId}
                  className="flex-1 h-12 bg-gradient-primary hover:opacity-90 text-white border-0 interactive-scale"
                  size="lg"
                >
                  {isSubmitting ? (
                    <div className="flex items-center gap-2">
                      <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                      Scheduling...
                    </div>
                  ) : (
                    <div className="flex items-center gap-2">
                      <CalendarIcon className="h-5 w-5" />
                      Schedule Post
                    </div>
                  )}
                </Button>
                <Button
                  type="button"
                  onClick={(e) => handleSubmit(e, true)}
                  disabled={isSubmitting || !selectedAccount || !videoFile || !title || !categoryId}
                  variant="outline"
                  className="flex-1 h-12 interactive-scale"
                  size="lg"
                >
                  {isSubmitting ? (
                    <div className="flex items-center gap-2">
                      <div className="w-4 h-4 border-2 border-current/30 border-t-current rounded-full animate-spin"></div>
                      Uploading...
                    </div>
                  ) : (
                    <div className="flex items-center gap-2">
                      <UploadCloud className="h-5 w-5" />
                      Upload Now
                    </div>
                  )}
                </Button>
              </div>
            </form>
          </div>
        </div>

        {/* Right Column - Sidebar Widgets */}
        <div className="space-y-6">
          {/* Quick Stats Widget */}
          <div className="card-modern p-6">
            <div className="flex items-center gap-3 mb-6">
              <div className="p-2 bg-gradient-secondary rounded-lg">
                <CalendarIcon className="h-6 w-6 text-white" />
              </div>
              <div>
                <h3 className="text-heading-3">Scheduler Stats</h3>
                <p className="text-body-small text-muted-foreground">Your posting analytics</p>
              </div>
            </div>

            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-3">
                <div className="relative overflow-hidden rounded-xl bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950/30 dark:to-blue-900/30 p-4 border border-blue-200/50 dark:border-blue-800/50">
                  <div className="relative z-10">
                    <div className="flex items-center gap-2 mb-2">
                      <div className="p-1.5 bg-blue-500 rounded-lg">
                        <CalendarIcon className="h-3 w-3 text-white" />
                      </div>
                      <span className="text-caption text-blue-700 dark:text-blue-300 font-medium">Scheduled</span>
                    </div>
                    <div className="text-heading-2 font-bold text-blue-900 dark:text-blue-100">
                      {scheduledPosts.filter(p => p.status === 'scheduled').length}
                    </div>
                  </div>
                  <div className="absolute top-0 right-0 w-16 h-16 bg-blue-400/10 rounded-full blur-xl"></div>
                </div>

                <div className="relative overflow-hidden rounded-xl bg-gradient-to-br from-emerald-50 to-emerald-100 dark:from-emerald-950/30 dark:to-emerald-900/30 p-4 border border-emerald-200/50 dark:border-emerald-800/50">
                  <div className="relative z-10">
                    <div className="flex items-center gap-2 mb-2">
                      <div className="p-1.5 bg-emerald-500 rounded-lg">
                        <Youtube className="h-3 w-3 text-white" />
                      </div>
                      <span className="text-caption text-emerald-700 dark:text-emerald-300 font-medium">Published</span>
                    </div>
                    <div className="text-heading-2 font-bold text-emerald-900 dark:text-emerald-100">
                      {scheduledPosts.filter(p => p.status === 'published').length}
                    </div>
                  </div>
                  <div className="absolute top-0 right-0 w-16 h-16 bg-emerald-400/10 rounded-full blur-xl"></div>
                </div>
              </div>

              <div className="relative overflow-hidden rounded-xl bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-950/30 dark:to-pink-950/30 p-4 border border-purple-200/50 dark:border-purple-800/50">
                <div className="relative z-10">
                  <div className="flex items-center gap-2 mb-2">
                    <div className="p-1.5 bg-purple-500 rounded-lg">
                      <UploadCloud className="h-4 w-4 text-white" />
                    </div>
                    <span className="font-semibold text-body text-purple-800 dark:text-purple-200">Total Videos</span>
                  </div>
                  <div className="text-heading-2 font-bold text-purple-900 dark:text-purple-100">{scheduledPosts.length}</div>
                </div>
                <div className="absolute top-0 right-0 w-16 h-16 bg-purple-400/10 rounded-full blur-xl"></div>
              </div>
            </div>
          </div>

          {/* Scheduled Posts Widget */}
          <div className="card-modern p-6">
            <div className="flex items-center gap-3 mb-6">
              <div className="p-2 bg-gradient-accent rounded-lg">
                <Youtube className="h-6 w-6 text-white" />
              </div>
              <div>
                <h3 className="text-heading-3">Recent Posts</h3>
                <p className="text-body-small text-muted-foreground">Your latest scheduled content</p>
              </div>
            </div>

            {scheduledPosts.length === 0 ? (
              <div className="text-center py-8">
                <div className="relative mb-4">
                  <div className="text-6xl mb-2">📅</div>
                  <div className="absolute inset-0 bg-gradient-to-br from-purple-400/20 to-pink-400/20 rounded-full blur-2xl"></div>
                </div>
                <p className="text-body-small text-muted-foreground mb-6">
                  No videos scheduled yet
                </p>
                <Button asChild size="sm" className="bg-gradient-primary hover:opacity-90 text-white border-0 interactive-scale">
                  <a href="#composer">
                    Schedule Your First Video
                  </a>
                </Button>
              </div>
            ) : (
              <div className="space-y-3">
                {scheduledPosts.slice(0, 3).map(post => (
                  <div
                    key={post.id}
                    className="relative overflow-hidden flex items-center gap-4 p-4 rounded-xl border border-border/50 hover:bg-accent/30 transition-all duration-300 group interactive-scale"
                  >
                    <div className="relative">
                      <div className="text-2xl">🎬</div>
                      <div className="absolute inset-0 bg-gradient-to-br from-red-400/10 to-orange-400/10 rounded-full blur-lg"></div>
                    </div>

                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-2">
                        <h4 className="font-semibold text-body truncate">
                          {post.title}
                        </h4>
                        <div className={`px-2 py-1 rounded-full text-caption font-medium ${
                          post.status === 'published' ? 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400' :
                          post.status === 'scheduled' ? 'bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400' :
                          post.status === 'failed' ? 'bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-400' :
                          'bg-gray-100 text-gray-700 dark:bg-gray-900/30 dark:text-gray-400'
                        }`}>
                          {post.status}
                        </div>
                      </div>
                      <p className="text-body-small text-muted-foreground">
                        {post.scheduledAt ? format(new Date(post.scheduledAt), 'PPP p') : 'No schedule'}
                      </p>
                      {post.platformPostId && (
                        <a
                          href={`https://www.youtube.com/watch?v=${post.platformPostId}`}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-body-small text-red-500 hover:text-red-600 flex items-center gap-1 mt-1"
                        >
                          <Youtube className="h-3 w-3" />
                          View on YouTube
                        </a>
                      )}
                    </div>

                    <div className="absolute top-0 right-0 w-16 h-16 bg-gradient-to-br from-red-400/5 to-orange-400/5 rounded-full blur-xl"></div>
                  </div>
                ))}

                {scheduledPosts.length > 3 && (
                  <div className="text-center pt-4 border-t border-border/50">
                    <p className="text-body-small text-muted-foreground">
                      +{scheduledPosts.length - 3} more videos
                    </p>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default SocialSchedulerPage;
