'use client';

import React, { useEffect } from 'react'; // Import useEffect
import { useUser } from '@clerk/nextjs'; // Import useUser hook
import { useRouter } from 'next/navigation'; // Import useRouter for redirection
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { motion } from 'framer-motion';
import useBilling from '@/hooks/useBilling'; // Import the custom hook
import { creditPackages } from './creditPackages'; // Import credit packages
import CreditPackageCard from './_components/CreditPackageCard'; // Import the new component

const gridContainerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2,
    },
  },
};

export default function BillingPage() {
  const { isLoaded, isSignedIn } = useUser(); // Use the useUser hook
  const router = useRouter(); // Use the useRouter hook

  // Redirect unauthenticated users to the sign-in page
  useEffect(() => {
    if (isLoaded && !isSignedIn) {
      router.push('/sign-in');
    }
  }, [isLoaded, isSignedIn, router]);

  const { creditBalance, loading, error, handlePurchase } = useBilling(); // Use the custom hook

  console.log('Credit Packages:', creditPackages); // Add logging

  // Optionally, show a loading state while checking authentication
  if (!isLoaded) {
    return <div>Loading...</div>; // Or a spinner component
  }

  // Only render the billing content if the user is signed in
  if (isSignedIn) {
    return (
      <div className="container mx-auto py-8">
        <h1 className="text-3xl font-bold mb-6">Billing</h1>

        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Your Credit Balance</CardTitle>
            <CardDescription>Manage your video generation credits.</CardDescription>
          </CardHeader>
          <CardContent>
            {loading ? (
              <p>Loading balance...</p>
            ) : error ? (
              <p className="text-red-500">{error}</p>
            ) : (
              <p className="text-2xl font-semibold">{creditBalance} Credits</p>
            )}
          </CardContent>
        </Card>

        <h2 className="text-2xl font-bold mb-6">Purchase Credits</h2>

        <motion.div
          className="grid grid-cols-1 md:grid-cols-3 gap-6"
          variants={gridContainerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.1 }}
        >
          {creditPackages.map((pkg) => (
            <CreditPackageCard key={pkg.title} pkg={pkg} handlePurchase={handlePurchase} />
          ))}
        </motion.div>
      </div>
    );
  }

  // If not signed in and not loading, the useEffect will handle the redirect,
  // so this return is technically not reached if the redirect works.
  return null;
}