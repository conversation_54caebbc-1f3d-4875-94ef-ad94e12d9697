'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Check } from 'lucide-react';
import { motion } from 'framer-motion';

const pricingCardVariant = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: { duration: 0.5, ease: "easeOut" },
  },
};

const CreditPackageCard = ({ pkg, handlePurchase }) => {
  return (
    <motion.div variants={pricingCardVariant}>
      <Card className={`flex flex-col h-full ${pkg.isPopular ? 'border-primary border-2' : ''}`}>
        {pkg.isPopular && (
          <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-primary text-primary-foreground px-3 py-1 text-xs font-semibold rounded-full">
            Best Value
          </div>
        )}
        <CardHeader>
          <CardTitle>{pkg.title}</CardTitle>
          <CardDescription>{pkg.description}</CardDescription>
        </CardHeader>
        <CardContent className="flex-grow">
          <div className="mb-6">
            <span className="text-4xl font-bold">{pkg.price}</span>
            <span className="text-muted-foreground"> ({pkg.credits} credits)</span>
          </div>
          <ul className="space-y-3">
            {pkg.features.map((feature, index) => (
              <li key={index} className="flex items-start space-x-2">
                <Check className="h-5 w-5 text-green-500 flex-shrink-0 mt-0.5" />
                <span className="text-sm">{feature}</span>
              </li>
            ))}
          </ul>
        </CardContent>
        <CardFooter>
          <Button className="w-full" onClick={() => handlePurchase(pkg.priceId)}>
            Purchase
          </Button>
        </CardFooter>
      </Card>
    </motion.div>
  );
};

export default CreditPackageCard;