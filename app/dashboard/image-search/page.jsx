"use client";
// app/dashboard/image-search/page.jsx
import React, { useState } from 'react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";

const ImageSearchPage = () => {
  const [query, setQuery] = useState('');
  const [images, setImages] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [apiSource, setApiSource] = useState('pexels'); // 'pexels' or 'pixabay'

  const handleSearch = async () => {
    if (!query.trim()) return;

    setLoading(true);
    setError(null);
    setImages([]);

    try {
      const endpoint = apiSource === 'pexels' ? '/api/pexels-search' : '/api/pixabay-search';
      const response = await fetch(`${endpoint}?query=${encodeURIComponent(query)}`);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || `Failed to fetch images from ${apiSource}`);
      }

      // Normalize image data structure and include all available source URLs
      let normalizedImages = [];
      if (apiSource === 'pexels') {
        normalizedImages = data.photos.map(photo => ({
          id: photo.id,
          src: photo.src.medium, // Default display size
          alt: photo.alt,
          photographer: photo.photographer,
          photographer_url: photo.photographer_url,
          source: 'pexels',
          // Include all Pexels source URLs
          sources: photo.src
        }));
      } else if (apiSource === 'pixabay') {
        normalizedImages = data.hits.map(hit => ({
          id: hit.id,
          src: hit.webformatURL, // Default display size
          alt: hit.tags, // Pixabay uses tags for alt text
          photographer: hit.user,
          photographer_url: `https://pixabay.com/users/${hit.user}-${hit.user_id}/`, // Construct user profile URL
          source: 'pixabay',
          // Include relevant Pixabay source URLs
          sources: {
            webformatURL: hit.webformatURL,
            largeImageURL: hit.largeImageURL,
            // Add other relevant sizes if available in the API response
          }
        }));
      }

      setImages(normalizedImages);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleDownload = (url, filename) => {
    const link = document.createElement('a');
    link.href = url;
    link.download = filename || 'download';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">Image Search</h1>

      <div className="flex items-center mb-4">
        <span className="mr-2">Source:</span>
        <button
          onClick={() => setApiSource('pexels')}
          className={`p-2 rounded-l-md ${apiSource === 'pexels' ? 'bg-blue-500 text-white' : 'bg-gray-200 text-black'}`}
          disabled={loading}
        >
          Pexels
        </button>
        <button
          onClick={() => setApiSource('pixabay')}
          className={`p-2 rounded-r-md ${apiSource === 'pixabay' ? 'bg-blue-500 text-white' : 'bg-gray-200 text-black'}`}
          disabled={loading}
        >
          Pixabay
        </button>
      </div>

      <div className="flex mb-4">
        <input
          type="text"
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          placeholder={`Search ${apiSource}...`}
          className="border p-2 flex-grow rounded-l-md text-black"
          onKeyPress={(e) => {
            if (e.key === 'Enter') {
              handleSearch();
            }
          }}
        />
        <button
          onClick={handleSearch}
          className="bg-blue-500 text-white p-2 rounded-r-md hover:bg-blue-600"
          disabled={loading}
        >
          {loading ? 'Searching...' : 'Search'}
        </button>
      </div>

      {error && <p className="text-red-500 mb-4">{error}</p>}

      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
        {images.map((image) => (
          <div key={`${image.source}-${image.id}`} className="border rounded-md overflow-hidden flex flex-col">
            <img
              src={image.src}
              alt={image.alt}
              className="w-full h-48 object-cover"
            />
            <div className="p-2 flex-grow flex flex-col justify-between">
              <div>
                <p className="text-sm text-gray-600 truncate">{image.alt}</p>
                <a
                  href={image.photographer_url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-xs text-blue-500 hover:underline"
                >
                  Photo by {image.photographer} ({image.source})
                </a>
              </div>
              <div className="mt-2">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" className="w-full">Download</Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent>
                    {image.source === 'pexels' && image.sources && (
                      <>
                        {image.sources.original && <DropdownMenuItem onClick={() => handleDownload(image.sources.original, `pexels-${image.id}-original.jpg`)}>Original</DropdownMenuItem>}
                        {image.sources.large2x && <DropdownMenuItem onClick={() => handleDownload(image.sources.large2x, `pexels-${image.id}-large2x.jpg`)}>Large 2x</DropdownMenuItem>}
                        {image.sources.large && <DropdownMenuItem onClick={() => handleDownload(image.sources.large, `pexels-${image.id}-large.jpg`)}>Large</DropdownMenuItem>}
                        {image.sources.medium && <DropdownMenuItem onClick={() => handleDownload(image.sources.medium, `pexels-${image.id}-medium.jpg`)}>Medium</DropdownMenuItem>}
                      </>
                    )}
                    {image.source === 'pixabay' && image.sources && (
                      <>
                        {image.sources.largeImageURL && <DropdownMenuItem onClick={() => handleDownload(image.sources.largeImageURL, `pixabay-${image.id}-large.jpg`)}>Large</DropdownMenuItem>}
                        {image.sources.webformatURL && <DropdownMenuItem onClick={() => handleDownload(image.sources.webformatURL, `pixabay-${image.id}-webformat.jpg`)}>Webformat</DropdownMenuItem>}
                      </>
                    )}
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default ImageSearchPage;