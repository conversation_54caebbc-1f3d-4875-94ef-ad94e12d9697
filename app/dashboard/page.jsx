'use client';

import React, { useEffect, useState } from 'react'; // Import useEffect and useState
import { useUser } from '@clerk/nextjs'; // Import useUser hook
import { useRouter } from 'next/navigation'; // Import useRouter for redirection
import VideoList from './_components/VideoList'; // Import VideoList which handles its own data
import RecentVideosWidget from './_components/RecentVideosWidget';
import QuickStatsWidget from './_components/QuickStatsWidget';
import VideoTemplatesWidget from './_components/VideoTemplatesWidget';
import VideoCreationBentoGrid from './_components/VideoCreationBentoGrid';
import DashboardStats from './_components/DashboardStats';
import ActivityFeed from './_components/ActivityFeed';
import { Button } from '@/components/ui/button';
import { LayoutGrid, List, Plus } from 'lucide-react';
import Link from 'next/link';

// Remove unused imports and placeholder logic
// import { Button } from '@/components/ui/button';
// import { PlusCircle } from 'lucide-react';
// import EmptyState from './_components/EmptyState';
// import PlayerDialog from './_components/PlayerDialog';
// import { Skeleton } from '@/components/ui/skeleton';

// Remove placeholder data fetching function
// async function fetchUserVideos() { ... }

export default function DashboardPage() {
  const { isLoaded, isSignedIn, user } = useUser(); // Use the useUser hook
  const router = useRouter(); // Use the useRouter hook
  const [viewMode, setViewMode] = useState('grid'); // 'grid' or 'list'
  const [userCredits, setUserCredits] = useState(0);

  // Redirect unauthenticated users to the sign-in page
  useEffect(() => {
    if (isLoaded && !isSignedIn) {
      router.push('/sign-in');
    }
  }, [isLoaded, isSignedIn, router]);

  // Fetch user credits
  useEffect(() => {
    if (isSignedIn) {
      fetchUserCredits();
    }
  }, [isSignedIn]);

  const fetchUserCredits = async () => {
    try {
      const response = await fetch('/api/get-credits');
      if (response.ok) {
        const data = await response.json();
        setUserCredits(data.credits || 0);
      }
    } catch (error) {
      console.error('Error fetching user credits:', error);
    }
  };

  // Remove local state for videoList, isLoading, isPlayerOpen, selectedVideoId
  // const [videoList, setVideoList] = useState([]);
  // const [isLoading, setIsLoading] = useState(true);
  // const [isPlayerOpen, setIsPlayerOpen] = useState(false);
  // const [selectedVideoId, setSelectedVideoId] = useState(null);

  // Remove useEffect for loading videos
  // useEffect(() => { ... }, []);

  // Remove handlers related to player dialog
  // const handleVideoSelect = (videoId) => { ... };
  // const handleClosePlayer = () => { ... };

  // Optionally, show a loading state while checking authentication
  if (!isLoaded) {
    return <div>Loading...</div>; // Or a spinner component
  }

  // Only render the dashboard content if the user is signed in
  if (isSignedIn) {
    return (
      <div className="space-y-8">
        {/* Welcome Header */}
        <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-blue-50 via-purple-50 to-blue-50 dark:from-blue-950/20 dark:via-purple-950/20 dark:to-blue-950/20 p-8 border border-border/50">
          <div className="relative z-10">
            <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-6">
              <div className="space-y-3">
                <h1 className="text-heading-1 text-gradient">
                  Welcome back{user?.firstName ? `, ${user.firstName}` : ''}! 👋
                </h1>
                <p className="text-body-large text-muted-foreground max-w-2xl">
                  Ready to create amazing videos? Choose from our AI-powered templates and bring your ideas to life.
                </p>
                <div className="flex items-center gap-4 text-body-small text-muted-foreground">
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                    <span>All systems operational</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span>⚡</span>
                    <span>{userCredits} credits available</span>
                  </div>
                </div>
              </div>
              <div className="flex flex-col sm:flex-row gap-3">
                <Button asChild size="lg" className="bg-gradient-primary hover:opacity-90 text-white border-0 shadow-medium interactive-scale">
                  <Link href="/dashboard/create-new-short">
                    <Plus className="h-5 w-5 mr-2" />
                    Create New Video
                  </Link>
                </Button>
                <Button variant="outline" size="lg" className="interactive-scale">
                  <Link href="/dashboard" className="flex items-center">
                    View Gallery
                  </Link>
                </Button>
              </div>
            </div>
          </div>
          {/* Background decoration */}
          <div className="absolute top-0 right-0 w-64 h-64 bg-gradient-to-br from-blue-400/10 to-purple-400/10 rounded-full blur-3xl"></div>
          <div className="absolute bottom-0 left-0 w-48 h-48 bg-gradient-to-tr from-purple-400/10 to-blue-400/10 rounded-full blur-3xl"></div>
        </div>

        {/* Dashboard Stats */}
        <DashboardStats userCredits={userCredits} />

        {/* Video Creation Tools - Modern Bento Grid */}
        <VideoCreationBentoGrid />

        {/* Dashboard Widgets Grid */}
        <div className="grid grid-cols-1 xl:grid-cols-4 gap-8">
          {/* Left Column - Main Content */}
          <div className="xl:col-span-3 space-y-8">
            {/* Videos Section */}
            <div className="space-y-6">
              <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
                <div>
                  <h2 className="text-heading-2">Your Video Library</h2>
                  <p className="text-body-small text-muted-foreground mt-1">
                    Manage and organize your created videos
                  </p>
                </div>
                <div className="flex items-center gap-3">
                  <div className="flex items-center gap-1 bg-muted rounded-lg p-1">
                    <Button
                      variant={viewMode === 'grid' ? 'default' : 'ghost'}
                      size="sm"
                      onClick={() => setViewMode('grid')}
                      className="h-8 w-8 p-0"
                    >
                      <LayoutGrid className="h-4 w-4" />
                    </Button>
                    <Button
                      variant={viewMode === 'list' ? 'default' : 'ghost'}
                      size="sm"
                      onClick={() => setViewMode('list')}
                      className="h-8 w-8 p-0"
                    >
                      <List className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>

              {/* Video List with view mode */}
              <VideoList viewMode={viewMode} />
            </div>
          </div>

          {/* Right Column - Sidebar Widgets */}
          <div className="space-y-6">
            <ActivityFeed />
            <RecentVideosWidget />
          </div>
        </div>
      </div>
    );
  }

  // If not signed in and not loading, the useEffect will handle the redirect,
  // so this return is technically not reached if the redirect works.
  return null;
}