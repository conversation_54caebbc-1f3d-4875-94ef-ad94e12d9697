"use client";

import { MoveDownLeft, MoveUpRight, Video, Users, Clock, TrendingUp } from "lucide-react";
import { cn } from "@/lib/utils";

function DashboardStats({ userCredits = 0 }) {
  const stats = [
    {
      title: "Videos Created",
      value: "127",
      change: "+23.1%",
      changeType: "positive",
      icon: Video,
      description: "Total videos generated"
    },
    {
      title: "Total Views",
      value: "45.2K",
      change: "+12.5%", 
      changeType: "positive",
      icon: Users,
      description: "Across all platforms"
    },
    {
      title: "Processing Time",
      value: "2.3min",
      change: "-15.2%",
      changeType: "positive", // Faster is better
      icon: Clock,
      description: "Average generation time"
    },
    {
      title: "Credits Available",
      value: userCredits.toString(),
      change: "Active",
      changeType: "neutral",
      icon: TrendingUp,
      description: "Ready to use"
    }
  ];

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-heading-2">Analytics Overview</h2>
        <p className="text-body-small text-muted-foreground mt-1">
          Track your video creation performance
        </p>
      </div>
      
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => {
          const IconComponent = stat.icon;
          const isPositive = stat.changeType === "positive";
          const isNeutral = stat.changeType === "neutral";
          
          return (
            <div 
              key={index}
              className="group relative p-6 border border-border/50 rounded-xl bg-card hover:bg-card/80 transition-all duration-300 hover:shadow-lg hover:shadow-primary/5 hover:-translate-y-1"
            >
              {/* Background gradient on hover */}
              <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-primary/5 rounded-xl" />
              </div>
              
              <div className="relative flex flex-col justify-between h-full">
                {/* Icon and trend indicator */}
                <div className="flex items-center justify-between mb-4">
                  <div className="w-10 h-10 rounded-lg flex items-center justify-center bg-muted group-hover:bg-primary/10 transition-all duration-300">
                    <IconComponent className="w-5 h-5 text-muted-foreground group-hover:text-primary transition-colors" />
                  </div>
                  {!isNeutral && (
                    <div className={cn(
                      "flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium",
                      isPositive 
                        ? "bg-emerald-100 text-emerald-700 dark:bg-emerald-900/20 dark:text-emerald-400"
                        : "bg-red-100 text-red-700 dark:bg-red-900/20 dark:text-red-400"
                    )}>
                      {isPositive ? (
                        <MoveUpRight className="w-3 h-3" />
                      ) : (
                        <MoveDownLeft className="w-3 h-3" />
                      )}
                      {stat.change}
                    </div>
                  )}
                  {isNeutral && (
                    <div className="px-2 py-1 rounded-full text-xs font-medium bg-muted text-muted-foreground">
                      {stat.change}
                    </div>
                  )}
                </div>

                {/* Main value */}
                <div className="space-y-2">
                  <div className="text-3xl font-bold text-foreground group-hover:text-primary transition-colors">
                    {stat.value}
                  </div>
                  <div className="space-y-1">
                    <div className="text-sm font-medium text-foreground">
                      {stat.title}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {stat.description}
                    </div>
                  </div>
                </div>
              </div>

              {/* Subtle border gradient on hover */}
              <div className="absolute inset-0 -z-10 rounded-xl p-px bg-gradient-to-br from-transparent via-border/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
            </div>
          );
        })}
      </div>
    </div>
  );
}

export default DashboardStats;
