import React from 'react';
import { Player } from '@remotion/player';
import { MyComposition } from '@/remotion/Composition';
import { Skeleton } from '@/components/ui/skeleton';
import { Progress } from '@/components/ui/progress';
import { AlertTriangle, Hourglass } from 'lucide-react';

// Define FPS and dimensions for consistent calculations
const FPS = 30;
const BASE_DIMENSION = 1080;

// Helper function to get dimensions from aspect ratio (consistent with Composition.jsx)
const getDimensionsFromRatio = (aspectRatioString) => {
  let wRatio = 9, hRatio = 16;
  try {
    [wRatio, hRatio] = aspectRatioString.split(':').map(Number);
    if (!wRatio || !hRatio || isNaN(wRatio) || isNaN(hRatio)) {
      throw new Error('Invalid ratio numbers');
    }
  } catch (e) {
    console.warn(`Invalid aspect ratio string: ${aspectRatioString}. Defaulting to 9:16.`);
    wRatio = 9;
    hRatio = 16;
  }

  if (wRatio > hRatio) { // Landscape
    return { width: BASE_DIMENSION, height: Math.round(BASE_DIMENSION * (hRatio / wRatio)) };
  } else if (hRatio > wRatio) { // Portrait
    return { width: Math.round(BASE_DIMENSION * (wRatio / hRatio)), height: BASE_DIMENSION };
  } else { // Square
    return { width: BASE_DIMENSION, height: BASE_DIMENSION };
  }
};

/**
 * Helper function to calculate video duration based on videoData.
 * @param {VideoPlayerData} videoData
 * @param {number} fps
 * @returns {number} Duration in frames
 */
const calculateDuration = (videoData, fps) => {
  if (!videoData) {
    console.warn('No videoData provided, using default duration');
    return 150; // 5 seconds default
  }

  // Template-specific duration calculations
  if (videoData.templateId === 'RedditPostVideo') {
    return videoData.redditDurationFrames || (45 * fps);
  }

  if (videoData.templateId === 'TwitterPostVideo') {
    return videoData.twitterDurationFrames || (45 * fps);
  }

  // StockMediaVideo duration calculation
  if (videoData.templateId === 'StockMediaVideo' && videoData.workflow_data?.scenes) {
    try {
      const totalDuration = videoData.workflow_data.scenes.reduce((total, scene) => {
        return total + (scene.duration || 5); // Default 5 seconds per scene
      }, 0);
      console.log(`StockMediaVideo duration calculated: ${totalDuration} seconds (${totalDuration * fps} frames)`);
      return Math.round(totalDuration * fps);
    } catch (e) {
      console.warn('Error calculating StockMediaVideo duration:', e);
    }
  }

  // Use estimatedDurationSeconds if available and valid
  if (videoData.estimatedDurationSeconds &&
      typeof videoData.estimatedDurationSeconds === 'number' &&
      videoData.estimatedDurationSeconds > 0) {
    return Math.round(videoData.estimatedDurationSeconds * fps);
  }

  // Calculate from captions if available
  if (videoData.captionJson && Array.isArray(videoData.captionJson) && videoData.captionJson.length > 0) {
    try {
      const maxEndTime = Math.max(...videoData.captionJson.map(segment => segment.end || 0));
      if (maxEndTime > 0) {
        return Math.ceil(maxEndTime / 1000 * fps);
      }
    } catch (e) {
      console.warn('Error calculating duration from captions:', e);
    }
  }

  // Calculate from workflow clips if available
  if (videoData.workflow_data?.clips && Array.isArray(videoData.workflow_data.clips) && videoData.workflow_data.clips.length > 0) {
    try {
      const totalDuration = videoData.workflow_data.clips.reduce((total, clip) => total + (clip.duration || 5), 0);
      return Math.round(totalDuration * fps);
    } catch (e) {
      console.warn('Error calculating duration from clips:', e);
    }
  }

  // Default fallback
  console.warn('Using default duration for video:', videoData.id || 'unknown');
  return 150; // 5 seconds default
};

/**
 * Helper function to calculate video dimensions and duration based on videoData.
 * @param {VideoPlayerData} videoData
 * @param {number} fps
 * @returns {{videoDurationInFrames: number, compositionWidth: number, compositionHeight: number}}
 */
const calculateVideoProps = (videoData, fps) => {
  // Calculate duration
  const videoDurationInFrames = calculateDuration(videoData, fps);

  // Calculate dimensions from aspect ratio
  const aspectRatio = videoData?.aspectRatio || '9:16';
  const { width: compositionWidth, height: compositionHeight } = getDimensionsFromRatio(aspectRatio);

  console.log(`VideoPlayerDisplay: Calculated props for video ${videoData?.id || 'unknown'}:`, {
    duration: `${videoDurationInFrames} frames (${(videoDurationInFrames / fps).toFixed(2)}s)`,
    dimensions: `${compositionWidth}x${compositionHeight}`,
    aspectRatio,
    templateId: videoData?.templateId
  });

  return { videoDurationInFrames, compositionWidth, compositionHeight };
};

const VideoPlayerDisplay = ({
  isLoading,
  error,
  renderStatus,
  renderError,
  videoData,
  renderProgress,
}) => {
  // Calculate video properties with error handling
  let videoDurationInFrames, compositionWidth, compositionHeight;

  try {
    ({ videoDurationInFrames, compositionWidth, compositionHeight } = calculateVideoProps(videoData, FPS));
  } catch (e) {
    console.error('Error calculating video properties:', e);
    // Fallback to safe defaults
    videoDurationInFrames = 150; // 5 seconds
    compositionWidth = 608; // 9:16 aspect ratio
    compositionHeight = 1080;
  }

  // Determine the composition component to use for the Player
  const RemotionComposition = MyComposition; // MyComposition is the main entry point that handles templates

  return (
    <div className="w-full md:w-1/2 p-4 min-h-[250px] flex items-center justify-center">
      {isLoading || renderStatus === 'fetching' ? (
        <div className="w-full space-y-3">
          <Skeleton className="aspect-[9/16] w-3/4 mx-auto" />
        </div>
      ) : error || renderStatus === 'error' ? (
        <div className="flex flex-col items-center text-center text-destructive">
          <AlertTriangle className="h-8 w-8 mb-2" />
          <p className="font-semibold mb-1">
            {error ? "Error Loading Video" : "Render Error"}
          </p>
          <p className="text-sm">{error || renderError || "An unknown error occurred."}</p>
        </div>
      ) : videoData ? (
        <div className="w-full mx-auto"> {/* Removed max-w-md to allow wider display */}
          <div className="aspect-[9/16] rounded-lg bg-black flex items-center justify-center text-white relative overflow-hidden">
            {renderStatus === 'rendering' && (
              <div className="absolute inset-0 bg-black/70 flex flex-col items-center justify-center z-10">
                <Hourglass className="h-10 w-10 mb-4 animate-spin text-blue-400" />
                <p className="text-lg font-semibold mb-2">Rendering Video...</p>
                {renderProgress > 0 && <Progress value={renderProgress * 100} className="w-3/4 max-w-sm" />}
                {renderProgress > 0 && <p className="text-sm mt-2 text-gray-300">{(renderProgress * 100).toFixed(0)}% Complete</p>}
                {renderProgress === 0 && <p className="text-sm mt-2 text-gray-300">Starting render...</p>}
              </div>
            )}
            {(renderStatus !== 'rendering' || videoData?.renderedVideoUrl) && videoData && (
              <Player
                component={RemotionComposition}
                durationInFrames={videoDurationInFrames}
                fps={FPS}
                style={{ width: "100%", height: "100%" }}
                compositionWidth={compositionWidth}
                compositionHeight={compositionHeight}
                inputProps={{ videoData }}
                controls
                errorFallback={({ error }) => (
                  <div className="flex flex-col items-center justify-center h-full text-destructive">
                    <AlertTriangle className="h-8 w-8 mb-2" />
                    <p className="text-sm text-center">Preview Error</p>
                    <p className="text-xs text-center opacity-70">{error.message}</p>
                  </div>
                )}
              />
            )}
            {renderStatus !== 'rendering' && !videoData && (
              <div className="flex flex-col items-center justify-center h-full text-muted-foreground">
                <AlertTriangle className="h-8 w-8 mb-2" />
                <p className="text-sm">No video data available</p>
              </div>
            )}
          </div>
        </div>
      ) : (
        <div className="text-muted-foreground">No video data available.</div>
      )}
    </div>
  );
};

export default VideoPlayerDisplay;
