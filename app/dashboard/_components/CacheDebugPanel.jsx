'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useVideoCache } from '@/hooks/useVideoCache';
import { useVideoCacheInvalidation } from '@/contexts/VideoCacheContext';

function CacheDebugPanel() {
  const { getCacheStats, clearCache } = useVideoCache();
  const { invalidateVideoCache } = useVideoCacheInvalidation();
  
  const [stats, setStats] = React.useState(null);

  const refreshStats = () => {
    try {
      const currentStats = getCacheStats();
      setStats(currentStats);
      console.log('[CacheDebug] Current cache stats:', currentStats);
    } catch (error) {
      console.error('[CacheDebug] Error getting cache stats:', error);
      setStats({ error: error.message });
    }
  };

  const handleClearCache = () => {
    try {
      clearCache();
      console.log('[CacheDebug] Cache cleared manually');
      refreshStats();
    } catch (error) {
      console.error('[CacheDebug] Error clearing cache:', error);
    }
  };

  const handleInvalidateCache = () => {
    try {
      invalidateVideoCache();
      console.log('[CacheDebug] Cache invalidated globally');
      refreshStats();
    } catch (error) {
      console.error('[CacheDebug] Error invalidating cache:', error);
    }
  };

  React.useEffect(() => {
    refreshStats();
    // Refresh stats every 5 seconds
    const interval = setInterval(refreshStats, 5000);
    return () => clearInterval(interval);
  }, []);

  return (
    <Card className="mb-4 border-orange-200 bg-orange-50/50">
      <CardHeader className="pb-3">
        <CardTitle className="text-sm font-medium text-orange-800">
          🐛 Cache Debug Panel (Development Only)
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        <div className="flex gap-2">
          <Button size="sm" variant="outline" onClick={refreshStats}>
            Refresh Stats
          </Button>
          <Button size="sm" variant="outline" onClick={handleClearCache}>
            Clear Cache
          </Button>
          <Button size="sm" variant="outline" onClick={handleInvalidateCache}>
            Global Invalidate
          </Button>
        </div>
        
        {stats && (
          <div className="text-xs space-y-2">
            {stats.error ? (
              <div className="text-red-600">Error: {stats.error}</div>
            ) : (
              <>
                <div className="grid grid-cols-3 gap-4 text-center">
                  <div className="bg-white rounded p-2">
                    <div className="font-semibold text-blue-600">{stats.totalEntries}</div>
                    <div className="text-gray-600">Total Entries</div>
                  </div>
                  <div className="bg-white rounded p-2">
                    <div className="font-semibold text-green-600">{stats.validEntries}</div>
                    <div className="text-gray-600">Valid Entries</div>
                  </div>
                  <div className="bg-white rounded p-2">
                    <div className="font-semibold text-red-600">{stats.expiredEntries}</div>
                    <div className="text-gray-600">Expired Entries</div>
                  </div>
                </div>
                
                {stats.entries.length > 0 && (
                  <div className="bg-white rounded p-2">
                    <div className="font-semibold mb-2">Cache Entries:</div>
                    {stats.entries.map((entry, index) => (
                      <div key={index} className="text-xs mb-1 p-1 bg-gray-50 rounded">
                        <div className="font-mono">
                          {entry.key} - Page {entry.page} ({entry.videosCount} videos)
                        </div>
                        <div className="text-gray-500">
                          {entry.isValid ? '✅ Valid' : '❌ Expired'} - 
                          Expires: {new Date(entry.expiry).toLocaleTimeString()}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </>
            )}
          </div>
        )}
        
        <div className="text-xs text-gray-500 mt-2">
          Check browser console for detailed cache logs. Look for [VideoCache] messages.
        </div>
      </CardContent>
    </Card>
  );
}

export default CacheDebugPanel;
