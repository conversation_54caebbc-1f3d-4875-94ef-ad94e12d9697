"use client";

import React from 'react';
import Link from 'next/link';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { FileVideo, PlusCircle, Video, Bot } from 'lucide-react';
import { cn } from '@/lib/utils';

function EmptyState() {
  const icons = [Video, Bot, FileVideo];

  return (
    <div className={cn(
      "bg-background border-border hover:border-border/80 text-center",
      "border-2 border-dashed rounded-xl p-14 w-full max-w-[620px] mx-auto",
      "group hover:bg-muted/50 transition duration-500 hover:duration-200"
    )}>
      <div className="flex justify-center isolate">
        {/* Three animated icons */}
        <div className="bg-background size-12 grid place-items-center rounded-xl relative left-2.5 top-1.5 -rotate-6 shadow-lg ring-1 ring-border group-hover:-translate-x-5 group-hover:-rotate-12 group-hover:-translate-y-0.5 transition duration-500 group-hover:duration-200">
          <Video className="w-6 h-6 text-muted-foreground" />
        </div>
        <div className="bg-background size-12 grid place-items-center rounded-xl relative z-10 shadow-lg ring-1 ring-border group-hover:-translate-y-0.5 transition duration-500 group-hover:duration-200">
          <Bot className="w-6 h-6 text-muted-foreground" />
        </div>
        <div className="bg-background size-12 grid place-items-center rounded-xl relative right-2.5 top-1.5 rotate-6 shadow-lg ring-1 ring-border group-hover:translate-x-5 group-hover:rotate-12 group-hover:-translate-y-0.5 transition duration-500 group-hover:duration-200">
          <FileVideo className="w-6 h-6 text-muted-foreground" />
        </div>
      </div>

      <h2 className="text-foreground font-medium mt-6">No Videos Created Yet</h2>
      <p className="text-sm text-muted-foreground mt-1 whitespace-pre-line">
        Ready to create your first AI-powered video?{'\n'}Choose from our powerful creation tools to get started.
      </p>

      <Button
        asChild
        variant="outline"
        className={cn(
          "mt-4",
          "shadow-sm active:shadow-none"
        )}
      >
        <Link href="/dashboard/create-new-short">
          <PlusCircle className="mr-2 h-4 w-4" />
          Create Your First Video
        </Link>
      </Button>
    </div>
  );
}

export default EmptyState;