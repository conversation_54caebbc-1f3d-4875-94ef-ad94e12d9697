'use client';

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { usePathname } from 'next/navigation';
import { PanelsTopLeft, FileVideo, ShieldPlus, Settings, ChevronDown, Star, User } from 'lucide-react';
import { cn } from '@/lib/utils';
import { UserButton } from '@clerk/nextjs';
import mainlogo from '../../../lib/logo.png';
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarHeader,
  SidebarFooter,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem, // Ensure this is imported
  SidebarProvider,
} from '@/components/ui/sidebar';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';


const menuItems = [
  { id: 1, name: 'Dashboard', icon: PanelsTopLeft, path: '/dashboard' },
  {
    id: 2,
    name: 'Create Video',
    icon: FileVideo,
    path: '/dashboard/create-new-short', // Base path for the collapsible menu
    isCollapsible: true,
    subItems: [
      { id: 2.1, name: 'AI Video', path: '/dashboard/create-new-short/create-ai-video' },
      { id: 2.2, name: 'AI UGC Video', path: '/dashboard/create-new-short/create-ai-ugc-video' },
      { id: 2.3, name: 'Meme Video', path: '/dashboard/create-new-short/create-meme-video' },
      { id: 2.4, name: 'Podcast Clipper', path: '/dashboard/create-new-short/create-podcast-clipper' },
      { id: 2.5, name: 'Reddit Post Video', path: '/dashboard/create-new-short/create-reddit-post-video' },
      { id: 2.6, name: 'Twitter Post Video', path: '/dashboard/create-new-short/create-twitter-post-video' },
      { id: 2.7, name: 'Stock Media Video', path: '/dashboard/create-new-short/create-stock-media-video' },
,
    ]
  },
  { id: 3, name: 'Billing', icon: ShieldPlus, path: '/dashboard/billing' },
  { id: 4, name: 'Social Scheduler', icon: Settings, path: '/dashboard/social-scheduler' },
];

function SidebarComponent({ userCredits, user }) { // Accept user data as props
  const pathname = usePathname();

  return (
    <SidebarProvider defaultOpen={true}>
      <Sidebar>
        <SidebarHeader className="bg-background border-b h-16 min-h-[64px] flex items-center justify-center"> {/* Apply background color, bottom border, consistent height, and center content vertically and horizontally */}
          <Link href="/dashboard" className="flex items-center font-semibold h-full w-full justify-center"> {/* Ensure Link takes full height and width, and centers its content */}
            <div className="relative h-10 w-10"> {/* Adjusted size to be more consistent with header height */}
              <Image
                src={mainlogo}
                alt="AI Short Logo"
                fill
                className="object-contain rounded-full select-none pointer-events-none"
                sizes="40px"
                draggable={false}
                priority
              />
            </div>
            <span className="truncate text-lg font-bold leading-tight ml-2">AI Short Vid</span> {/* Added ml-2 for spacing */}
          </Link>
        </SidebarHeader>
        <SidebarContent>
          <SidebarGroup>
            <SidebarMenu>
              {menuItems.map((item) => {
                const IconComponent = item.icon;

                if (item.isCollapsible) {
                  const isCollapsibleActive = item.subItems.some(subItem => pathname.startsWith(subItem.path));
                  return (
                    <SidebarMenuItem key={item.id}>
                      <Collapsible defaultOpen={isCollapsibleActive}>
                        <CollapsibleTrigger
                          className={cn(
                            'flex items-center justify-between w-full gap-3 rounded-md px-3 py-2 text-sm font-medium transition-colors',
                            isCollapsibleActive
                              ? 'bg-black text-white'
                              : 'text-muted-foreground hover:bg-accent hover:text-accent-foreground'
                          )}
                        >
                          <div className="flex items-center gap-3">
                            {IconComponent && <IconComponent className="h-5 w-5" />}
                            <span>{item.name}</span>
                          </div>
                          <ChevronDown className={cn("h-4 w-4 transition-transform", isCollapsibleActive && "rotate-180")} />
                        </CollapsibleTrigger>
                        <CollapsibleContent className="ml-6 mt-1 space-y-1">
                          <SidebarMenuSub>
                            {item.subItems.map((subItem) => {
                              const isSubItemActive = pathname.startsWith(subItem.path);
                              return (
                                <SidebarMenuSubItem key={subItem.id}>
                                  <Link
                                    href={subItem.path}
                                    className={cn(
                                      'flex items-center gap-3 rounded-md px-3 py-2 text-sm font-medium transition-colors',
                                      isSubItemActive
                                        ? 'bg-black text-white'
                                        : 'text-muted-foreground hover:bg-accent hover:text-accent-foreground'
                                    )}
                                    // onClick={() => setIsSidebarOpen(false)} // No longer needed with Shadcn UI Sidebar
                                  >
                                    <span>{subItem.name}</span>
                                  </Link>
                                </SidebarMenuSubItem>
                              );
                            })}
                          </SidebarMenuSub>
                        </CollapsibleContent>
                      </Collapsible>
                    </SidebarMenuItem>
                  );
                }

                const isActive = item.path === '/dashboard'
                  ? pathname === item.path
                  : pathname.startsWith(item.path);

                if (!IconComponent) {
                  console.error(`Icon component missing for menu item: ${item.name}`);
                  return null;
                }

                return (
                  <SidebarMenuItem key={item.id}>
                    <Link
                      href={item.path}
                      className={cn(
                        'flex items-center gap-3 rounded-md px-3 py-2 text-sm font-medium transition-colors',
                        isActive
                          ? 'bg-black text-white'
                          : 'text-muted-foreground hover:bg-accent hover:text-accent-foreground'
                      )}
                      // onClick={() => setIsSidebarOpen(false)} // No longer needed with Shadcn UI Sidebar
                    >
                      <IconComponent className="h-5 w-5" />
                      <span>{item.name}</span>
                    </Link>
                  </SidebarMenuItem>
                );
              })}
            </SidebarMenu>
          </SidebarGroup>
        </SidebarContent>

        {/* User Information Footer */}
        <SidebarFooter className="border-t bg-background/50 backdrop-blur-sm">
          <div className="p-4 space-y-3">
            {/* Credits Display */}
            <div className="flex items-center justify-center gap-2 rounded-lg bg-gradient-to-r from-yellow-50 to-amber-50 dark:from-yellow-950/20 dark:to-amber-950/20 px-3 py-2 border border-yellow-200/50 dark:border-yellow-800/50">
              <Star className="h-4 w-4 text-yellow-600 dark:text-yellow-400" />
              <span className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                {userCredits !== undefined ? userCredits : '...'} Credits
              </span>
            </div>

            {/* User Profile Section */}
            <div className="flex items-center gap-3 p-3 rounded-lg bg-muted/50 hover:bg-muted/80 transition-colors">
              <div className="flex-shrink-0">
                <UserButton
                  appearance={{
                    elements: {
                      avatarBox: "w-8 h-8",
                      userButtonPopoverCard: "shadow-lg border",
                    }
                  }}
                />
              </div>
              <div className="flex-1 min-w-0">
                <div className="text-sm font-medium text-foreground truncate">
                  {user?.firstName && user?.lastName
                    ? `${user.firstName} ${user.lastName}`
                    : user?.firstName
                    ? user.firstName
                    : user?.emailAddresses?.[0]?.emailAddress?.split('@')[0]
                    || 'User'
                  }
                </div>
                <div className="text-xs text-muted-foreground truncate">
                  {user?.emailAddresses?.[0]?.emailAddress || 'No email'}
                </div>
              </div>
            </div>
          </div>
        </SidebarFooter>
      </Sidebar>
    </SidebarProvider>
  );
}

export default SidebarComponent;
