"use client";

import { cn } from "@/lib/utils";
import { Video, CheckCircle, Clock, AlertCircle, Zap } from "lucide-react";

const activities = [
  {
    id: 1,
    type: "video_created",
    title: "AI Video Generated",
    description: "Successfully created 'Marketing Campaign Video'",
    time: "2 minutes ago",
    icon: Video,
    iconColor: "text-blue-500",
    iconBg: "bg-blue-100 dark:bg-blue-900/20"
  },
  {
    id: 2,
    type: "processing_complete",
    title: "Processing Complete",
    description: "UGC Video 'Product Demo' is ready",
    time: "5 minutes ago",
    icon: CheckCircle,
    iconColor: "text-green-500",
    iconBg: "bg-green-100 dark:bg-green-900/20"
  },
  {
    id: 3,
    type: "processing",
    title: "Video Processing",
    description: "Meme Video 'Trending Topic' is being generated",
    time: "8 minutes ago",
    icon: Clock,
    iconColor: "text-orange-500",
    iconBg: "bg-orange-100 dark:bg-orange-900/20"
  },
  {
    id: 4,
    type: "credits_used",
    title: "Credits Used",
    description: "5 credits used for Podcast Clipper",
    time: "15 minutes ago",
    icon: Zap,
    iconColor: "text-purple-500",
    iconBg: "bg-purple-100 dark:bg-purple-900/20"
  },
  {
    id: 5,
    type: "error",
    title: "Processing Failed",
    description: "Stock Media Video failed - insufficient credits",
    time: "1 hour ago",
    icon: AlertCircle,
    iconColor: "text-red-500",
    iconBg: "bg-red-100 dark:bg-red-900/20"
  }
];

function ActivityFeed() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold text-foreground">Recent Activity</h3>
          <p className="text-sm text-muted-foreground">
            Latest updates from your video creation
          </p>
        </div>
        <button className="text-sm text-primary hover:text-primary/80 font-medium">
          View All
        </button>
      </div>

      <div className="space-y-4">
        {activities.map((activity, index) => {
          const IconComponent = activity.icon;
          
          return (
            <div
              key={activity.id}
              className={cn(
                "group relative flex items-start gap-4 p-4 rounded-lg",
                "border border-border/50 bg-card hover:bg-card/80",
                "transition-all duration-200 hover:shadow-md hover:shadow-primary/5",
                "hover:-translate-y-0.5"
              )}
            >
              {/* Timeline line */}
              {index < activities.length - 1 && (
                <div className="absolute left-8 top-12 w-px h-8 bg-border/50" />
              )}
              
              {/* Icon */}
              <div className={cn(
                "flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center",
                activity.iconBg,
                "group-hover:scale-110 transition-transform duration-200"
              )}>
                <IconComponent className={cn("w-4 h-4", activity.iconColor)} />
              </div>

              {/* Content */}
              <div className="flex-1 min-w-0 space-y-1">
                <div className="flex items-center justify-between">
                  <h4 className="text-sm font-medium text-foreground group-hover:text-primary transition-colors">
                    {activity.title}
                  </h4>
                  <span className="text-xs text-muted-foreground">
                    {activity.time}
                  </span>
                </div>
                <p className="text-sm text-muted-foreground leading-relaxed">
                  {activity.description}
                </p>
              </div>

              {/* Hover effect */}
              <div className="absolute inset-0 rounded-lg bg-gradient-to-r from-primary/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
            </div>
          );
        })}
      </div>

      {/* Quick Actions */}
      <div className="pt-4 border-t border-border/50">
        <div className="grid grid-cols-2 gap-3">
          <button className="flex items-center justify-center gap-2 p-3 rounded-lg border border-border/50 bg-card hover:bg-card/80 transition-all duration-200 hover:shadow-md group">
            <Video className="w-4 h-4 text-primary group-hover:scale-110 transition-transform" />
            <span className="text-sm font-medium text-foreground">Create Video</span>
          </button>
          <button className="flex items-center justify-center gap-2 p-3 rounded-lg border border-border/50 bg-card hover:bg-card/80 transition-all duration-200 hover:shadow-md group">
            <Zap className="w-4 h-4 text-primary group-hover:scale-110 transition-transform" />
            <span className="text-sm font-medium text-foreground">Buy Credits</span>
          </button>
        </div>
      </div>
    </div>
  );
}

export default ActivityFeed;
