import React from 'react';
import { Skeleton } from '@/components/ui/skeleton';

/**
 * @typedef {object} VideoPlayerData
 * @property {number | string} id
 * @property {string} clerkId
 * @property {string} title
 * @property {string | null} topic
 * @property {string | null} script
 * @property {string | null} videoStyle
 * @property {string | null} aspectRatio
 * @property {string | null} templateId
 * @property {string | null} voice
 * @property {string | null} captionName
 * @property {any | null} captionStyleJson // JSONB
 * @property {any | null} captionContainerStyle // JSONB
 * @property {number | null} audioSpeed
 * @property {string | null} backgroundMusic
 * @property {number | null} estimatedDurationSeconds
 * @property {string | null} status
 * @property {string | null} audioUrl
 * @property {string[] | null} images // text[]
 * @property {string | null} renderId // Added renderId field
 * @property {string | null} bucketName // Added bucketName field
 * @property {string | null} renderedVideoUrl // Added renderedVideoUrl field
 * @property {any | null} workflow_data // JSONB for workflow-specific data
 * @property {string} createdAt
 * @property {string} updatedAt
 * @property {number | null} redditDurationFrames // Added for Remotion
 * @property {number | null} twitterDurationFrames // Added for Remotion
 */

/** @typedef {'Pending' | 'processing' | 'rendering' | 'data_saved' | 'Completed' | 'failed' | 'unknown' | 'idle' | 'fetching' | 'done' | 'error' | 'no credits'} RenderStatus */


const VideoDetailsDisplay = ({
  isLoading,
  renderStatus,
  videoData,
}) => {
  return (
    <div className="w-full md:w-1/2 p-4 border-l flex flex-col">
      <div className="overflow-y-auto max-h-[calc(100vh-300px)] pr-2">
        {isLoading || renderStatus === 'fetching' ? (
          <div className="space-y-2">
            <Skeleton className="h-6 w-3/4" />
            <Skeleton className="h-4 w-1/2" />
            <Skeleton className="h-24 w-full" />
            <Skeleton className="h-4 w-1/3" />
            <Skeleton className="h-4 w-1/4" />
          </div>
        ) : videoData ? (
          <div className="space-y-4">
            <div>
              <h3 className="text-base font-semibold mb-1">Title</h3>
              <p className="text-sm text-muted-foreground">{videoData.title}</p>
            </div>
            <div>
              <h3 className="text-base font-semibold mb-1">Topic</h3>
              <p className="text-sm text-muted-foreground">{videoData.topic}</p>
            </div>
            <div>
              <h3 className="text-base font-semibold mb-1">Script</h3>
              <p className="text-sm text-muted-foreground whitespace-pre-wrap">{videoData.script}</p>
            </div>
            <div>
              <h3 className="text-base font-semibold mb-1">Video Style</h3>
              <p className="text-sm text-muted-foreground">{videoData.videoStyle}</p>
            </div>
            {/* Display workflow-specific data from workflow_data JSONB */}
            {videoData.workflow_data && (
              <div>
                <h3 className="text-base font-semibold mb-1">Workflow Data</h3>
                <pre className="text-sm text-muted-foreground whitespace-pre-wrap overflow-auto max-h-40">
                  {JSON.stringify(videoData.workflow_data, null, 2)}
                </pre>
              </div>
            )}
            <div>
              <h3 className="text-base font-semibold mb-1">Created At</h3>
              <p className="text-sm text-muted-foreground">
                {new Date(videoData.createdAt).toLocaleString()}
              </p>
            </div>
            {/* Display current status */}
            <div>
              <h3 className="text-base font-semibold mb-1">Status</h3>
              <p className={`text-sm font-semibold ${videoData.status === 'Completed' ? 'text-green-500' : videoData.status === 'failed' ? 'text-destructive' : 'text-yellow-500'}`}>
                {videoData.status}
              </p>
            </div>
          </div>
        ) : null}
      </div>
    </div>
  );
};

export default VideoDetailsDisplay;
