'use client';

import React, { useState, useEffect, useCallback } from 'react'; // Added useCallback
import { Card, CardContent } from '@/components/ui/card';
import Image from 'next/image';
import { Loader2, Play, RefreshCw } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { Button } from '@/components/ui/button';
import EmptyState from './EmptyState';
import PlayerDialog from './PlayerDialog';
import { useVideoCache } from '@/hooks/useVideoCache';
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis, // Optional: for more advanced pagination display
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination"; // Import Pagination components

// Define the expected structure of a video object
// This helps with type hinting and understanding the data
/**
 * @typedef {object} Video
 * @property {number} id
 * @property {string} clerkId
 * @property {string} title
 * @property {string} topic
 * @property {string} script
 * @property {string} videoStyle
 * @property {string} voice
 * @property {string} captionName
 * @property {string} captionStyle
 * @property {'Pending' | 'Processing' | 'Completed' | 'Failed'} status
 * @property {string} audioUrl
 * @property {any} captionJson // Adjust type if a specific interface is available
 * @property {string[]} images // Array of image URLs (base64 or hosted)
 * @property {string | null} downloadUrl // URL to the final rendered video
 * @property {string} createdAt // ISO string timestamp
 * @property {string} updatedAt // ISO string timestamp
 */


function VideoList() {
  // Use the video cache hook
  const {
    getVideos,
    refreshVideos,
    startPolling,
    stopPolling,
    needsPolling,
    loading,
    error,
    isRefreshing,
    setError
  } = useVideoCache();

  // Component state
  /** @type {[Video[], React.Dispatch<React.SetStateAction<Video[]>>]} */
  const [videos, setVideos] = useState([]);
  const [isPlayerOpen, setIsPlayerOpen] = useState(false); // State for dialog
  const [selectedVideoId, setSelectedVideoId] = useState(null);

  // --- Pagination State ---
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalCount, setTotalCount] = useState(0); // Total videos across all pages
  const limit = 12; // Videos per page (adjust as needed)
  // --- End Pagination State ---

  // --- Modified fetchVideos with caching ---
  const fetchVideos = useCallback(async (page, forceRefresh = false) => {
    console.log(`[VideoList] Fetching videos for page: ${page}, limit: ${limit}, forceRefresh: ${forceRefresh}`);

    try {
      const data = await getVideos(page, limit, forceRefresh);

      setVideos(Array.isArray(data?.videos) ? data.videos : []);

      // Update pagination state from API response
      if (data.pagination) {
        setCurrentPage(data.pagination.currentPage);
        setTotalPages(data.pagination.totalPages);
        setTotalCount(data.pagination.totalCount);
        console.log('[VideoList] Pagination data received:', data.pagination);
      } else {
        // Fallback if pagination data is missing
        console.warn('[VideoList] Pagination data missing in API response.');
        setCurrentPage(page); // Assume current page is the one requested
        setTotalPages(1); // Default to 1 page if unknown
        setTotalCount(Array.isArray(data?.videos) ? data.videos.length : 0); // Count only current page videos
      }

      // Start polling if needed
      if (needsPolling(data.videos)) {
        startPolling(page, limit, data.videos, (updatedData) => {
          console.log('[VideoList] Polling update received');
          setVideos(Array.isArray(updatedData?.videos) ? updatedData.videos : []);

          // Update pagination if needed
          if (updatedData.pagination) {
            setCurrentPage(updatedData.pagination.currentPage);
            setTotalPages(updatedData.pagination.totalPages);
            setTotalCount(updatedData.pagination.totalCount);
          }
        });
      }

      return data;
    } catch (err) {
      console.error("[VideoList] Failed to fetch videos:", err);
      setVideos([]);
      // Reset pagination on error
      setCurrentPage(1);
      setTotalPages(1);
      setTotalCount(0);
      throw err; // Re-throw to let the hook handle error state
    }
  }, [limit, getVideos, needsPolling, startPolling]); // Include dependencies

  // --- Modified useEffect for Fetching ---
  useEffect(() => {
    // Fetch data for the current page
    fetchVideos(currentPage);

    // Cleanup polling when page changes or component unmounts
    return () => {
      stopPolling();
    };
  }, [currentPage, fetchVideos, stopPolling]); // Re-run effect when currentPage changes

  // Manual refresh handler
  const handleRefresh = useCallback(async () => {
    console.log('[VideoList] Manual refresh triggered');
    try {
      await fetchVideos(currentPage, true); // Force refresh
    } catch (error) {
      console.error('[VideoList] Manual refresh failed:', error);
    }
  }, [currentPage, fetchVideos]);

  const handleVideoClick = (videoId) => {
    setSelectedVideoId(videoId);
    setIsPlayerOpen(true);
  };

  const handleClosePlayer = () => {
    setIsPlayerOpen(false);
    setSelectedVideoId(null);
    // Optionally refetch videos if status might have changed while player was open
    // fetchVideos(currentPage); // Refetch current page after closing dialog? Maybe not necessary.
  };

  // --- Page Change Handler ---
  const handlePageChange = (newPage) => {
    if (newPage >= 1 && newPage <= totalPages && newPage !== currentPage) {
      // Stop current polling before changing page
      stopPolling();
      // Update currentPage state to trigger useEffect with caching
      setCurrentPage(newPage);
    }
  };

  // --- Render Logic ---

  if (loading && videos.length === 0) { // Show loading only on initial load or when videos are empty
    return (
      <div className="flex justify-center items-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
        <p className="ml-2 text-muted-foreground">Loading videos...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="mt-10 flex flex-col items-center justify-center rounded-lg border border-destructive/50 bg-destructive/10 p-12 text-center">
         <h3 className="mb-2 text-xl font-semibold text-destructive">Loading Error</h3>
         <p className="mb-4 text-sm text-destructive/80">{error}</p>
         {/* Pass page 1 to retry */}
         <Button onClick={() => fetchVideos(1)} variant="destructive" size="sm">Retry</Button>
      </div>
    );
  }

  // Show EmptyState only if not loading and total count is confirmed zero
  if (!loading && totalCount === 0 && !error) {
    return (
      <EmptyState />
    );
  }

  return (
    <>
      {/* Refresh Controls */}
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center gap-2 text-body-small text-muted-foreground">
          <span>
            {totalCount > 0 ? `${totalCount} video${totalCount === 1 ? '' : 's'}` : 'No videos'}
          </span>
          {totalCount > 0 && (
            <span>•</span>
          )}
          {totalCount > 0 && (
            <span>Page {currentPage} of {totalPages}</span>
          )}
        </div>

        <Button
          variant="outline"
          size="sm"
          onClick={handleRefresh}
          disabled={isRefreshing}
          className="interactive-scale"
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
          {isRefreshing ? 'Refreshing...' : 'Refresh'}
        </Button>
      </div>

      {/* Optional: Show loading indicator during page transitions */}
      {loading && videos.length > 0 && (
         <div className="absolute inset-0 bg-background/50 flex justify-center items-center z-10">
             <Loader2 className="h-6 w-6 animate-spin text-primary" />
         </div>
      )}

      {/* Video Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 relative"> {/* Changed to proper grid */}
        {videos.map((video) => (
          <Card
            key={video.id}
            className="card-modern overflow-hidden cursor-pointer group interactive-scale"
            // Disable click if not Completed
            onClick={() => video.status === 'Completed' ? handleVideoClick(video.id) : null}
            aria-disabled={video.status !== 'Completed'} // Accessibility
          >
            <CardContent className="p-0">
              <div className="relative aspect-[9/16] w-full bg-gradient-to-br from-muted to-muted/50">
                {/* Thumbnail or Status Placeholder */}
                {video.status === 'Completed' && video.images && video.images.length > 0 ? (
                  <Image
                    src={video.images[0]} // Use first image as thumbnail
                    alt={`Thumbnail for ${video.title || 'video'}`}
                    fill // Use fill instead of layout
                    sizes="(max-width: 640px) 100vw, 280px" // Provide sizes for fill
                    style={{ objectFit: 'cover' }} // Use style for objectFit with fill
                    priority={false} // Avoid making all thumbnails priority
                    unoptimized // If images are externally hosted and not optimized by Next/Image
                  />
                ) : video.status === 'Completed' ? (
                   <div className="flex items-center justify-center w-full h-full bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-900 text-muted-foreground text-caption">
                     <div className="text-center">
                       <div className="text-2xl mb-2">🎬</div>
                       <span>No Thumbnail</span>
                     </div>
                   </div>
                ) : (
                  // Pending/Processing/Failed Placeholder
                  <div className="flex flex-col items-center justify-center w-full h-full bg-gradient-to-br from-muted to-muted/80 text-muted-foreground">
                    {video.status === 'Processing' || video.status === 'Pending' ? (
                      <>
                        <Loader2 className="h-8 w-8 animate-spin mb-3 text-blue-500" />
                        <div className="text-center">
                          <div className="text-body-small font-medium capitalize mb-1">{video.status}</div>
                          <div className="text-caption">Please wait...</div>
                        </div>
                      </>
                    ) : video.status === 'Failed' ? (
                      <div className="text-center">
                        <div className="text-2xl mb-2">❌</div>
                        <div className="text-body-small font-medium text-red-500 mb-1">Failed</div>
                        <div className="text-caption">Try again</div>
                      </div>
                    ): null}
                  </div>
                )}
                {/* Overlay for hover effect on Completed videos */}
                {video.status === 'Completed' && (
                    <div className="absolute inset-0 bg-black/0 group-hover:bg-black/40 transition-all duration-300 flex items-center justify-center">
                        <div className="transform scale-75 group-hover:scale-100 transition-transform duration-300">
                          <Play className="h-12 w-12 text-white opacity-0 group-hover:opacity-90 transition-opacity duration-300" />
                        </div>
                    </div>
                )}
              </div>
              <div className="p-4">
                <h3 className="truncate text-body font-semibold mb-2">
                  {video.title || 'Untitled Video'}
                </h3>
                <div className="flex items-center justify-between">
                  {video.createdAt && (
                    <p className="text-body-small text-muted-foreground">
                      {formatDistanceToNow(new Date(video.createdAt), { addSuffix: true })}
                    </p>
                  )}
                  <div className={`px-2 py-1 rounded-full text-caption font-medium ${
                    video.status === 'Completed' ? 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400' :
                    video.status === 'Processing' ? 'bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400' :
                    video.status === 'Failed' ? 'bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-400' :
                    'bg-gray-100 text-gray-700 dark:bg-gray-900/30 dark:text-gray-400'
                  }`}>
                    {video.status}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Pagination Controls */}
      {totalPages > 1 && (
        <div className="mt-8 flex justify-center">
          <Pagination>
            <PaginationContent>
              <PaginationItem>
                <PaginationPrevious
                  href="#" // Prevent default link behavior
                  onClick={(e) => { e.preventDefault(); handlePageChange(currentPage - 1); }}
                  // Disable button visually and functionally
                  className={currentPage <= 1 ? 'pointer-events-none opacity-50' : undefined}
                  aria-disabled={currentPage <= 1}
                />
              </PaginationItem>

              {/* Basic Page Number Display - Consider a more advanced implementation for many pages */}
              {[...Array(totalPages)].map((_, i) => (
                <PaginationItem key={i + 1}>
                  <PaginationLink
                    href="#" // Prevent default link behavior
                    onClick={(e) => { e.preventDefault(); handlePageChange(i + 1); }}
                    isActive={currentPage === i + 1}
                    aria-current={currentPage === i + 1 ? 'page' : undefined} // Accessibility
                  >
                    {i + 1}
                  </PaginationLink>
                </PaginationItem>
              ))}
              {/* Example: Add Ellipsis if needed (requires more logic) */}
              {/* {totalPages > 5 && currentPage < totalPages - 2 && <PaginationEllipsis />} */}

              <PaginationItem>
                <PaginationNext
                  href="#" // Prevent default link behavior
                  onClick={(e) => { e.preventDefault(); handlePageChange(currentPage + 1); }}
                  // Disable button visually and functionally
                  className={currentPage >= totalPages ? 'pointer-events-none opacity-50' : undefined}
                  aria-disabled={currentPage >= totalPages}
                />
              </PaginationItem>
            </PaginationContent>
          </Pagination>
        </div>
      )}

      {/* Render Player Dialog */}
      <PlayerDialog
        isOpen={isPlayerOpen}
        onOpenChange={setIsPlayerOpen}
        videoId={selectedVideoId}
        onClose={handleClosePlayer}
      />
    </>
  );
}

export default VideoList;