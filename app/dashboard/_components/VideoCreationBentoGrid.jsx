"use client";

import { cn } from "@/lib/utils";
import { TrendingUp } from "lucide-react";
import Link from "next/link";
import { VIDEO_TEMPLATES } from '@/lib/constants/videoTemplates';

// Transform centralized template data for bento grid layout
const videoCreationItems = VIDEO_TEMPLATES.map(template => {
  const IconComponent = template.icon;

  // Special layout configurations
  const layoutConfig = {
    'ai-video': { colSpan: 2, hasPersistentHover: true },
    'meme-video': { colSpan: 2 },
    'ai-ugc-video': { status: 'Beta' },
    'podcast-clipper': { status: 'Pro' },
    'stock-media-video': { status: 'Premium' }
  };

  return {
    title: template.name,
    meta: template.meta,
    description: template.description,
    icon: <IconComponent className={`w-4 h-4 ${template.textColor}`} />,
    status: layoutConfig[template.id]?.status || template.badge,
    tags: template.tags,
    colSpan: layoutConfig[template.id]?.colSpan || 1,
    hasPersistentHover: layoutConfig[template.id]?.hasPersistentHover || false,
    href: template.href,
    cta: template.cta
  };
});

function VideoCreationBentoGrid() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-heading-2">Video Creation Tools</h2>
          <p className="text-body-small text-muted-foreground mt-1">
            Choose from our AI-powered tools to create amazing videos
          </p>
        </div>
        <Link 
          href="/dashboard/create-new-short"
          className="text-sm text-primary hover:text-primary/80 font-medium"
        >
          View All Tools →
        </Link>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 max-w-7xl">
        {videoCreationItems.map((item, index) => (
          <Link
            key={index}
            href={item.href}
            className={cn(
              "group relative p-6 rounded-xl overflow-hidden transition-all duration-300",
              "border border-border/50 bg-card hover:bg-card/80",
              "hover:shadow-lg hover:shadow-primary/5 dark:hover:shadow-primary/10",
              "hover:-translate-y-1 will-change-transform cursor-pointer",
              item.colSpan === 2 ? "md:col-span-2" : "col-span-1",
              {
                "shadow-lg -translate-y-1 bg-card/80": item.hasPersistentHover,
              }
            )}
          >
            <div
              className={`absolute inset-0 ${
                item.hasPersistentHover
                  ? "opacity-100"
                  : "opacity-0 group-hover:opacity-100"
              } transition-opacity duration-300`}
            >
              <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-primary/5" />
            </div>

            <div className="relative flex flex-col space-y-4">
              <div className="flex items-center justify-between">
                <div className="w-10 h-10 rounded-lg flex items-center justify-center bg-muted group-hover:bg-primary/10 transition-all duration-300">
                  {item.icon}
                </div>
                <span
                  className={cn(
                    "text-xs font-medium px-3 py-1 rounded-full",
                    "bg-muted text-muted-foreground",
                    "transition-colors duration-300 group-hover:bg-primary/10 group-hover:text-primary"
                  )}
                >
                  {item.status || item.meta}
                </span>
              </div>

              <div className="space-y-3">
                <h3 className="font-semibold text-foreground tracking-tight text-lg group-hover:text-primary transition-colors">
                  {item.title}
                </h3>
                <p className="text-sm text-muted-foreground leading-relaxed">
                  {item.description}
                </p>
              </div>

              <div className="flex items-center justify-between mt-4">
                <div className="flex items-center space-x-2">
                  {item.tags?.map((tag, i) => (
                    <span
                      key={i}
                      className="text-xs px-2 py-1 rounded-md bg-muted/50 text-muted-foreground group-hover:bg-primary/10 group-hover:text-primary transition-all duration-200"
                    >
                      #{tag}
                    </span>
                  ))}
                </div>
                <span className="text-xs text-primary opacity-0 group-hover:opacity-100 transition-opacity font-medium">
                  {item.cta}
                </span>
              </div>
            </div>

            <div className="absolute inset-0 -z-10 rounded-xl p-px bg-gradient-to-br from-transparent via-border/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
          </Link>
        ))}
      </div>
    </div>
  );
}

export default VideoCreationBentoGrid;
