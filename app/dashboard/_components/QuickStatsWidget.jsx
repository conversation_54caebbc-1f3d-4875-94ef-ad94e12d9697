'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { 
  Video, 
  Clock, 
  TrendingUp, 
  Star, 
  Zap, 
  Target,
  Award,
  Calendar
} from 'lucide-react';
import { cn } from '@/lib/utils';

function QuickStatsWidget({ userCredits }) {
  const [stats, setStats] = useState({
    totalVideos: 0,
    videosThisMonth: 0,
    completedVideos: 0,
    processingVideos: 0,
    totalWatchTime: 0,
    favoriteType: 'AI Video'
  });
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    fetchUserStats();
  }, []);

  const fetchUserStats = async () => {
    try {
      const response = await fetch('/api/user-videos?stats=true');
      if (response.ok) {
        const data = await response.json();
        setStats(data.stats || stats);
      }
    } catch (error) {
      console.error('Error fetching user stats:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getProgressLevel = () => {
    const total = stats.totalVideos;
    if (total >= 50) return { level: 'Expert', progress: 100, next: 'Master', color: 'bg-purple-500' };
    if (total >= 20) return { level: 'Advanced', progress: (total / 50) * 100, next: 'Expert', color: 'bg-blue-500' };
    if (total >= 10) return { level: 'Intermediate', progress: (total / 20) * 100, next: 'Advanced', color: 'bg-green-500' };
    if (total >= 5) return { level: 'Beginner', progress: (total / 10) * 100, next: 'Intermediate', color: 'bg-yellow-500' };
    return { level: 'Starter', progress: (total / 5) * 100, next: 'Beginner', color: 'bg-gray-500' };
  };

  const getCreditStatus = () => {
    if (userCredits >= 100) return { status: 'Excellent', color: 'text-green-600', icon: '🚀' };
    if (userCredits >= 50) return { status: 'Good', color: 'text-blue-600', icon: '⚡' };
    if (userCredits >= 20) return { status: 'Moderate', color: 'text-yellow-600', icon: '⚠️' };
    if (userCredits >= 10) return { status: 'Low', color: 'text-orange-600', icon: '🔋' };
    return { status: 'Critical', color: 'text-red-600', icon: '🚨' };
  };

  const progressLevel = getProgressLevel();
  const creditStatus = getCreditStatus();

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Your Progress
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-full mb-2"></div>
                <div className="h-2 bg-gray-200 rounded w-3/4"></div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="card-modern">
      <CardHeader className="pb-4">
        <CardTitle className="flex items-center gap-2 text-heading-3">
          <div className="p-2 bg-gradient-primary rounded-lg">
            <TrendingUp className="h-5 w-5 text-white" />
          </div>
          Your Progress
        </CardTitle>
        <CardDescription className="text-body-small">
          Track your video creation journey
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Creator Level */}
        <div className="relative">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center gap-2">
              <div className="p-1.5 bg-yellow-100 dark:bg-yellow-900/30 rounded-lg">
                <Award className="h-4 w-4 text-yellow-600 dark:text-yellow-400" />
              </div>
              <span className="font-semibold text-body">{progressLevel.level} Creator</span>
            </div>
            <div className="text-right">
              <div className="text-body font-semibold">{stats.totalVideos}</div>
              <div className="text-caption text-muted-foreground">videos</div>
            </div>
          </div>
          <div className="relative">
            <Progress value={progressLevel.progress} className="h-3 bg-muted" />
            <div className="absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full opacity-80"
                 style={{ width: `${progressLevel.progress}%` }}></div>
          </div>
          <p className="text-caption text-muted-foreground mt-2">
            {Math.ceil((progressLevel.level === 'Expert' ? 50 :
              progressLevel.level === 'Advanced' ? 50 - stats.totalVideos :
              progressLevel.level === 'Intermediate' ? 20 - stats.totalVideos :
              progressLevel.level === 'Beginner' ? 10 - stats.totalVideos :
              5 - stats.totalVideos))} more videos to reach {progressLevel.next}
          </p>
        </div>

        {/* Quick Stats Grid */}
        <div className="grid grid-cols-2 gap-3">
          <div className="relative overflow-hidden rounded-xl bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950/30 dark:to-blue-900/30 p-4 border border-blue-200/50 dark:border-blue-800/50">
            <div className="relative z-10">
              <div className="flex items-center gap-2 mb-2">
                <div className="p-1.5 bg-blue-500 rounded-lg">
                  <Video className="h-3 w-3 text-white" />
                </div>
                <span className="text-caption text-blue-700 dark:text-blue-300 font-medium">Completed</span>
              </div>
              <div className="text-heading-2 font-bold text-blue-900 dark:text-blue-100">{stats.completedVideos}</div>
            </div>
            <div className="absolute top-0 right-0 w-16 h-16 bg-blue-400/10 rounded-full blur-xl"></div>
          </div>

          <div className="relative overflow-hidden rounded-xl bg-gradient-to-br from-emerald-50 to-emerald-100 dark:from-emerald-950/30 dark:to-emerald-900/30 p-4 border border-emerald-200/50 dark:border-emerald-800/50">
            <div className="relative z-10">
              <div className="flex items-center gap-2 mb-2">
                <div className="p-1.5 bg-emerald-500 rounded-lg">
                  <Calendar className="h-3 w-3 text-white" />
                </div>
                <span className="text-caption text-emerald-700 dark:text-emerald-300 font-medium">This Month</span>
              </div>
              <div className="text-heading-2 font-bold text-emerald-900 dark:text-emerald-100">{stats.videosThisMonth}</div>
            </div>
            <div className="absolute top-0 right-0 w-16 h-16 bg-emerald-400/10 rounded-full blur-xl"></div>
          </div>
        </div>

        {/* Credits Status */}
        <div className="relative overflow-hidden rounded-xl bg-gradient-to-br from-yellow-50 to-orange-50 dark:from-yellow-950/30 dark:to-orange-950/30 p-4 border border-yellow-200/50 dark:border-yellow-800/50">
          <div className="relative z-10">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center gap-2">
                <div className="p-1.5 bg-yellow-500 rounded-lg">
                  <Star className="h-4 w-4 text-white" />
                </div>
                <span className="font-semibold text-body text-yellow-800 dark:text-yellow-200">Credits</span>
              </div>
              <span className={cn("text-caption font-semibold px-2 py-1 rounded-full", creditStatus.color)}>
                {creditStatus.icon} {creditStatus.status}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <div className="text-heading-2 font-bold text-yellow-900 dark:text-yellow-100">{userCredits || 0}</div>
              <Button variant="outline" size="sm" className="interactive-scale bg-white/50 dark:bg-black/20" asChild>
                <Link href="/dashboard/billing">
                  <Zap className="h-3 w-3 mr-1" />
                  Get More
                </Link>
              </Button>
            </div>
          </div>
          <div className="absolute top-0 right-0 w-20 h-20 bg-yellow-400/10 rounded-full blur-xl"></div>
        </div>

        {/* Favorite Video Type */}
        {stats.favoriteType && (
          <div className="relative overflow-hidden rounded-xl bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-950/30 dark:to-pink-950/30 p-4 border border-purple-200/50 dark:border-purple-800/50">
            <div className="relative z-10">
              <div className="flex items-center gap-2 mb-2">
                <div className="p-1.5 bg-purple-500 rounded-lg">
                  <Target className="h-4 w-4 text-white" />
                </div>
                <span className="font-semibold text-body text-purple-800 dark:text-purple-200">Favorite Type</span>
              </div>
              <div className="text-body-large font-medium text-purple-900 dark:text-purple-100">{stats.favoriteType}</div>
            </div>
            <div className="absolute top-0 right-0 w-16 h-16 bg-purple-400/10 rounded-full blur-xl"></div>
          </div>
        )}

        {/* Quick Actions */}
        <div className="pt-4 border-t border-border/50">
          <div className="grid grid-cols-2 gap-3">
            <Button variant="outline" size="sm" className="interactive-scale h-10" asChild>
              <Link href="/dashboard/create-new-short">
                <Video className="h-4 w-4 mr-2" />
                Create
              </Link>
            </Button>
            <Button variant="outline" size="sm" className="interactive-scale h-10" asChild>
              <Link href="/dashboard">
                <TrendingUp className="h-4 w-4 mr-2" />
                View All
              </Link>
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export default QuickStatsWidget;
