'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Play, Clock, Eye, MoreHorizontal, Download, Share2 } from 'lucide-react';
import { cn } from '@/lib/utils';
import { formatDistanceToNow } from 'date-fns';

function RecentVideosWidget() {
  const [recentVideos, setRecentVideos] = useState([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    fetchRecentVideos();
  }, []);

  const fetchRecentVideos = async () => {
    try {
      const response = await fetch('/api/user-videos?limit=3&recent=true');
      if (response.ok) {
        const data = await response.json();
        setRecentVideos(data.videos || []);
      }
    } catch (error) {
      console.error('Error fetching recent videos:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'completed':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'processing':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'failed':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getVideoTypeIcon = (templateId) => {
    // Return appropriate icon based on template ID
    switch (templateId) {
      case 'AI_UGC_TALKING_HEAD':
        return '👤';
      case 'MemeVideoTemplate':
        return '😂';
      case 'StockMediaVideo':
        return '🎬';
      default:
        return '🎥';
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Recent Videos
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {[1, 2, 3].map((i) => (
              <div key={i} className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (recentVideos.length === 0) {
    return (
      <Card className="card-modern">
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center gap-2 text-heading-3">
            <div className="p-2 bg-gradient-secondary rounded-lg">
              <Clock className="h-5 w-5 text-white" />
            </div>
            Recent Videos
          </CardTitle>
          <CardDescription className="text-body-small">
            Your recently created videos will appear here
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <div className="relative mb-4">
              <div className="text-6xl mb-2">🎬</div>
              <div className="absolute inset-0 bg-gradient-to-br from-blue-400/20 to-purple-400/20 rounded-full blur-2xl"></div>
            </div>
            <p className="text-body-small text-muted-foreground mb-6">
              No videos created yet
            </p>
            <Button asChild size="sm" className="bg-gradient-primary hover:opacity-90 text-white border-0 interactive-scale">
              <Link href="/dashboard/create-new-short">
                Create Your First Video
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="card-modern">
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2 text-heading-3">
              <div className="p-2 bg-gradient-secondary rounded-lg">
                <Clock className="h-5 w-5 text-white" />
              </div>
              Recent Videos
            </CardTitle>
            <CardDescription className="text-body-small">
              Your latest video creations
            </CardDescription>
          </div>
          <Button variant="ghost" size="sm" className="interactive-scale" asChild>
            <Link href="/dashboard">
              <Eye className="h-4 w-4 mr-1" />
              View All
            </Link>
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {recentVideos.map((video) => (
            <div
              key={video.id}
              className="relative overflow-hidden flex items-center gap-4 p-4 rounded-xl border border-border/50 hover:bg-accent/30 transition-all duration-300 group interactive-scale"
            >
              {/* Video Type Icon */}
              <div className="relative">
                <div className="text-3xl">
                  {getVideoTypeIcon(video.templateId)}
                </div>
                <div className="absolute inset-0 bg-gradient-to-br from-blue-400/10 to-purple-400/10 rounded-full blur-lg"></div>
              </div>

              {/* Video Info */}
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2 mb-2">
                  <h4 className="font-semibold text-body truncate">
                    {video.title || 'Untitled Video'}
                  </h4>
                  <Badge
                    variant="outline"
                    className={cn("text-caption font-medium", getStatusColor(video.status))}
                  >
                    {video.status || 'Unknown'}
                  </Badge>
                </div>
                <p className="text-body-small text-muted-foreground">
                  {video.createdAt ? formatDistanceToNow(new Date(video.createdAt), { addSuffix: true }) : 'Recently'}
                </p>
              </div>

              {/* Quick Actions */}
              <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-all duration-300">
                {video.status === 'Completed' && video.renderedVideoUrl && (
                  <Button variant="ghost" size="sm" className="h-8 w-8 p-0 interactive-scale" asChild>
                    <Link href={`/preview/${video.id}`}>
                      <Play className="h-4 w-4" />
                    </Link>
                  </Button>
                )}
                <Button variant="ghost" size="sm" className="h-8 w-8 p-0 interactive-scale">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </div>

              {/* Background decoration */}
              <div className="absolute top-0 right-0 w-16 h-16 bg-gradient-to-br from-blue-400/5 to-purple-400/5 rounded-full blur-xl"></div>
            </div>
          ))}
        </div>

        {/* Quick Create Actions */}
        <div className="mt-6 pt-4 border-t border-border/50">
          <div className="grid grid-cols-3 gap-2">
            <Button variant="outline" size="sm" asChild className="interactive-scale h-10 flex-col gap-1 p-2">
              <Link href="/dashboard/create-new-short/create-ai-ugc-video">
                <span className="text-lg">👤</span>
                <span className="text-caption">AI UGC</span>
              </Link>
            </Button>
            <Button variant="outline" size="sm" asChild className="interactive-scale h-10 flex-col gap-1 p-2">
              <Link href="/dashboard/create-new-short/create-ai-video">
                <span className="text-lg">🤖</span>
                <span className="text-caption">AI Video</span>
              </Link>
            </Button>
            <Button variant="outline" size="sm" asChild className="interactive-scale h-10 flex-col gap-1 p-2">
              <Link href="/dashboard/create-new-short/create-meme-video">
                <span className="text-lg">😂</span>
                <span className="text-caption">Meme</span>
              </Link>
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export default RecentVideosWidget;
