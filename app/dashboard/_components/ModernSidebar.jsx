"use client";

import { cn } from "@/lib/utils";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { 
  LayoutDashboard, 
  Video, 
  Calendar, 
  CreditCard, 
  Settings, 
  Search,
  ChevronLeft,
  ChevronRight,
  User,
  LogOut
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { useUser } from "@clerk/nextjs";

const sidebarVariants = {
  open: {
    width: "16rem",
    transition: { type: "spring", stiffness: 300, damping: 30 }
  },
  closed: {
    width: "4rem",
    transition: { type: "spring", stiffness: 300, damping: 30 }
  },
};

const contentVariants = {
  open: { opacity: 1, display: "block" },
  closed: { opacity: 0, display: "none" },
};

const navigation = [
  {
    name: "Dashboard",
    href: "/dashboard",
    icon: LayoutDashboard,
  },
  {
    name: "Create Video",
    href: "/dashboard/create-new-short",
    icon: Video,
  },
  {
    name: "Social Scheduler",
    href: "/dashboard/social-scheduler",
    icon: Calendar,
  },
  {
    name: "Image Search",
    href: "/dashboard/image-search",
    icon: Search,
  },
  {
    name: "Billing",
    href: "/dashboard/billing",
    icon: CreditCard,
  },
  {
    name: "Settings",
    href: "/dashboard/settings",
    icon: Settings,
  },
];

function ModernSidebar() {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const pathname = usePathname();
  const { user } = useUser();

  return (
    <motion.div
      className={cn(
        "fixed left-0 top-0 z-40 h-full shrink-0 border-r border-border bg-card/50 backdrop-blur-sm"
      )}
      initial={isCollapsed ? "closed" : "open"}
      animate={isCollapsed ? "closed" : "open"}
      variants={sidebarVariants}
    >
      <div className="flex h-full flex-col">
        {/* Header */}
        <div className="flex h-16 items-center justify-between px-4 border-b border-border">
          <motion.div
            variants={contentVariants}
            className="flex items-center gap-3"
          >
            <div className="w-8 h-8 bg-gradient-to-br from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
              <Video className="w-4 h-4 text-white" />
            </div>
            {!isCollapsed && (
              <span className="font-semibold text-foreground">AI Reel Gen</span>
            )}
          </motion.div>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsCollapsed(!isCollapsed)}
            className="h-8 w-8 p-0"
          >
            {isCollapsed ? (
              <ChevronRight className="h-4 w-4" />
            ) : (
              <ChevronLeft className="h-4 w-4" />
            )}
          </Button>
        </div>

        {/* Navigation */}
        <div className="flex-1 overflow-y-auto py-4">
          <nav className="space-y-2 px-3">
            {navigation.map((item) => {
              const isActive = pathname === item.href || pathname.startsWith(item.href + '/');
              
              return (
                <Link
                  key={item.name}
                  href={item.href}
                  className={cn(
                    "flex items-center gap-3 rounded-lg px-3 py-2 text-sm font-medium transition-all duration-200",
                    "hover:bg-accent hover:text-accent-foreground",
                    isActive 
                      ? "bg-primary text-primary-foreground shadow-sm" 
                      : "text-muted-foreground"
                  )}
                >
                  <item.icon className="h-4 w-4 shrink-0" />
                  <motion.span
                    variants={contentVariants}
                    className="truncate"
                  >
                    {!isCollapsed && item.name}
                  </motion.span>
                </Link>
              );
            })}
          </nav>
        </div>

        {/* User Profile */}
        <div className="border-t border-border p-3">
          <div className={cn(
            "flex items-center gap-3 rounded-lg px-3 py-2",
            "hover:bg-accent transition-colors"
          )}>
            <div className="w-8 h-8 rounded-full bg-gradient-to-br from-blue-500 to-purple-500 flex items-center justify-center">
              <User className="w-4 h-4 text-white" />
            </div>
            <motion.div
              variants={contentVariants}
              className="flex-1 min-w-0"
            >
              {!isCollapsed && (
                <div>
                  <p className="text-sm font-medium text-foreground truncate">
                    {user?.firstName || "User"}
                  </p>
                  <p className="text-xs text-muted-foreground truncate">
                    {user?.emailAddresses?.[0]?.emailAddress || "<EMAIL>"}
                  </p>
                </div>
              )}
            </motion.div>
          </div>
        </div>
      </div>
    </motion.div>
  );
}

export default ModernSidebar;
