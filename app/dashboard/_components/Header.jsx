import React from 'react';
import Link from 'next/link';
import { Plus } from 'lucide-react';
import { SidebarTrigger } from "@/components/ui/sidebar";
import { But<PERSON> } from "@/components/ui/button";

// Simplified header with only Create Video button
function Header() {
  return (
    <header className="sticky top-0 z-30 flex h-16 items-center border-b bg-background px-4 sm:px-6 min-h-[64px]">
      {/* Hamburger menu for mobile, using Shadcn UI SidebarTrigger */}
      <SidebarTrigger className="shrink-0 md:hidden" />

      {/* Spacer to push content to the right */}
      <div className="flex-1" />

      {/* Create Video Button - Primary action */}
      <div className="flex items-center">
        <Button asChild className="bg-gradient-primary hover:opacity-90 text-white border-0 shadow-medium interactive-scale" size="sm">
          <Link href="/dashboard/create-new-short">
            <Plus className="h-4 w-4 mr-2" />
            Create Video
          </Link>
        </Button>
      </div>
    </header>
  );
}

export default Header;
