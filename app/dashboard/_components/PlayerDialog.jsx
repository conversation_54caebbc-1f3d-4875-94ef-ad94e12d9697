"use client"; // Needed for state, effects, dialog interaction, router

import React, { useState, useEffect, useRef } from "react";
import axios from "axios"; // Use axios for consistency
import { toast } from "sonner"; // Import toast for notifications
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Footer,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { X, Download, Play, AlertTriangle, Hourglass } from "lucide-react"; // Icons
// import { Player } from '@remotion/player'; // No longer needed here
// import { MyComposition } from '@/remotion/Composition'; // No longer needed here
// import { getTemplateComponent } from '@/remotion/templates'; // No longer needed here

import VideoPlayerDisplay from './VideoPlayerDisplay'; // Import the new component
import VideoDetailsDisplay from './VideoDetailsDisplay'; // Import the new VideoDetailsDisplay component

// Define a type for the video data expected by the player (based on video_data schema)
/**
 * @typedef {object} VideoPlayerData
 * @property {number | string} id
 * @property {string} clerkId
 * @property {string} title
 * @property {string | null} topic
 * @property {string | null} script
 * @property {string | null} videoStyle
 * @property {string | null} aspectRatio
 * @property {string | null} templateId
 * @property {string | null} voice
 * @property {string | null} captionName
 * @property {any | null} captionStyleJson // JSONB
 * @property {any | null} captionContainerStyle // JSONB
 * @property {number | null} audioSpeed
 * @property {string | null} backgroundMusic
 * @property {number | null} estimatedDurationSeconds
 * @property {string | null} status
 * @property {string | null} audioUrl
 * @property {string[] | null} images // text[]
 * @property {string | null} renderId // Added renderId field
 * @property {string | null} bucketName // Added bucketName field
 * @property {string | null} renderedVideoUrl // Added renderedVideoUrl field
 * @property {any | null} workflow_data // JSONB for workflow-specific data
 * @property {string} createdAt
 * @property {string} updatedAt
 * @property {number | null} redditDurationFrames // Added for Remotion
 * @property {number | null} twitterDurationFrames // Added for Remotion
 */

// Define possible render statuses (matching DB status or Remotion Lambda status)
/** @typedef {'idle' | 'fetching' | 'rendering' | 'done' | 'error'} RenderStatus */

  // Helper function to determine if rendering is disabled
  const isRenderDisabled = (renderStatus, isLoading, error, videoData) => {
    // Disable if any loading state is active
    if (isLoading || renderStatus === 'fetching' || renderStatus === 'rendering') {
      return true;
    }

    // Disable if there are errors or no video data
    if (error || !videoData) {
      return true;
    }

    // Disable if video is already completed or failed (case-insensitive)
    const status = videoData.status?.toLowerCase();
    if (status === 'completed' || status === 'failed') {
      return true;
    }

    return false;
  };

const POLLING_INTERVAL_MS = 5000; // Check status every 5 seconds

function PlayerDialog({ isOpen, onOpenChange, videoId, onClose }) {
  /** @type {[VideoPlayerData | null, React.Dispatch<React.SetStateAction<VideoPlayerData | null>>]} */
  const [videoData, setVideoData] = useState(null);
  const [isLoading, setIsLoading] = useState(false); // Loading video details
  const [error, setError] = useState(null); // General error (fetching details)

  // --- State for Rendering ---
  /** @type {[RenderStatus, React.Dispatch<React.SetStateAction<RenderStatus>>]} */
  const [renderStatus, setRenderStatus] = useState('idle'); // Use a state that maps to DB/Remotion status
  const [renderProgress, setRenderProgress] = useState(0); // Progress from 0 to 1
  const [renderError, setRenderError] = useState(null); // Specific render error details

  const pollingIntervalRef = useRef(null); // Ref to store interval ID

  // --- Fetch Initial Video Details ---
  useEffect(() => {
    const fetchVideoDetails = async () => {
      if (!videoId) {
        // Reset state if videoId is cleared
        setVideoData(null);
        setIsLoading(false);
        setError(null);
        setRenderError(null);
        setRenderStatus('idle');
        setRenderProgress(0);
        if (pollingIntervalRef.current) {
          clearInterval(pollingIntervalRef.current);
          pollingIntervalRef.current = null;
        }
        return;
      }

      console.log(`Fetching details for videoId: ${videoId}`);
      setIsLoading(true);
      setError(null);
      setRenderError(null);
      setVideoData(null);
      setRenderStatus('fetching'); // Indicate fetching state
      setRenderProgress(0);

      try {
        // Fetch video data from the unified /api/video/[id] endpoint
        const response = await axios.get(`/api/video/${videoId}`);
        if (response.status === 200 && response.data?.video) {
          const fetchedData = response.data.video;
          console.log("PlayerDialog: Fetched video data:", fetchedData); // Added console.log
          setVideoData(fetchedData);

          // Set initial render status based on fetched data with consistent mapping
          const dbStatus = fetchedData.status?.toLowerCase();
          if (dbStatus === 'completed' && fetchedData.renderedVideoUrl) {
            setRenderStatus('done');
          } else if (dbStatus === 'rendering' || dbStatus === 'processing') {
            setRenderStatus('rendering');
          } else if (dbStatus === 'failed') {
            setRenderStatus('error');
            setRenderError("Render failed previously.");
          } else {
            setRenderStatus('idle');
          }
        } else {
          throw new Error(response.data?.error || "Failed to fetch video details.");
        }
      } catch (err) {
        console.error(`Failed to fetch video details for ID ${videoId}:`, err);
        const message = err.message || "Could not load video data. Please try again.";
        setError(message);
        setRenderStatus('error');
        toast.error("Error loading video", { description: message });
      } finally {
        setIsLoading(false);
      }
    };

    if (isOpen) {
        fetchVideoDetails();
    } else {
        // Reset all state when dialog closes
        setVideoData(null);
        setIsLoading(false);
        setError(null);
        setRenderError(null);
        setRenderStatus('idle');
        setRenderProgress(0);
        // Clear any existing polling interval
        if (pollingIntervalRef.current) {
          clearInterval(pollingIntervalRef.current);
          pollingIntervalRef.current = null;
        }
    }
  }, [isOpen, videoId]);

  // --- Polling Effect ---
  useEffect(() => {
    const checkProgress = async () => {
      if (!isOpen || !videoData || renderStatus !== 'rendering') {
        console.log("Polling check skipped: Dialog closed, videoData not loaded, or status is not 'rendering'.");
        return;
      }

      console.log(`Polling status for videoId=${videoData.id}`);
      try {
        const response = await axios.get('/api/render-lambda', {
          params: { videoId: videoData.id },
        });

        const progressData = response.data;
        console.log("Polling response:", progressData);

        const status = progressData.status?.toLowerCase();

        if (status === 'error' || status === 'failed') {
          console.error('Render failed:', progressData.errors || progressData.details);
          setRenderError(progressData.message || progressData.details || 'Render encountered an error.');
          setRenderStatus('error');
          // Update videoData status to reflect failure
          setVideoData(prev => prev ? { ...prev, status: 'failed' } : null);
          toast.error("Render Failed", { description: progressData.message || 'An error occurred during rendering.' });
          clearInterval(pollingIntervalRef.current);
          pollingIntervalRef.current = null;
        } else if (status === 'done' || status === 'completed') {
          console.log('Render finished:', progressData.url);
          setRenderStatus('done');
          setRenderProgress(1);
          // Update videoData with the rendered URL and status
          if (progressData.url) {
            setVideoData(prev => prev ? { ...prev, status: 'Completed', renderedVideoUrl: progressData.url } : null);
          }
          toast.success("Render Complete!", { description: "Your video is ready." });
          clearInterval(pollingIntervalRef.current);
          pollingIntervalRef.current = null;
        } else if (status === 'rendering' || status === 'processing') {
          console.log(`Render in progress: ${(progressData.progress || 0) * 100}%`);
          setRenderProgress(progressData.progress ?? 0);
          setRenderStatus('rendering');
          // Update videoData status to reflect current state
          setVideoData(prev => prev ? { ...prev, status: 'rendering' } : null);
        } else {
          console.warn("Received unexpected status from polling endpoint:", progressData);
        }
      } catch (err) {
        console.error('Polling error:', err);
        setRenderError('Failed to get render status. Please try again later.');
        setRenderStatus('error');
        toast.error("Status Check Failed", { description: "Could not retrieve render progress." });
        clearInterval(pollingIntervalRef.current);
        pollingIntervalRef.current = null;
      }
    };

    if (isOpen && videoData && renderStatus === 'rendering' && !pollingIntervalRef.current) {
      console.log("Starting polling...");
      pollingIntervalRef.current = setInterval(checkProgress, POLLING_INTERVAL_MS);
      checkProgress();
    }

    return () => {
      if (pollingIntervalRef.current) {
        console.log("Clearing polling interval.");
        clearInterval(pollingIntervalRef.current);
        pollingIntervalRef.current = null;
      }
    };
  }, [isOpen, videoData, renderStatus]);

  // --- Event Handlers ---
  const handleCancel = () => {
    if (onClose) onClose();
    onOpenChange(false);
  };

  const handleRenderRequest = async () => {
    if (renderStatus === 'rendering' || renderStatus === 'fetching' || isLoading || !videoData) return;

    if (renderStatus === 'done') {
        toast.info("Video is already rendered.");
        return;
    }
     if (renderStatus === 'error') {
        toast.info("Video rendering failed previously. Please try again.");
    }

    setRenderStatus('rendering');
    setRenderProgress(0);
    setRenderError(null);
    toast.info("Initiating video render...");

    try {
      console.log(`Initiating CLOUD render for videoId: ${videoData.id}`);
      const response = await axios.post('/api/render-lambda', {
        videoId: videoData.id,
      });

      if (response.status === 202 && response.data.renderId && response.data.bucketName) {
        console.log('Cloud render request accepted:', response.data);
      } else {
        throw new Error(response.data?.error || `API returned status ${response.status}`);
      }
    } catch (err) {
      console.error('Failed to initiate CLOUD video render:', err);
      const errorMessage = err.response?.data?.error || err.message || 'Could not start the cloud video rendering process.';
      setRenderError(errorMessage);
      setRenderStatus('error');
      toast.error(`Render Request Failed: ${errorMessage}`);
    }
  };

  const handleDownload = () => {
    if (videoData?.renderedVideoUrl) {
        window.open(videoData.renderedVideoUrl, '_blank');
    } else {
        toast.error("Download URL not available.");
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl p-0 sm:max-w-3xl md:max-w-4xl overflow-hidden">
        <DialogHeader className="p-4 pb-2 border-b">
          <DialogTitle className="text-lg truncate">
            {isLoading || renderStatus === 'fetching'
              ? "Loading Video..."
              : videoData?.title || (error || renderError ? "Error" : "Video Player")}
          </DialogTitle>
        </DialogHeader>

        {/* Content Area - Two Column Layout */}
        <div className="flex flex-col md:flex-row">
          <VideoPlayerDisplay
            isLoading={isLoading}
            error={error}
            renderStatus={renderStatus}
            renderError={renderError}
            videoData={videoData}
            renderProgress={renderProgress}
          />

          <VideoDetailsDisplay
            isLoading={isLoading}
            renderStatus={renderStatus}
            videoData={videoData}
          />
        </div>

        <DialogFooter className="border-t bg-muted/50 px-4 py-3 flex justify-between sm:justify-end">
          <Button variant="ghost" onClick={handleCancel} size="sm">
            <X className="mr-2 h-4 w-4" /> Close
          </Button>
          {/* Show Download button if status is done and renderedVideoUrl is available */}
          {videoData?.status?.toLowerCase() === 'completed' && videoData?.renderedVideoUrl ? (
            <Button onClick={handleDownload} disabled={!videoData.renderedVideoUrl} size="sm">
              <Download className="mr-2 h-4 w-4" /> Download Video
            </Button>
          ) : (
            <Button
              onClick={handleRenderRequest}
              disabled={isRenderDisabled(renderStatus, isLoading, error, videoData)}
              size="sm"
            >
              {renderStatus === 'rendering' ? (
                <>
                  <Hourglass className="animate-spin -ml-1 mr-3 h-5 w-5" />
                  Rendering...
                </>
              ) : videoData?.status === 'failed' ? (
                 <>
                   <AlertTriangle className="mr-2 h-4 w-4" /> Render Failed
                 </>
              ) : (
                <>
                  <Play className="mr-2 h-4 w-4" /> Render Video
                </>
              )}
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

export default PlayerDialog;
