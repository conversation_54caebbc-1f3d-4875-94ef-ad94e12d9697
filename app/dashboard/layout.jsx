'use client';

import React, { useState, useEffect } from 'react'; // Import useEffect
import Header from './_components/Header';
import SidebarComponent from './_components/Sidebar'; // Renamed to avoid conflict with Shadcn UI Sidebar
import FloatingActionButton from './_components/FloatingActionButton';
import { useUser } from '@clerk/nextjs';
import { SidebarProvider, Sidebar, SidebarContent, SidebarRail, SidebarTrigger, SidebarInset } from '@/components/ui/sidebar'; // Import Shadcn UI Sidebar components
import { VideoCacheProvider } from '@/contexts/VideoCacheContext';

export default function DashboardLayout({ children }) {
  const { user, isLoaded } = useUser();
  const [userCredits, setUserCredits] = useState(undefined);

  useEffect(() => {
    const fetchCredits = async () => {
      if (isLoaded && user) {
        try {
          const response = await fetch(`/api/get-credits?clerkId=${user.id}`);
          if (response.ok) {
            const data = await response.json();
            setUserCredits(data.credits);
          } else {
            console.error('Failed to fetch credits:', response.statusText);
            setUserCredits(0);
          }
        } catch (error) {
          console.error('Error fetching credits:', error);
          setUserCredits(0);
        }
      } else {
        setUserCredits(undefined);
      }
    };

    fetchCredits();
  }, [isLoaded, user]);

  return (
    <VideoCacheProvider>
      <SidebarProvider defaultOpen={true}> {/* Use SidebarProvider from Shadcn UI */}
        <Sidebar variant="default" collapsible="offcanvas"> {/* Use Shadcn UI Sidebar */}
          <SidebarComponent userCredits={userCredits} user={user} /> {/* Pass user data to sidebar instead */}
        </Sidebar>
        <SidebarInset> {/* Use SidebarInset for the main content area */}
          <div className="flex flex-1 flex-col overflow-y-auto overflow-x-hidden">
            <Header /> {/* Simplified header without user data */}
            <main className="flex-1 bg-muted/40 p-4 md:p-8">
              {children}
            </main>
            <FloatingActionButton />
          </div>
        </SidebarInset>
      </SidebarProvider>
    </VideoCacheProvider>
  );
}
