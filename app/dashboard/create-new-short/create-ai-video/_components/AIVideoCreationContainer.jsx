/**
 * AI Video Creation Container Component
 * Main orchestrator for the AI video creation workflow
 */

import React from 'react';
import VideoConfigurationForm from './VideoConfigurationForm';
import VideoPreviewPanel from './VideoPreviewPanel';
import { useAIVideoForm } from '../hooks/useAIVideoForm';
import { useVideoGeneration } from '../hooks/useVideoGeneration';

/**
 * AI Video Creation Container
 *
 * @param initialCredits - Initial credit balance passed from server component
 */
export default function AIVideoCreationContainer({ initialCredits }) {
  // Form state management
  const {
    formData,
    errors,
    touched,
    isValid,
    hasErrors,
    estimatedDuration,
    updateField,
    updateFields,
    validateForm,
    handleFieldBlur,
    getFieldError,
    resetForm,
    getSubmissionData
  } = useAIVideoForm();

  // Video generation management
  const {
    isGenerating,
    generationMessage,
    previewScript,
    generatePreview,
    generateVideo,
    clearPreview,
    isGenerateButtonDisabled,
    getGenerationButtonText,
    getGenerationButtonTitle,
    hasSufficientCredits,
    VIDEO_GENERATION_COST
  } = useVideoGeneration();

  // Enhanced preview generation that updates form
  const handleGeneratePreview = async (topic, videoStyle) => {
    const generatedScript = await generatePreview(topic, videoStyle);
    if (generatedScript) {
      updateField('script', generatedScript);
    }
    return generatedScript;
  };

  // Enhanced video generation with validation
  const handleGenerateVideo = async (formData) => {
    // Validate form before submission
    if (!validateForm()) {
      return false;
    }

    // Get clean submission data
    const submissionData = getSubmissionData();
    return await generateVideo(submissionData);
  };

  return (
    <div className="space-y-8 max-w-7xl mx-auto">
      {/* Header Section */}
      <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-blue-50 via-purple-50 to-blue-50 dark:from-blue-950/20 dark:via-purple-950/20 dark:to-blue-950/20 p-8 border border-border/50">
        <div className="relative z-10">
          <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-6">
            <div className="space-y-3">
              <h1 className="text-heading-1 text-gradient">
                AI Video Creator ✨
              </h1>
              <p className="text-body-large text-muted-foreground max-w-2xl">
                Create engaging videos with AI-generated scripts, professional voiceovers, and dynamic visuals. 
                Perfect for social media, marketing, and educational content.
              </p>
            </div>
            
            {/* Quick Actions */}
            <div className="flex items-center gap-3">
              {formData.script && (
                <button
                  onClick={clearPreview}
                  className="text-caption text-muted-foreground hover:text-foreground transition-colors"
                >
                  Clear Script
                </button>
              )}
              {hasErrors && (
                <button
                  onClick={resetForm}
                  className="text-caption text-muted-foreground hover:text-foreground transition-colors"
                >
                  Reset Form
                </button>
              )}
            </div>
          </div>
        </div>
        
        {/* Background decoration */}
        <div className="absolute top-0 right-0 w-64 h-64 bg-gradient-to-br from-blue-400/10 to-purple-400/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 left-0 w-48 h-48 bg-gradient-to-tr from-purple-400/10 to-blue-400/10 rounded-full blur-3xl"></div>
      </div>

      {/* Main Content Grid */}
      <div className="grid xl:grid-cols-3 gap-8">
        {/* Left Column - Configuration Form */}
        <div className="xl:col-span-2">
          <VideoConfigurationForm
            formData={formData}
            errors={errors}
            touched={touched}
            onFieldChange={updateField}
            onFieldBlur={handleFieldBlur}
            getFieldError={getFieldError}
            onGeneratePreview={handleGeneratePreview}
            isGenerating={isGenerating}
            estimatedDuration={estimatedDuration}
          />
        </div>

        {/* Right Column - Preview Panel */}
        <div className="xl:col-span-1">
          <div className="sticky top-6">
            <VideoPreviewPanel
              formData={formData}
              onGenerate={handleGenerateVideo}
              isGenerating={isGenerating}
              generationMessage={generationMessage}
              isValid={isValid}
              hasSufficientCredits={hasSufficientCredits}
              userDetail={{ credits: initialCredits || 0 }} // Use server-provided credits
              isGenerateButtonDisabled={isGenerateButtonDisabled}
              getGenerationButtonText={getGenerationButtonText}
              getGenerationButtonTitle={getGenerationButtonTitle}
              VIDEO_GENERATION_COST={VIDEO_GENERATION_COST}
              initialCredits={initialCredits}
            />
          </div>
        </div>
      </div>

      {/* Development Debug Panel (remove in production) */}
      {process.env.NODE_ENV === 'development' && (
        <details className="bg-muted/50 rounded-lg p-4">
          <summary className="text-caption font-medium cursor-pointer">
            🔧 Debug Information
          </summary>
          <div className="mt-4 space-y-2 text-caption font-mono">
            <div>
              <strong>Form Valid:</strong> {isValid ? '✅' : '❌'}
            </div>
            <div>
              <strong>Has Errors:</strong> {hasErrors ? '❌' : '✅'}
            </div>
            <div>
              <strong>Estimated Duration:</strong> {estimatedDuration}s
            </div>
            <div>
              <strong>Is Generating:</strong> {isGenerating ? '⏳' : '✅'}
            </div>
            <div>
              <strong>Errors:</strong> {JSON.stringify(errors, null, 2)}
            </div>
            <div>
              <strong>Touched Fields:</strong> {JSON.stringify(touched, null, 2)}
            </div>
          </div>
        </details>
      )}
    </div>
  );
}
