/**
 * Script Section Component
 * Handles script input, preview generation, and validation
 */

import React from 'react';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { <PERSON>ert<PERSON>ir<PERSON>, Sparkles, Loader2, RefreshCw } from 'lucide-react';

export default function ScriptSection({
  script,
  topic,
  videoStyle,
  onScriptChange,
  onGeneratePreview,
  onFieldBlur,
  getFieldError,
  isGenerating,
  estimatedDuration,
  className = ''
}) {
  const scriptError = getFieldError('script');
  const canGeneratePreview = topic && videoStyle && !isGenerating;

  const handleGeneratePreview = async () => {
    if (!canGeneratePreview) return;
    
    const generatedScript = await onGeneratePreview(topic, videoStyle);
    if (generatedScript) {
      onScriptChange(generatedScript);
    }
  };

  const getWordCount = (text) => {
    return text.trim().split(/\s+/).filter(word => word.length > 0).length;
  };

  const getReadingTime = (text) => {
    const words = getWordCount(text);
    const wordsPerMinute = 150; // Average reading speed
    const minutes = words / wordsPerMinute;
    return Math.max(0.1, minutes);
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Section Header */}
      <div className="flex items-center justify-between">
        <div className="space-y-2">
          <h3 className="text-heading-3 text-foreground">Video Script</h3>
          <p className="text-body-small text-muted-foreground">
            Write your script or generate one with AI
          </p>
        </div>
        
        {/* Generate Preview Button */}
        <Button
          onClick={handleGeneratePreview}
          disabled={!canGeneratePreview}
          variant="outline"
          size="sm"
          className="shrink-0"
        >
          {isGenerating ? (
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          ) : (
            <Sparkles className="mr-2 h-4 w-4" />
          )}
          {isGenerating ? 'Generating...' : 'Generate Preview'}
        </Button>
      </div>

      {/* Script Input */}
      <div className="space-y-2">
        <Label htmlFor="script" className="text-body-medium font-medium">
          Script Content *
        </Label>
        <div className="relative">
          <Textarea
            id="script"
            placeholder={
              canGeneratePreview 
                ? "Click 'Generate Preview' to create a script, or write your own..."
                : "Enter your video script here, or fill in topic and style first to generate one..."
            }
            value={script}
            onChange={(e) => onScriptChange(e.target.value)}
            onBlur={() => onFieldBlur('script')}
            className={`w-full min-h-[200px] resize-none ${scriptError ? 'border-destructive focus:border-destructive' : ''}`}
            maxLength={2000}
          />
          {scriptError && (
            <div className="absolute right-3 top-3">
              <AlertCircle className="h-4 w-4 text-destructive" />
            </div>
          )}
        </div>
        
        {scriptError && (
          <p className="text-caption text-destructive flex items-center gap-1">
            <AlertCircle className="h-3 w-3" />
            {scriptError}
          </p>
        )}

        {/* Script Statistics */}
        <div className="flex justify-between items-center text-caption text-muted-foreground">
          <div className="flex items-center gap-4">
            <span>{getWordCount(script)} words</span>
            <span>~{getReadingTime(script).toFixed(1)} min read</span>
            {estimatedDuration > 0 && (
              <span>~{estimatedDuration}s video</span>
            )}
          </div>
          <span>{script.length}/2000 characters</span>
        </div>
      </div>

      {/* Script Guidelines */}
      {!script && (
        <div className="bg-muted/50 rounded-lg p-4 space-y-3">
          <h4 className="text-body-small font-medium text-foreground flex items-center gap-2">
            <Sparkles className="h-4 w-4" />
            Script Writing Tips:
          </h4>
          <ul className="space-y-2 text-caption text-muted-foreground">
            <li className="flex items-start gap-2">
              <span className="text-primary">•</span>
              <span>Keep it conversational and engaging</span>
            </li>
            <li className="flex items-start gap-2">
              <span className="text-primary">•</span>
              <span>Aim for 100-150 words for a 60-second video</span>
            </li>
            <li className="flex items-start gap-2">
              <span className="text-primary">•</span>
              <span>Start with a hook to grab attention</span>
            </li>
            <li className="flex items-start gap-2">
              <span className="text-primary">•</span>
              <span>Include a clear call-to-action at the end</span>
            </li>
          </ul>
        </div>
      )}

      {/* Preview Generation Help */}
      {!canGeneratePreview && !script && (
        <div className="bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
          <div className="flex items-start gap-3">
            <div className="bg-blue-100 dark:bg-blue-900/30 rounded-full p-1">
              <Sparkles className="h-4 w-4 text-blue-600 dark:text-blue-400" />
            </div>
            <div className="space-y-1">
              <h4 className="text-body-small font-medium text-blue-900 dark:text-blue-100">
                AI Script Generation Available
              </h4>
              <p className="text-caption text-blue-700 dark:text-blue-300">
                Fill in your topic and select a video style above to generate a script automatically.
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Regenerate Option */}
      {script && canGeneratePreview && (
        <div className="flex items-center justify-center">
          <Button
            onClick={handleGeneratePreview}
            disabled={isGenerating}
            variant="ghost"
            size="sm"
            className="text-muted-foreground hover:text-foreground"
          >
            <RefreshCw className="mr-2 h-4 w-4" />
            Regenerate Script
          </Button>
        </div>
      )}
    </div>
  );
}
