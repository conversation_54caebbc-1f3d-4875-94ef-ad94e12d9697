/**
 * Project Details Section Component
 * Handles project title and topic input with validation
 */

import React from 'react';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { AlertCircle } from 'lucide-react';

export default function ProjectDetailsSection({
  title,
  topic,
  onTitleChange,
  onTopicChange,
  onFieldBlur,
  getFieldError,
  className = ''
}) {
  const titleError = getFieldError('projectTitle');
  const topicError = getFieldError('topic');

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Section Header */}
      <div className="space-y-2">
        <h3 className="text-heading-3 text-foreground">Project Details</h3>
        <p className="text-body-small text-muted-foreground">
          Set up your video project with a title and topic
        </p>
      </div>

      {/* Project Title */}
      <div className="space-y-2">
        <Label htmlFor="projectTitle" className="text-body-medium font-medium">
          Project Title *
        </Label>
        <div className="relative">
          <Input
            id="projectTitle"
            type="text"
            placeholder="Enter your project title..."
            value={title}
            onChange={(e) => onTitleChange(e.target.value)}
            onBlur={() => onFieldBlur('projectTitle')}
            className={`w-full ${titleError ? 'border-destructive focus:border-destructive' : ''}`}
            maxLength={100}
          />
          {titleError && (
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
              <AlertCircle className="h-4 w-4 text-destructive" />
            </div>
          )}
        </div>
        {titleError && (
          <p className="text-caption text-destructive flex items-center gap-1">
            <AlertCircle className="h-3 w-3" />
            {titleError}
          </p>
        )}
        <p className="text-caption text-muted-foreground">
          {title.length}/100 characters
        </p>
      </div>

      {/* Topic */}
      <div className="space-y-2">
        <Label htmlFor="topic" className="text-body-medium font-medium">
          Video Topic *
        </Label>
        <div className="relative">
          <Textarea
            id="topic"
            placeholder="Describe what your video should be about..."
            value={topic}
            onChange={(e) => onTopicChange(e.target.value)}
            onBlur={() => onFieldBlur('topic')}
            className={`w-full min-h-[100px] resize-none ${topicError ? 'border-destructive focus:border-destructive' : ''}`}
            maxLength={500}
          />
          {topicError && (
            <div className="absolute right-3 top-3">
              <AlertCircle className="h-4 w-4 text-destructive" />
            </div>
          )}
        </div>
        {topicError && (
          <p className="text-caption text-destructive flex items-center gap-1">
            <AlertCircle className="h-3 w-3" />
            {topicError}
          </p>
        )}
        <div className="flex justify-between items-center">
          <p className="text-caption text-muted-foreground">
            Be specific about your topic for better AI-generated content
          </p>
          <p className="text-caption text-muted-foreground">
            {topic.length}/500 characters
          </p>
        </div>
      </div>

      {/* Topic Examples */}
      {!topic && (
        <div className="bg-muted/50 rounded-lg p-4 space-y-3">
          <h4 className="text-body-small font-medium text-foreground">
            💡 Topic Examples:
          </h4>
          <div className="grid grid-cols-1 gap-2">
            {[
              "The benefits of morning meditation for productivity",
              "How to start a successful online business in 2024",
              "5 simple cooking tips for busy professionals",
              "The science behind climate change explained simply"
            ].map((example, index) => (
              <button
                key={index}
                onClick={() => onTopicChange(example)}
                className="text-left text-caption text-muted-foreground hover:text-foreground transition-colors p-2 rounded hover:bg-muted/70"
              >
                "{example}"
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
