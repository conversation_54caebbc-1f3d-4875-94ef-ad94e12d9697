// app/dashboard/create-new/_components/Captions.jsx
'use client';

import React, { useState, useEffect } from 'react'; // Added useEffect
import { cn } from '@/lib/utils';

// --- Placeholder Data ---
const captionOptions = [
    {
        name: 'None',
        description: 'No captions will be added.',
        captionStyle: null, // No specific style for text
        captionContainerStyle: null, // No specific style for container
    },
    {
        name: 'Standard Bottom',
        description: 'Simple white text, slightly offset from bottom.',
        captionStyle: {
            color: 'white',
            fontSize: '5vmin', // Responsive font size
            fontWeight: 'bold',
            textAlign: 'center',
            textShadow: '2px 2px 4px rgba(0,0,0,0.7)',
        },
        captionContainerStyle: {
            justifyContent: 'flex-end', // Aligns caption to bottom
            alignItems: 'center',    // Centers horizontally
            paddingBottom: '5%',     // Padding from bottom
            // backgroundColor: 'rgba(0,0,0,0.3)', // Optional subtle background for container
            // borderRadius: '5px',
        },
    },
    {
        name: 'Yell',
        description: 'Yellow text in a black box at the top.',
        captionStyle: {
            color: 'yellow',
            fontSize: '6vmin',
            fontWeight: 'bold',
            textAlign: 'center',
            padding: '2vmin', // Padding inside the box
        },
        captionContainerStyle: {
            justifyContent: 'flex-start', // Aligns to top
            alignItems: 'center',
            paddingTop: '5%',
            backgroundColor: 'rgba(0,0,0,0.75)',
            width: '90%', // Box width
            marginLeft: '5%', // Center the box
            marginRight: '5%',
            borderRadius: '10px',
        },
    },
    {
        name: 'Yellow Boxed Top',
        description: 'Larger text, white with black outline, centered.',
        // Note: Actual animation would be handled in Remotion component if 'currentWord' changes
        captionStyle: {
            color: 'white',
            fontSize: '7vmin',
            fontWeight: 'bold',
            textAlign: 'center',
            textShadow: '0 0 5px black, 0 0 5px black, 0 0 5px black, 0 0 5px black', // Simple outline
            // For a "pop" effect, you might animate scale or opacity in Remotion based on word timing
        },
        captionContainerStyle: {
            justifyContent: 'center', // Center vertically
            alignItems: 'center',   // Center horizontally
            // No specific background, relies on text shadow for visibility
        },
    },
    {
        name: 'Clean Left Align',
        description: 'White text, left-aligned, subtle background, lower-left.',
        captionStyle: {
            color: 'white',
            fontSize: '4.5vmin',
            fontWeight: '500',
            textAlign: 'left',
            padding: '1.5vmin 2vmin',
        },
        captionContainerStyle: {
            justifyContent: 'flex-end',
            alignItems: 'flex-start',
            paddingBottom: '5%',
            paddingLeft: '5%',
            backgroundColor: 'rgba(0,0,0,0.4)',
            width: 'auto', // Fit content
            maxWidth: '70%', // Max width for readability
            borderRadius: '8px',
        },
    },
    {
        name: 'Bold Red Center',
        description: 'Large, bold red text, centered, high contrast.',
        captionStyle: {
            color: 'red',
            fontSize: '7vmin',
            fontWeight: 'bold',
            textAlign: 'center',
            textShadow: '3px 3px 6px rgba(0,0,0,0.9)',
        },
        captionContainerStyle: {
            justifyContent: 'center',
            alignItems: 'center',
            width: '100%', // Full width for centering text
        },
    },
    {
        name: 'Elegant Script Bottom',
        description: 'Script-like font, white, centered at bottom.',
        captionStyle: {
            fontFamily: 'Georgia, serif', // Example script-like font
            color: 'white',
            fontSize: '5.5vmin',
            fontWeight: 'normal',
            textAlign: 'center',
            textShadow: '1px 1px 3px rgba(0,0,0,0.5)',
        },
        captionContainerStyle: {
            justifyContent: 'flex-end',
            alignItems: 'center',
            paddingBottom: '6%',
        },
    },
];
// --- End Placeholder ---

// Update props to be specific
// export default function Captions({ onInputChange, formData }) {
export default function Captions({ onCaptionSelect, selectedCaption: initialSelectedCaption }) {
    // Use local state synced with the prop (initialSelectedCaption is the object)
    const [selectedCaption, setSelectedCaption] = useState(initialSelectedCaption || captionOptions.find(c => c.name === 'None') || null);

    // Sync local state if the prop changes from the parent
    useEffect(() => {
        setSelectedCaption(initialSelectedCaption || captionOptions.find(c => c.name === 'None') || null);
    }, [initialSelectedCaption]);

    const handleSelectCaption = (captionObject) => {
        setSelectedCaption(captionObject);
        // Call the specific prop handler, passing the selected object
        if (onCaptionSelect) {
            onCaptionSelect(captionObject);
        }
    };

    return (
        <div className="space-y-4 rounded-lg border bg-card p-6 shadow-sm">
            {/* Assuming Voice selection was intended to be here or is removed */}
            {/* <h3 className="text-lg font-semibold mb-2">3. Voice</h3> ... */}

            <h3 className="text-lg font-semibold mb-2">3. Captions Style</h3>
            <p className="text-sm text-muted-foreground mb-4">Choose how captions should appear (if any).</p>
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
                {captionOptions.map((caption) => (
                    <div
                        key={caption.name}
                        onClick={() => handleSelectCaption(caption)} // caption is the full object
                        className={cn(
                            "group cursor-pointer rounded-lg border bg-background p-3 transition-all duration-200 ease-in-out hover:shadow-md flex flex-col h-full",
                            selectedCaption?.name === caption.name ? "border-primary ring-2 ring-primary ring-offset-2 ring-offset-background" : "hover:border-muted-foreground/50"
                        )}
                    >
                        {/* Placeholder for Visual Representation - This part is tricky to make fully dynamic without a mini-renderer */}
                        <div className="relative mb-2 flex h-16 w-full items-center justify-center overflow-hidden rounded bg-muted flex-shrink-0">
                             <span className="text-center text-xs text-muted-foreground px-1">
                                {caption.name === 'None' ? 'No Captions' : `${caption.name}`}
                             </span>
                             {/* Basic preview based on some style properties */}
                             {caption.name === 'Standard Bottom' && (
                                <div style={{position: 'absolute', bottom: '5px', color: caption.captionStyle?.color || 'white', fontSize: '10px', textShadow: caption.captionStyle?.textShadow || 'none' }}>Abc</div>
                             )}
                             {caption.name === 'Yellow Boxed Top' && (
                                <div style={{position: 'absolute', top: '5px', backgroundColor: caption.captionContainerStyle?.backgroundColor || 'black', color: caption.captionStyle?.color || 'yellow', padding: '2px', borderRadius: '3px', fontSize: '10px' }}>Abc</div>
                             )}
                              {caption.name === 'Animated Pop' && (
                               <div style={{position: 'absolute', color: caption.captionStyle?.color || 'white', fontSize: '12px', fontWeight: 'bold', textShadow: caption.captionStyle?.textShadow || '0 0 2px black' }}>Abc</div>
                            )}
                           {caption.name === 'Clean Left Align' && (
                              <div style={{position: 'absolute', bottom: '5px', left: '5px', backgroundColor: caption.captionContainerStyle?.backgroundColor || 'rgba(0,0,0,0.4)', color: caption.captionStyle?.color || 'white', padding: '2px 4px', borderRadius: '3px', fontSize: '9px', textAlign: 'left' }}>Abc</div>
                           )}
                           {caption.name === 'Bold Red Center' && (
                              <div style={{position: 'absolute', color: caption.captionStyle?.color || 'red', fontSize: '12px', fontWeight: 'bold', textShadow: '1px 1px 1px black' }}>Abc</div>
                           )}
                           {caption.name === 'Elegant Script Bottom' && (
                              <div style={{position: 'absolute', bottom: '5px', fontFamily: caption.captionStyle?.fontFamily || 'Georgia, serif', color: caption.captionStyle?.color || 'white', fontSize: '11px', textShadow: '1px 1px 1px black' }}>Abc</div>
                           )}
                      </div>
                      <div className="flex flex-col flex-grow min-w-0">
                          <h4 className="text-center font-medium text-sm mb-1 truncate">{caption.name}</h4>
                            <p className="text-xs text-muted-foreground text-center line-clamp-2 break-words">{caption.description}</p>
                        </div>
                    </div>
                ))}
            </div>
             {/* Display selected caption style for confirmation */}
             {selectedCaption && (
                 <p className="text-sm text-muted-foreground mt-3">Selected: <span className="font-medium text-foreground">{selectedCaption.name}</span></p>
             )}
        </div>
    );
}