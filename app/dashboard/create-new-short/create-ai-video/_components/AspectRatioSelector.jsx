// app/dashboard/create-new/_components/AspectRatioSelector.jsx
'use client';

import React, { useState, useEffect } from 'react';
import { RectangleHorizontal, Ratio, RectangleVertical } from 'lucide-react';
import { OptionSelector } from '@/components/shared/OptionSelector';
import { FormSection } from '@/components/shared/FormSection';

// Aspect ratio options configuration
const aspectRatios = [
    {
        id: '16:9',
        name: 'Landscape (16:9)',
        icon: RectangleHorizontal,
        description: 'Standard widescreen format.',
        enabled: false,
        disabledReason: 'Coming Soon'
    },
    {
        id: '1:1',
        name: 'Square (1:1)',
        icon: Ratio,
        description: 'Ideal for social media feeds.',
        enabled: false,
        disabledReason: 'Coming Soon'
    },
    {
        id: '9:16',
        name: 'Vertical (9:16)',
        icon: RectangleVertical,
        description: 'Perfect for stories and reels.',
        enabled: true
    },
];

const defaultEnabledRatio = aspectRatios.find(r => r.enabled)?.id || '';

export default function AspectRatioSelector({ onAspectRatioSelect, selectedAspectRatio: initialSelectedAspectRatio }) {
    const getInitialRatio = () => {
        const initial = aspectRatios.find(r => r.id === initialSelectedAspectRatio);
        if (initial && initial.enabled) {
            return initial.id;
        }
        return defaultEnabledRatio;
    };

    const [selectedRatio, setSelectedRatio] = useState(getInitialRatio());

    useEffect(() => {
        const currentSelected = aspectRatios.find(r => r.id === initialSelectedAspectRatio);
        if (currentSelected && currentSelected.enabled) {
            setSelectedRatio(initialSelectedAspectRatio);
        } else if (initialSelectedAspectRatio && !currentSelected?.enabled) {
            // If an initial aspect ratio was provided but it's now disabled,
            // select the default enabled one and inform the parent.
            setSelectedRatio(defaultEnabledRatio);
            if (onAspectRatioSelect) {
                onAspectRatioSelect(defaultEnabledRatio);
            }
        } else if (!initialSelectedAspectRatio) {
             setSelectedRatio(defaultEnabledRatio);
        }
    }, [initialSelectedAspectRatio, onAspectRatioSelect]);

    const handleSelectRatio = (ratioId) => {
        const ratio = aspectRatios.find(r => r.id === ratioId);
        if (ratio && ratio.enabled) {
            setSelectedRatio(ratioId);
            if (onAspectRatioSelect) {
                onAspectRatioSelect(ratioId);
            }
        }
    };

    return (
        <FormSection
            title="3. Aspect Ratio"
            description="Choose the dimensions for your video."
            variant="card"
            className="space-y-4"
        >
            <OptionSelector
                options={aspectRatios}
                selectedValue={selectedRatio}
                onSelect={handleSelectRatio}
                layout="grid"
                columns={3}
            />
        </FormSection>
    );
}