// app/dashboard/create-new/_components/AudioSpeedSelector.jsx
'use client';

import React from 'react';
import { OptionSelector } from '@/components/shared/OptionSelector';
import { FormSection } from '@/components/shared/FormSection';

const speedOptions = [
	{ id: 0.5, name: '0.5x (Very Slow)', value: 0.5 },
	{ id: 0.75, name: '0.75x (Slower)', value: 0.75 },
	{ id: 1.0, name: '1.0x (Normal)', value: 1.0 },
	{ id: 1.25, name: '1.25x (Faster)', value: 1.25 },
	{ id: 1.5, name: '1.5x (Very Fast)', value: 1.5 },
	{ id: 1.75, name: '1.75x (Even Faster)', value: 1.75 },
	{ id: 2.0, name: '2.0x (Fastest)', value: 2.0 },
];

export default function AudioSpeedSelector({ selectedSpeed, onSpeedChange }) {
	const handleSpeedChange = (value) => {
		if (onSpeedChange) {
			onSpeedChange(value);
		}
	};

	const helpText = selectedSpeed !== 1.0
		? "Note: Changing audio speed will affect the overall video duration. Captions will still sync with the audio."
		: undefined;

	return (
		<FormSection
			title="Audio Speed"
			description="Adjust the playback speed of the generated audio."
			variant="card"
			helpText={helpText}
		>
			<OptionSelector
				options={speedOptions}
				selectedValue={selectedSpeed || 1.0}
				onSelect={handleSpeedChange}
				layout="dropdown"
				placeholder="Select speed"
			/>
		</FormSection>
	);
}