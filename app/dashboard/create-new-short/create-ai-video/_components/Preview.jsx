// app/dashboard/create-new/_components/Preview.jsx
'use client';

import React, { useState, useEffect } from 'react'; // Added useState, useEffect
import Image from 'next/image'; // Use Next.js Image component for optimization
import { cn } from '@/lib/utils';
import { Label } from '@/components/ui/label'; // Import Label
import { Input } from '@/components/ui/input'; // Import Input
import { Textarea } from '@/components/ui/textarea'; // Import Textarea
import { Button } from '@/components/ui/button'; // Import Button
import { Sparkles, Loader2 } from 'lucide-react'; // Import icons

// --- Placeholder Data (Match with VideoStyle component) ---
const styleImages = {
    cinematic: '/placeholders/style-cinematic.jpg',
    minimalist: '/placeholders/style-minimalist.jpg',
    cartoon: '/placeholders/style-cartoon.jpg',
    documentary: '/placeholders/style-documentary.jpg',
    default: '/placeholders/style-default.jpg' // Fallback image
};

// --- Placeholder Caption Styles (Match with Captions component) ---
const captionStyleClasses = {
    'standard-bottom': 'absolute bottom-4 left-4 right-4 bg-black/50 text-white p-2 text-center text-sm rounded',
    'highlighted-karaoke': 'absolute bottom-4 left-4 right-4 text-center text-lg font-bold text-yellow-300 stroke-black', // Example
    'boxed-background': 'absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-gray-800/70 text-white px-4 py-1 text-sm rounded-md',
};
// --- End Placeholders ---

// Update props to be specific
// export default function Preview({ formData }) {
export default function Preview({
    projectTitle,
    onTitleChange,
    script,
    onScriptChange,
    videoStyle,
    caption,
    voice,
    // Generation props
    onGenerateVideo,
    isGenerating,
    generationMessage,
    isGenerateButtonDisabled,
    userDetail,
    VIDEO_GENERATION_COST
}) { // Added generation props

    // Local state for controlled inputs, synced with props
    const [titleInput, setTitleInput] = useState(projectTitle || '');
    const [scriptInput, setScriptInput] = useState(script || '');

    useEffect(() => {
        setTitleInput(projectTitle || '');
    }, [projectTitle]);

    useEffect(() => {
        setScriptInput(script || '');
    }, [script]);

    const handleTitleInputChange = (e) => {
        const value = e.target.value;
        setTitleInput(value);
        if (onTitleChange) {
            onTitleChange(value);
        }
    };

    const handleScriptInputChange = (e) => {
        const value = e.target.value;
        setScriptInput(value);
        if (onScriptChange) {
            onScriptChange(value);
        }
    };

    const previewImageUrl = styleImages[videoStyle] || styleImages.default;
    const selectedCaptionStyle = caption?.style; // e.g., 'standard-bottom'
    const captionText = "This is how your captions might look..."; // Placeholder text

    return (
        <div className="relative sticky top-4 xl:top-20 h-fit">
            <div className="absolute inset-0 bg-gradient-to-br from-indigo-50/50 to-purple-50/50 dark:from-indigo-950/10 dark:to-purple-950/10 rounded-2xl"></div>
            <div className="relative bg-background/80 backdrop-blur-sm border border-border/50 rounded-2xl p-6 max-h-[calc(100vh-2rem)] xl:max-h-[calc(100vh-6rem)] overflow-y-auto">
                {/* Section Header */}
                <div className="flex items-center gap-3 mb-6 pb-4 border-b border-border/30">
                    <div className="p-2 bg-gradient-to-br from-indigo-500 to-purple-500 rounded-lg shadow-soft">
                        <div className="h-6 w-6 text-white flex items-center justify-center text-lg">👁️</div>
                    </div>
                    <div>
                        <h3 className="text-heading-2 text-gradient">Preview & Finalize</h3>
                        <p className="text-body-small text-muted-foreground">Review your video configuration</p>
                    </div>
                </div>

            {/* Visual Preview */}
            <div className="space-y-4">
                <div className="space-y-3">
                    <Label className="text-body font-medium">Visual Preview</Label>
                    <div className="aspect-video bg-gradient-to-br from-muted to-muted/50 rounded-xl overflow-hidden relative border border-border/50">
                        <Image
                            src={previewImageUrl}
                            alt={videoStyle ? `${videoStyle} style preview` : 'Video preview'}
                            layout="fill"
                            objectFit="cover"
                            className="transition-opacity duration-300 ease-in-out"
                            key={previewImageUrl}
                        />
                        {selectedCaptionStyle && captionStyleClasses[selectedCaptionStyle] && (
                            <div className={cn(captionStyleClasses[selectedCaptionStyle])}>
                                {captionText}
                            </div>
                        )}
                        {!videoStyle && (
                            <div className="absolute inset-0 flex items-center justify-center bg-muted/80">
                                <div className="text-center">
                                    <div className="text-4xl mb-2">🎨</div>
                                    <p className="text-body-small text-muted-foreground">Select a video style to see preview</p>
                                </div>
                            </div>
                        )}
                        {/* Preview overlay */}
                        <div className="absolute top-3 right-3 bg-black/50 text-white px-2 py-1 rounded text-caption">
                            Preview
                        </div>
                    </div>

                    {/* Configuration Summary */}
                    <div className="grid grid-cols-1 gap-3">
                        <div className="p-3 bg-muted/20 rounded-lg border border-border/20">
                            <div className="text-body-small text-muted-foreground space-y-1">
                                <div className="flex justify-between">
                                    <span>Style:</span>
                                    <span className="font-medium text-foreground">{videoStyle || 'Not selected'}</span>
                                </div>
                                <div className="flex justify-between">
                                    <span>Voice:</span>
                                    <span className="font-medium text-foreground">{voice || 'Not selected'}</span>
                                </div>
                                <div className="flex justify-between">
                                    <span>Captions:</span>
                                    <span className="font-medium text-foreground">{caption?.name || 'Not selected'}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Script Preview/Edit */}
                <div className="space-y-3">
                    <Label htmlFor="script-preview" className="text-body font-medium">Script Editor</Label>
                    <Textarea
                        id="script-preview"
                        placeholder="Your generated script will appear here. You can edit it before generating the video..."
                        value={scriptInput}
                        onChange={handleScriptInputChange}
                        rows={8}
                        className="min-h-[160px] text-body resize-none border-border/30"
                    />
                    <p className="text-body-small text-muted-foreground">Review and edit the generated script. This will be used for the AI voiceover.</p>
                </div>

                {/* Generation Status */}
                {isGenerateButtonDisabled && !isGenerating && (
                    <div className="p-3 bg-yellow-50/50 dark:bg-yellow-950/20 rounded-lg border border-yellow-200/30 dark:border-yellow-800/30">
                        <p className="text-body-small text-yellow-800 dark:text-yellow-200 text-center">
                            {!userDetail || userDetail.credits < VIDEO_GENERATION_COST
                                ? `Insufficient credits (Need ${VIDEO_GENERATION_COST}, have ${userDetail?.credits || 0})`
                                : "Please complete all required fields to generate your video"
                            }
                        </p>
                    </div>
                )}

                {/* Generate Video Button */}
                <div className="pt-4 border-t border-border/30">
                    <Button
                        onClick={onGenerateVideo}
                        disabled={isGenerateButtonDisabled}
                        size="lg"
                        className="w-full bg-gradient-to-br from-indigo-500 to-purple-500 hover:from-indigo-600 hover:to-purple-600 text-white border-0 shadow-medium interactive-scale h-12"
                    >
                        {isGenerating ? (
                            <div className="flex items-center gap-2">
                                <Loader2 className="h-5 w-5 animate-spin" />
                                <span className="text-body">{generationMessage}</span>
                            </div>
                        ) : (
                            <div className="flex items-center gap-2">
                                <Sparkles className="h-5 w-5" />
                                <span className="text-body font-semibold">Generate Video</span>
                            </div>
                        )}
                    </Button>
                </div>
            </div>
            </div>
        </div>
    );
}