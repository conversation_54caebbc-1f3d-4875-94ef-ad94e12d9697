"use client";

import React from 'react';
import { Music2 } from "lucide-react";
import { OptionSelector } from '@/components/shared/OptionSelector';
import { FormSection } from '@/components/shared/FormSection';

// Background music options with preview functionality
const musicOptions = [
  {
    id: 'none',
    name: 'None',
    icon: null,
    previewUrl: null
  },
  {
    id: 'izzamuzzic',
    name: '<PERSON><PERSON><PERSON>zzi<PERSON>',
    icon: Music2,
    previewUrl: 'https://cdn.revid.ai/audio/_izzamuzzic.mp3'
  },
  {
    id: 'snowfall',
    name: 'Snowfall',
    icon: Music2,
    previewUrl: 'https://cdn.revid.ai/audio/_snowfall.mp3'
  },
  {
    id: 'roaming-free',
    name: 'Roaming Free',
    icon: Music2,
    previewUrl: 'https://cdn.revid.ai/audio/roaming-free.mp3'
  },
  {
    id: 'scary-song',
    name: 'Scary Song',
    icon: Music2,
    previewUrl: 'https://cdn.revid.ai/audio/scary_song.mp3'
  },
  {
    id: 'observer',
    name: 'Observer',
    icon: Music2,
    previewUrl: 'https://cdn.revid.ai/audio/observer.mp3'
  },
  {
    id: 'paris-else',
    name: 'Paris Else',
    icon: Music2,
    previewUrl: 'https://cdn.revid.ai/audio/_paris-else.mp3'
  },
  {
    id: 'burlesque',
    name: 'Burlesque',
    icon: Music2,
    previewUrl: 'https://cdn.revid.ai/audio/burlesque.mp3'
  },
];

function BackgroundMusicSelector({ selectedMusic, onMusicSelect }) {
  return (
    <FormSection
      title="Background Music"
      description="Choose an audio track for your video."
      variant="card"
    >
      <OptionSelector
        options={musicOptions}
        selectedValue={selectedMusic}
        onSelect={onMusicSelect}
        layout="grid"
        columns={4}
        showPreview={true}
      />
    </FormSection>
  );
}

export default BackgroundMusicSelector;