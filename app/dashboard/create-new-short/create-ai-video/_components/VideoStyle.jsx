// app/dashboard/create-new/_components/VideoStyle.jsx
'use client';

import React, { useState, useEffect } from 'react';
import { OptionSelector } from '@/components/shared/OptionSelector';
import { FormSection } from '@/components/shared/FormSection';

// Video style options with image previews
const videoStyles = [
    {
        id: 'cinematic',
        name: 'Cinematic',
        description: 'Dramatic, high-quality visuals.',
        imageUrl: '/app/dashboard/create-new/placeholders/cinamatic.png'
    },
    {
        id: 'minimalist',
        name: 'Minimalist',
        description: 'Clean lines, simple animations.',
        imageUrl: '/placeholders/style-minimalist.jpg'
    },
    {
        id: 'cartoon',
        name: 'Cartoon',
        description: 'Animated, fun, and engaging.',
        imageUrl: '/placeholders/style-cartoon.jpg'
    },
    {
        id: 'documentary',
        name: 'Documentary',
        description: 'Informative, realistic style.',
        imageUrl: '/placeholders/style-documentary.jpg'
    },
];

export default function VideoStyle({ onStyleSelect, selectedStyle: initialSelectedStyle }) {
    // Use local state synced with the prop
    const [selectedStyle, setSelectedStyle] = useState(initialSelectedStyle || '');

    // Sync local state if the prop changes from the parent
    useEffect(() => {
        setSelectedStyle(initialSelectedStyle || '');
    }, [initialSelectedStyle]);

    const handleSelectStyle = (styleId) => {
        setSelectedStyle(styleId);
        // Call the specific prop handler
        if (onStyleSelect) {
            onStyleSelect(styleId);
        }
    };

    return (
        <FormSection
            title="Video Style"
            description="Choose the visual aesthetic for your video"
            variant="minimal"
        >
            <OptionSelector
                options={videoStyles}
                selectedValue={selectedStyle}
                onSelect={handleSelectStyle}
                layout="grid"
                columns={4}
            />
        </FormSection>
    );
}