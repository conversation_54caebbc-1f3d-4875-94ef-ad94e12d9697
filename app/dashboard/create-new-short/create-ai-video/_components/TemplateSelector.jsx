"use client";

import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";

// Define available templates here
// In a real application, this might come from an API or config file
const availableTemplates = [
  { id: 'template1', name: 'Template 1 - Light Blue', description: 'Basic template with a light blue background.' },
  { id: 'template2', name: 'Template 2 - Light Coral', description: 'Basic template with a light coral background.' },
  { id: 'template3', name: 'Template 3 - Light Green', description: 'Basic template with a light green background.' },
  { id: 'default', name: 'Default Template', description: 'Standard image slideshow with captions.' },
  // Add more templates as they are created
  // { id: 'animated', name: 'Animated Text', description: 'Focuses on dynamic text overlays.' },
  // { id: 'minimalist', name: 'Minimalist', description: 'Clean and simple transitions.' },
];

function TemplateSelector({ selectedTemplateId, onTemplateSelect }) {
  return (
    <div className="space-y-4">
      <div className="mb-4">
        <h3 className="text-body font-semibold mb-1">Video Template</h3>
        <p className="text-body-small text-muted-foreground">Choose a template that matches your content style</p>
      </div>

      <RadioGroup
        value={selectedTemplateId}
        onValueChange={onTemplateSelect}
        className="space-y-3"
      >
        {availableTemplates.map((template) => (
          <div
            key={template.id}
            className="rounded-lg border border-border/30 p-4 hover:bg-accent/20 transition-all duration-200"
          >
            <div className="flex items-center space-x-4">
              <RadioGroupItem value={template.id} id={`template-${template.id}`} className="mt-1" />
              <Label htmlFor={`template-${template.id}`} className="flex flex-col space-y-1 cursor-pointer flex-1">
                <span className="font-semibold text-body">{template.name}</span>
                <span className="text-body-small text-muted-foreground">{template.description}</span>
              </Label>
            </div>
          </div>
        ))}
      </RadioGroup>
    </div>
  );
}

export default TemplateSelector;