// app/dashboard/create-new/_components/Topic.jsx
'use client';

import React, { useState } from 'react';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Loader2, Sparkles } from 'lucide-react';
import { toast } from 'sonner';
// Assuming you might use Clerk's useUser hook here later if needed for UI logic
// import { useUser } from '@clerk/nextjs';

// Removed generateScriptFromTopic function - logic moved to parent/utils

// Removed projectTitleInput state and handler
// export default function Topic({ onInputChange, formData, onGenerateScriptClick, isGeneratingScript }) {
export default function Topic({ onTopicChange, onScriptChange, topic, script, onGenerateScript, isGeneratingScript, estimatedDuration }) {
    // const { user } = useUser(); // Get user if needed for UI logic

    // Use local state synced with props for controlled inputs
    const [topicInput, setTopicInput] = useState(topic || '');
    const [scriptInput, setScriptInput] = useState(script || '');

    React.useEffect(() => {
        setTopicInput(topic || '');
    }, [topic]);

    React.useEffect(() => {
        setScriptInput(script || '');
    }, [script]);

    // Removed projectTitleInput state and effect
    // const [projectTitleInput, setProjectTitleInput] = useState(formData?.projectTitle || '');
    // React.useEffect(() => {
    //     setProjectTitleInput(formData?.projectTitle || '');
    // }, [formData?.projectTitle]);

    // isGeneratingScript state is now managed by the parent component

    // Removed handleProjectTitleChange
    // const handleProjectTitleChange = (e) => { ... };

    const handleTopicInputChange = (e) => {
        const value = e.target.value;
        setTopicInput(value);
        // Call the specific prop for topic change
        if (onTopicChange) {
            onTopicChange(value);
        }
    };

    const handleScriptInputChange = (e) => {
        const value = e.target.value;
        setScriptInput(value);
        // Call the specific prop for script change
        if (onScriptChange) {
            onScriptChange(value);
        }
    };

    const handleGenerateScriptClick = async () => {
        if (!topicInput.trim()) {
            toast.warning("Missing Topic", { description: "Please enter a topic first." });
            return;
        }
        // Basic client-side length check (optional, backend should also validate)
        if (topicInput.trim().length > 200) {
             toast.warning("Topic Too Long", { description: "Topic should be under 200 characters." });
             return;
        }

        // Call the function passed down from the parent component
        if (onGenerateScript) {
            onGenerateScript(topicInput.trim());
        } else {
            console.error("[Topic Component] onGenerateScript prop is missing!");
            toast.error("Configuration Error", { description: "Unable to trigger script generation." });
        }
    };

    const formatDuration = (totalSeconds) => {
        if (totalSeconds === null || totalSeconds === undefined || totalSeconds === 0) {
            return "0 sec";
        }
        const minutes = Math.floor(totalSeconds / 60);
        const seconds = totalSeconds % 60;
        let durationString = "";
        if (minutes > 0) {
            durationString += `${minutes} min `;
        }
        durationString += `${seconds} sec`;
        return durationString.trim();
    };

    return (
        <div className="space-y-6">
            {/* Topic Input */}
            <div className="space-y-4">
                <div className="space-y-3">
                    <Label htmlFor="topic" className="text-body font-medium">Video Topic</Label>
                    <div className="relative">
                        <Input
                            id="topic"
                            placeholder="e.g., Benefits of daily meditation, How to start a business, Top 10 productivity tips..."
                            value={topicInput}
                            onChange={handleTopicInputChange}
                            maxLength={200}
                            required
                            className="w-full h-12 pr-16 text-body"
                        />
                        <div className="absolute right-3 top-1/2 -translate-y-1/2 text-caption text-muted-foreground bg-background px-2 rounded">
                            {topicInput.length}/200
                        </div>
                    </div>
                    <p className="text-body-small text-muted-foreground">Enter the main topic or idea for your video. Be specific for better results.</p>
                </div>

                {/* Generate Script Button */}
                <div className="flex justify-start">
                    <Button
                        type="button"
                        variant="outline"
                        size="lg"
                        onClick={handleGenerateScriptClick}
                        disabled={isGeneratingScript || !topicInput.trim()}
                        className="interactive-scale h-12"
                        aria-label={isGeneratingScript ? 'Generating script' : 'Generate script from topic'}
                    >
                        {isGeneratingScript ? (
                            <div className="flex items-center gap-2">
                                <Loader2 className="h-4 w-4 animate-spin" aria-hidden="true" />
                                Generating Script...
                            </div>
                        ) : (
                            <div className="flex items-center gap-2">
                                <Sparkles className="h-4 w-4" aria-hidden="true" />
                                Generate Script Preview
                            </div>
                        )}
                    </Button>
                </div>

                {/* Script Display */}
                <div className="space-y-3">
                    <Label htmlFor="script" className="text-body font-medium">Generated Script</Label>
                    <Textarea
                        id="script"
                        placeholder="Your AI-generated script will appear here. Click 'Generate Script Preview' to create content based on your topic..."
                        value={scriptInput}
                        onChange={handleScriptInputChange}
                        rows={8}
                        className="min-h-[160px] text-body resize-none"
                        required
                    />
                    <p className="text-body-small text-muted-foreground">Review and edit the generated script as needed. The script will be used for voiceover generation.</p>
                </div>

                {/* Estimated Duration Display */}
                {estimatedDuration > 0 && (
                    <div className="p-4 bg-blue-50/50 dark:bg-blue-950/20 rounded-lg border border-blue-200/30 dark:border-blue-800/30">
                        <div className="flex items-center gap-2 mb-2">
                            <div className="p-1.5 bg-blue-500 rounded-lg">
                                <div className="h-3 w-3 text-white flex items-center justify-center text-xs">⏱️</div>
                            </div>
                            <span className="font-semibold text-body text-blue-800 dark:text-blue-200">Estimated Duration</span>
                        </div>
                        <div className="text-heading-3 font-bold text-blue-900 dark:text-blue-100">{formatDuration(estimatedDuration)}</div>
                    </div>
                )}
            </div>
        </div>
    );
}