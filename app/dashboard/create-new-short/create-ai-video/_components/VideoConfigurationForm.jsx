/**
 * Video Configuration Form Component
 * Orchestrates all form sections for AI video creation
 */

import React from 'react';
import ProjectDetailsSection from './sections/ProjectDetailsSection';
import ScriptSection from './sections/ScriptSection';

// Import existing components (these already exist in your codebase)
import VideoStyle from './VideoStyle';
import Voice from './Voice';
import Captions from './Captions';
import AspectRatioSelector from './AspectRatioSelector';
import TemplateSelector from './TemplateSelector';
import AudioSpeedSelector from './AudioSpeedSelector';
import BackgroundMusicSelector from './BackgroundMusicSelector';

export default function VideoConfigurationForm({
  formData,
  errors,
  touched,
  onFieldChange,
  onFieldBlur,
  getFieldError,
  onGeneratePreview,
  isGenerating,
  estimatedDuration,
  className = ''
}) {
  // Helper function to handle input changes with proper typing
  const handleInputChange = (field, value) => {
    onFieldChange(field, value);
  };

  return (
    <div className={`space-y-8 ${className}`}>
      {/* Project Details Section */}
      <ProjectDetailsSection
        title={formData.projectTitle}
        topic={formData.topic}
        onTitleChange={(value) => handleInputChange('projectTitle', value)}
        onTopicChange={(value) => handleInputChange('topic', value)}
        onFieldBlur={onFieldBlur}
        getFieldError={getFieldError}
      />

      {/* Video Style and Aspect Ratio */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="space-y-2">
          <h4 className="text-body-medium font-medium text-foreground">Video Style *</h4>
          <VideoStyle
            selectedStyle={formData.videoStyle}
            onStyleChange={(value) => handleInputChange('videoStyle', value)}
          />
          {getFieldError('videoStyle') && (
            <p className="text-caption text-destructive">
              {getFieldError('videoStyle')}
            </p>
          )}
        </div>

        <div className="space-y-2">
          <h4 className="text-body-medium font-medium text-foreground">Aspect Ratio *</h4>
          <AspectRatioSelector
            selectedRatio={formData.aspectRatio}
            onRatioChange={(value) => handleInputChange('aspectRatio', value)}
          />
          {getFieldError('aspectRatio') && (
            <p className="text-caption text-destructive">
              {getFieldError('aspectRatio')}
            </p>
          )}
        </div>
      </div>

      {/* Script Section */}
      <ScriptSection
        script={formData.script}
        topic={formData.topic}
        videoStyle={formData.videoStyle}
        onScriptChange={(value) => handleInputChange('script', value)}
        onGeneratePreview={onGeneratePreview}
        onFieldBlur={onFieldBlur}
        getFieldError={getFieldError}
        isGenerating={isGenerating}
        estimatedDuration={estimatedDuration}
      />

      {/* Audio Configuration */}
      <div className="space-y-6">
        <div className="space-y-2">
          <h3 className="text-heading-3 text-foreground">Audio Configuration</h3>
          <p className="text-body-small text-muted-foreground">
            Configure voice, speed, and background music
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Voice Selection */}
          <div className="space-y-2">
            <h4 className="text-body-medium font-medium text-foreground">Voice *</h4>
            <Voice
              selectedVoice={formData.voice}
              onVoiceChange={(value) => handleInputChange('voice', value)}
            />
            {getFieldError('voice') && (
              <p className="text-caption text-destructive">
                {getFieldError('voice')}
              </p>
            )}
          </div>

          {/* Audio Speed */}
          <div className="space-y-2">
            <h4 className="text-body-medium font-medium text-foreground">Audio Speed *</h4>
            <AudioSpeedSelector
              selectedSpeed={formData.audioSpeed}
              onSpeedChange={(value) => handleInputChange('audioSpeed', value)}
            />
            {getFieldError('audioSpeed') && (
              <p className="text-caption text-destructive">
                {getFieldError('audioSpeed')}
              </p>
            )}
          </div>
        </div>

        {/* Background Music */}
        <div className="space-y-2">
          <h4 className="text-body-medium font-medium text-foreground">Background Music *</h4>
          <BackgroundMusicSelector
            selectedMusic={formData.backgroundMusic}
            onMusicChange={(value) => handleInputChange('backgroundMusic', value)}
          />
          {getFieldError('backgroundMusic') && (
            <p className="text-caption text-destructive">
              {getFieldError('backgroundMusic')}
            </p>
          )}
        </div>
      </div>

      {/* Visual Configuration */}
      <div className="space-y-6">
        <div className="space-y-2">
          <h3 className="text-heading-3 text-foreground">Visual Configuration</h3>
          <p className="text-body-small text-muted-foreground">
            Configure captions and video template
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Captions */}
          <div className="space-y-2">
            <h4 className="text-body-medium font-medium text-foreground">Captions</h4>
            <Captions
              selectedCaption={formData.caption}
              onCaptionChange={(value) => handleInputChange('caption', value)}
            />
            {getFieldError('caption') && (
              <p className="text-caption text-destructive">
                {getFieldError('caption')}
              </p>
            )}
          </div>

          {/* Template */}
          <div className="space-y-2">
            <h4 className="text-body-medium font-medium text-foreground">Template *</h4>
            <TemplateSelector
              selectedTemplate={formData.templateId}
              onTemplateChange={(value) => handleInputChange('templateId', value)}
            />
            {getFieldError('templateId') && (
              <p className="text-caption text-destructive">
                {getFieldError('templateId')}
              </p>
            )}
          </div>
        </div>
      </div>

      {/* Form Summary */}
      {estimatedDuration > 0 && (
        <div className="bg-muted/50 rounded-lg p-4">
          <h4 className="text-body-small font-medium text-foreground mb-2">
            📊 Video Summary
          </h4>
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 text-caption">
            <div>
              <span className="text-muted-foreground">Duration:</span>
              <span className="ml-1 font-medium">~{estimatedDuration}s</span>
            </div>
            <div>
              <span className="text-muted-foreground">Style:</span>
              <span className="ml-1 font-medium">{formData.videoStyle || 'Not selected'}</span>
            </div>
            <div>
              <span className="text-muted-foreground">Ratio:</span>
              <span className="ml-1 font-medium">{formData.aspectRatio || 'Not selected'}</span>
            </div>
            <div>
              <span className="text-muted-foreground">Voice:</span>
              <span className="ml-1 font-medium">{formData.voice || 'Not selected'}</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
