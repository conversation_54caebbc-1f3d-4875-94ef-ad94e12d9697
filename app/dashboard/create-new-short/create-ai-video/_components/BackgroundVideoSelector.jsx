'use client';

import React from 'react';
import { Label } from "@/components/ui/label";
import { defaultBackgroundVideos } from '../../data/defaultBackgroundVideos'; // Adjust path as needed

function BackgroundVideoSelector({ selectedVideoUrl, onSelectVideo }) {
  return (
    <div className="space-y-4">
      <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-3">
        {defaultBackgroundVideos.map((video) => (
          <div
            key={video.url}
            className={`cursor-pointer border rounded-lg overflow-hidden transition-all duration-200 ${
              selectedVideoUrl === video.url
                ? 'border-primary ring-2 ring-primary ring-offset-1 bg-primary/5'
                : 'border-border/30 hover:border-primary/50'
            }`}
            onClick={() => onSelectVideo(video.url)}
          >
            <div className="relative w-full h-20 bg-muted flex items-center justify-center text-muted-foreground text-body-small">
              {video.name}
              {selectedVideoUrl === video.url && (
                <div className="absolute inset-0 bg-primary/20 flex items-center justify-center">
                  <div className="w-6 h-6 bg-primary rounded-full flex items-center justify-center">
                    <div className="w-2 h-2 bg-white rounded-full"></div>
                  </div>
                </div>
              )}
            </div>
            <div className="p-2 text-center text-caption">
              {video.name}
            </div>
          </div>
        ))}
      </div>

      {/* Display selected video for confirmation */}
      {selectedVideoUrl && (
        <div className="mt-3 p-2 bg-primary/10 rounded border border-primary/20">
          <p className="text-body-small text-primary font-medium">
            ✓ Selected: {defaultBackgroundVideos.find(v => v.url === selectedVideoUrl)?.name}
          </p>
        </div>
      )}
    </div>
  );
}

export default BackgroundVideoSelector;