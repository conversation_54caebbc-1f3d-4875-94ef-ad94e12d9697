/**
 * Video Preview Panel Component
 * Shows video configuration summary and generation controls
 */

import React from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Sparkles, Loader2, CreditCard, AlertCircle, CheckCircle2 } from 'lucide-react';

export default function VideoPreviewPanel({
  formData,
  onGenerate,
  isGenerating,
  generationMessage,
  isValid,
  hasSufficientCredits,
  userDetail,
  isGenerateButtonDisabled,
  getGenerationButtonText,
  getGenerationButtonTitle,
  VIDEO_GENERATION_COST,
  className = ''
}) {
  const handleGenerate = () => {
    if (!isGenerateButtonDisabled(formData)) {
      onGenerate(formData);
    }
  };

  const getCompletionStatus = (value, label) => {
    const isComplete = Boolean(value);
    return {
      isComplete,
      label,
      icon: isComplete ? CheckCircle2 : AlertCircle,
      color: isComplete ? 'text-green-600' : 'text-muted-foreground'
    };
  };

  const configurationItems = [
    getCompletionStatus(formData.projectTitle, 'Project Title'),
    getCompletionStatus(formData.topic, 'Topic'),
    getCompletionStatus(formData.videoStyle, 'Video Style'),
    getCompletionStatus(formData.aspectRatio, 'Aspect Ratio'),
    getCompletionStatus(formData.script, 'Script'),
    getCompletionStatus(formData.voice, 'Voice'),
    getCompletionStatus(formData.audioSpeed, 'Audio Speed'),
    getCompletionStatus(formData.backgroundMusic, 'Background Music'),
    getCompletionStatus(formData.templateId, 'Template')
  ];

  const completedItems = configurationItems.filter(item => item.isComplete).length;
  const totalItems = configurationItems.length;
  const completionPercentage = Math.round((completedItems / totalItems) * 100);

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Configuration Progress */}
      <Card>
        <CardHeader>
          <CardTitle className="text-heading-4 flex items-center gap-2">
            <Sparkles className="h-5 w-5 text-primary" />
            Configuration Progress
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Progress Bar */}
          <div className="space-y-2">
            <div className="flex justify-between text-caption">
              <span className="text-muted-foreground">Completion</span>
              <span className="font-medium">{completedItems}/{totalItems} ({completionPercentage}%)</span>
            </div>
            <div className="w-full bg-muted rounded-full h-2">
              <div 
                className="bg-primary h-2 rounded-full transition-all duration-300"
                style={{ width: `${completionPercentage}%` }}
              />
            </div>
          </div>

          {/* Configuration Checklist */}
          <div className="space-y-2">
            {configurationItems.map((item, index) => (
              <div key={index} className="flex items-center gap-2 text-caption">
                <item.icon className={`h-3 w-3 ${item.color}`} />
                <span className={item.isComplete ? 'text-foreground' : 'text-muted-foreground'}>
                  {item.label}
                </span>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Video Preview */}
      <Card>
        <CardHeader>
          <CardTitle className="text-heading-4">Video Preview</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Aspect Ratio Preview */}
          <div className="space-y-2">
            <span className="text-caption text-muted-foreground">Aspect Ratio</span>
            <div className="flex justify-center">
              <div 
                className={`border-2 border-dashed border-muted-foreground/30 bg-muted/20 flex items-center justify-center text-caption text-muted-foreground ${
                  formData.aspectRatio === '9:16' ? 'w-20 h-32' :
                  formData.aspectRatio === '16:9' ? 'w-32 h-20' :
                  formData.aspectRatio === '1:1' ? 'w-24 h-24' :
                  'w-24 h-24'
                }`}
              >
                {formData.aspectRatio || '?:?'}
              </div>
            </div>
          </div>

          {/* Configuration Summary */}
          <div className="space-y-3">
            <h4 className="text-body-small font-medium">Configuration Summary</h4>
            <div className="space-y-2">
              {formData.videoStyle && (
                <div className="flex justify-between items-center">
                  <span className="text-caption text-muted-foreground">Style:</span>
                  <Badge variant="secondary" className="text-xs">
                    {formData.videoStyle}
                  </Badge>
                </div>
              )}
              {formData.voice && (
                <div className="flex justify-between items-center">
                  <span className="text-caption text-muted-foreground">Voice:</span>
                  <Badge variant="secondary" className="text-xs">
                    {formData.voice}
                  </Badge>
                </div>
              )}
              {formData.audioSpeed && (
                <div className="flex justify-between items-center">
                  <span className="text-caption text-muted-foreground">Speed:</span>
                  <Badge variant="secondary" className="text-xs">
                    {formData.audioSpeed}x
                  </Badge>
                </div>
              )}
              {formData.backgroundMusic && (
                <div className="flex justify-between items-center">
                  <span className="text-caption text-muted-foreground">Music:</span>
                  <Badge variant="secondary" className="text-xs">
                    {formData.backgroundMusic}
                  </Badge>
                </div>
              )}
            </div>
          </div>

          {/* Script Preview */}
          {formData.script && (
            <div className="space-y-2">
              <h4 className="text-body-small font-medium">Script Preview</h4>
              <div className="bg-muted/50 rounded-lg p-3 max-h-32 overflow-y-auto">
                <p className="text-caption text-muted-foreground">
                  {formData.script.length > 150 
                    ? `${formData.script.substring(0, 150)}...` 
                    : formData.script
                  }
                </p>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Credit Information */}
      <Card>
        <CardHeader>
          <CardTitle className="text-heading-4 flex items-center gap-2">
            <CreditCard className="h-5 w-5 text-primary" />
            Credit Information
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="flex justify-between items-center">
            <span className="text-body-small text-muted-foreground">Generation Cost:</span>
            <Badge variant="outline" className="font-medium">
              {VIDEO_GENERATION_COST} credits
            </Badge>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-body-small text-muted-foreground">Your Balance:</span>
            <Badge 
              variant={hasSufficientCredits() ? "default" : "destructive"}
              className="font-medium"
            >
              {userDetail?.credits || 0} credits
            </Badge>
          </div>
          {!hasSufficientCredits() && (
            <div className="bg-destructive/10 border border-destructive/20 rounded-lg p-3">
              <p className="text-caption text-destructive flex items-center gap-2">
                <AlertCircle className="h-3 w-3" />
                Insufficient credits. You need {VIDEO_GENERATION_COST} credits to generate a video.
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Generation Button */}
      <Button
        onClick={handleGenerate}
        disabled={isGenerateButtonDisabled(formData)}
        className="w-full"
        size="lg"
        title={getGenerationButtonTitle(formData)}
      >
        {isGenerating ? (
          <Loader2 className="mr-2 h-5 w-5 animate-spin" />
        ) : (
          <Sparkles className="mr-2 h-5 w-5" />
        )}
        {getGenerationButtonText()}
      </Button>

      {/* Generation Status */}
      {isGenerating && generationMessage && (
        <div className="bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3">
          <p className="text-caption text-blue-700 dark:text-blue-300 flex items-center gap-2">
            <Loader2 className="h-3 w-3 animate-spin" />
            {generationMessage}
          </p>
        </div>
      )}
    </div>
  );
}
