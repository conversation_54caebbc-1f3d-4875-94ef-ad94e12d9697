// app/dashboard/create-new/_components/Voice.jsx
'use client';

import React, { useState, useRef, useEffect } from 'react'; // Added useRef, useEffect
import { cn } from '@/lib/utils';
import { Volume2, PlayCircle, Square } from 'lucide-react'; // Added Square icon for Stop

// --- Placeholder Data ---
// Replace with your actual voice options, including identifiers/codes
// that your backend/TTS service uses. Add audio preview URLs if available.
const voiceOptions = [
    { id: 'alloy', name: 'Alloy', gender: 'Male', description: 'Clear, professional tone.', previewUrl: 'https://cloud.google.com/static/text-to-speech/docs/audio/en-US-Chirp3-HD-Umbriel.wav' }, // Example URL
    { id: 'echo', name: 'Echo', gender: 'Male', description: 'Friendly, conversational.', previewUrl: "https://cloud.google.com/static/text-to-speech/docs/audio/en-US-Chirp3-HD-Gacrux.wav" },
    { id: 'fable', name: 'Fable', gender: 'Male', description: 'Storyteller, warm.', previewUrl: "https://cloud.google.com/static/text-to-speech/docs/audio/en-US-Chirp3-HD-Fenrir.wav" },
    { id: 'onyx', name: 'Onyx', gender: 'Male', description: 'Deep, authoritative.', previewUrl: "https://cloud.google.com/static/text-to-speech/docs/audio/en-US-Chirp3-HD-Enceladus.wav" },
    { id: 'nova', name: 'Nova', gender: 'Female', description: 'Energetic, youthful.', previewUrl: "https://cloud.google.com/static/text-to-speech/docs/audio/en-US-Chirp3-HD-Leda.wav" },
    { id: 'shimmer', name: 'Shimmer', gender: 'Female', description: 'Smooth, calming.', previewUrl: "https://cloud.google.com/static/text-to-speech/docs/audio/en-US-Chirp3-HD-Kore.wav" },
];
// --- End Placeholder ---

export default function Voice({ onInputChange, formData }) {
    const [selectedVoice, setSelectedVoice] = useState(formData?.voice || '');
    const [playingPreview, setPlayingPreview] = useState(null); // Track which voice ID's preview is playing
    const audioRef = useRef(null); // Ref to store the Audio object

    // Cleanup audio on component unmount
    useEffect(() => {
        return () => {
            if (audioRef.current) {
                audioRef.current.pause();
                audioRef.current = null;
            }
        };
    }, []);

    const stopCurrentPreview = () => {
        if (audioRef.current) {
            audioRef.current.pause();
            audioRef.current.currentTime = 0; // Reset time
            // Don't nullify audioRef.current here if we might resume or if it's handled by new Audio() creation
        }
        setPlayingPreview(null); // Indicate no preview is actively playing
    };

    const handleSelectVoice = (voiceId) => {
        setSelectedVoice(voiceId);
        onInputChange('voice', voiceId); // Update parent state
        stopCurrentPreview(); // Stop any playing preview when selection changes
    };

    const handlePlayPreview = (e, voice) => {
        e.stopPropagation(); // Prevent selection when clicking preview button

        if (playingPreview === voice.id) { // If this voice's preview is already playing, stop it
            stopCurrentPreview();
            return;
        }

        stopCurrentPreview(); // Stop any other preview first

        if (!voice.previewUrl) {
            alert("Preview not available for this voice.");
            return;
        }

        console.log(`Playing preview for ${voice.name}: ${voice.previewUrl}`);
        // Create a new Audio object only if one doesn't exist or if it's for a different voice
        if (!audioRef.current || audioRef.current.src !== voice.previewUrl) {
            if(audioRef.current) { // Clean up old audio object's listeners if switching
                audioRef.current.onended = null;
                audioRef.current.onerror = null;
            }
            audioRef.current = new Audio(voice.previewUrl);
            audioRef.current.onended = () => {
                setPlayingPreview(null); // Reset playing state, keep audioRef for potential replay
            };
            audioRef.current.onerror = () => {
                console.error("Error during audio playback.");
                alert(`Could not play preview for ${voice.name}.`);
                setPlayingPreview(null);
                if (audioRef.current) audioRef.current.src = ''; // Prevent further attempts with bad src
            };
        }
        
        setPlayingPreview(voice.id);
        audioRef.current.play().catch(err => {
            console.error("Audio preview failed:", err);
            setPlayingPreview(null); // Reset state on error
            if (audioRef.current) audioRef.current.src = ''; // Prevent further attempts
        });
    };

    return (
        <div className="space-y-4">
            <h3 className="text-lg font-semibold mb-2">3. Voice Selection</h3>
            <p className="text-sm text-muted-foreground mb-4">Choose the voice for the video narration.</p>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                {voiceOptions.map((voice) => (
                    <div
                        key={voice.id}
                        onClick={() => handleSelectVoice(voice.id)}
                        className={cn(
                            "border rounded-lg p-3 cursor-pointer transition-all duration-200 ease-in-out hover:shadow-md flex flex-col justify-between",
                            selectedVoice === voice.id ? "border-primary ring-2 ring-primary ring-offset-2" : "border-border"
                        )}
                    >
                        <div>
                            <div className="flex justify-between items-center mb-1">
                                <h4 className="font-medium text-sm">{voice.name}</h4>
                                <span className="text-xs bg-secondary text-secondary-foreground px-1.5 py-0.5 rounded">{voice.gender}</span>
                            </div>
                            <p className="text-xs text-muted-foreground mb-2">{voice.description}</p>
                        </div>
                        <button
                            onClick={(e) => handlePlayPreview(e, voice)}
                            disabled={!voice.previewUrl} // Only disable if no preview URL
                            className={cn(
                                "mt-2 w-full text-xs inline-flex items-center justify-center px-2 py-1 border border-transparent rounded shadow-sm font-medium focus:outline-none focus:ring-2 focus:ring-offset-2",
                                !voice.previewUrl ? "bg-muted text-muted-foreground cursor-not-allowed" : "text-primary-foreground bg-primary hover:bg-primary/90 focus:ring-primary",
                                playingPreview === voice.id && "bg-red-500 hover:bg-red-600" // Style for "Stop" state
                            )}
                            aria-label={playingPreview === voice.id ? `Stop preview for ${voice.name}` : `Play preview for ${voice.name}`}
                        >
                            {playingPreview === voice.id ? (
                                <Square className="mr-1 h-3 w-3" /> // Stop icon
                            ) : (
                                <PlayCircle className="mr-1 h-3 w-3" /> // Play icon
                            )}
                            {playingPreview === voice.id ? 'Stop' : 'Preview'}
                        </button>
                    </div>
                ))}
            </div>
             {/* Display selected voice for confirmation */}
             {selectedVoice && (
                 <p className="text-sm text-muted-foreground mt-2">Selected voice: <span className="font-medium text-foreground">{voiceOptions.find(v => v.id === selectedVoice)?.name}</span></p>
             )}
        </div>
    );
}