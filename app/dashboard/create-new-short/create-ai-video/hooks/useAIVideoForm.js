/**
 * Custom hook for managing AI Video form state and validation
 * Centralizes form logic and provides clean interface for components
 */

import { useState, useCallback, useMemo } from 'react';

const initialFormData = {
  projectTitle: '',
  topic: '',
  videoStyle: '',
  aspectRatio: '',
  script: '',
  voice: '',
  audioSpeed: 1.0,
  backgroundMusic: '',
  caption: '',
  templateId: ''
};

const validationRules = {
  projectTitle: {
    required: true,
    minLength: 3,
    maxLength: 100,
    message: 'Project title must be between 3-100 characters'
  },
  topic: {
    required: true,
    minLength: 5,
    maxLength: 500,
    message: 'Topic must be between 5-500 characters'
  },
  videoStyle: {
    required: true,
    message: 'Please select a video style'
  },
  aspectRatio: {
    required: true,
    message: 'Please select an aspect ratio'
  },
  script: {
    required: true,
    minLength: 10,
    maxLength: 2000,
    message: 'Script must be between 10-2000 characters'
  },
  voice: {
    required: true,
    message: 'Please select a voice'
  },
  audioSpeed: {
    required: true,
    min: 0.5,
    max: 2.0,
    message: 'Audio speed must be between 0.5x and 2.0x'
  },
  backgroundMusic: {
    required: true,
    message: 'Please select background music'
  },
  templateId: {
    required: true,
    message: 'Please select a template'
  }
};

export function useAIVideoForm() {
  const [formData, setFormData] = useState(initialFormData);
  const [errors, setErrors] = useState({});
  const [touched, setTouched] = useState({});

  /**
   * Update a single form field and clear its error
   */
  const updateField = useCallback((field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear error when field is updated
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: null }));
    }
    
    // Mark field as touched
    setTouched(prev => ({ ...prev, [field]: true }));
  }, [errors]);

  /**
   * Update multiple fields at once
   */
  const updateFields = useCallback((updates) => {
    setFormData(prev => ({ ...prev, ...updates }));
    
    // Clear errors for updated fields
    const updatedFields = Object.keys(updates);
    if (updatedFields.some(field => errors[field])) {
      setErrors(prev => {
        const newErrors = { ...prev };
        updatedFields.forEach(field => {
          delete newErrors[field];
        });
        return newErrors;
      });
    }
    
    // Mark fields as touched
    setTouched(prev => {
      const newTouched = { ...prev };
      updatedFields.forEach(field => {
        newTouched[field] = true;
      });
      return newTouched;
    });
  }, [errors]);

  /**
   * Validate a single field
   */
  const validateField = useCallback((field, value) => {
    const rules = validationRules[field];
    if (!rules) return null;

    // Required validation
    if (rules.required && (!value || (typeof value === 'string' && !value.trim()))) {
      return rules.message || `${field} is required`;
    }

    // String length validation
    if (typeof value === 'string') {
      if (rules.minLength && value.trim().length < rules.minLength) {
        return rules.message || `${field} must be at least ${rules.minLength} characters`;
      }
      if (rules.maxLength && value.trim().length > rules.maxLength) {
        return rules.message || `${field} must be no more than ${rules.maxLength} characters`;
      }
    }

    // Number range validation
    if (typeof value === 'number') {
      if (rules.min && value < rules.min) {
        return rules.message || `${field} must be at least ${rules.min}`;
      }
      if (rules.max && value > rules.max) {
        return rules.message || `${field} must be no more than ${rules.max}`;
      }
    }

    return null;
  }, []);

  /**
   * Validate entire form
   */
  const validateForm = useCallback(() => {
    const newErrors = {};
    
    Object.keys(validationRules).forEach(field => {
      const error = validateField(field, formData[field]);
      if (error) {
        newErrors[field] = error;
      }
    });
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [formData, validateField]);

  /**
   * Validate field on blur
   */
  const handleFieldBlur = useCallback((field) => {
    const error = validateField(field, formData[field]);
    if (error) {
      setErrors(prev => ({ ...prev, [field]: error }));
    }
    setTouched(prev => ({ ...prev, [field]: true }));
  }, [formData, validateField]);

  /**
   * Check if form is valid (all required fields filled)
   */
  const isValid = useMemo(() => {
    return Object.keys(validationRules).every(field => {
      const rules = validationRules[field];
      const value = formData[field];
      
      if (rules.required) {
        return value && (typeof value !== 'string' || value.trim());
      }
      return true;
    });
  }, [formData]);

  /**
   * Check if form has any errors
   */
  const hasErrors = useMemo(() => {
    return Object.keys(errors).length > 0;
  }, [errors]);

  /**
   * Get field error (only show if touched)
   */
  const getFieldError = useCallback((field) => {
    return touched[field] ? errors[field] : null;
  }, [errors, touched]);

  /**
   * Reset form to initial state
   */
  const resetForm = useCallback(() => {
    setFormData(initialFormData);
    setErrors({});
    setTouched({});
  }, []);

  /**
   * Get form data for submission
   */
  const getSubmissionData = useCallback(() => {
    return { ...formData };
  }, [formData]);

  /**
   * Calculate estimated duration based on script length
   */
  const estimatedDuration = useMemo(() => {
    if (!formData.script) return 0;
    
    // Rough calculation: ~150 words per minute, ~5 characters per word
    const wordsPerMinute = 150;
    const charactersPerWord = 5;
    const charactersPerMinute = wordsPerMinute * charactersPerWord;
    
    const minutes = formData.script.length / charactersPerMinute;
    return Math.max(10, Math.round(minutes * 60)); // Minimum 10 seconds
  }, [formData.script]);

  return {
    // Form data
    formData,
    errors,
    touched,
    
    // Computed properties
    isValid,
    hasErrors,
    estimatedDuration,
    
    // Actions
    updateField,
    updateFields,
    validateForm,
    handleFieldBlur,
    getFieldError,
    resetForm,
    getSubmissionData,
    
    // Individual field accessors for convenience
    projectTitle: formData.projectTitle,
    topic: formData.topic,
    videoStyle: formData.videoStyle,
    aspectRatio: formData.aspectRatio,
    script: formData.script,
    voice: formData.voice,
    audioSpeed: formData.audioSpeed,
    backgroundMusic: formData.backgroundMusic,
    caption: formData.caption,
    templateId: formData.templateId
  };
}
