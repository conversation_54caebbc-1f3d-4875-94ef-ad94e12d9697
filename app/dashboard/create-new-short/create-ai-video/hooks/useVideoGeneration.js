/**
 * Custom hook for managing video generation operations
 * Handles script preview generation and full video generation
 */

import { useState, useCallback, useContext } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { UserDetailContext } from '@/context/UserDetailContext';
import { triggerAIVideoGeneration } from '@/actions/aiVideoGeneration';
import { VIDEO_GENERATION_COST } from '@/lib/creditUtils';

export function useVideoGeneration() {
  const [isGenerating, setIsGenerating] = useState(false);
  const [generationMessage, setGenerationMessage] = useState('');
  const [previewScript, setPreviewScript] = useState('');
  
  const { userDetail, setUserDetail, invalidateVideoCache } = useContext(UserDetailContext);
  const router = useRouter();

  /**
   * Generate script preview using AI
   */
  const generatePreview = useCallback(async (topic, videoStyle) => {
    if (!topic?.trim()) {
      toast.error('Please enter a topic first');
      return null;
    }

    if (!videoStyle) {
      toast.error('Please select a video style first');
      return null;
    }

    setIsGenerating(true);
    setGenerationMessage('Generating script preview...');
    
    try {
      console.log('[Preview] Generating script preview for:', { topic, videoStyle });
      
      const response = await fetch('/api/generate-script-preview', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ topic, videoStyle })
      });
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
      }
      
      const data = await response.json();
      
      if (!data.script || typeof data.script !== 'string') {
        throw new Error('Invalid script response from server');
      }
      
      setPreviewScript(data.script);
      toast.success('Script Preview Generated!');
      console.log('[Preview] Script generated successfully');
      
      return data.script;
    } catch (error) {
      console.error('[Preview] Script generation failed:', error);
      toast.error('Script Preview Failed', { 
        description: error.message || 'An unexpected error occurred' 
      });
      return null;
    } finally {
      setIsGenerating(false);
      setGenerationMessage('');
    }
  }, []);

  /**
   * Generate full video using Inngest workflow
   */
  const generateVideo = useCallback(async (formData) => {
    // Validation checks
    if (!userDetail) {
      toast.error('User information not loaded');
      return false;
    }

    if (userDetail.credits < VIDEO_GENERATION_COST) {
      toast.error('Insufficient Credits', {
        description: `You need ${VIDEO_GENERATION_COST} credits to generate a video. You have ${userDetail.credits} credits.`
      });
      return false;
    }

    // Form validation
    const requiredFields = [
      'projectTitle', 'topic', 'videoStyle', 'aspectRatio', 
      'script', 'voice', 'audioSpeed', 'backgroundMusic', 'templateId'
    ];
    
    const missingFields = requiredFields.filter(field => !formData[field]);
    if (missingFields.length > 0) {
      toast.error('Form Incomplete', {
        description: `Please fill in: ${missingFields.join(', ')}`
      });
      return false;
    }

    setIsGenerating(true);
    setGenerationMessage('Initiating video generation...');

    try {
      console.log('[Generation] Starting AI video generation with data:', formData);
      
      const result = await triggerAIVideoGeneration(formData);
      
      if (result.success) {
        console.log('[Generation] Inngest workflow triggered successfully:', result.eventId);
        
        toast.success('Generation Started!', {
          description: 'Your video is being generated in the background. Check your dashboard for updates.'
        });
        
        // Optimistically update credits in UI
        setUserDetail(prev => prev ? { 
          ...prev, 
          credits: Math.max(0, prev.credits - VIDEO_GENERATION_COST) 
        } : null);
        
        // Invalidate video cache since a new video is being created
        invalidateVideoCache();
        
        // Redirect to dashboard after triggering
        router.push('/dashboard');
        
        return true;
      } else {
        console.error('[Generation] Failed to trigger Inngest workflow:', result.error);
        throw new Error(result.error || 'Failed to start video generation');
      }
    } catch (error) {
      console.error('[Generation] Video generation failed:', error);
      toast.error('Generation Failed', {
        description: error.message || 'An unexpected error occurred while starting video generation.'
      });
      return false;
    } finally {
      setIsGenerating(false);
      setGenerationMessage('');
    }
  }, [userDetail, setUserDetail, invalidateVideoCache, router]);

  /**
   * Check if generation button should be disabled
   */
  const isGenerateButtonDisabled = useCallback((formData) => {
    if (isGenerating) return true;
    if (!userDetail || userDetail.credits < VIDEO_GENERATION_COST) return true;
    
    // Check required fields
    const requiredFields = [
      'topic', 'videoStyle', 'aspectRatio', 'script', 
      'templateId', 'voice', 'backgroundMusic'
    ];
    
    return requiredFields.some(field => !formData[field]) || 
           formData.audioSpeed === null || 
           formData.audioSpeed === undefined;
  }, [isGenerating, userDetail]);

  /**
   * Get generation button text
   */
  const getGenerationButtonText = useCallback(() => {
    if (isGenerating) return generationMessage || 'Generating...';
    if (!userDetail || userDetail.credits < VIDEO_GENERATION_COST) {
      return `Insufficient Credits (Need ${VIDEO_GENERATION_COST})`;
    }
    return 'Generate AI Video';
  }, [isGenerating, generationMessage, userDetail]);

  /**
   * Get generation button title (tooltip)
   */
  const getGenerationButtonTitle = useCallback((formData) => {
    if (!userDetail || userDetail.credits < VIDEO_GENERATION_COST) {
      return `Insufficient credits (Need ${VIDEO_GENERATION_COST}, Have ${userDetail?.credits || 0})`;
    }
    if (isGenerateButtonDisabled(formData)) {
      return 'Please complete all required fields';
    }
    if (isGenerating) {
      return generationMessage || 'Generating video...';
    }
    return 'Generate your AI video';
  }, [userDetail, isGenerateButtonDisabled, isGenerating, generationMessage]);

  /**
   * Clear preview script
   */
  const clearPreview = useCallback(() => {
    setPreviewScript('');
  }, []);

  /**
   * Check if user has sufficient credits
   */
  const hasSufficientCredits = useCallback(() => {
    return userDetail && userDetail.credits >= VIDEO_GENERATION_COST;
  }, [userDetail]);

  return {
    // State
    isGenerating,
    generationMessage,
    previewScript,
    
    // Actions
    generatePreview,
    generateVideo,
    clearPreview,
    
    // Computed properties
    isGenerateButtonDisabled,
    getGenerationButtonText,
    getGenerationButtonTitle,
    hasSufficientCredits,
    
    // Constants
    VIDEO_GENERATION_COST
  };
}
