import { auth } from '@clerk/nextjs/server';
import { redirect } from 'next/navigation';
import { getUserCredits } from '@/app/lib/user-initialization';
import AIVideoCreationClient from './components/ai-video-creation-client';

export const metadata = {
  title: 'AI Video Creator - Generate Videos with AI | AI Reel Gen',
  description: 'Create engaging videos with AI-generated scripts, professional voiceovers, and dynamic visuals. Perfect for social media, marketing, and educational content.',
  keywords: ['AI video creation', 'video generator', 'AI scripts', 'voiceover', 'social media videos'],
};

/**
 * AI Video Creation Page - Server Component
 *
 * This server component handles authentication and initial data fetching
 * before rendering the client-side interactive components.
 *
 * Benefits:
 * - Server-side authentication check (faster redirects)
 * - Pre-fetch user credits for better UX
 * - SEO optimization with proper metadata
 * - Maintains all existing functionality through client component
 */
export default async function CreateAIVideoPage() {
  // Server-side authentication check
  const { userId } = await auth();

  if (!userId) {
    // Server-side redirect for unauthenticated users
    redirect('/sign-in');
  }

  // Pre-fetch user credits on the server for better performance
  const userCredits = await getUserCredits(userId);
  const initialCredits = userCredits?.currentCreditBalance || userCredits?.credits || 0;

  // Render the client component with initial data
  return <AIVideoCreationClient initialCredits={initialCredits} />;
}
