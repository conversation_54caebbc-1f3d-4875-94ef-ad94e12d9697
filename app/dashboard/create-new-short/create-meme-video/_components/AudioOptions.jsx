import React from 'react';
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { Slider } from "@/components/ui/slider";

const AudioOptions = ({
  useOriginalAudio, onUseOriginalAudioChange,
  backgroundMusic, onBackgroundMusicChange,
  originalAudioVolume, onOriginalAudioVolumeChange,
  backgroundMusicVolume, onBackgroundMusicVolumeChange,
}) => {
  // TODO: Implement background music selection/upload and more robust volume controls

  const handleOriginalVolumeChange = (value) => {
    onOriginalAudioVolumeChange(value[0]);
  };

  const handleBackgroundVolumeChange = (value) => {
    onBackgroundMusicVolumeChange(value[0]);
  };

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200">Audio Options</h3>

      {/* Use Original Audio */}
      <div className="flex items-center space-x-2">
        <Checkbox
          id="useOriginalAudio"
          checked={useOriginalAudio}
          onCheckedChange={onUseOriginalAudioChange}
        />
        <Label htmlFor="useOriginalAudio">Use Original Video Audio</Label>
      </div>

      {/* Original Audio Volume */}
      {useOriginalAudio && (
        <div>
          <Label htmlFor="originalVolumeSlider" className="mb-2 block">Original Audio Volume ({(originalAudioVolume * 100).toFixed(0)}%)</Label>
          <Slider
            id="originalVolumeSlider"
            min={0}
            max={1}
            step={0.01}
            value={[originalAudioVolume]}
            onValueChange={handleOriginalVolumeChange}
          />
        </div>
      )}

      <hr className="my-4 border-gray-200 dark:border-gray-700" />

      {/* Add Background Music */}
      <div>
        <Label htmlFor="backgroundMusicUpload" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Add Background Music (Optional)</Label>
        {/* TODO: Replace with a proper file/music selector */}
        <Input
          id="backgroundMusicUpload"
          type="file"
          accept="audio/*"
          onChange={(e) => onBackgroundMusicChange(e.target.files[0])}
        />
         {backgroundMusic && (
            <div className="mt-2 text-sm text-gray-600 dark:text-gray-400">
              Selected: {backgroundMusic.name}
            </div>
          )}
      </div>

      {/* Background Music Volume */}
      {backgroundMusic && (
        <div>
          <Label htmlFor="backgroundVolumeSlider" className="mb-2 block">Background Music Volume ({(backgroundMusicVolume * 100).toFixed(0)}%)</Label>
          <Slider
            id="backgroundVolumeSlider"
            min={0}
            max={1}
            step={0.01}
            value={[backgroundMusicVolume]}
            onValueChange={handleBackgroundVolumeChange}
          />
        </div>
      )}
    </div>
  );
};

export default AudioOptions;
