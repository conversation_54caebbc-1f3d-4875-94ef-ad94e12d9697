import React from 'react';
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

const AspectRatioControl = ({ selectedAspectRatio, onAspectRatioSelect }) => {
  return (
    <div className="space-y-2">
      <Label htmlFor="aspectRatioSelect">Aspect Ratio</Label>
      <Select value={selectedAspectRatio} onValueChange={onAspectRatioSelect}>
        <SelectTrigger id="aspectRatioSelect">
          <SelectValue placeholder="Select aspect ratio" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="9:16">9:16 (Vertical - Shorts/Reels)</SelectItem>
          <SelectItem value="1:1">1:1 (Square)</SelectItem>
          <SelectItem value="16:9">16:9 (Horizontal - Wide)</SelectItem>
        </SelectContent>
      </Select>
    </div>
  );
};

export default AspectRatioControl;