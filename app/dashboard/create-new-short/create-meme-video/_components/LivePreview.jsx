import React from 'react';

const LivePreview = ({ videoSource, memeText, font, fontSize, textColor, textOutline, outlineColor, outlineThickness, textShadow, backgroundColor, textPosition, aspectRatio }) => {
  // TODO: Implement live video preview with text overlay
  // This component should display the videoSource and overlay the memeText
  // with the specified styling and position.
  // The aspectRatio prop can be used to control the video container's aspect ratio.

  return (
    <div className={`relative w-full overflow-hidden rounded-md shadow-md aspect-${aspectRatio.replace(':', '-')} bg-black`}> {/* Added bg-black back for the video container */}
      {/* Video element */}
      <video
        src="http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4" // Placeholder video source
        controls={false} // Optional: set to true if you want controls in preview
        loop // Loop the placeholder video
        muted // Mute the placeholder video
        autoPlay // Autoplay the placeholder video
        className="absolute top-0 left-0 w-full h-full object-cover"
      >
        Your browser does not support the video tag.
      </video>
     
      {/* Text Overlay */}
      <div className={`absolute inset-0 flex items-${textPosition.split('-')[0] === 'top' ? 'start' : textPosition.split('-')[0] === 'bottom' ? 'end' : 'center'} justify-${textPosition.split('-')[1]} p-6`}>
         <p
            className="text-lg font-bold"
            style={{
              fontFamily: font,
              fontSize: `${fontSize}px`,
              color: textColor,
              // TODO: Implement outline and shadow styles based on props
            }}
         >
            {memeText || "Your Meme Text Here"}
         </p>
      </div>
    </div>
  );
};

export default LivePreview;
