import React from 'react';
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";

const VideoInput = ({ videoSource, onVideoSourceChange }) => {
  const handleFileUpload = (event) => {
    const file = event.target.files[0];
    if (file) {
      onVideoSourceChange(file);
    }
  };

  const handleUrlChange = (event) => {
    onVideoSourceChange(event.target.value);
  };

  return (
    <div className="space-y-6"> {/* Increased space */}
      <div className="border border-gray-200 dark:border-gray-700 rounded-md p-4 space-y-4"> {/* Added border and padding */}
        <div>
          <label htmlFor="videoUpload" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Upload Video File</label>
          <Input id="videoUpload" type="file" accept="video/*" onChange={handleFileUpload} />
        </div>
        <div className="flex items-center justify-center text-gray-500 dark:text-gray-400 text-sm"> {/* Styled "Or" */}
          — OR —
        </div>
        <div>
          <label htmlFor="videoUrl" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Paste Video URL</label>
          <Input
            id="videoUrl"
            type="text"
            placeholder="e.g., https://www.youtube.com/watch?v=..."
            value={typeof videoSource === 'string' ? videoSource : ''}
            onChange={handleUrlChange}
          />
        </div>
      </div>
      {videoSource && (
        <div className="mt-2 text-sm text-gray-600 dark:text-gray-400 p-2 rounded-md"> {/* Styled selected source display */}
          Selected: <span className="font-semibold">{typeof videoSource === 'string' ? videoSource : videoSource.name}</span>
        </div>
      )}
    </div>
  );
};

export default VideoInput;
