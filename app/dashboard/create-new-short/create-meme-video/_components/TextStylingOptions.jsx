import React from 'react';
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Slider } from "@/components/ui/slider";
import { Checkbox } from "@/components/ui/checkbox";
import { Separator } from "@/components/ui/separator"; // Assuming Separator is available in shadcn/ui

const TextStylingOptions = ({
  font, onFontChange,
  fontSize, onFontSizeChange,
  textColor, onTextColorChange,
  textOutline, onTextOutlineChange,
  outlineColor, onOutlineColorChange,
  outlineThickness, onOutlineThicknessChange,
  textShadow, onTextShadowChange,
  textPosition, onTextPositionChange,
}) => {
  const handleFontSizeChange = (value) => {
    onFontSizeChange(value[0]);
  };

  const handleOutlineThicknessChange = (value) => {
    onOutlineThicknessChange(value[0]);
  };

  return (
    <div className="space-y-6"> {/* Increased space between main sections */}
      <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200">Text Styling</h3>

      {/* Basic Text Properties */}
      <div className="space-y-4">
        <h4 className="text-md font-medium text-gray-700 dark:text-gray-300">Basic Properties</h4>

        {/* Font Selection */}
        <div>
          <Label htmlFor="fontSelect">Font</Label>
          <Select value={font} onValueChange={onFontChange}>
            <SelectTrigger id="fontSelect">
              <SelectValue placeholder="Select a font" />
            </SelectTrigger>
            <SelectContent>
              {/* Add actual font options here */}
              <SelectItem value="Impact">Impact</SelectItem>
              <SelectItem value="Arial Bold">Arial Bold</SelectItem>
              <SelectItem value="Montserrat Bold">Montserrat Bold</SelectItem>
              <SelectItem value="Open Sans ExtraBold">Open Sans ExtraBold</SelectItem>
              {/* Add more meme-friendly fonts */}
            </SelectContent>
          </Select>
        </div>

        {/* Font Size */}
        <div>
          <Label htmlFor="fontSizeSlider" className="mb-2 block">Font Size ({fontSize}px)</Label>
          <Slider
            id="fontSizeSlider"
            min={12}
            max={100}
            step={1}
            value={[fontSize]}
            onValueChange={handleFontSizeChange}
          />
        </div>

        {/* Text Color */}
        <div className="flex items-center justify-between gap-4">
          <Label htmlFor="textColorPicker" className="shrink-0 mb-2">Text Color</Label>
          <Input id="textColorPicker" type="color" value={textColor} onChange={(e) => onTextColorChange(e.target.value)} className="w-16 h-10 p-1" />
        </div>
      </div>

      <Separator /> {/* Visual separator */}

      {/* Text Effects */}
      <div className="space-y-4">
        <h4 className="text-md font-medium text-gray-700 dark:text-gray-300">Effects</h4>

        {/* Text Outline */}
        <div className="flex items-center space-x-2">
          <Checkbox id="textOutline" checked={textOutline} onCheckedChange={onTextOutlineChange} />
          <Label htmlFor="textOutline">Add Outline</Label>
        </div>

        {textOutline && (
          <div className="space-y-4 pl-6 border-l border-gray-200 dark:border-gray-700"> {/* Added border for nested options */}
            {/* Outline Color */}
            <div className="flex items-center justify-between gap-4">
              <Label htmlFor="outlineColorPicker" className="shrink-0 mb-2">Outline Color</Label>
              <Input id="outlineColorPicker" type="color" value={outlineColor} onChange={(e) => onOutlineColorChange(e.target.value)} className="w-16 h-10 p-1" />
            </div>
            {/* Outline Thickness */}
            <div>
              <Label htmlFor="outlineThicknessSlider" className="mb-2 block">Outline Thickness ({outlineThickness}px)</Label>
              <Slider
                id="outlineThicknessSlider"
                min={1}
                max={10}
                step={0.5}
                value={[outlineThickness]}
                onValueChange={handleOutlineThicknessChange}
              />
            </div>
          </div>
        )}

        {/* Text Shadow */}
        <div className="flex items-center space-x-2">
          <Checkbox id="textShadow" checked={textShadow} onCheckedChange={onTextShadowChange} />
          <Label htmlFor="textShadow">Add Shadow</Label>
        </div>
      </div>

      <Separator /> {/* Visual separator */}

      {/* Position and Background */}
      <div className="space-y-4">
        <h4 className="text-md font-medium text-gray-700 dark:text-gray-300">Position & Background</h4>

        {/* Text Position */}
        <div>
          <Label htmlFor="textPositionSelect">Text Position</Label>
          <Select value={textPosition} onValueChange={onTextPositionChange}>
            <SelectTrigger id="textPositionSelect">
              <SelectValue placeholder="Select position" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="top">Top</SelectItem>
              <SelectItem value="middle">Middle</SelectItem>
              <SelectItem value="bottom">Bottom</SelectItem>
            </SelectContent>
          </Select>
        </div>

      </div>

    </div>
  );
};

export default TextStylingOptions;
