import React from 'react';
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";

const TextInput = ({ memeText, onMemeTextChange }) => {
  return (
    <div className="space-y-2">
      <Label htmlFor="memeText">Your Meme Text Here</Label>
      <Textarea
        id="memeText"
        value={memeText}
        onChange={(e) => onMemeTextChange(e.target.value)}
        placeholder="Enter Your Viral Caption"
        rows={4}
      />
    </div>
  );
};

export default TextInput;