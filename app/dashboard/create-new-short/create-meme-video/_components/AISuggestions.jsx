import React from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>rk<PERSON> } from "lucide-react";

const AISuggestions = ({ onTextSuggest }) => {
  // TODO: Implement AI suggestion logic
  // This component should call an API or function to get AI text suggestions
  // and use onTextSuggest to update the memeText state in the parent.

  const handleSuggestText = () => {
    // Simulate fetching a suggestion
    const suggestions = [
      "POV: You just finished coding for the day",
      "When the build finally passes",
      "That feeling when your code works on the first try",
      "Me trying to explain my code to the PM",
    ];
    const randomSuggestion = suggestions[Math.floor(Math.random() * suggestions.length)];
    onTextSuggest(randomSuggestion);
  };

  return (
    <div className="space-y-2">
      <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200">AI Suggestions</h3>
      <Button onClick={handleSuggestText} className="w-full">
        <Sparkles className="mr-2 h-4 w-4" /> Suggest Viral Text
      </Button>
      {/* TODO: Optionally display multiple suggestions */}
    </div>
  );
};

export default AISuggestions;