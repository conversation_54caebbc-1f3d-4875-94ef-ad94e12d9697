import React from 'react';
import { OptionSelector } from '@/components/shared/OptionSelector';
import { FormSection } from '@/components/shared/FormSection';

const MemeStylePresets = ({ onPresetSelect }) => {
  // Meme style preset options
  const presets = [
    {
      id: "classic-impact",
      name: "Classic Impact",
      description: "Bold white text with black outline",
      metadata: {
        font: "Impact",
        fontSize: 56,
        textColor: "#FFFFFF",
        textOutline: true,
        outlineColor: "#000000",
        outlineThickness: 3,
        textShadow: false,
        backgroundColor: null,
        textPosition: "bottom-center",
      }
    },
    {
      id: "subtle-caption",
      name: "Subtle Caption",
      description: "Smaller text with shadow effect",
      metadata: {
        font: "Arial Bold",
        fontSize: 24,
        textColor: "#FFFFFF",
        textOutline: true,
        outlineColor: "#000000",
        outlineThickness: 1,
        textShadow: true,
        backgroundColor: null,
        textPosition: "bottom-center",
      }
    },
    // Add more presets as needed
  ];

  const handlePresetSelect = (presetId) => {
    const selectedPreset = presets.find(preset => preset.id === presetId);
    if (selectedPreset && onPresetSelect) {
      onPresetSelect(selectedPreset.metadata);
    }
  };

  return (
    <FormSection
      title="Meme Style Presets"
      description="Choose a predefined style for your meme text"
      variant="minimal"
    >
      <OptionSelector
        options={presets}
        selectedValue={null}
        onSelect={handlePresetSelect}
        layout="dropdown"
        placeholder="Choose a preset"
      />
    </FormSection>
  );
};

export default MemeStylePresets;