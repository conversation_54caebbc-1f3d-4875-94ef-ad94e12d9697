'use client';

import React, { useState, useEffect, useContext } from 'react';
import { useUser } from "@clerk/nextjs";
import { useRouter } from 'next/navigation';
import { Spark<PERSON>, Loader2 } from 'lucide-react';
import { toast } from "sonner"; // Import toast
import { UserDetailContext } from "@/context/UserDetailContext";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
// Remove useVideoGeneration hook import
// import useVideoGeneration from "@/hooks/useVideoGeneration";
import TwitterPostForm from './_components/TwitterPostForm';
import TwitterPostPreview from './_components/TwitterPostPreview';
import { defaultBackgroundVideos } from '../data/defaultBackgroundVideos';
import BackgroundVideoSelector from '../create-ai-video/_components/BackgroundVideoSelector'; // Import the new component

// Import the new server action
import { triggerTwitterVideoGeneration } from "@/actions/twitterVideoGeneration"; // Import the server action

// Import the constant for video generation cost
import { VIDEO_GENERATION_COST } from '@/lib/creditUtils'; // Import cost constant
 
 function CreateTwitterVideoPage() {
  const { isLoaded, isSignedIn, user } = useUser();
  const router = useRouter();
  const { userDetail, setUserDetail } = useContext(UserDetailContext); // Get setUserDetail

  useEffect(() => {
    if (isLoaded && !isSignedIn) {
      router.push('/sign-in');
    }
  }, [isLoaded, isSignedIn, router]);

  // State for Twitter post content
  const [username, setUsername] = useState('');
  const [tweetContent, setTweetContent] = useState('');
  const [likes, setLikes] = useState(0);
  const [retweets, setRetweets] = useState(0);
  const [avatar, setAvatar] = useState('/twitter-avatar.svg'); // Placeholder avatar
  const [handle, setHandle] = useState(''); // Twitter handle

  // State for display options
  const [darkMode, setDarkMode] = useState(false);
  const [showAdvancedOptions, setShowAdvancedOptions] = useState(false);

  // State for Twitter post position and size in video
  const [twitterPostX, setTwitterPostX] = useState(0); // Default X position
  const [twitterPostY, setTwitterPostY] = useState(0); // Default Y position
  const [twitterPostScale, setTwitterPostScale] = useState(1); // Default scale

  // State for video generation options
  const [selectedTemplateId, setSelectedTemplateId] = useState('TwitterPostVideo'); // Use the new template ID
  const [backgroundVideoUrls, setBackgroundVideoUrls] = useState(['']);
  const [selectedDefaultVideo, setSelectedDefaultVideo] = useState(''); // State for selected default video

  // State for Generation Process (Local)
  const [isGenerating, setIsGenerating] = useState(false);
  const [generationMessage, setGenerationMessage] = useState("");


  const handleBackgroundVideoUrlChange = (index, url) => {
    const newUrls = [...backgroundVideoUrls];
    newUrls[index] = url;
    setBackgroundVideoUrls(newUrls);
  };

  const addBackgroundVideoUrlInput = () => {
    setBackgroundVideoUrls([...backgroundVideoUrls, '']);
  };

  const removeBackgroundVideoUrlInput = (index) => {
    const newUrls = backgroundVideoUrls.filter((_, i) => i !== index);
    setBackgroundVideoUrls(newUrls);
  };

  // Remove useVideoGeneration hook usage
  // const {
  //   generationState,
  //   handleGenerateAndSaveVideo,
  //   VIDEO_GENERATION_COST,
  // } = useVideoGeneration();

  const handleGenerateVideo = async () => {
    console.log("Initiating Twitter video generation via server action...");

    const urlsToGenerate = backgroundVideoUrls.filter(url => url.trim() !== '');
    if (selectedDefaultVideo) {
      urlsToGenerate.push(selectedDefaultVideo);
    }

    const formData = {
      projectTitle: `Tweet by ${username || 'user'}`,
      twitterPost: { username, tweetContent, likes, retweets, avatar, handle },
      backgroundVideoUrls: urlsToGenerate,
      templateId: selectedTemplateId,
      twitterPostX: twitterPostX,
      twitterPostY: twitterPostY,
      twitterPostScale: twitterPostScale,
      aspectRatio: '9:16', // Assuming 9:16 for shorts
      // Add other relevant data for video generation if needed by the Inngest function
    };

    // --- Form Validation (Client-side) ---
     if (!formData.projectTitle || !formData.twitterPost.username || !formData.twitterPost.tweetContent || formData.backgroundVideoUrls.length === 0 || !formData.templateId || formData.aspectRatio === null || formData.aspectRatio === undefined) {
         toast.error("Missing Information", { description: "Please fill in all required fields (Username, Tweet Content, and at least one Background Video URL or select a default video)." });
         return;
     }
    // --- End Form Validation ---

    // --- Credit Check (Frontend - for immediate feedback) ---
    if (!userDetail || userDetail.credits < VIDEO_GENERATION_COST) {
      toast.error("Insufficient Credits", { description: `Need ${VIDEO_GENERATION_COST}, have ${userDetail?.credits || 0}.` });
      return;
    }

    setIsGenerating(true);
    setGenerationMessage("Triggering generation process...");

    try {
      // Call the new server action to trigger the Inngest workflow
      const result = await triggerTwitterVideoGeneration(formData);

      if (result.success) {
        console.log("Inngest workflow triggered successfully:", result.eventId);
        toast.success("Generation Started!", {
          description: "Your Twitter video is being generated in the background. Check your dashboard for updates.",
        });
         // Optimistically update credits in UI
        setUserDetail(prev => prev ? { ...prev, credits: Math.max(0, prev.credits - VIDEO_GENERATION_COST) } : null);
        // Redirect to dashboard after triggering
        router.push('/dashboard');
      } else {
        console.error("Failed to trigger Inngest workflow:", result.error);
        toast.error("Generation Failed", {
          description: result.error || "An error occurred triggering the generation process.",
        });
      }

    } catch (error) {
      console.error("Error calling triggerTwitterVideoGeneration server action:", error);
      toast.error("Generation Failed", {
        description: error.message || "An unexpected error occurred.",
      });
    } finally {
      setIsGenerating(false);
      setGenerationMessage("");
    }
  };

  const handleAvatarUpload = (e) => {
    const file = e.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setAvatar(e.target.result);
      };
      reader.readAsDataURL(file);
    }
  };

  if (!isLoaded) {
    return <div>Loading...</div>;
  }

  if (isSignedIn) {
     const isGenerateButtonDisabled = isGenerating || // Disable during processing
                                      !username || !tweetContent || (backgroundVideoUrls.every(url => url.trim() === '') && !selectedDefaultVideo) || // Form validation
                                      !userDetail || userDetail.credits < VIDEO_GENERATION_COST; // Credit check

    return (
      <div className="space-y-8 max-w-7xl mx-auto">
        {/* Header Section */}
        <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50 via-blue-50 to-purple-50 dark:from-purple-950/20 dark:via-blue-950/20 dark:to-purple-950/20 p-8 border border-border/50">
          <div className="relative z-10">
            <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-6">
              <div className="space-y-3">
                <h1 className="text-heading-1 text-gradient">
                  Twitter Post Video Creator 🐦
                </h1>
                <p className="text-body-large text-muted-foreground max-w-2xl">
                  Transform Twitter posts into engaging videos. Create authentic-looking tweets with customizable styling and backgrounds.
                </p>
                <div className="flex items-center gap-4 text-body-small text-muted-foreground">
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                    <span>Twitter renderer ready</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span>⚡</span>
                    <span>{userDetail?.credits || 0} credits available</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span>🎬</span>
                    <span>Cost: {VIDEO_GENERATION_COST} credits</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          {/* Background decoration */}
          <div className="absolute top-0 right-0 w-64 h-64 bg-gradient-to-br from-purple-400/10 to-blue-400/10 rounded-full blur-3xl"></div>
          <div className="absolute bottom-0 left-0 w-48 h-48 bg-gradient-to-tr from-blue-400/10 to-purple-400/10 rounded-full blur-3xl"></div>
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 xl:grid-cols-3 gap-8">
          {/* Left Column - Form Sections */}
          <div className="xl:col-span-2 space-y-6">

            {/* Section 1: Twitter Post Content */}
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-br from-sky-50/50 to-blue-50/50 dark:from-sky-950/10 dark:to-blue-950/10 rounded-2xl"></div>
              <div className="relative bg-background/80 backdrop-blur-sm border border-border/50 rounded-2xl p-6">
                {/* Section Header */}
                <div className="flex items-center gap-3 mb-6 pb-4 border-b border-border/30">
                  <div className="p-2 bg-gradient-to-br from-sky-500 to-blue-500 rounded-lg shadow-soft">
                    <div className="h-6 w-6 text-white flex items-center justify-center text-lg">🐦</div>
                  </div>
                  <div>
                    <h2 className="text-heading-2 text-gradient">Twitter Post Content</h2>
                    <p className="text-body-small text-muted-foreground">Create your Twitter post content and styling</p>
                  </div>
                </div>

                <TwitterPostForm
                  username={username}
                  setUsername={setUsername}
                  tweetContent={tweetContent}
                  setTweetContent={setTweetContent}
                  likes={likes}
                  setLikes={setLikes}
                  retweets={retweets}
                  setRetweets={setRetweets}
                  avatar={avatar}
                  handleAvatarUpload={handleAvatarUpload}
                  handle={handle}
                  setHandle={setHandle}
                  darkMode={darkMode}
                  setDarkMode={setDarkMode}
                  showAdvancedOptions={showAdvancedOptions}
                  setShowAdvancedOptions={setShowAdvancedOptions}
                />
              </div>
            </div>

            {/* Section Connector */}
            <div className="flex justify-center">
              <div className="w-px h-8 bg-gradient-to-b from-sky-300 to-purple-300 dark:from-sky-600 dark:to-purple-600"></div>
            </div>

            {/* Section 2: Video Layout & Positioning */}
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-br from-purple-50/50 to-pink-50/50 dark:from-purple-950/10 dark:to-pink-950/10 rounded-2xl"></div>
              <div className="relative bg-background/80 backdrop-blur-sm border border-border/50 rounded-2xl p-6">
                {/* Section Header */}
                <div className="flex items-center gap-3 mb-6 pb-4 border-b border-border/30">
                  <div className="p-2 bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg shadow-soft">
                    <div className="h-6 w-6 text-white flex items-center justify-center text-lg">🎯</div>
                  </div>
                  <div>
                    <h2 className="text-heading-2 text-gradient">Video Layout & Positioning</h2>
                    <p className="text-body-small text-muted-foreground">Configure post position, scale, and template</p>
                  </div>
                </div>

                <div className="space-y-6">
                  {/* Twitter Post Position and Size Controls */}
                  <div>
                    <h3 className="text-body font-semibold mb-3">Post Position & Scale</h3>
                    <div className="grid grid-cols-3 gap-4">
                      <div>
                        <Label htmlFor="twitterPostX" className="text-body-small font-medium">X Position</Label>
                        <Input
                          id="twitterPostX"
                          type="number"
                          value={twitterPostX}
                          onChange={(e) => setTwitterPostX(parseFloat(e.target.value) || 0)}
                          step="1"
                          className="mt-1"
                        />
                      </div>
                      <div>
                        <Label htmlFor="twitterPostY" className="text-body-small font-medium">Y Position</Label>
                        <Input
                          id="twitterPostY"
                          type="number"
                          value={twitterPostY}
                          onChange={(e) => setTwitterPostY(parseFloat(e.target.value) || 0)}
                          step="1"
                          className="mt-1"
                        />
                      </div>
                      <div>
                        <Label htmlFor="twitterPostScale" className="text-body-small font-medium">Scale</Label>
                        <Input
                          id="twitterPostScale"
                          type="number"
                          value={twitterPostScale}
                          onChange={(e) => setTwitterPostScale(parseFloat(e.target.value) || 1)}
                          step="0.1"
                          min="0.1"
                          className="mt-1"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Template Selection */}
                  <div>
                    <h3 className="text-body font-semibold mb-3">Template</h3>
                    <div className="p-3 bg-muted/20 rounded-lg border border-border/20">
                      <p className="text-body-small text-muted-foreground">Selected Template:</p>
                      <p className="text-body font-medium">{selectedTemplateId}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Section Connector */}
            <div className="flex justify-center">
              <div className="w-px h-8 bg-gradient-to-b from-purple-300 to-emerald-300 dark:from-purple-600 dark:to-emerald-600"></div>
            </div>

            {/* Section 3: Background Videos */}
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-br from-emerald-50/50 to-green-50/50 dark:from-emerald-950/10 dark:to-green-950/10 rounded-2xl"></div>
              <div className="relative bg-background/80 backdrop-blur-sm border border-border/50 rounded-2xl p-6">
                {/* Section Header */}
                <div className="flex items-center gap-3 mb-6 pb-4 border-b border-border/30">
                  <div className="p-2 bg-gradient-to-br from-emerald-500 to-green-500 rounded-lg shadow-soft">
                    <div className="h-6 w-6 text-white flex items-center justify-center text-lg">🎥</div>
                  </div>
                  <div>
                    <h2 className="text-heading-2 text-gradient">Background Videos</h2>
                    <p className="text-body-small text-muted-foreground">Choose background videos for your Twitter post</p>
                  </div>
                </div>

                <div className="space-y-6">
                  {/* Default Background Videos Selection */}
                  <div>
                    <h3 className="text-body font-semibold mb-3">Default Background Videos</h3>
                    <BackgroundVideoSelector
                      selectedVideoUrl={selectedDefaultVideo}
                      onSelectVideo={setSelectedDefaultVideo}
                    />
                  </div>

                  {/* Custom Background Video URLs Input */}
                  <div>
                    <h3 className="text-body font-semibold mb-3">Custom Background Video URLs</h3>
                    <div className="space-y-3">
                      {backgroundVideoUrls.map((url, index) => (
                        <div key={index} className="flex items-center space-x-2">
                          <Input
                            type="text"
                            value={url}
                            onChange={(e) => handleBackgroundVideoUrlChange(index, e.target.value)}
                            placeholder={`Video URL ${index + 1}`}
                            className="flex-grow"
                          />
                          {backgroundVideoUrls.length > 1 && (
                            <Button
                              variant="destructive"
                              size="sm"
                              onClick={() => removeBackgroundVideoUrlInput(index)}
                            >
                              Remove
                            </Button>
                          )}
                        </div>
                      ))}
                      <Button
                        variant="outline"
                        onClick={addBackgroundVideoUrlInput}
                        className="w-full"
                      >
                        Add Another Custom Video URL
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Right Column - Preview */}
          <div className="xl:col-span-1">
            <TwitterPostPreview
              username={username}
              tweetContent={tweetContent}
              likes={likes}
              retweets={retweets}
              avatar={avatar}
              handle={handle}
              darkMode={darkMode}
              twitterPostX={twitterPostX}
              twitterPostY={twitterPostY}
              twitterPostScale={twitterPostScale}
              backgroundVideoUrl={selectedDefaultVideo || backgroundVideoUrls[0]}
              // Generation props
              onGenerateVideo={handleGenerateVideo}
              isGenerating={isGenerating}
              generationMessage={generationMessage}
              isGenerateButtonDisabled={isGenerateButtonDisabled}
              userDetail={userDetail}
              VIDEO_GENERATION_COST={VIDEO_GENERATION_COST}
            />
          </div>
        </div>
      </div>
    );
  }

  return null;
}

export default CreateTwitterVideoPage;
