'use client';

import React from 'react';
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";

import RedditAvatarUpload from '../../create-reddit-post-video/_components/RedditAvatarUpload'; // Reusing the avatar upload 

function TwitterPostForm({
  username,
  setUsername,
  tweetContent,
  setTweetContent,
  likes,
  setLikes,
  retweets,
  setRetweets,
  avatar,
  handleAvatarUpload,
  handle,
  setHandle,
  darkMode,
  setDarkMode,
  showAdvancedOptions,
  setShowAdvancedOptions,
}) {
  return (
    <div className="space-y-6">
      <form className="space-y-6">
        {/* Reusing RedditAvatarUpload - consider creating a generic AvatarUpload if needed elsewhere */}
        <RedditAvatarUpload avatar={avatar} handleAvatarUpload={handleAvatarUpload} />

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="username" className="text-body-small font-medium">Username</Label>
            <Input
              id="username"
              type="text"
              value={username}
              onChange={(e) => setUsername(e.target.value)}
              placeholder="e.g., Elon Musk"
              className="mt-1"
            />
          </div>
          <div>
            <Label htmlFor="handle" className="text-body-small font-medium">Twitter Handle</Label>
            <Input
              id="handle"
              type="text"
              value={handle}
              onChange={(e) => setHandle(e.target.value)}
              placeholder="e.g., @elonmusk"
              className="mt-1"
            />
          </div>
        </div>

        <div>
          <Label htmlFor="tweetContent" className="text-body-small font-medium">Tweet Content</Label>
          <Textarea
            id="tweetContent"
            value={tweetContent}
            onChange={(e) => setTweetContent(e.target.value)}
            placeholder="Enter the tweet content here..."
            rows={6}
            className="mt-1 resize-none"
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="likes" className="text-body-small font-medium">Likes</Label>
            <Input
              id="likes"
              type="number"
              value={likes}
              onChange={(e) => setLikes(parseInt(e.target.value) || 0)}
              min="0"
              className="mt-1"
            />
          </div>
          <div>
            <Label htmlFor="retweets" className="text-body-small font-medium">Retweets</Label>
            <Input
              id="retweets"
              type="number"
              value={retweets}
              onChange={(e) => setRetweets(parseInt(e.target.value) || 0)}
              min="0"
              className="mt-1"
            />
          </div>
        </div>

        {/* Advanced Options Toggle */}
        <div className="pt-4 border-t border-border/30">
          <button
            type="button"
            onClick={() => setShowAdvancedOptions(!showAdvancedOptions)}
            className="text-body-small font-medium text-primary hover:text-primary/80 transition-colors"
          >
            {showAdvancedOptions ? 'Hide Advanced Options' : 'Show Advanced Options'}
          </button>
        </div>

        {showAdvancedOptions && (
          <div className="pt-4 space-y-4">
            {/* Dark Mode Toggle */}
            <div className="flex items-center justify-between">
              <Label htmlFor="darkMode" className="text-body-small font-medium">Dark Mode</Label>
              <Switch
                id="darkMode"
                checked={darkMode}
                onCheckedChange={setDarkMode}
              />
            </div>
            {/* Add other advanced options here if needed */}
          </div>
        )}
      </form>
    </div>
  );
}

export default TwitterPostForm;