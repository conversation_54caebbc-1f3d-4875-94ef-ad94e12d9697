'use client';

import React from 'react';
import { Player } from '@remotion/player';
import TwitterPostComposition from '@/remotion/compositions/TwitterPostComposition';
import { VideoPreviewPanel } from '@/components/shared/VideoPreviewPanel';

function TwitterPostPreview({
  username,
  tweetContent,
  likes,
  retweets,
  avatar,
  handle,
  darkMode,
  twitterPostX,
  twitterPostY,
  twitterPostScale,
  backgroundVideoUrl,
  // Generation props
  onGenerateVideo,
  isGenerating,
  generationMessage,
  isGenerateButtonDisabled,
  userDetail,
  VIDEO_GENERATION_COST
}) {
  // Define video dimensions and duration
  const width = 1080;
  const height = 1920;
  const durationInFrames = 30 * 30;

  // Configuration for VideoPreviewPanel
  const previewConfig = {
    showRemotionPlayer: true,
    showConfigSummary: true,
    showGenerationStatus: true,
    title: "Preview & Generate",
    description: "Review your Twitter post video",
    icon: () => <div className="h-6 w-6 text-white flex items-center justify-center text-lg">👁️</div>
  };

  // Remotion player props
  const remotionProps = {
    component: TwitterPostComposition,
    durationInFrames,
    fps: 30,
    compositionWidth: width,
    compositionHeight: height,
    inputProps: {
      username,
      tweetContent,
      likes,
      retweets,
      avatar,
      handle,
      darkMode,
      twitterPostX,
      twitterPostY,
      twitterPostScale,
      backgroundVideoUrl,
    },
    loop: true,
    controls: true,
    style: { width: '100%', height: '100%', borderRadius: '8px' }
  };

  // Configuration summary items
  const configSummary = [
    { isComplete: Boolean(username), label: `Username: ${username || 'Not set'}`, icon: () => null, color: 'text-foreground' },
    { isComplete: Boolean(handle), label: `Handle: ${handle || 'Not set'}`, icon: () => null, color: 'text-foreground' },
    { isComplete: Boolean(twitterPostX && twitterPostY), label: `Position: ${twitterPostX}, ${twitterPostY}`, icon: () => null, color: 'text-foreground' },
    { isComplete: Boolean(twitterPostScale), label: `Scale: ${twitterPostScale}x`, icon: () => null, color: 'text-foreground' }
  ];

  return (
    <VideoPreviewPanel
      videoType="twitter-post"
      previewConfig={previewConfig}
      formData={{ username, tweetContent, handle }}
      onGenerate={onGenerateVideo}
      isGenerating={isGenerating}
      generationMessage={generationMessage}
      isGenerateButtonDisabled={isGenerateButtonDisabled}
      userDetail={userDetail}
      VIDEO_GENERATION_COST={VIDEO_GENERATION_COST}
      remotionProps={remotionProps}
      configSummary={configSummary}
    />
  );
}

export default TwitterPostPreview;