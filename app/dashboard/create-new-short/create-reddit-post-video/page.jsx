'use client';

import React, { useState, useEffect, useContext } from 'react'; // Import useContext
import { useUser } from "@clerk/nextjs"; // Import useUser
import { useRouter } from 'next/navigation'; // Import useRouter
import { <PERSON><PERSON><PERSON>, Loader2 } from 'lucide-react';
import { toast } from "sonner"; // Import toast
import RedditPostForm from './_components/RedditPostForm';
import { UserDetailContext } from "@/context/UserDetailContext"; // Import UserDetailContext
import { Label } from "@/components/ui/label";
import { defaultBackgroundVideos } from '../data/defaultBackgroundVideos'; // Import default background videos
import BackgroundVideoSelector from '../create-ai-video/_components/BackgroundVideoSelector'; // Import the new component
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
// Remove useVideoGeneration hook import
// import useVideoGeneration from "@/hooks/useVideoGeneration";
import RedditPostPreview from './_components/RedditPostPreview';

// Import the new server action
import { triggerRedditVideoGeneration } from "@/actions/redditVideoGeneration"; // Import the server action

// Import the constant for video generation cost
import { VIDEO_GENERATION_COST } from '@/lib/creditUtils'; // Import cost constant


function CreateRedditVideoPage() {
  const { isLoaded, isSignedIn, user } = useUser(); // Use the useUser hook
  const router = useRouter(); // Use the useRouter hook
  const { userDetail, setUserDetail } = useContext(UserDetailContext); // Use the UserDetailContext and get setUserDetail

  // Redirect unauthenticated users to the sign-in page
  useEffect(() => {
    if (isLoaded && !isSignedIn) {
      router.push('/sign-in');
    }
  }, [isLoaded, isSignedIn, router]);

  // State for Reddit post content
  const [title, setTitle] = useState('');
  const [subreddit, setSubreddit] = useState('');
  const [username, setUsername] = useState('');
  const [postContent, setPostContent] = useState('');
  const [upvotes, setUpvotes] = useState(249);
  const [comments, setComments] = useState(57);
  const [avatar, setAvatar] = useState('/reddit-avatar.svg'); // Default avatar
  const [badge, setBadge] = useState('');

  // State for display options
  const [darkMode, setDarkMode] = useState(false);
  const [wideLayout, setWideLayout] = useState(false);
  const [approximateCounts, setApproximateCounts] = useState(false);
  const [hideTrophies, setHideTrophies] = useState(false);
  const [hideUpvotes, setHideUpvotes] = useState(false);
  const [hideComments, setHideComments] = useState(false);
  const [hideShare, setHideShare] = useState(false);
  const [showAdvancedOptions, setShowAdvancedOptions] = useState(false);

  // State for Reddit post position and size in video
  const [redditPostX, setRedditPostX] = useState(0); // Default X position (centered)
  const [redditPostY, setRedditPostY] = useState(0); // Default Y position (centered)
  const [redditPostScale, setRedditPostScale] = useState(1); // Default scale

  // State for video generation options
  const [selectedTemplateId, setSelectedTemplateId] = useState('RedditPostVideo'); // Use the correct template ID
  const [backgroundVideoUrls, setBackgroundVideoUrls] = useState(['']); // State for background video URLs, start with one empty input
  const [selectedDefaultVideo, setSelectedDefaultVideo] = useState(''); // State for selected default video

  // State for Generation Process (Local)
  const [isGenerating, setIsGenerating] = useState(false);
  const [generationMessage, setGenerationMessage] = useState("");


  // Handle video URL changes
  const handleBackgroundVideoUrlChange = (index, url) => {
    const newUrls = [...backgroundVideoUrls];
    newUrls[index] = url;
    setBackgroundVideoUrls(newUrls);
  };

  // Add another video URL input
  const addBackgroundVideoUrlInput = () => {
    setBackgroundVideoUrls([...backgroundVideoUrls, '']);
  };

  // Remove a video URL input
  const removeBackgroundVideoUrlInput = (index) => {
    const newUrls = backgroundVideoUrls.filter((_, i) => i !== index);
    setBackgroundVideoUrls(newUrls);
  };

  // Remove useVideoGeneration hook usage
  // const {
  //   generationState, // This state tracks the generation process
  //   handleGenerateAndSaveVideo,
  //   VIDEO_GENERATION_COST,
  // } = useVideoGeneration();


  // Handle video generation logic (Trigger Inngest)
  const handleGenerateVideo = async () => {
    console.log("Initiating Reddit video generation via server action...");

    const urlsToGenerate = backgroundVideoUrls.filter(url => url.trim() !== '');
    if (selectedDefaultVideo) {
      urlsToGenerate.push(selectedDefaultVideo);
    }

    const formData = {
      projectTitle: title, // Using title as project title for now
      redditPost: { title, subreddit, username, postContent, upvotes, comments, avatar, badge },
      backgroundVideoUrls: urlsToGenerate,
      templateId: selectedTemplateId,
      redditPostX: redditPostX, // Add X position
      redditPostY: redditPostY, // Add Y position
      redditPostScale: redditPostScale, // Add scale
      aspectRatio: '9:16', // Assuming 9:16 for shorts, adjust if needed
      // Add other relevant data for video generation if needed by the Inngest function
    };

    // --- Form Validation (Client-side) ---
    if (!formData.projectTitle || !formData.redditPost.title || !formData.redditPost.postContent || formData.backgroundVideoUrls.length === 0 || !formData.templateId || formData.aspectRatio === null || formData.aspectRatio === undefined) {
         toast.error("Missing Information", { description: "Please fill in all required fields (Title, Content, and at least one Background Video URL or select a default video)." });
         return;
    }
    // --- End Form Validation ---

    // --- Credit Check (Frontend - for immediate feedback) ---
    if (!userDetail || userDetail.credits < VIDEO_GENERATION_COST) {
      toast.error("Insufficient Credits", { description: `Need ${VIDEO_GENERATION_COST}, have ${userDetail?.credits || 0}.` });
      return;
    }

    setIsGenerating(true);
    setGenerationMessage("Triggering generation process...");

    try {
      // Call the new server action to trigger the Inngest workflow
      const result = await triggerRedditVideoGeneration(formData);

      if (result.success) {
        console.log("Inngest workflow triggered successfully:", result.eventId);
        toast.success("Generation Started!", {
          description: "Your Reddit video is being generated in the background. Check your dashboard for updates.",
        });
         // Optimistically update credits in UI
        setUserDetail(prev => prev ? { ...prev, credits: Math.max(0, prev.credits - VIDEO_GENERATION_COST) } : null);
        // Redirect to dashboard after triggering
        router.push('/dashboard');
      } else {
        console.error("Failed to trigger Inngest workflow:", result.error);
        toast.error("Generation Failed", {
          description: result.error || "An error occurred triggering the generation process.",
        });
      }

    } catch (error) {
      console.error("Error calling triggerRedditVideoGeneration server action:", error);
      toast.error("Generation Failed", {
        description: error.message || "An unexpected error occurred.",
      });
    } finally {
      setIsGenerating(false);
      setGenerationMessage("");
    }
  };

  // Handle file upload for avatar
  const handleAvatarUpload = (e) => {
    const file = e.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setAvatar(e.target.result);
      };
      reader.readAsDataURL(file);
    }
    // No return statement needed here, just handle the file logic
  };

  // Optionally, show a loading state while checking authentication
  if (!isLoaded) {
    return <div>Loading...</div>; // Or a spinner component
  }

  // Only render the page content if the user is signed in
  if (isSignedIn) {
    const isGenerateButtonDisabled = isGenerating || // Disable during processing
                                     !title || !postContent || (backgroundVideoUrls.every(url => url.trim() === '') && !selectedDefaultVideo) || // Form validation
                                     !userDetail || userDetail.credits < VIDEO_GENERATION_COST; // Credit check

    return (
      <div className="space-y-8 max-w-7xl mx-auto">
        {/* Header Section */}
        <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50 via-blue-50 to-purple-50 dark:from-purple-950/20 dark:via-blue-950/20 dark:to-purple-950/20 p-8 border border-border/50">
          <div className="relative z-10">
            <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-6">
              <div className="space-y-3">
                <h1 className="text-heading-1 text-gradient">
                  Reddit Post Video Creator 📱
                </h1>
                <p className="text-body-large text-muted-foreground max-w-2xl">
                  Transform Reddit posts into engaging videos. Create authentic-looking Reddit content with customizable styling and backgrounds.
                </p>
                <div className="flex items-center gap-4 text-body-small text-muted-foreground">
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                    <span>Reddit renderer ready</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span>⚡</span>
                    <span>{userDetail?.credits || 0} credits available</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span>🎬</span>
                    <span>Cost: {VIDEO_GENERATION_COST} credits</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          {/* Background decoration */}
          <div className="absolute top-0 right-0 w-64 h-64 bg-gradient-to-br from-purple-400/10 to-blue-400/10 rounded-full blur-3xl"></div>
          <div className="absolute bottom-0 left-0 w-48 h-48 bg-gradient-to-tr from-blue-400/10 to-purple-400/10 rounded-full blur-3xl"></div>
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 xl:grid-cols-3 gap-8">
          {/* Left Column - Form Sections */}
          <div className="xl:col-span-2 space-y-6">

            {/* Section 1: Reddit Post Content */}
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-br from-orange-50/50 to-red-50/50 dark:from-orange-950/10 dark:to-red-950/10 rounded-2xl"></div>
              <div className="relative bg-background/80 backdrop-blur-sm border border-border/50 rounded-2xl p-6">
                {/* Section Header */}
                <div className="flex items-center gap-3 mb-6 pb-4 border-b border-border/30">
                  <div className="p-2 bg-gradient-to-br from-orange-500 to-red-500 rounded-lg shadow-soft">
                    <div className="h-6 w-6 text-white flex items-center justify-center text-lg">📱</div>
                  </div>
                  <div>
                    <h2 className="text-heading-2 text-gradient">Reddit Post Content</h2>
                    <p className="text-body-small text-muted-foreground">Create your Reddit post content and styling</p>
                  </div>
                </div>

                <RedditPostForm
                  title={title}
                  setTitle={setTitle}
                  subreddit={subreddit}
                  setSubreddit={setSubreddit}
                  username={username}
                  setUsername={setUsername}
                  postContent={postContent}
                  setPostContent={setPostContent}
                  upvotes={upvotes}
                  setUpvotes={setUpvotes}
                  comments={comments}
                  setComments={setComments}
                  avatar={avatar}
                  handleAvatarUpload={handleAvatarUpload}
                  badge={badge}
                  setBadge={setBadge}
                  darkMode={darkMode}
                  setDarkMode={setDarkMode}
                  wideLayout={wideLayout}
                  setWideLayout={setWideLayout}
                  approximateCounts={approximateCounts}
                  setApproximateCounts={setApproximateCounts}
                  hideTrophies={hideTrophies}
                  setHideTrophies={setHideTrophies}
                  hideUpvotes={hideUpvotes}
                  setHideUpvotes={setHideUpvotes}
                  hideComments={hideComments}
                  setHideComments={setHideComments}
                  hideShare={hideShare}
                  setHideShare={setHideShare}
                  showAdvancedOptions={showAdvancedOptions}
                  setShowAdvancedOptions={setShowAdvancedOptions}
                />
              </div>
            </div>

            {/* Section Connector */}
            <div className="flex justify-center">
              <div className="w-px h-8 bg-gradient-to-b from-orange-300 to-blue-300 dark:from-orange-600 dark:to-blue-600"></div>
            </div>

            {/* Section 2: Video Layout & Positioning */}
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-br from-blue-50/50 to-cyan-50/50 dark:from-blue-950/10 dark:to-cyan-950/10 rounded-2xl"></div>
              <div className="relative bg-background/80 backdrop-blur-sm border border-border/50 rounded-2xl p-6">
                {/* Section Header */}
                <div className="flex items-center gap-3 mb-6 pb-4 border-b border-border/30">
                  <div className="p-2 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-lg shadow-soft">
                    <div className="h-6 w-6 text-white flex items-center justify-center text-lg">🎯</div>
                  </div>
                  <div>
                    <h2 className="text-heading-2 text-gradient">Video Layout & Positioning</h2>
                    <p className="text-body-small text-muted-foreground">Configure post position, scale, and template</p>
                  </div>
                </div>

                <div className="space-y-6">
                  {/* Reddit Post Position and Size Controls */}
                  <div>
                    <h3 className="text-body font-semibold mb-3">Post Position & Scale</h3>
                    <div className="grid grid-cols-3 gap-4">
                      <div>
                        <Label htmlFor="redditPostX" className="text-body-small font-medium">X Position</Label>
                        <Input
                          id="redditPostX"
                          type="number"
                          value={redditPostX}
                          onChange={(e) => setRedditPostX(parseFloat(e.target.value) || 0)}
                          step="1"
                          className="mt-1"
                        />
                      </div>
                      <div>
                        <Label htmlFor="redditPostY" className="text-body-small font-medium">Y Position</Label>
                        <Input
                          id="redditPostY"
                          type="number"
                          value={redditPostY}
                          onChange={(e) => setRedditPostY(parseFloat(e.target.value) || 0)}
                          step="1"
                          className="mt-1"
                        />
                      </div>
                      <div>
                        <Label htmlFor="redditPostScale" className="text-body-small font-medium">Scale</Label>
                        <Input
                          id="redditPostScale"
                          type="number"
                          value={redditPostScale}
                          onChange={(e) => setRedditPostScale(parseFloat(e.target.value) || 1)}
                          step="0.1"
                          min="0.1"
                          className="mt-1"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Template Selection */}
                  <div>
                    <h3 className="text-body font-semibold mb-3">Template</h3>
                    <div className="p-3 bg-muted/20 rounded-lg border border-border/20">
                      <p className="text-body-small text-muted-foreground">Selected Template:</p>
                      <p className="text-body font-medium">{selectedTemplateId}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Section Connector */}
            <div className="flex justify-center">
              <div className="w-px h-8 bg-gradient-to-b from-blue-300 to-emerald-300 dark:from-blue-600 dark:to-emerald-600"></div>
            </div>

            {/* Section 3: Background Videos */}
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-br from-emerald-50/50 to-green-50/50 dark:from-emerald-950/10 dark:to-green-950/10 rounded-2xl"></div>
              <div className="relative bg-background/80 backdrop-blur-sm border border-border/50 rounded-2xl p-6">
                {/* Section Header */}
                <div className="flex items-center gap-3 mb-6 pb-4 border-b border-border/30">
                  <div className="p-2 bg-gradient-to-br from-emerald-500 to-green-500 rounded-lg shadow-soft">
                    <div className="h-6 w-6 text-white flex items-center justify-center text-lg">🎥</div>
                  </div>
                  <div>
                    <h2 className="text-heading-2 text-gradient">Background Videos</h2>
                    <p className="text-body-small text-muted-foreground">Choose background videos for your Reddit post</p>
                  </div>
                </div>

                <div className="space-y-6">
                  {/* Default Background Videos Selection */}
                  <div>
                    <h3 className="text-body font-semibold mb-3">Default Background Videos</h3>
                    <BackgroundVideoSelector
                      selectedVideoUrl={selectedDefaultVideo}
                      onSelectVideo={setSelectedDefaultVideo}
                    />
                  </div>

                  {/* Custom Background Video URLs Input */}
                  <div>
                    <h3 className="text-body font-semibold mb-3">Custom Background Video URLs</h3>
                    <div className="space-y-3">
                      {backgroundVideoUrls.map((url, index) => (
                        <div key={index} className="flex items-center space-x-2">
                          <Input
                            type="text"
                            value={url}
                            onChange={(e) => handleBackgroundVideoUrlChange(index, e.target.value)}
                            placeholder={`Video URL ${index + 1}`}
                            className="flex-grow"
                          />
                          {backgroundVideoUrls.length > 1 && (
                            <Button
                              variant="destructive"
                              size="sm"
                              onClick={() => removeBackgroundVideoUrlInput(index)}
                            >
                              Remove
                            </Button>
                          )}
                        </div>
                      ))}
                      <Button
                        variant="outline"
                        onClick={addBackgroundVideoUrlInput}
                        className="w-full"
                      >
                        Add Another Custom Video URL
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Right Column - Preview */}
          <div className="xl:col-span-1">
            <RedditPostPreview
              title={title}
              subreddit={subreddit}
              username={username}
              postContent={postContent}
              upvotes={upvotes}
              comments={comments}
              badge={badge}
              darkMode={darkMode}
              wideLayout={wideLayout}
              approximateCounts={approximateCounts}
              hideTrophies={hideTrophies}
              hideUpvotes={hideUpvotes}
              hideComments={hideComments}
              hideShare={hideShare}
              redditPostX={redditPostX}
              redditPostY={redditPostY}
              redditPostScale={redditPostScale}
              backgroundVideoUrl={selectedDefaultVideo || backgroundVideoUrls[0]}
              // Generation props
              onGenerateVideo={handleGenerateVideo}
              isGenerating={isGenerating}
              generationMessage={generationMessage}
              isGenerateButtonDisabled={isGenerateButtonDisabled}
              userDetail={userDetail}
              VIDEO_GENERATION_COST={VIDEO_GENERATION_COST}
            />
          </div>
        </div>
        </div>
      );
    }

    // If not signed in and not loading, the useEffect will handle the redirect,
    // so this return is technically not reached if the redirect works.
    return null;
  }

  export default CreateRedditVideoPage;
