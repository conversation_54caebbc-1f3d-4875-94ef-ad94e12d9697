'use client';

import React from 'react';
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";

function RedditAvatarUpload({ avatar, handleAvatarUpload }) {
  return (
    <div>
      <Label>Avatar</Label>
      <div className="flex items-center space-x-4">
        <Avatar className="h-12 w-12">
          <AvatarImage
            src={avatar}
            alt="Avatar"
            onError={(e) => {
              e.target.onerror = null;
              e.target.src = 'https://www.redditstatic.com/avatars/defaults/v2/avatar_default_1.png';
            }}
          />
          <AvatarFallback>CN</AvatarFallback> {/* Fallback for avatar */}
        </Avatar>
        <Button asChild>
          <label htmlFor="avatar-upload" className="cursor-pointer">
            Upload
            <input
              id="avatar-upload"
              type="file"
              className="sr-only"
              accept="image/*"
              onChange={handleAvatarUpload}
            />
          </label>
        </Button>
      </div>
    </div>
  );
}

export default RedditAvatarUpload;