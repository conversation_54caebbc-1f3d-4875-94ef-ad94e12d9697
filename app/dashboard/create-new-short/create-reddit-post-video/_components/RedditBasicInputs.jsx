'use client';

import React from 'react';
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

function RedditBasicInputs({
  title,
  setTitle,
  subreddit,
  setSubreddit,
  username,
  setUsername,
  postContent,
  setPostContent,
  upvotes,
  setUpvotes,
  comments,
  setComments,
  badge,
  setBadge,
}) {
  return (
    <>
      {/* Title */}
      <div>
        <Label htmlFor="title">Story</Label>
        <Input
          type="text"
          id="title"
          value={title}
          onChange={(e) => setTitle(e.target.value)}
          placeholder="Enter post title"
          maxLength={300}
        />
        <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">{title.length} / 300 characters</p>
      </div>

      {/* Subreddit/Username */}
      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="subreddit">Subreddit</Label>
          <div className="mt-1 flex rounded-md shadow-sm">
            <span className="inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500 text-sm dark:border-gray-700 dark:bg-gray-700 dark:text-gray-300">r/</span>
            <Input
              type="text"
              id="subreddit"
              value={subreddit}
              onChange={(e) => setSubreddit(e.target.value)}
              placeholder="subreddit"
              className="rounded-none rounded-r-md"
            />
          </div>
        </div>
        <div>
          <Label htmlFor="username">Username</Label>
          <div className="mt-1 flex rounded-md shadow-sm">
            <span className="inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500 text-sm dark:border-gray-700 dark:bg-gray-700 dark:text-gray-300">u/</span>
            <Input
              type="text"
              id="username"
              value={username}
              onChange={(e) => setUsername(e.target.value)}
              placeholder="username"
              className="rounded-none rounded-r-md"
            />
          </div>
        </div>
      </div>

      {/* Post Content */}
      <div>
        <Label htmlFor="postContent">Text</Label>
        <Textarea
          id="postContent"
          value={postContent}
          onChange={(e) => setPostContent(e.target.value)}
          rows={6}
          placeholder="Enter post content"
        />
      </div>

      {/* Badge */}
      <div>
        <Label htmlFor="badge">Badge</Label>
        <Select onValueChange={setBadge} value={badge}>
          <SelectTrigger id="badge">
            <SelectValue placeholder="Select a badge" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="none">No badge</SelectItem>
            <SelectItem value="op">OP</SelectItem>
            <SelectItem value="mod">MOD</SelectItem>
            <SelectItem value="admin">ADMIN</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Upvotes & Comments */}
      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="upvotes">Upvotes</Label>
          <Input
            type="number"
            id="upvotes"
            value={upvotes}
            onChange={(e) => setUpvotes(parseInt(e.target.value) || 0)}
            min={0}
          />
        </div>
        <div>
          <Label htmlFor="comments">Comments</Label>
          <Input
            type="number"
            id="comments"
            value={comments}
            onChange={(e) => setComments(parseInt(e.target.value) || 0)}
            min={0}
          />
        </div>
      </div>
    </>
  );
}

export default RedditBasicInputs;