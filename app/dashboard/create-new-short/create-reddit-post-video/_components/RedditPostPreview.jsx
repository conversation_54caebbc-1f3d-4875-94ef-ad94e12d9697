'use client';

import React from 'react';
import { Player } from '@remotion/player';
import RedditPostComposition from '@/remotion/compositions/RedditPostComposition';
import { VideoPreviewPanel } from '@/components/shared/VideoPreviewPanel';

function RedditPostPreview({
  title,
  subreddit,
  username,
  postContent,
  upvotes,
  comments,
  badge,
  darkMode,
  wideLayout,
  approximateCounts,
  hideTrophies,
  hideUpvotes,
  hideComments,
  hideShare,
  avatar,
  redditPostX,
  redditPostY,
  redditPostScale,
  backgroundVideoUrl,
  // Generation props
  onGenerateVideo,
  isGenerating,
  generationMessage,
  isGenerateButtonDisabled,
  userDetail,
  VIDEO_GENERATION_COST
}) {
  // Define video dimensions and duration
  const width = 1080;
  const height = 1920;
  const durationInFrames = 30 * 30;

  // Configuration for VideoPreviewPanel
  const previewConfig = {
    showRemotionPlayer: true,
    showConfigSummary: true,
    showGenerationStatus: true,
    title: "Preview & Generate",
    description: "Review your Reddit post video",
    icon: () => <div className="h-6 w-6 text-white flex items-center justify-center text-lg">👁️</div>
  };

  // Remotion player props
  const remotionProps = {
    component: RedditPostComposition,
    durationInFrames,
    fps: 30,
    compositionWidth: width,
    compositionHeight: height,
    inputProps: {
      title,
      subreddit,
      username,
      postContent,
      upvotes,
      comments,
      avatar,
      badge,
      darkMode,
      wideLayout,
      approximateCounts,
      hideTrophies,
      hideUpvotes,
      hideComments,
      hideShare,
      redditPostX,
      redditPostY,
      redditPostScale,
      backgroundVideoUrl,
    },
    loop: true,
    controls: true,
    style: { width: '100%', height: '100%', borderRadius: '8px' }
  };

  // Configuration summary items
  const configSummary = [
    { isComplete: Boolean(subreddit), label: `Subreddit: r/${subreddit || 'Not set'}`, icon: () => null, color: 'text-foreground' },
    { isComplete: Boolean(username), label: `Username: ${username || 'Not set'}`, icon: () => null, color: 'text-foreground' },
    { isComplete: Boolean(redditPostX && redditPostY), label: `Position: ${redditPostX}, ${redditPostY}`, icon: () => null, color: 'text-foreground' },
    { isComplete: Boolean(redditPostScale), label: `Scale: ${redditPostScale}x`, icon: () => null, color: 'text-foreground' }
  ];

  return (
    <VideoPreviewPanel
      videoType="reddit-post"
      previewConfig={previewConfig}
      formData={{ title, subreddit, username, postContent }}
      onGenerate={onGenerateVideo}
      isGenerating={isGenerating}
      generationMessage={generationMessage}
      isGenerateButtonDisabled={isGenerateButtonDisabled}
      userDetail={userDetail}
      VIDEO_GENERATION_COST={VIDEO_GENERATION_COST}
      remotionProps={remotionProps}
      configSummary={configSummary}
    />
  );
}

export default RedditPostPreview;