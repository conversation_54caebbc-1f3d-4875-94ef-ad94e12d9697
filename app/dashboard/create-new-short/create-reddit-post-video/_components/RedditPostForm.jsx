'use client';

import React, { useState } from 'react';
import RedditAvatarUpload from './RedditAvatarUpload';
import RedditBasicInputs from './RedditBasicInputs';
import RedditAdvancedOptions from './RedditAdvancedOptions';

function RedditPostForm({
  title,
  setTitle,
  subreddit,
  setSubreddit,
  username,
  setUsername,
  postContent,
  setPostContent,
  upvotes,
  setUpvotes,
  comments,
  setComments,
  avatar,
  handleAvatarUpload,
  badge,
  setBadge,
  darkMode,
  setDarkMode,
  wideLayout,
  setWideLayout,
  approximateCounts,
  setApproximateCounts,
  hideTrophies,
  setHideTrophies,
  hideUpvotes,
  setHideUpvotes,
  hideComments,
  setHideComments,
  hideShare,
  setHideShare,
  showAdvancedOptions,
  setShowAdvancedOptions,
}) {
  return (
    <div className="space-y-6">
      <form className="space-y-6">
        <RedditAvatarUpload avatar={avatar} handleAvatarUpload={handleAvatarUpload} />

        <RedditBasicInputs
          title={title}
          setTitle={setTitle}
          subreddit={subreddit}
          setSubreddit={setSubreddit}
          username={username}
          setUsername={setUsername}
          postContent={postContent}
          setPostContent={setPostContent}
          upvotes={upvotes}
          setUpvotes={setUpvotes}
          comments={comments}
          setComments={setComments}
          badge={badge}
          setBadge={setBadge}
        />

        {/* Advanced Options Toggle */}
        <div className="pt-4 border-t border-border/30">
          <button
            type="button"
            onClick={() => setShowAdvancedOptions(!showAdvancedOptions)}
            className="text-body-small font-medium text-primary hover:text-primary/80 transition-colors"
          >
            {showAdvancedOptions ? 'Hide Advanced Options' : 'Show Advanced Options'}
          </button>
        </div>

        {showAdvancedOptions && (
          <div className="pt-4">
            <RedditAdvancedOptions
              darkMode={darkMode}
              setDarkMode={setDarkMode}
              wideLayout={wideLayout}
              setWideLayout={setWideLayout}
              approximateCounts={approximateCounts}
              setApproximateCounts={setApproximateCounts}
              hideTrophies={hideTrophies}
              setHideTrophies={setHideTrophies}
              hideUpvotes={hideUpvotes}
              setHideUpvotes={setHideUpvotes}
              hideComments={hideComments}
              setHideComments={setHideComments}
              hideShare={hideShare}
              setHideShare={setHideShare}
            />
          </div>
        )}

      </form>
    </div>
  );
}

export default RedditPostForm;