'use client';

import React from 'react';
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";

function RedditAdvancedOptions({
  darkMode,
  setDarkMode,
  wideLayout,
  setWideLayout,
  approximateCounts,
  setApproximateCounts,
  hideTrophies,
  setHideTrophies,
  hideUpvotes,
  setHideUpvotes,
  hideComments,
  setHideComments,
  hideShare,
  setHideShare,
}) {
  return (
    <div className="space-y-4 border p-6 rounded-md bg-gray-800 text-gray-100 border-gray-700">
      <h3 className="font-medium text-white">Display Options</h3>

      <div className="flex items-center justify-between space-x-2 py-1">
        <Label htmlFor="darkMode">Dark mode</Label>
        <Switch
          id="darkMode"
          checked={darkMode}
          onCheckedChange={setDarkMode}
        />
      </div>

      <div className="flex items-center justify-between space-x-2 py-1">
        <Label htmlFor="wideLayout">Wide layout</Label>
        <Switch
          id="wideLayout"
          checked={wideLayout}
          onCheckedChange={setWideLayout}
        />
      </div>

      <div className="flex items-center justify-between space-x-2 py-1">
        <Label htmlFor="approximateCounts">Approximate counts</Label>
        <Switch
          id="approximateCounts"
          checked={approximateCounts}
          onCheckedChange={setApproximateCounts}
        />
      </div>

      <div className="flex items-center justify-between space-x-2 py-1">
        <Label htmlFor="hideTrophies">Hide trophies</Label>
        <Switch
          id="hideTrophies"
          checked={hideTrophies}
          onCheckedChange={setHideTrophies}
        />
      </div>

      <div className="flex items-center justify-between space-x-2 py-1">
        <Label htmlFor="hideUpvotes">Hide upvotes</Label>
        <Switch
          id="hideUpvotes"
          checked={hideUpvotes}
          onCheckedChange={setHideUpvotes}
        />
      </div>

      <div className="flex items-center justify-between space-x-2 py-1">
        <Label htmlFor="hideComments">Hide comments</Label>
        <Switch
          id="hideComments"
          checked={hideComments}
          onCheckedChange={setHideComments}
        />
      </div>

      <div className="flex items-center justify-between space-x-2 py-1">
        <Label htmlFor="hideShare">Hide share</Label>
        <Switch
          id="hideShare"
          checked={hideShare}
          onCheckedChange={setHideShare}
        />
      </div>
    </div>
  );
}

export default RedditAdvancedOptions;