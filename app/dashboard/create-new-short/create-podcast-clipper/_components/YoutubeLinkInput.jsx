import React from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

const YoutubeLinkInput = ({ value, onChange, disabled }) => {
  return (
    <div className="space-y-3">
      <Label htmlFor="youtube-link" className="text-body-small font-medium">YouTube URL</Label>
      <Input
        id="youtube-link"
        type="text"
        placeholder="https://www.youtube.com/watch?v=..."
        value={value}
        onChange={onChange}
        className="h-10"
        disabled={disabled}
      />
      <p className="text-body-small text-muted-foreground">
        Paste the YouTube URL of the podcast or video you want to clip.
      </p>
    </div>
  );
};

export default YoutubeLinkInput;