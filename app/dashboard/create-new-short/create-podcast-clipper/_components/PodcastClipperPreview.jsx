import React from 'react';
import { VideoPreviewPanel } from '@/components/shared/VideoPreviewPanel';
import ClippingResultsDisplay from './ClippingResultsDisplay';

function PodcastClipperPreview({
  projectTitle,
  youtubeLink,
  uploadedFileDetails,
  userPrompt,
  numberOfClips,
  clipDuration,
  audioQuality,
  includeTranscription,
  onClipPodcast,
  isClipping,
  isFileUploading,
  generationMessage,
  isGenerateButtonDisabled,
  userDetail,
  VIDEO_GENERATION_COST,
  error,
  results
}) {
  // Configuration for VideoPreviewPanel
  const previewConfig = {
    showRemotionPlayer: false,
    showConfigSummary: true,
    showGenerationStatus: true,
    title: "Preview & Generate",
    description: "Review your podcast clipping configuration",
    icon: () => <div className="h-6 w-6 text-white flex items-center justify-center text-lg">👁️</div>
  };

  // Configuration summary items
  const configSummary = [
    { isComplete: Boolean(projectTitle), label: `Project: ${projectTitle || 'Untitled'}`, icon: () => null, color: 'text-foreground' },
    { isComplete: Boolean(youtubeLink || uploadedFileDetails), label: `Source: ${youtubeLink ? 'YouTube' : uploadedFileDetails ? 'Uploaded File' : 'Not selected'}`, icon: () => null, color: 'text-foreground' },
    { isComplete: Boolean(numberOfClips), label: `Number of Clips: ${numberOfClips}`, icon: () => null, color: 'text-foreground' },
    { isComplete: Boolean(clipDuration), label: `Max Duration: ${clipDuration}s`, icon: () => null, color: 'text-foreground' },
    { isComplete: Boolean(audioQuality), label: `Audio Quality: ${audioQuality}`, icon: () => null, color: 'text-foreground' },
    { isComplete: Boolean(includeTranscription !== undefined), label: `Transcription: ${includeTranscription ? 'Included' : 'Not included'}`, icon: () => null, color: 'text-foreground' }
  ];

  // Custom preview content
  const customPreviewContent = (
    <>

      {/* Podcast Preview Placeholder */}
      <div className="space-y-3">
        <h4 className="text-body font-medium">Podcast Clip Preview</h4>
        <div className="bg-gradient-to-br from-muted to-muted/50 rounded-xl overflow-hidden border border-border/50">
          <div className="aspect-video flex items-center justify-center bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-900">
            <div className="text-center p-6">
              <div className="text-4xl mb-3">🎙️</div>
              <h5 className="text-body font-medium mb-2">Podcast Clipper</h5>
              <p className="text-body-small text-muted-foreground">
                {youtubeLink || uploadedFileDetails ? 'AI will analyze and extract the best clips' : 'Choose a source to see preview'}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Source Summary */}
      {(youtubeLink || uploadedFileDetails) && (
        <div className="space-y-3">
          <h4 className="text-body font-medium">Source Details</h4>
          <div className="p-3 bg-muted/20 rounded-lg border border-border/20">
            {youtubeLink && (
              <p className="text-body-small text-muted-foreground">
                <span className="font-medium">YouTube URL:</span> {youtubeLink}
              </p>
            )}
            {uploadedFileDetails && (
              <p className="text-body-small text-muted-foreground">
                <span className="font-medium">File:</span> {uploadedFileDetails.fileName}
              </p>
            )}
          </div>
        </div>
      )}

      {/* User Prompt Summary */}
      {userPrompt && (
        <div className="space-y-3">
          <h4 className="text-body font-medium">Clipping Instructions</h4>
          <div className="p-3 bg-muted/20 rounded-lg border border-border/20">
            <p className="text-body-small text-muted-foreground line-clamp-3">
              {userPrompt}
            </p>
          </div>
        </div>
      )}

      {/* AI Process Info */}
      <div className="space-y-3">
        <h4 className="text-body font-medium">AI Clipping Process</h4>
        <div className="p-3 bg-blue-50/50 dark:bg-blue-950/20 rounded-lg border border-blue-200/30 dark:border-blue-800/30">
          <div className="space-y-2 text-body-small text-blue-800 dark:text-blue-200">
            <div className="flex items-center gap-2">
              <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
              <span>Analyze audio content and speech patterns</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
              <span>Identify engaging moments and highlights</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
              <span>Extract clips based on your criteria</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
              <span>Generate transcriptions and timestamps</span>
            </div>
          </div>
        </div>
      </div>

      {/* Results Display */}
      {results && (
        <div className="pt-4 border-t border-border/30">
          <ClippingResultsDisplay results={results} />
        </div>
      )}
    </>
  );

  // Handle special loading states for podcast clipper
  const isGenerating = isClipping || isFileUploading;
  const currentGenerationMessage = isFileUploading ? 'Uploading File...' : (generationMessage || 'Processing...');

  return (
    <VideoPreviewPanel
      videoType="podcast-clipper"
      previewConfig={previewConfig}
      formData={{ projectTitle, youtubeLink, uploadedFileDetails, userPrompt }}
      onGenerate={onClipPodcast}
      isGenerating={isGenerating}
      generationMessage={currentGenerationMessage}
      isGenerateButtonDisabled={isGenerateButtonDisabled}
      userDetail={userDetail}
      VIDEO_GENERATION_COST={VIDEO_GENERATION_COST}
      configSummary={configSummary}
      error={error}
      results={results}
    >
      {customPreviewContent}
    </VideoPreviewPanel>
  );
}

export default PodcastClipperPreview;
