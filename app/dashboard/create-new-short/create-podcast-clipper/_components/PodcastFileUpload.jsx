'use client';

import React, { useState, useCallback } from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { UploadCloud } from 'lucide-react';
import { toast } from 'sonner';

import { generateUploadUrl } from '@/actions/s3';

const PodcastFileUpload = ({ onFileSelect, disabled, onLoadingChange }) => {
  const [uploadedFile, setUploadedFile] = useState(null);
  const [isDragging, setIsDragging] = useState(false);
  const [isUploading, setIsUploading] = useState(false);

  const processAndUploadFile = async (file) => {
    if (!file) {
      return;
    }

    setIsUploading(true);
    if (onLoadingChange) {
      onLoadingChange(true);
    }
    setUploadedFile(file);

    try {
      const fileInfo = { filename: file.name, contentType: file.type };
      const uploadUrlResult = await generateUploadUrl(fileInfo);

      if (!uploadUrlResult.success || !uploadUrlResult.data?.signedUrl || !uploadUrlResult.data?.key || !uploadUrlResult.data?.uploadedFileId) {
        const errorMsg = uploadUrlResult.error || "Failed to get upload URL or missing data.";
        toast.error(`Upload failed: ${errorMsg}`);
        setUploadedFile(null);
        return;
      }

      const { signedUrl, key, uploadedFileId } = uploadUrlResult.data;

      const uploadResponse = await fetch(signedUrl, {
        method: 'PUT',
        body: file,
        headers: {
          'Content-Type': file.type,
        },
      });

      if (!uploadResponse.ok) {
        const errorMsg = `S3 upload failed with status: ${uploadResponse.status}`;
        toast.error(`Upload failed: ${errorMsg}`);
        setUploadedFile(null);
        return;
      }

      toast.success("File uploaded successfully!");

      onFileSelect({ s3Key: key, uploadedFileId: uploadedFileId, fileName: file.name });

    } catch (error) {
      toast.error(`Upload error: ${error.message || 'An unexpected error occurred.'}`);
      setUploadedFile(null);
    } finally {
      setIsUploading(false);
       if (onLoadingChange) {
        onLoadingChange(false);
      }
    }
  };

  const handleFileChange = (event) => {
    const file = event.target.files[0];
    if (file) {
      processAndUploadFile(file);
    }
  };

  const handleDragOver = useCallback((event) => {
    event.preventDefault();
    event.stopPropagation();
    if (!disabled && !isUploading) {
      setIsDragging(true);
    }
  }, [disabled, isUploading]);

  const handleDragLeave = useCallback((event) => {
    event.preventDefault();
    event.stopPropagation();
    if (!disabled && !isUploading) {
      setIsDragging(false);
    }
  }, [disabled, isUploading]);

  const handleDrop = useCallback((event) => {
    event.preventDefault();
    event.stopPropagation();
    setIsDragging(false);

    if (disabled || isUploading) {
      return;
    }

    const files = event.dataTransfer.files;
    if (files && files.length > 0) {
      const file = files[0];
      processAndUploadFile(file);
    }
  }, [disabled, isUploading, processAndUploadFile]);

  const handleContainerClick = () => {
    if (!disabled && !isUploading) {
      document.getElementById('hidden-file-input').click();
    }
  };

  const dropZoneText = isUploading ? (
    <>Uploading <span className="font-medium">{uploadedFile?.name || 'file'}</span>...</>
  ) : uploadedFile ? (
    <>Selected file: <span className="font-medium">{uploadedFile.name}</span></>
  ) : (
    <>Drag and drop a file here, or <span className="text-blue-600 dark:text-blue-400 font-medium">click to browse</span></>
  );

  return (
    <div
      className={`w-full border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors ${
        isDragging ? 'border-primary bg-primary/5' : 'border-border bg-muted/20'
      } ${disabled || isUploading ? 'opacity-50 cursor-not-allowed' : 'hover:border-primary/50 hover:bg-muted/30'}`}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
      onClick={handleContainerClick}
    >
      <input
        id="hidden-file-input"
        type="file"
        className="hidden"
        onChange={handleFileChange}
        disabled={disabled || isUploading}
      />
      <div className="flex flex-col items-center justify-center space-y-2">
        <UploadCloud size={36} className="text-muted-foreground" />
        <p className="text-body-small text-foreground">
          {dropZoneText}
        </p>
        {!uploadedFile && !isUploading && <p className="text-body-small text-muted-foreground">(Max file size: 100MB)</p>}
         {isUploading && (
            <p className="text-body-small text-muted-foreground">Please wait...</p>
         )}
      </div>
    </div>
  );
};

export default PodcastFileUpload;