import React from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

const NumberOfClipsInput = ({ value, onChange, disabled }) => {
  return (
    <div className="space-y-3">
      <Label htmlFor="number-of-clips" className="text-body font-medium">Number of Clips to Extract</Label>
      <Input
        id="number-of-clips"
        type="number"
        placeholder="e.g., 3"
        value={value}
        onChange={(e) => onChange(parseInt(e.target.value, 10) || 1)}
        className="h-10"
        min="1"
        max="10"
        disabled={disabled}
      />
      <p className="text-body-small text-muted-foreground">
        Choose how many clips you want to extract from your podcast (1-10 clips).
      </p>
    </div>
  );
};

export default NumberOfClipsInput;