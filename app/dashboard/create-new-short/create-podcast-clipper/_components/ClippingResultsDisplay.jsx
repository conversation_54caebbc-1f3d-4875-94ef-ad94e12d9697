import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';

const ClippingResultsDisplay = ({ results }) => {
  if (!results) {
    return null; // Don't render if there are no results
  }

  // Assuming results is an object, you might want to adjust this based on the actual API response structure
  // For now, let's display some basic info if available, or just the raw JSON
  const hasClips = results.clips && Array.isArray(results.clips) && results.clips.length > 0;

  return (
    <Card className="w-full mt-4">
      <CardHeader>
        <CardTitle>Clipping Results</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {hasClips ? (
          <div className="space-y-4">
            {results.clips.map((clip, index) => (
              <div key={index} className="border-b pb-4 last:border-b-0 last:pb-0">
                <h4 className="text-md font-semibold mb-2">Clip {index + 1}</h4>
                {/* Assuming clip object has properties like url, start_time, end_time */}
                {clip.url && (
                  <p className="text-sm text-gray-700 dark:text-gray-300">
                    URL: <a href={clip.url} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">{clip.url}</a>
                  </p>
                )}
                {(clip.start_time !== undefined || clip.end_time !== undefined) && (
                   <p className="text-sm text-gray-700 dark:text-gray-300">
                     Time: {clip.start_time !== undefined ? `Start: ${clip.start_time}s` : ''}{clip.start_time !== undefined && clip.end_time !== undefined ? ', ' : ''}{clip.end_time !== undefined ? `End: ${clip.end_time}s` : ''}
                   </p>
                )}
                {/* Display other relevant clip details here */}
                {/* Example: clip.summary, clip.title */}
                {clip.summary && (
                   <p className="text-sm text-gray-700 dark:text-gray-300">Summary: {clip.summary}</p>
                )}
                 {clip.title && (
                   <p className="text-sm text-gray-700 dark:text-gray-300">Title: {clip.title}</p>
                )}
              </div>
            ))}
          </div>
        ) : (
          <div className="text-sm text-gray-700 dark:text-gray-300">
            <h4 className="text-md font-semibold mb-2">Raw Results:</h4>
            <pre className="overflow-auto">{JSON.stringify(results, null, 2)}</pre>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default ClippingResultsDisplay;