import React from 'react';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';

const UserPromptInput = ({ value, onChange, disabled }) => {
  return (
    <div className="space-y-3">
      <Label htmlFor="user-prompt" className="text-body font-medium">Clipping Instructions</Label>
      <Textarea
        id="user-prompt"
        placeholder="Describe what type of clips you want to extract. For example: 'Find the most engaging moments with high energy', 'Extract key insights and actionable advice', or 'Clip funny moments and interesting stories'."
        value={value}
        onChange={onChange}
        rows={4}
        className="min-h-[100px] text-body resize-none"
        disabled={disabled}
      />
      <p className="text-body-small text-muted-foreground">
        Be specific about the type of content you want to extract. This helps our AI identify the most relevant clips.
      </p>
    </div>
  );
};

export default UserPromptInput;