/**
 * VideoGenerationSection Component
 * Handles form submission and video generation preview
 */

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2, Video, Sparkles, CheckCircle, AlertCircle, Info } from "lucide-react";
import { cn } from "@/lib/utils";

/**
 * Form Summary Component
 * Shows a summary of the current form state
 * @param {Object} props - Component props
 * @param {Object} props.formData - Current form data
 * @param {string} props.scriptMode - Current script mode
 * @param {Object} props.selectedCreator - Selected creator object
 * @param {Array} props.uploadedFiles - Uploaded files
 * @returns {JSX.Element} Form summary
 */
const FormSummary = ({ formData, scriptMode, selectedCreator, uploadedFiles }) => (
    <div className="bg-muted/30 rounded-lg p-4 space-y-3">
        <h5 className="text-sm font-medium flex items-center gap-2">
            <Info className="h-4 w-4 text-blue-500" />
            Video Configuration Summary
        </h5>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-xs">
            <div className="space-y-2">
                <div className="flex justify-between">
                    <span className="text-muted-foreground">Project Title:</span>
                    <span className="font-medium">
                        {formData.projectTitle || 'Untitled Project'}
                    </span>
                </div>
                
                <div className="flex justify-between">
                    <span className="text-muted-foreground">Script Mode:</span>
                    <span className="font-medium capitalize">
                        {scriptMode === 'topic' ? 'AI Generated' : 'Custom Script'}
                    </span>
                </div>
                
                <div className="flex justify-between">
                    <span className="text-muted-foreground">Script Length:</span>
                    <span className="font-medium">
                        {scriptMode === 'topic' 
                            ? `${formData.scriptTopic.length} chars`
                            : `${formData.userScript.length} chars`
                        }
                    </span>
                </div>
            </div>
            
            <div className="space-y-2">
                <div className="flex justify-between">
                    <span className="text-muted-foreground">AI Creator:</span>
                    <span className="font-medium">
                        {selectedCreator ? selectedCreator.name : 'Not selected'}
                    </span>
                </div>
                
                <div className="flex justify-between">
                    <span className="text-muted-foreground">Background Assets:</span>
                    <span className="font-medium">
                        {uploadedFiles.length} file(s)
                    </span>
                </div>
                
                <div className="flex justify-between">
                    <span className="text-muted-foreground">Aspect Ratio:</span>
                    <span className="font-medium">
                        {formData.aspectRatio}
                    </span>
                </div>
            </div>
        </div>
    </div>
);

/**
 * Validation Status Component
 * Shows current form validation status
 * @param {Object} props - Component props
 * @param {boolean} props.isFormValid - Whether form is valid
 * @param {Object} props.errors - Validation errors
 * @param {string} props.scriptMode - Current script mode
 * @param {Object} props.formData - Current form data
 * @returns {JSX.Element} Validation status
 */
const ValidationStatus = ({ isFormValid, errors, scriptMode, formData }) => {
    const requirements = [
        {
            key: 'script',
            label: scriptMode === 'topic' ? 'Video topic provided' : 'Custom script written',
            isValid: scriptMode === 'topic' 
                ? formData.scriptTopic.trim().length > 0
                : formData.userScript.trim().length > 0,
            error: errors.script
        },
        {
            key: 'creator',
            label: 'AI creator selected',
            isValid: !!formData.avatarChoice,
            error: errors.avatar
        }
    ];

    return (
        <div className="space-y-3">
            <h5 className="text-sm font-medium">Requirements Checklist</h5>
            <div className="space-y-2">
                {requirements.map((req) => (
                    <div key={req.key} className="flex items-center gap-2 text-sm">
                        {req.isValid ? (
                            <CheckCircle className="h-4 w-4 text-green-500" />
                        ) : (
                            <AlertCircle className="h-4 w-4 text-muted-foreground" />
                        )}
                        <span className={cn(
                            req.isValid ? 'text-foreground' : 'text-muted-foreground'
                        )}>
                            {req.label}
                        </span>
                        {req.error && (
                            <span className="text-xs text-red-500 ml-auto">
                                {Array.isArray(req.error) ? req.error[0] : req.error}
                            </span>
                        )}
                    </div>
                ))}
            </div>
        </div>
    );
};

/**
 * VideoGenerationSection Component
 * @param {Object} props - Component props
 * @param {boolean} props.isLoading - Whether form is being submitted
 * @param {boolean} props.isFormValid - Whether form is valid for submission
 * @param {Function} props.onSubmit - Form submission handler
 * @param {Object} props.formData - Current form data
 * @param {string} props.scriptMode - Current script mode
 * @param {Object} props.selectedCreator - Selected creator object
 * @param {Array} props.uploadedFiles - Uploaded files
 * @param {Object} props.errors - Validation errors
 * @returns {JSX.Element} VideoGenerationSection component
 */
const VideoGenerationSection = ({ 
    isLoading, 
    isFormValid, 
    onSubmit, 
    formData, 
    scriptMode, 
    selectedCreator, 
    uploadedFiles,
    errors = {}
}) => {
    const handleSubmit = (e) => {
        e.preventDefault();
        onSubmit(e);
    };

    return (
        <div className="relative sticky top-4 xl:top-20 h-fit">
            <div className="absolute inset-0 bg-gradient-to-br from-indigo-50/50 to-purple-50/50 dark:from-indigo-950/10 dark:to-purple-950/10 rounded-2xl"></div>
            <div className="relative bg-background/80 backdrop-blur-sm border border-border/50 rounded-2xl p-6 max-h-[calc(100vh-2rem)] xl:max-h-[calc(100vh-6rem)] overflow-y-auto">
                {/* Section Header */}
                <div className="flex items-center gap-3 mb-6 pb-4 border-b border-border/30">
                    <div className="p-2 bg-gradient-to-br from-indigo-500 to-purple-500 rounded-lg shadow-soft">
                        <div className="h-6 w-6 text-white flex items-center justify-center text-lg">🎬</div>
                    </div>
                    <div>
                        <h3 className="text-heading-2 text-gradient">Preview & Generate</h3>
                        <p className="text-body-small text-muted-foreground">Review your UGC video configuration</p>
                    </div>
                </div>

                    {/* Configuration Summary */}
                    <div className="space-y-3">
                        <h4 className="text-body font-medium">Configuration Summary</h4>
                        <div className="grid grid-cols-1 gap-3">
                            <div className="p-3 bg-muted/20 rounded-lg border border-border/20">
                                <div className="text-body-small text-muted-foreground space-y-1">
                                    <div className="flex justify-between">
                                        <span>Project:</span>
                                        <span className="font-medium text-foreground">{formData.projectTitle || 'Untitled'}</span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span>Script Mode:</span>
                                        <span className="font-medium text-foreground">{scriptMode === 'topic' ? 'AI Generated' : 'Custom'}</span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span>Creator:</span>
                                        <span className="font-medium text-foreground">{selectedCreator?.name || 'Not selected'}</span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span>Assets:</span>
                                        <span className="font-medium text-foreground">{uploadedFiles.length} file(s)</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Validation Status */}
                    <ValidationStatus
                        isFormValid={isFormValid}
                        errors={errors}
                        scriptMode={scriptMode}
                        formData={formData}
                    />

                    {/* Generation Status */}
                    {!isFormValid && !isLoading && (
                        <div className="p-3 bg-yellow-50/50 dark:bg-yellow-950/20 rounded-lg border border-yellow-200/30 dark:border-yellow-800/30">
                            <p className="text-body-small text-yellow-800 dark:text-yellow-200 text-center">
                                Please complete all required fields to generate your UGC video
                            </p>
                        </div>
                    )}

                    {/* Generate Video Button */}
                    <div className="pt-4 border-t border-border/30">
                        <Button
                            onClick={handleSubmit}
                            disabled={!isFormValid || isLoading}
                            size="lg"
                            className="w-full bg-gradient-to-br from-indigo-500 to-purple-500 hover:from-indigo-600 hover:to-purple-600 text-white border-0 shadow-medium interactive-scale h-12"
                        >
                            {isLoading ? (
                                <div className="flex items-center gap-2">
                                    <Loader2 className="h-5 w-5 animate-spin" />
                                    <span className="text-body">Generating UGC Video...</span>
                                </div>
                            ) : (
                                <div className="flex items-center gap-2">
                                    <Sparkles className="h-5 w-5" />
                                    <span className="text-body font-semibold">Generate UGC Video</span>
                                </div>
                            )}
                        </Button>
                    </div>

                    {/* Additional Info */}
                    <div className="text-body-small text-muted-foreground space-y-1 pt-2">
                        <p>• Video generation typically takes 2-5 minutes</p>
                        <p>• Uses Captions AI technology for authentic UGC style</p>
                        <p>• You'll be redirected to monitor progress</p>
                    </div>
            </div>
        </div>
    );
};

export default VideoGenerationSection;
