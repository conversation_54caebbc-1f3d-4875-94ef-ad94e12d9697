/**
 * BackgroundAssetsSection Component
 * Handles file upload functionality for background assets
 */

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Loader2, Upload, Image, Video, X } from "lucide-react";
import { cn } from "@/lib/utils";

/**
 * File Upload Area Component
 * @param {Object} props - Component props
 * @param {boolean} props.uploadingFiles - Whether files are being uploaded
 * @param {Function} props.onFileUpload - File upload handler
 * @returns {JSX.Element} File upload area
 */
const FileUploadArea = ({ uploadingFiles, onFileUpload }) => (
    <div className="border-2 border-dashed border-border/50 rounded-xl p-8 text-center bg-gradient-to-br from-muted/30 to-muted/10 hover:border-primary/30 transition-colors duration-200">
        {uploadingFiles ? (
            <div className="flex flex-col items-center">
                <div className="p-3 rounded-full bg-primary/10 mb-4">
                    <Loader2 className="h-8 w-8 animate-spin text-primary" />
                </div>
                <p className="text-sm font-medium">Uploading files...</p>
                <p className="text-xs text-muted-foreground mt-1">
                    Please wait while we process your files
                </p>
            </div>
        ) : (
            <>
                <div className="p-3 rounded-full bg-green-500/10 mb-4 mx-auto w-fit">
                    <Upload className="h-8 w-8 text-green-500" />
                </div>
                <h4 className="text-sm font-medium mb-2">
                    Drag and drop your background files here
                </h4>
                <p className="text-xs text-muted-foreground mb-4">
                    or click to browse your files
                </p>
                <div className="flex items-center justify-center gap-4 text-xs text-muted-foreground mb-4">
                    <div className="flex items-center gap-1">
                        <Image className="h-3 w-3" />
                        <span>JPG, PNG, GIF, WebP</span>
                    </div>
                    <div className="flex items-center gap-1">
                        <Video className="h-3 w-3" />
                        <span>MP4, MOV, AVI, WebM</span>
                    </div>
                    <span>Max 50MB each</span>
                </div>
                <input
                    type="file"
                    multiple
                    accept="image/*,video/*"
                    onChange={onFileUpload}
                    className="hidden"
                    id="file-upload"
                    disabled={uploadingFiles}
                />
                <Button
                    type="button"
                    onClick={() => document.getElementById('file-upload').click()}
                    disabled={uploadingFiles}
                    className="bg-primary hover:bg-primary/90 text-primary-foreground"
                >
                    Select Files
                </Button>
            </>
        )}
    </div>
);

/**
 * Uploaded Files Grid Component
 * @param {Object} props - Component props
 * @param {Array} props.uploadedFiles - List of uploaded files
 * @param {Function} props.onRemoveFile - File removal handler
 * @param {Function} props.getFileTypeIcon - Get file type icon
 * @returns {JSX.Element} Uploaded files grid
 */
const UploadedFilesGrid = ({ uploadedFiles, onRemoveFile, getFileTypeIcon }) => (
    <div className="space-y-3">
        <h4 className="text-sm font-medium text-foreground">
            Uploaded Files ({uploadedFiles.length})
        </h4>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {uploadedFiles.map((file, index) => {
                const IconComponent = getFileTypeIcon(file) === 'Image' ? Image : Video;
                const iconColor = getFileTypeIcon(file) === 'Image' ? 'text-blue-500' : 'text-purple-500';
                const bgColor = getFileTypeIcon(file) === 'Image' ? 'bg-blue-500/10' : 'bg-purple-500/10';
                
                return (
                    <div key={index} className="relative group">
                        <div className="aspect-video bg-gradient-to-br from-muted/50 to-muted/20 rounded-lg flex items-center justify-center border border-border/50 group-hover:border-primary/30 transition-colors duration-200">
                            <div className={cn("p-2 rounded-lg", bgColor)}>
                                <IconComponent className={cn("h-6 w-6", iconColor)} />
                            </div>
                        </div>
                        
                        {/* Remove button */}
                        <button
                            onClick={() => onRemoveFile(index)}
                            className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 hover:bg-red-600 rounded-full flex items-center justify-center text-white opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                            title="Remove file"
                        >
                            <X className="h-3 w-3" />
                        </button>
                        
                        <p className="text-xs text-center mt-2 truncate font-medium px-1" title={file.name}>
                            {file.name}
                        </p>
                        
                        {/* File size */}
                        <p className="text-[10px] text-center text-muted-foreground">
                            {(file.size / (1024 * 1024)).toFixed(1)} MB
                        </p>
                    </div>
                );
            })}
        </div>
    </div>
);

/**
 * BackgroundAssetsSection Component
 * @param {Object} props - Component props
 * @param {Array} props.uploadedFiles - List of uploaded files
 * @param {boolean} props.uploadingFiles - Whether files are being uploaded
 * @param {Function} props.onFileUpload - File upload handler
 * @param {Function} props.onRemoveFile - File removal handler
 * @param {Function} props.getFileTypeIcon - Get file type icon function
 * @returns {JSX.Element} BackgroundAssetsSection component
 */
const BackgroundAssetsSection = ({ 
    uploadedFiles, 
    uploadingFiles, 
    onFileUpload, 
    onRemoveFile,
    getFileTypeIcon
}) => {
    return (
        <div className="space-y-6">
                    {/* File Upload Area */}
                    <FileUploadArea 
                        uploadingFiles={uploadingFiles}
                        onFileUpload={onFileUpload}
                    />

                    {/* Uploaded Files Grid */}
                    {uploadedFiles.length > 0 && (
                        <UploadedFilesGrid
                            uploadedFiles={uploadedFiles}
                            onRemoveFile={onRemoveFile}
                            getFileTypeIcon={getFileTypeIcon}
                        />
                    )}
                    
                    {/* Help Text */}
                    <div className="bg-muted/30 rounded-lg p-4">
                        <h5 className="text-sm font-medium mb-2">Tips for better results:</h5>
                        <ul className="text-xs text-muted-foreground space-y-1">
                            <li>• Use high-quality images (1080p or higher recommended)</li>
                            <li>• Videos should be at least 720p resolution</li>
                            <li>• Avoid copyrighted content</li>
                            <li>• Images will be automatically cropped to fit video format</li>
                            <li>• Multiple files will be used in sequence throughout the video</li>
                        </ul>
                    </div>
                </div>
    );
};

export default BackgroundAssetsSection;
