/**
 * ScriptContentSection Component
 * Handles script input with AI generation and custom script options
 */

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Mic } from "lucide-react";
import { cn } from "@/lib/utils";
import { 
    validateScriptTopic, 
    validateUserScript, 
    getCharacterCountStatus 
} from '../utils/formValidation';

/**
 * ScriptContentSection Component
 * @param {Object} props - Component props
 * @param {Object} props.formData - Current form data
 * @param {string} props.scriptMode - Current script mode ('topic' or 'custom')
 * @param {Function} props.onInputChange - Handler for input changes
 * @param {Function} props.onScriptModeChange - Handler for script mode changes
 * @param {Object} props.errors - Validation errors
 * @returns {JSX.Element} ScriptContentSection component
 */
const ScriptContentSection = ({ 
    formData, 
    scriptMode, 
    onInputChange, 
    onScriptModeChange, 
    errors = {} 
}) => {
    // Validation for current script mode
    const topicValidation = validateScriptTopic(formData.scriptTopic);
    const scriptValidation = validateUserScript(formData.userScript);
    
    // Character count status
    const topicCharacterStatus = getCharacterCountStatus(formData.scriptTopic, 1000);
    const scriptCharacterStatus = getCharacterCountStatus(formData.userScript, 5000);

    const handleScriptTopicChange = (e) => {
        onInputChange('scriptTopic', e.target.value);
    };

    const handleUserScriptChange = (e) => {
        onInputChange('userScript', e.target.value);
    };

    return (
        <div className="space-y-6">
            <Tabs value={scriptMode} onValueChange={onScriptModeChange}>
                <TabsList className="grid w-full grid-cols-2">
                    <TabsTrigger value="topic">AI Generated</TabsTrigger>
                    <TabsTrigger value="custom">Write Your Own</TabsTrigger>
                </TabsList>

                    {/* AI Generated Script Tab */}
                    <TabsContent value="topic" className="space-y-4 mt-6">
                        <div className="space-y-3">
                            <Label
                                htmlFor="scriptTopic"
                                className="text-body font-medium"
                            >
                                Video Topic
                            </Label>

                            <div className="relative">
                                <Textarea
                                    id="scriptTopic"
                                    placeholder="e.g., Benefits of daily meditation, How to start a business, Top 10 productivity tips..."
                                    value={formData.scriptTopic}
                                    onChange={handleScriptTopicChange}
                                    rows={4}
                                    className={cn(
                                        "min-h-[100px] text-body resize-none pr-16",
                                        errors.script && scriptMode === 'topic' ? 'border-red-500 focus:border-red-500' :
                                        topicCharacterStatus.isOverLimit ? 'border-red-500' :
                                        topicCharacterStatus.isNearLimit ? 'border-orange-500' : ''
                                    )}
                                    maxLength={1000}
                                />
                                <div className="absolute bottom-3 right-3 text-caption text-muted-foreground bg-background px-2 rounded">
                                    {topicCharacterStatus.current}/1000
                                </div>
                            </div>
                            
                            {/* Error Messages */}
                            {errors.script && scriptMode === 'topic' && (
                                <div className="text-sm text-red-500">
                                    {Array.isArray(errors.script) 
                                        ? errors.script.join(', ')
                                        : errors.script
                                    }
                                </div>
                            )}
                            
                            {/* Validation Errors */}
                            {scriptMode === 'topic' && !topicValidation.isValid && (
                                <div className="text-sm text-red-500">
                                    {topicValidation.errors.join(', ')}
                                </div>
                            )}

                            <p className="text-body-small text-muted-foreground">
                                Enter the main topic or idea for your UGC video. Be specific for better results.
                            </p>
                        </div>
                    </TabsContent>

                    {/* Custom Script Tab */}
                    <TabsContent value="custom" className="space-y-4 mt-6">
                        <div className="space-y-3">
                            <Label
                                htmlFor="userScript"
                                className="text-body font-medium"
                            >
                                Your Script
                            </Label>

                            <div className="relative">
                                <Textarea
                                    id="userScript"
                                    placeholder="Write your own script here. Keep it engaging and conversational for best results..."
                                    value={formData.userScript}
                                    onChange={handleUserScriptChange}
                                    rows={8}
                                    className={cn(
                                        "min-h-[160px] text-body resize-none pr-16",
                                        errors.script && scriptMode === 'custom' ? 'border-red-500 focus:border-red-500' :
                                        scriptCharacterStatus.isOverLimit ? 'border-red-500' :
                                        scriptCharacterStatus.isNearLimit ? 'border-orange-500' : ''
                                    )}
                                    maxLength={5000}
                                />
                                <div className="absolute bottom-3 right-3 text-caption text-muted-foreground bg-background px-2 rounded">
                                    {scriptCharacterStatus.current}/5000
                                </div>
                            </div>
                            
                            {/* Error Messages */}
                            {errors.script && scriptMode === 'custom' && (
                                <div className="text-sm text-red-500">
                                    {Array.isArray(errors.script) 
                                        ? errors.script.join(', ')
                                        : errors.script
                                    }
                                </div>
                            )}
                            
                            {/* Validation Errors */}
                            {scriptMode === 'custom' && !scriptValidation.isValid && (
                                <div className="text-sm text-red-500">
                                    {scriptValidation.errors.join(', ')}
                                </div>
                            )}

                            <p className="text-body-small text-muted-foreground">
                                Review and edit the script as needed. Keep it engaging and conversational for best UGC results.
                            </p>
                        </div>
                    </TabsContent>
                </Tabs>
            </div>
    );
};

export default ScriptContentSection;
