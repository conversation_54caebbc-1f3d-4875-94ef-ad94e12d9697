"use client";

import React, { useState, useRef, useEffect, useCallback, useMemo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { User, Check, ChevronDown, ChevronUp, Play, X, Maximize2, Search, ArrowUp, ArrowDown } from "lucide-react";
import { cn } from "@/lib/utils";
import { motion, AnimatePresence } from "framer-motion";

// Custom hook for intersection observer (lazy loading)
const useIntersectionObserver = (options = {}) => {
    const [isIntersecting, setIsIntersecting] = useState(false);
    const [hasIntersected, setHasIntersected] = useState(false);
    const ref = useRef(null);

    useEffect(() => {
        const observer = new IntersectionObserver(([entry]) => {
            setIsIntersecting(entry.isIntersecting);
            if (entry.isIntersecting && !hasIntersected) {
                setHasIntersected(true);
            }
        }, {
            threshold: 0.1,
            rootMargin: '50px',
            ...options
        });

        if (ref.current) {
            observer.observe(ref.current);
        }

        return () => observer.disconnect();
    }, [hasIntersected, options]);

    return [ref, isIntersecting, hasIntersected];
};

// Video Preview Modal Component
const VideoPreviewModal = ({ isOpen, onClose, creator }) => {
    const videoRef = useRef(null);

    useEffect(() => {
        if (isOpen && videoRef.current) {
            videoRef.current.play();
        }
    }, [isOpen]);

    if (!isOpen || !creator) return null;

    return (
        <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-50 flex items-center justify-center bg-black/80 backdrop-blur-sm"
            onClick={onClose}
        >
            <motion.div
                initial={{ scale: 0.8, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                exit={{ scale: 0.8, opacity: 0 }}
                className="relative max-w-2xl w-full mx-4 bg-background rounded-2xl overflow-hidden shadow-2xl"
                onClick={(e) => e.stopPropagation()}
            >
                <div className="relative aspect-video">
                    {creator.previewVideo ? (
                        <video
                            ref={videoRef}
                            src={creator.previewVideo}
                            className="w-full h-full object-cover"
                            autoPlay
                            loop
                            muted
                            playsInline
                        />
                    ) : (
                        <div className="w-full h-full bg-gradient-to-br from-primary/20 to-primary/10 flex items-center justify-center">
                            <User className="h-16 w-16 text-primary" />
                        </div>
                    )}
                    <button
                        onClick={onClose}
                        className="absolute top-4 right-4 p-2 bg-black/50 hover:bg-black/70 rounded-full text-white transition-colors"
                    >
                        <X className="h-4 w-4" />
                    </button>
                </div>
                <div className="p-6">
                    <h3 className="text-xl font-semibold mb-2">{creator.name}</h3>
                    <p className="text-muted-foreground capitalize">
                        {creator.category} • AI Creator
                    </p>
                </div>
            </motion.div>
        </motion.div>
    );
};

// Creator Card Component with Video Preview and Lazy Loading
const CreatorCard = ({ creator, isSelected, onSelect, onPreview }) => {
    const [isHovered, setIsHovered] = useState(false);
    const [videoLoaded, setVideoLoaded] = useState(false);
    const [imageLoaded, setImageLoaded] = useState(false);
    const videoRef = useRef(null);
    const [cardRef, isIntersecting, hasIntersected] = useIntersectionObserver();

    useEffect(() => {
        if (isHovered && videoRef.current && creator.previewVideo && hasIntersected) {
            videoRef.current.play().catch(() => {
                // Video play failed, ignore
            });
        } else if (videoRef.current) {
            videoRef.current.pause();
        }
    }, [isHovered, creator.previewVideo, hasIntersected]);

    return (
        <motion.div
            ref={cardRef}
            layout
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className={cn(
                "relative group cursor-pointer transition-all duration-200",
                "border rounded-lg overflow-hidden", // Reduced border radius for more compact look
                "bg-gradient-to-br from-background to-background/50 backdrop-blur-sm",
                isSelected
                    ? "border-primary bg-primary/5 shadow-lg ring-1 ring-primary/20"
                    : "border-border/50 hover:border-primary/30 hover:shadow-md"
            )}
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
            onClick={onSelect}
        >
            {/* Video/Image Preview */}
            <div className="relative aspect-square overflow-hidden">
                {hasIntersected ? (
                    <>
                        {creator.previewVideo && (
                            <video
                                ref={videoRef}
                                src={creator.previewVideo}
                                className={cn(
                                    "w-full h-full object-cover transition-opacity duration-300",
                                    isHovered && videoLoaded ? "opacity-100" : "opacity-0"
                                )}
                                muted
                                loop
                                playsInline
                                preload="metadata"
                                onLoadedData={() => setVideoLoaded(true)}
                            />
                        )}
                        {creator.thumbnail && (
                            <img
                                src={creator.thumbnail}
                                alt={creator.name}
                                className={cn(
                                    "absolute inset-0 w-full h-full object-cover transition-opacity duration-300",
                                    isHovered && videoLoaded && creator.previewVideo ? "opacity-0" : "opacity-100"
                                )}
                                loading="lazy"
                                onLoad={() => setImageLoaded(true)}
                                onError={(e) => {
                                    e.target.style.display = 'none';
                                }}
                            />
                        )}
                    </>
                ) : (
                    // Placeholder while not in view
                    <div className="w-full h-full bg-muted animate-pulse" />
                )}

                {/* Fallback when no image/video */}
                {hasIntersected && !creator.thumbnail && !creator.previewVideo && (
                    <div className="absolute inset-0 bg-gradient-to-br from-primary/20 to-primary/10 flex items-center justify-center">
                        <User className="h-6 w-6 text-primary" />
                    </div>
                )}

                {/* Play icon overlay for video preview */}
                {creator.previewVideo && !isHovered && hasIntersected && (
                    <div className="absolute inset-0 bg-black/20 flex items-center justify-center">
                        <div className="p-1.5 bg-white/90 rounded-full">
                            <Play className="h-3 w-3 text-black fill-black" />
                        </div>
                    </div>
                )}

                {/* Full preview button */}
                {creator.previewVideo && hasIntersected && (
                    <button
                        onClick={(e) => {
                            e.stopPropagation();
                            onPreview(creator);
                        }}
                        className="absolute top-1 right-1 p-1 bg-black/50 hover:bg-black/70 rounded-full text-white opacity-0 group-hover:opacity-100 transition-opacity"
                    >
                        <Maximize2 className="h-2.5 w-2.5" />
                    </button>
                )}

                {/* Selection indicator */}
                {isSelected && (
                    <div className="absolute top-1 left-1 w-5 h-5 bg-primary rounded-full flex items-center justify-center shadow-lg">
                        <Check className="h-3 w-3 text-primary-foreground" />
                    </div>
                )}
            </div>

            {/* Creator Info - More compact */}
            <div className="p-2">
                <h4 className="font-medium text-xs truncate mb-0.5">{creator.name}</h4>
                <p className="text-[10px] text-muted-foreground capitalize leading-tight">
                    {creator.category}
                </p>
            </div>
        </motion.div>
    );
};

// Virtual Scrolling Component for Performance
const VirtualizedCreatorGrid = ({ creators, selectedCreatorId, onCreatorSelect, onCreatorPreview, itemsPerPage = 24 }) => {
    const [currentPage, setCurrentPage] = useState(0);
    const totalPages = Math.ceil(creators.length / itemsPerPage);

    const currentCreators = useMemo(() => {
        const start = currentPage * itemsPerPage;
        return creators.slice(start, start + itemsPerPage);
    }, [creators, currentPage, itemsPerPage]);

    return (
        <div className="space-y-3">
            <div className="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-6 lg:grid-cols-8 xl:grid-cols-10 gap-2">
                {currentCreators.map((creator) => (
                    <CreatorCard
                        key={creator.id}
                        creator={creator}
                        isSelected={selectedCreatorId === creator.id}
                        onSelect={() => onCreatorSelect(creator)}
                        onPreview={onCreatorPreview}
                    />
                ))}
            </div>

            {totalPages > 1 && (
                <div className="flex items-center justify-between text-xs text-muted-foreground">
                    <span>
                        Showing {currentPage * itemsPerPage + 1}-{Math.min((currentPage + 1) * itemsPerPage, creators.length)} of {creators.length}
                    </span>
                    <div className="flex items-center gap-2">
                        <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setCurrentPage(Math.max(0, currentPage - 1))}
                            disabled={currentPage === 0}
                            className="h-6 px-2 text-xs"
                        >
                            <ArrowUp className="h-3 w-3" />
                        </Button>
                        <span className="text-xs">
                            {currentPage + 1}/{totalPages}
                        </span>
                        <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setCurrentPage(Math.min(totalPages - 1, currentPage + 1))}
                            disabled={currentPage === totalPages - 1}
                            className="h-6 px-2 text-xs"
                        >
                            <ArrowDown className="h-3 w-3" />
                        </Button>
                    </div>
                </div>
            )}
        </div>
    );
};

// Category Section Component with Sticky Headers
const CategorySection = ({ category, creators, selectedCreatorId, onCreatorSelect, onCreatorPreview, isSticky = false }) => {
    const [isExpanded, setIsExpanded] = useState(true);

    return (
        <div className="space-y-2">
            <div
                className={cn(
                    "flex items-center justify-between py-2 bg-background/95 backdrop-blur-sm border-b border-border/50",
                    isSticky && "sticky top-0 z-10"
                )}
            >
                <button
                    onClick={() => setIsExpanded(!isExpanded)}
                    className="flex items-center gap-2 text-sm font-semibold text-foreground capitalize hover:text-primary transition-colors"
                >
                    <div className="w-1.5 h-1.5 rounded-full bg-primary"></div>
                    {category} ({creators.length})
                    {isExpanded ? (
                        <ChevronUp className="h-3 w-3" />
                    ) : (
                        <ChevronDown className="h-3 w-3" />
                    )}
                </button>
            </div>

            <AnimatePresence>
                {isExpanded && (
                    <motion.div
                        initial={{ height: 0, opacity: 0 }}
                        animate={{ height: "auto", opacity: 1 }}
                        exit={{ height: 0, opacity: 0 }}
                        className="overflow-hidden"
                    >
                        <VirtualizedCreatorGrid
                            creators={creators}
                            selectedCreatorId={selectedCreatorId}
                            onCreatorSelect={onCreatorSelect}
                            onCreatorPreview={onCreatorPreview}
                        />
                    </motion.div>
                )}
            </AnimatePresence>
        </div>
    );
};

// Scroll Indicator Component
const ScrollIndicator = ({ scrollRef }) => {
    const [scrollProgress, setScrollProgress] = useState(0);
    const [canScrollUp, setCanScrollUp] = useState(false);
    const [canScrollDown, setCanScrollDown] = useState(false);

    useEffect(() => {
        const handleScroll = () => {
            if (scrollRef.current) {
                const { scrollTop, scrollHeight, clientHeight } = scrollRef.current;
                const progress = scrollHeight > clientHeight ? (scrollTop / (scrollHeight - clientHeight)) * 100 : 0;
                setScrollProgress(progress);
                setCanScrollUp(scrollTop > 0);
                setCanScrollDown(scrollTop < scrollHeight - clientHeight - 1);
            }
        };

        const scrollElement = scrollRef.current;
        if (scrollElement) {
            scrollElement.addEventListener('scroll', handleScroll);
            handleScroll(); // Initial check
            return () => scrollElement.removeEventListener('scroll', handleScroll);
        }
    }, [scrollRef]);

    return (
        <div className="absolute right-2 top-2 bottom-2 w-1 bg-muted/30 rounded-full overflow-hidden">
            <div
                className="bg-primary rounded-full transition-all duration-200 ease-out"
                style={{
                    height: `${scrollProgress}%`,
                    transformOrigin: 'top'
                }}
            />
            {canScrollUp && (
                <div className="absolute top-1 right-1 w-3 h-3 bg-primary/20 rounded-full flex items-center justify-center">
                    <ArrowUp className="h-2 w-2 text-primary" />
                </div>
            )}
            {canScrollDown && (
                <div className="absolute bottom-1 right-1 w-3 h-3 bg-primary/20 rounded-full flex items-center justify-center">
                    <ArrowDown className="h-2 w-2 text-primary" />
                </div>
            )}
        </div>
    );
};

// Main Creator Selection Card Component
const CreatorSelectionCard = ({
    loadingAvatars,
    avatars,
    selectedCreator,
    formData,
    setSelectedCreator,
    handleInputChange
}) => {
    const [previewCreator, setPreviewCreator] = useState(null);
    const [searchTerm, setSearchTerm] = useState('');
    const scrollRef = useRef(null);

    const handleCreatorSelect = (creator) => {
        setSelectedCreator(creator);
        handleInputChange('avatarChoice', creator.id);
    };

    const handleCreatorPreview = (creator) => {
        setPreviewCreator(creator);
    };

    // Filter and group creators
    const filteredAvatars = useMemo(() => {
        return avatars.filter(creator =>
            creator.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            creator.category.toLowerCase().includes(searchTerm.toLowerCase())
        );
    }, [avatars, searchTerm]);

    const groupedCreators = useMemo(() => {
        return filteredAvatars.reduce((groups, creator) => {
            const category = creator.category;
            if (!groups[category]) groups[category] = [];
            groups[category].push(creator);
            return groups;
        }, {});
    }, [filteredAvatars]);

    return (
        <>
            <div className="space-y-4">
                {/* Search Bar */}
                <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                        placeholder="Search creators by name or category..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-10 h-12 text-body"
                    />
                </div>

                <div className="relative">
                    {/* Fixed Height Scrollable Container */}
                    <div className="relative">
                        <div
                            ref={scrollRef}
                            className="h-[450px] overflow-y-auto overflow-x-hidden scroll-smooth pr-4"
                            style={{ scrollbarWidth: 'thin' }}
                        >
                            {loadingAvatars ? (
                                <div className="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-6 lg:grid-cols-8 xl:grid-cols-10 gap-2">
                                    {[...Array(40)].map((_, i) => (
                                        <div key={i} className="space-y-1">
                                            <div className="aspect-square bg-muted rounded-lg animate-pulse"></div>
                                            <div className="space-y-0.5">
                                                <div className="h-2 bg-muted rounded w-3/4 animate-pulse"></div>
                                                <div className="h-1.5 bg-muted rounded w-1/2 animate-pulse"></div>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            ) : Object.keys(groupedCreators).length > 0 ? (
                                <div className="space-y-4">
                                    {Object.entries(groupedCreators).map(([category, creators]) => (
                                        <CategorySection
                                            key={category}
                                            category={category}
                                            creators={creators}
                                            selectedCreatorId={formData.avatarChoice}
                                            onCreatorSelect={handleCreatorSelect}
                                            onCreatorPreview={handleCreatorPreview}
                                            isSticky={true}
                                        />
                                    ))}
                                </div>
                            ) : searchTerm ? (
                                <div className="text-center py-16 text-muted-foreground">
                                    <Search className="h-12 w-12 mx-auto mb-4 opacity-50" />
                                    <p className="text-sm">No creators found matching "{searchTerm}"</p>
                                    <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => setSearchTerm('')}
                                        className="mt-2 text-xs"
                                    >
                                        Clear search
                                    </Button>
                                </div>
                            ) : (
                                <div className="text-center py-16 text-muted-foreground">
                                    <User className="h-12 w-12 mx-auto mb-4 opacity-50" />
                                    <p className="text-sm">No creators available. Please try again later.</p>
                                </div>
                            )}
                        </div>

                        {/* Scroll Indicator */}
                        {!loadingAvatars && Object.keys(groupedCreators).length > 0 && (
                            <ScrollIndicator scrollRef={scrollRef} />
                        )}
                    </div>
                </div>
            </div>

            {/* Video Preview Modal */}
            <AnimatePresence>
                {previewCreator && (
                    <VideoPreviewModal
                        isOpen={!!previewCreator}
                        onClose={() => setPreviewCreator(null)}
                        creator={previewCreator}
                    />
                )}
            </AnimatePresence>
        </>
    );
};

export default CreatorSelectionCard;
