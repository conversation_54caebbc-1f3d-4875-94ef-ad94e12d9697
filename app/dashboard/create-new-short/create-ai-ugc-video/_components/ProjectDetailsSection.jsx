/**
 * ProjectDetailsSection Component
 * Handles project title input and basic project configuration
 */

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Sparkles } from "lucide-react";
import { validateProjectTitle, getCharacterCountStatus } from '../utils/formValidation';

/**
 * ProjectDetailsSection Component
 * @param {Object} props - Component props
 * @param {Object} props.formData - Current form data
 * @param {Function} props.onInputChange - Handler for input changes
 * @param {Object} props.errors - Validation errors
 * @returns {JSX.Element} ProjectDetailsSection component
 */
const ProjectDetailsSection = ({ 
    formData, 
    onInputChange, 
    errors = {} 
}) => {
    const projectTitleValidation = validateProjectTitle(formData.projectTitle);
    const characterStatus = getCharacterCountStatus(formData.projectTitle, 100);

    const handleProjectTitleChange = (e) => {
        onInputChange('projectTitle', e.target.value);
    };

    return (
        <div className="space-y-3">
            <Label
                htmlFor="projectTitle"
                className="block text-body font-medium"
            >
                Project Title
                <span className="text-body-small text-muted-foreground ml-1">
                    (Optional)
                </span>
            </Label>

            <div className="relative">
                <Input
                    id="projectTitle"
                    placeholder="e.g., My Awesome UGC Video"
                    value={formData.projectTitle}
                    onChange={handleProjectTitleChange}
                    className={`w-full h-12 pr-16 text-body ${
                        errors.projectTitle ? 'border-red-500 focus:border-red-500' :
                        characterStatus.isOverLimit ? 'border-red-500' :
                        characterStatus.isNearLimit ? 'border-orange-500' : ''
                    }`}
                    maxLength={100}
                />
                <div className="absolute right-3 top-1/2 -translate-y-1/2 text-caption text-muted-foreground bg-background px-2 rounded">
                    {characterStatus.current}/100
                </div>
            </div>

            {/* Error Messages */}
            {errors.projectTitle && (
                <div className="text-body-small text-red-500">
                    {Array.isArray(errors.projectTitle)
                        ? errors.projectTitle.join(', ')
                        : errors.projectTitle
                    }
                </div>
            )}

            {/* Validation Errors */}
            {!projectTitleValidation.isValid && (
                <div className="text-body-small text-red-500">
                    {projectTitleValidation.errors.join(', ')}
                </div>
            )}

            {/* Help Text */}
            <p className="text-body-small text-muted-foreground">
                Choose a name that helps you identify this project later. This won't appear in your final video.
            </p>
        </div>
    );
};

export default ProjectDetailsSection;
