"use client";

/**
 * AI UGC Video Creation Page
 *
 * This page allows users to create AI-generated User Generated Content (UGC) style videos
 * using Captions AI technology. Users can:
 * - Set project details
 * - Choose between AI-generated or custom scripts
 * - Select AI creators/avatars
 * - Upload background assets
 * - Generate videos with their configuration
 *
 * <AUTHOR> Reel Gen Team
 * @version 2.0.0 - Refactored for modularity and maintainability
 */

import React from 'react';
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { Sparkles } from "lucide-react";

// Actions
import { generateAiUgcVideo } from "@/actions/aiUgcVideoGeneration";

// Custom Hooks
import { useFormState } from './hooks/useFormState';
import { useFileUpload } from './hooks/useFileUpload';
import { useAvatars } from './hooks/useAvatars';

// Components
import ProjectDetailsSection from './_components/ProjectDetailsSection';
import ScriptContentSection from './_components/ScriptContentSection';
import CreatorSelectionCard from './_components/CreatorSelectionCard';
import BackgroundAssetsSection from './_components/BackgroundAssetsSection';
import VideoGenerationSection from './_components/VideoGenerationSection';

/**
 * Main AI UGC Video Creation Page Component
 *
 * Orchestrates the entire video creation workflow using modular components
 * and custom hooks for state management.
 *
 * @returns {JSX.Element} The complete AI UGC video creation page
 */
export default function CreateAiUgcVideoPage() {
    const router = useRouter();

    // Custom hooks for state management
    const {
        formData,
        scriptMode,
        errors,
        handleInputChange,
        handleScriptModeChange,
        isFormValid,
        getSubmissionData,
        validateCurrentForm
    } = useFormState();

    const {
        avatars,
        loadingAvatars,
        selectedCreator,
        selectCreator,
        hasError: avatarError,
        retryLoadAvatars
    } = useAvatars();

    const {
        uploadedFiles,
        uploadingFiles,
        handleFileUpload,
        removeFile,
        getFileTypeIcon
    } = useFileUpload((newUrls) => {
        // Update form data when files are uploaded
        handleInputChange('userAssetUrls', [...formData.userAssetUrls, ...newUrls]);
    });

    // Loading state for form submission
    const [isSubmitting, setIsSubmitting] = React.useState(false);

    /**
     * Handle creator selection
     * @param {Object} creator - Selected creator object
     */
    const handleCreatorSelect = (creator) => {
        selectCreator(creator, handleInputChange);
    };

    /**
     * Handle file removal
     * @param {number} index - Index of file to remove
     */
    const handleFileRemove = (index) => {
        removeFile(index, (removedIndex) => {
            const newUrls = formData.userAssetUrls.filter((_, i) => i !== removedIndex);
            handleInputChange('userAssetUrls', newUrls);
        });
    };

    /**
     * Handle form submission
     * @param {Event} e - Form submit event
     */
    const handleSubmit = async (e) => {
        e.preventDefault();

        // Validate form before submission
        const validation = validateCurrentForm();
        if (!validation.isValid) {
            toast.error("Please fix the form errors before submitting");
            return;
        }

        setIsSubmitting(true);

        try {
            const submitData = getSubmissionData();
            const result = await generateAiUgcVideo(submitData);

            if (result.success) {
                toast.success(result.message || "Video generation started successfully!");
                router.push('/dashboard');
            } else {
                toast.error(result.error || "Failed to start video generation");
            }
        } catch (error) {
            console.error('Video generation error:', error);
            toast.error("An unexpected error occurred. Please try again.");
        } finally {
            setIsSubmitting(false);
        }
    };

    return (
        <div className="space-y-8 max-w-7xl mx-auto">
            {/* Header Section - Matching AI Video page design */}
            <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50 via-blue-50 to-purple-50 dark:from-purple-950/20 dark:via-blue-950/20 dark:to-purple-950/20 p-8 border border-border/50">
                <div className="relative z-10">
                    <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-6">
                        <div className="space-y-3">
                            <h1 className="text-heading-1 text-gradient">
                                AI UGC Video Creator 🎬
                            </h1>
                            <p className="text-body-large text-muted-foreground max-w-2xl">
                                Create authentic UGC-style videos with AI creators speaking over your custom backgrounds using Captions AI technology.
                            </p>
                            <div className="flex items-center gap-4 text-body-small text-muted-foreground">
                                <div className="flex items-center gap-2">
                                    <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                                    <span>AI creators ready</span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <span>⚡</span>
                                    <span>{avatars.length} creators available</span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <span>🎥</span>
                                    <span>UGC-style videos</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {/* Background decoration */}
                <div className="absolute top-0 right-0 w-64 h-64 bg-gradient-to-br from-purple-400/10 to-blue-400/10 rounded-full blur-3xl"></div>
                <div className="absolute bottom-0 left-0 w-48 h-48 bg-gradient-to-tr from-blue-400/10 to-purple-400/10 rounded-full blur-3xl"></div>
            </div>

            {/* Main Content Grid - Matching AI Video page layout */}
            <form onSubmit={handleSubmit}>
                <div className="grid grid-cols-1 xl:grid-cols-3 gap-8">
                    {/* Left Column - Form Sections */}
                    <div className="xl:col-span-2 space-y-6">

                        {/* Section 1: Project Setup */}
                        <div className="relative">
                            <div className="absolute inset-0 bg-gradient-to-br from-purple-50/50 to-blue-50/50 dark:from-purple-950/10 dark:to-blue-950/10 rounded-2xl"></div>
                            <div className="relative bg-background/80 backdrop-blur-sm border border-border/50 rounded-2xl p-6">
                                {/* Section Header */}
                                <div className="flex items-center gap-3 mb-6 pb-4 border-b border-border/30">
                                    <div className="p-2 bg-gradient-to-br from-purple-500 to-blue-500 rounded-lg shadow-soft">
                                        <div className="h-6 w-6 text-white flex items-center justify-center text-lg">📝</div>
                                    </div>
                                    <div>
                                        <h2 className="text-heading-2 text-gradient">Project Setup</h2>
                                        <p className="text-body-small text-muted-foreground">Configure your UGC video project details</p>
                                    </div>
                                </div>

                                <ProjectDetailsSection
                                    formData={formData}
                                    onInputChange={handleInputChange}
                                    errors={errors}
                                />
                            </div>
                        </div>

                        {/* Section Connector */}
                        <div className="flex justify-center">
                            <div className="w-px h-8 bg-gradient-to-b from-purple-300 to-emerald-300 dark:from-purple-600 dark:to-emerald-600"></div>
                        </div>

                        {/* Section 2: Content Creation */}
                        <div className="relative">
                            <div className="absolute inset-0 bg-gradient-to-br from-emerald-50/50 to-purple-50/50 dark:from-emerald-950/10 dark:to-purple-950/10 rounded-2xl"></div>
                            <div className="relative bg-background/80 backdrop-blur-sm border border-border/50 rounded-2xl p-6">
                                {/* Section Header */}
                                <div className="flex items-center gap-3 mb-6 pb-4 border-b border-border/30">
                                    <div className="p-2 bg-gradient-to-br from-emerald-500 to-purple-500 rounded-lg shadow-soft">
                                        <div className="h-6 w-6 text-white flex items-center justify-center text-lg">✍️</div>
                                    </div>
                                    <div>
                                        <h2 className="text-heading-2 text-gradient">Script Content</h2>
                                        <p className="text-body-small text-muted-foreground">Create your UGC video script with AI or write your own</p>
                                    </div>
                                </div>

                                <ScriptContentSection
                                    formData={formData}
                                    scriptMode={scriptMode}
                                    onInputChange={handleInputChange}
                                    onScriptModeChange={handleScriptModeChange}
                                    errors={errors}
                                />
                            </div>
                        </div>

                        {/* Section Connector */}
                        <div className="flex justify-center">
                            <div className="w-px h-8 bg-gradient-to-b from-emerald-300 to-blue-300 dark:from-emerald-600 dark:to-blue-600"></div>
                        </div>

                        {/* Section 3: AI Creator Selection */}
                        <div className="relative">
                            <div className="absolute inset-0 bg-gradient-to-br from-blue-50/50 to-cyan-50/50 dark:from-blue-950/10 dark:to-cyan-950/10 rounded-2xl"></div>
                            <div className="relative bg-background/80 backdrop-blur-sm border border-border/50 rounded-2xl p-6">
                                {/* Section Header */}
                                <div className="flex items-center gap-3 mb-6 pb-4 border-b border-border/30">
                                    <div className="p-2 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-lg shadow-soft">
                                        <div className="h-6 w-6 text-white flex items-center justify-center text-lg">👤</div>
                                    </div>
                                    <div>
                                        <h2 className="text-heading-2 text-gradient">AI Creator</h2>
                                        <p className="text-body-small text-muted-foreground">Choose an AI creator to present your content</p>
                                    </div>
                                </div>

                                <CreatorSelectionCard
                                    loadingAvatars={loadingAvatars}
                                    avatars={avatars}
                                    selectedCreator={selectedCreator}
                                    formData={formData}
                                    setSelectedCreator={handleCreatorSelect}
                                    handleInputChange={handleInputChange}
                                />
                            </div>
                        </div>

                        {/* Section Connector */}
                        <div className="flex justify-center">
                            <div className="w-px h-8 bg-gradient-to-b from-blue-300 to-orange-300 dark:from-blue-600 dark:to-orange-600"></div>
                        </div>

                        {/* Section 4: Background Assets */}
                        <div className="relative">
                            <div className="absolute inset-0 bg-gradient-to-br from-orange-50/50 to-red-50/50 dark:from-orange-950/10 dark:to-red-950/10 rounded-2xl"></div>
                            <div className="relative bg-background/80 backdrop-blur-sm border border-border/50 rounded-2xl p-6">
                                {/* Section Header */}
                                <div className="flex items-center gap-3 mb-6 pb-4 border-b border-border/30">
                                    <div className="p-2 bg-gradient-to-br from-orange-500 to-red-500 rounded-lg shadow-soft">
                                        <div className="h-6 w-6 text-white flex items-center justify-center text-lg">🖼️</div>
                                    </div>
                                    <div>
                                        <h2 className="text-heading-2 text-gradient">Background Assets</h2>
                                        <p className="text-body-small text-muted-foreground">Upload custom backgrounds for your UGC video</p>
                                    </div>
                                </div>

                                <BackgroundAssetsSection
                                    uploadedFiles={uploadedFiles}
                                    uploadingFiles={uploadingFiles}
                                    onFileUpload={handleFileUpload}
                                    onRemoveFile={handleFileRemove}
                                    getFileTypeIcon={getFileTypeIcon}
                                />
                            </div>
                        </div>

                    </div>

                    {/* Right Column - Preview */}
                    <div className="xl:col-span-1">
                        <VideoGenerationSection
                            isLoading={isSubmitting}
                            isFormValid={isFormValid()}
                            onSubmit={handleSubmit}
                            formData={formData}
                            scriptMode={scriptMode}
                            selectedCreator={selectedCreator}
                            uploadedFiles={uploadedFiles}
                            errors={errors}
                        />
                    </div>
                </div>
            </form>
        </div>
    );
}
