/**
 * Custom hook for handling file uploads
 * Manages file validation, upload state, and S3 upload process
 */

import { useState, useCallback } from 'react';
import { toast } from 'sonner';
import { generateMultipleUgcAssetUploadUrls } from '@/actions/ugcAssetUpload';

/**
 * File validation configuration
 */
const FILE_CONFIG = {
    maxSize: 50 * 1024 * 1024, // 50MB
    allowedTypes: {
        image: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'],
        video: ['video/mp4', 'video/mov', 'video/avi', 'video/webm']
    }
};

/**
 * Validate a single file
 * @param {File} file - File to validate
 * @returns {Object} Validation result
 */
const validateFile = (file) => {
    const isImage = FILE_CONFIG.allowedTypes.image.includes(file.type);
    const isVideo = FILE_CONFIG.allowedTypes.video.includes(file.type);
    const isValidType = isImage || isVideo;
    const isValidSize = file.size <= FILE_CONFIG.maxSize;

    return {
        isValid: isValidType && isValidSize,
        isImage,
        isVideo,
        errors: {
            type: !isValidType ? `${file.name} is not a valid image or video file` : null,
            size: !isValidSize ? `${file.name} is too large. Maximum size is 50MB` : null
        }
    };
};

/**
 * Upload a single file to S3
 * @param {File} file - File to upload
 * @param {Object} uploadData - Upload configuration from server
 * @returns {Promise<Object>} Upload result
 */
const uploadFileToS3 = async (file, uploadData) => {
    const response = await fetch(uploadData.signedUrl, {
        method: 'PUT',
        body: file,
        headers: {
            'Content-Type': file.type,
        },
    });

    if (!response.ok) {
        throw new Error(`Failed to upload ${file.name}`);
    }

    return {
        file,
        url: uploadData.finalUrl,
        key: uploadData.key
    };
};

/**
 * Custom hook for file upload functionality
 * @param {Function} onFilesUploaded - Callback when files are successfully uploaded
 * @returns {Object} Upload state and handlers
 */
export const useFileUpload = (onFilesUploaded) => {
    const [uploadedFiles, setUploadedFiles] = useState([]);
    const [uploadingFiles, setUploadingFiles] = useState(false);
    const [uploadProgress, setUploadProgress] = useState(0);

    /**
     * Handle file selection and upload
     * @param {Event} event - File input change event
     */
    const handleFileUpload = useCallback(async (event) => {
        const files = Array.from(event.target.files);
        
        if (files.length === 0) return;

        // Validate files
        const validFiles = [];
        const errors = [];

        files.forEach(file => {
            const validation = validateFile(file);
            if (validation.isValid) {
                validFiles.push(file);
            } else {
                if (validation.errors.type) errors.push(validation.errors.type);
                if (validation.errors.size) errors.push(validation.errors.size);
            }
        });

        // Show validation errors
        errors.forEach(error => toast.error(error));

        if (validFiles.length === 0) return;

        setUploadingFiles(true);
        setUploadProgress(0);

        try {
            // Prepare file info for upload URL generation
            const filesInfo = validFiles.map(file => ({
                filename: file.name,
                contentType: file.type,
                fileSize: file.size
            }));

            // Get pre-signed URLs
            const uploadResult = await generateMultipleUgcAssetUploadUrls(filesInfo);

            if (!uploadResult.success) {
                toast.error(uploadResult.error);
                return;
            }

            // Upload files to S3 with progress tracking
            const uploadPromises = validFiles.map(async (file, index) => {
                const uploadData = uploadResult.data[index];
                const result = await uploadFileToS3(file, uploadData);
                
                // Update progress
                setUploadProgress(prev => prev + (100 / validFiles.length));
                
                return result;
            });

            const uploadedResults = await Promise.all(uploadPromises);

            // Update state with uploaded files and URLs
            setUploadedFiles(prev => [...prev, ...uploadedResults.map(r => r.file)]);
            const newUrls = uploadedResults.map(r => r.url);
            
            // Notify parent component
            if (onFilesUploaded) {
                onFilesUploaded(newUrls);
            }

            toast.success(`Successfully uploaded ${uploadedResults.length} file(s)`);

        } catch (error) {
            console.error('Upload error:', error);
            toast.error('Failed to upload files. Please try again.');
        } finally {
            setUploadingFiles(false);
            setUploadProgress(0);
            // Reset file input
            event.target.value = '';
        }
    }, [onFilesUploaded]);

    /**
     * Remove a file from the uploaded files list
     * @param {number} index - Index of file to remove
     * @param {Function} onFileRemoved - Callback when file is removed
     */
    const removeFile = useCallback((index, onFileRemoved) => {
        setUploadedFiles(prev => prev.filter((_, i) => i !== index));
        if (onFileRemoved) {
            onFileRemoved(index);
        }
    }, []);

    /**
     * Clear all uploaded files
     * @param {Function} onFilesCleared - Callback when files are cleared
     */
    const clearFiles = useCallback((onFilesCleared) => {
        setUploadedFiles([]);
        if (onFilesCleared) {
            onFilesCleared();
        }
    }, []);

    /**
     * Get file type icon based on file type
     * @param {File} file - File object
     * @returns {string} Icon name
     */
    const getFileTypeIcon = useCallback((file) => {
        if (file.type.startsWith('image/')) return 'Image';
        if (file.type.startsWith('video/')) return 'Video';
        return 'File';
    }, []);

    return {
        // State
        uploadedFiles,
        uploadingFiles,
        uploadProgress,
        
        // Handlers
        handleFileUpload,
        removeFile,
        clearFiles,
        
        // Utilities
        getFileTypeIcon,
        
        // Configuration
        maxFileSize: FILE_CONFIG.maxSize,
        allowedTypes: FILE_CONFIG.allowedTypes
    };
};
