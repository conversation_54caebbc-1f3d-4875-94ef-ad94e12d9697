/**
 * Custom hook for managing AI avatars/creators
 * Handles loading, selection, and state management for AI creators
 */

import { useState, useEffect, useCallback } from 'react';
import { toast } from 'sonner';
import { getAvailableAvatars } from '@/actions/aiUgcVideoGeneration';

/**
 * Custom hook for avatar management
 * @returns {Object} Avatar state and handlers
 */
export const useAvatars = () => {
    const [avatars, setAvatars] = useState([]);
    const [loadingAvatars, setLoadingAvatars] = useState(true);
    const [selectedCreator, setSelectedCreator] = useState(null);
    const [error, setError] = useState(null);

    /**
     * Load avatars from the API
     */
    const loadAvatars = useCallback(async () => {
        try {
            setLoadingAvatars(true);
            setError(null);
            
            const result = await getAvailableAvatars();
            
            if (result.success) {
                setAvatars(result.avatars);
                console.log(`Loaded ${result.avatars.length} creators`);
            } else {
                setError(result.error);
                toast.error(result.error);
            }
        } catch (error) {
            const errorMessage = "Failed to load creators";
            setError(errorMessage);
            toast.error(errorMessage);
            console.error('Avatar loading error:', error);
        } finally {
            setLoadingAvatars(false);
        }
    }, []);

    /**
     * Retry loading avatars
     */
    const retryLoadAvatars = useCallback(() => {
        loadAvatars();
    }, [loadAvatars]);

    /**
     * Select a creator and update form state
     * @param {Object} creator - Creator object
     * @param {Function} onCreatorSelect - Callback for form state update
     */
    const selectCreator = useCallback((creator, onCreatorSelect) => {
        setSelectedCreator(creator);
        if (onCreatorSelect) {
            onCreatorSelect('avatarChoice', creator.id);
        }
    }, []);

    /**
     * Clear selected creator
     * @param {Function} onCreatorClear - Callback for form state update
     */
    const clearSelectedCreator = useCallback((onCreatorClear) => {
        setSelectedCreator(null);
        if (onCreatorClear) {
            onCreatorClear('avatarChoice', '');
        }
    }, []);

    /**
     * Get creator by ID
     * @param {string} creatorId - Creator ID
     * @returns {Object|null} Creator object or null
     */
    const getCreatorById = useCallback((creatorId) => {
        return avatars.find(creator => creator.id === creatorId) || null;
    }, [avatars]);

    /**
     * Get creators grouped by category
     * @returns {Object} Creators grouped by category
     */
    const getGroupedCreators = useCallback(() => {
        return avatars.reduce((groups, creator) => {
            const category = creator.category;
            if (!groups[category]) groups[category] = [];
            groups[category].push(creator);
            return groups;
        }, {});
    }, [avatars]);

    /**
     * Get creator statistics
     * @returns {Object} Creator statistics
     */
    const getCreatorStats = useCallback(() => {
        const groupedCreators = getGroupedCreators();
        const categories = Object.keys(groupedCreators);
        
        return {
            totalCreators: avatars.length,
            totalCategories: categories.length,
            creatorsWithVideo: avatars.filter(c => c.previewVideo).length,
            creatorsWithThumbnail: avatars.filter(c => c.thumbnail).length,
            categoriesWithCounts: categories.map(category => ({
                category,
                count: groupedCreators[category].length
            }))
        };
    }, [avatars, getGroupedCreators]);

    /**
     * Search creators by name or category
     * @param {string} searchTerm - Search term
     * @returns {Array} Filtered creators
     */
    const searchCreators = useCallback((searchTerm) => {
        if (!searchTerm.trim()) return avatars;
        
        const term = searchTerm.toLowerCase();
        return avatars.filter(creator => 
            creator.name.toLowerCase().includes(term) ||
            creator.category.toLowerCase().includes(term)
        );
    }, [avatars]);

    // Load avatars on mount
    useEffect(() => {
        loadAvatars();
    }, [loadAvatars]);

    return {
        // State
        avatars,
        loadingAvatars,
        selectedCreator,
        error,
        
        // Handlers
        selectCreator,
        clearSelectedCreator,
        retryLoadAvatars,
        
        // Utilities
        getCreatorById,
        getGroupedCreators,
        getCreatorStats,
        searchCreators,
        
        // Computed values
        hasAvatars: avatars.length > 0,
        isEmpty: !loadingAvatars && avatars.length === 0,
        hasError: !!error
    };
};
