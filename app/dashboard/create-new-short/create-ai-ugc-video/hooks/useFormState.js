/**
 * Custom hook for managing AI UGC video form state
 * Handles form data, validation, and state updates
 */

import { useState, useCallback } from 'react';

/**
 * Initial form state
 */
const initialFormState = {
    projectTitle: '',
    scriptTopic: '',
    userScript: '',
    avatarChoice: '',
    userAssetUrls: [],
    aspectRatio: '9:16'
};

/**
 * Form validation rules
 */
const validateForm = (formData, scriptMode) => {
    const errors = {};
    
    // Script validation
    const hasScript = scriptMode === 'topic' 
        ? formData.scriptTopic.trim() 
        : formData.userScript.trim();
    
    if (!hasScript) {
        errors.script = scriptMode === 'topic' 
            ? 'Please provide a topic for AI script generation'
            : 'Please write your custom script';
    }
    
    // Script length validation for custom scripts
    if (scriptMode === 'custom' && formData.userScript.length > 5000) {
        errors.script = 'Script must be less than 5000 characters';
    }
    
    // Avatar validation
    if (!formData.avatarChoice) {
        errors.avatar = 'Please select an AI creator';
    }
    
    return {
        isValid: Object.keys(errors).length === 0,
        errors
    };
};

/**
 * Custom hook for form state management
 * @returns {Object} Form state and handlers
 */
export const useFormState = () => {
    const [formData, setFormData] = useState(initialFormState);
    const [scriptMode, setScriptMode] = useState('topic'); // 'topic' or 'custom'
    const [errors, setErrors] = useState({});

    /**
     * Handle input changes for form fields
     * @param {string} field - Field name
     * @param {any} value - Field value
     */
    const handleInputChange = useCallback((field, value) => {
        setFormData(prev => ({
            ...prev,
            [field]: value
        }));
        
        // Clear error for this field when user starts typing
        if (errors[field]) {
            setErrors(prev => {
                const newErrors = { ...prev };
                delete newErrors[field];
                return newErrors;
            });
        }
    }, [errors]);

    /**
     * Handle script mode change
     * @param {string} mode - 'topic' or 'custom'
     */
    const handleScriptModeChange = useCallback((mode) => {
        setScriptMode(mode);
        // Clear script-related errors when switching modes
        setErrors(prev => {
            const newErrors = { ...prev };
            delete newErrors.script;
            return newErrors;
        });
    }, []);

    /**
     * Validate the current form state
     * @returns {Object} Validation result
     */
    const validateCurrentForm = useCallback(() => {
        const validation = validateForm(formData, scriptMode);
        setErrors(validation.errors);
        return validation;
    }, [formData, scriptMode]);

    /**
     * Check if form is valid for submission
     * @returns {boolean} Is form valid
     */
    const isFormValid = useCallback(() => {
        return validateForm(formData, scriptMode).isValid;
    }, [formData, scriptMode]);

    /**
     * Get form data prepared for submission
     * @returns {Object} Prepared form data
     */
    const getSubmissionData = useCallback(() => {
        return {
            ...formData,
            scriptTopic: scriptMode === 'topic' ? formData.scriptTopic : '',
            userScript: scriptMode === 'custom' ? formData.userScript : ''
        };
    }, [formData, scriptMode]);

    /**
     * Reset form to initial state
     */
    const resetForm = useCallback(() => {
        setFormData(initialFormState);
        setScriptMode('topic');
        setErrors({});
    }, []);

    return {
        // State
        formData,
        scriptMode,
        errors,
        
        // Handlers
        handleInputChange,
        handleScriptModeChange,
        
        // Validation
        validateCurrentForm,
        isFormValid,
        
        // Utilities
        getSubmissionData,
        resetForm
    };
};
