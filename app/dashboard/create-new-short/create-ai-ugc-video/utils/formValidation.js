/**
 * Form validation utilities for AI UGC video creation
 * Contains validation rules and helper functions
 */

/**
 * Validation rules configuration
 */
export const VALIDATION_RULES = {
    projectTitle: {
        maxLength: 100,
        required: false
    },
    scriptTopic: {
        maxLength: 1000,
        minLength: 10,
        required: true
    },
    userScript: {
        maxLength: 5000,
        minLength: 20,
        required: true
    },
    avatarChoice: {
        required: true
    }
};

/**
 * Validation error messages
 */
export const VALIDATION_MESSAGES = {
    required: (field) => `${field} is required`,
    minLength: (field, min) => `${field} must be at least ${min} characters`,
    maxLength: (field, max) => `${field} must be less than ${max} characters`,
    invalidChoice: (field) => `Please select a valid ${field}`,
    invalidFormat: (field) => `${field} format is invalid`
};

/**
 * Validate project title
 * @param {string} title - Project title
 * @returns {Object} Validation result
 */
export const validateProjectTitle = (title) => {
    const errors = [];
    
    if (title && title.length > VALIDATION_RULES.projectTitle.maxLength) {
        errors.push(VALIDATION_MESSAGES.maxLength('Project title', VALIDATION_RULES.projectTitle.maxLength));
    }
    
    return {
        isValid: errors.length === 0,
        errors
    };
};

/**
 * Validate script topic
 * @param {string} topic - Script topic
 * @returns {Object} Validation result
 */
export const validateScriptTopic = (topic) => {
    const errors = [];
    const trimmedTopic = topic.trim();
    
    if (!trimmedTopic) {
        errors.push(VALIDATION_MESSAGES.required('Script topic'));
    } else {
        if (trimmedTopic.length < VALIDATION_RULES.scriptTopic.minLength) {
            errors.push(VALIDATION_MESSAGES.minLength('Script topic', VALIDATION_RULES.scriptTopic.minLength));
        }
        if (trimmedTopic.length > VALIDATION_RULES.scriptTopic.maxLength) {
            errors.push(VALIDATION_MESSAGES.maxLength('Script topic', VALIDATION_RULES.scriptTopic.maxLength));
        }
    }
    
    return {
        isValid: errors.length === 0,
        errors
    };
};

/**
 * Validate user script
 * @param {string} script - User script
 * @returns {Object} Validation result
 */
export const validateUserScript = (script) => {
    const errors = [];
    const trimmedScript = script.trim();
    
    if (!trimmedScript) {
        errors.push(VALIDATION_MESSAGES.required('Script'));
    } else {
        if (trimmedScript.length < VALIDATION_RULES.userScript.minLength) {
            errors.push(VALIDATION_MESSAGES.minLength('Script', VALIDATION_RULES.userScript.minLength));
        }
        if (trimmedScript.length > VALIDATION_RULES.userScript.maxLength) {
            errors.push(VALIDATION_MESSAGES.maxLength('Script', VALIDATION_RULES.userScript.maxLength));
        }
    }
    
    return {
        isValid: errors.length === 0,
        errors
    };
};

/**
 * Validate avatar choice
 * @param {string} avatarId - Selected avatar ID
 * @param {Array} availableAvatars - List of available avatars
 * @returns {Object} Validation result
 */
export const validateAvatarChoice = (avatarId, availableAvatars = []) => {
    const errors = [];
    
    if (!avatarId) {
        errors.push(VALIDATION_MESSAGES.required('AI Creator'));
    } else if (availableAvatars.length > 0) {
        const isValidChoice = availableAvatars.some(avatar => avatar.id === avatarId);
        if (!isValidChoice) {
            errors.push(VALIDATION_MESSAGES.invalidChoice('AI Creator'));
        }
    }
    
    return {
        isValid: errors.length === 0,
        errors
    };
};

/**
 * Validate entire form based on current mode
 * @param {Object} formData - Form data object
 * @param {string} scriptMode - Current script mode ('topic' or 'custom')
 * @param {Array} availableAvatars - Available avatars for validation
 * @returns {Object} Complete validation result
 */
export const validateCompleteForm = (formData, scriptMode, availableAvatars = []) => {
    const validationResults = {
        projectTitle: validateProjectTitle(formData.projectTitle),
        avatar: validateAvatarChoice(formData.avatarChoice, availableAvatars)
    };
    
    // Validate script based on mode
    if (scriptMode === 'topic') {
        validationResults.script = validateScriptTopic(formData.scriptTopic);
    } else {
        validationResults.script = validateUserScript(formData.userScript);
    }
    
    // Collect all errors
    const allErrors = {};
    let isFormValid = true;
    
    Object.keys(validationResults).forEach(field => {
        const result = validationResults[field];
        if (!result.isValid) {
            allErrors[field] = result.errors;
            isFormValid = false;
        }
    });
    
    return {
        isValid: isFormValid,
        errors: allErrors,
        fieldResults: validationResults
    };
};

/**
 * Get character count status for text fields
 * @param {string} text - Text content
 * @param {number} maxLength - Maximum allowed length
 * @returns {Object} Character count status
 */
export const getCharacterCountStatus = (text, maxLength) => {
    const length = text.length;
    const remaining = maxLength - length;
    const percentage = (length / maxLength) * 100;
    
    return {
        current: length,
        max: maxLength,
        remaining,
        percentage,
        isNearLimit: percentage > 80,
        isOverLimit: percentage > 100,
        status: percentage > 100 ? 'error' : percentage > 80 ? 'warning' : 'normal'
    };
};

/**
 * Format validation errors for display
 * @param {Object} errors - Validation errors object
 * @returns {Array} Formatted error messages
 */
export const formatValidationErrors = (errors) => {
    const messages = [];
    
    Object.keys(errors).forEach(field => {
        const fieldErrors = errors[field];
        if (Array.isArray(fieldErrors)) {
            messages.push(...fieldErrors);
        } else if (typeof fieldErrors === 'string') {
            messages.push(fieldErrors);
        }
    });
    
    return messages;
};

/**
 * Check if form is ready for submission
 * @param {Object} formData - Form data
 * @param {string} scriptMode - Script mode
 * @param {Array} availableAvatars - Available avatars
 * @returns {boolean} Is form ready for submission
 */
export const isFormReadyForSubmission = (formData, scriptMode, availableAvatars) => {
    const validation = validateCompleteForm(formData, scriptMode, availableAvatars);
    return validation.isValid;
};
