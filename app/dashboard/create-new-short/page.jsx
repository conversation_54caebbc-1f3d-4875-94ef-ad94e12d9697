"use client";

import Link from 'next/link';
import { Bot, Images, FileText, Upload, Mic, Film, User, Sparkles, TrendingUp, Clock, Zap } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { motion, AnimatePresence } from 'framer-motion';
import { useState } from 'react';
import { useRouter } from 'next/navigation';

export default function CreateNewShortPage() {
  const router = useRouter();
  const [selectedCard, setSelectedCard] = useState(null);
  const [fadingCards, setFadingCards] = useState(new Set());

  const creationOptions = [
    {
      title: 'AI Video',
      description: 'Generate videos from scripts or topics using AI',
      href: '/dashboard/create-new-short/create-ai-video',
      icon: <Bot size={32} className="text-blue-600" />,
      isActive: true,
      credits: 10,
      duration: '2-4 min',
      badge: 'Popular',
      badgeColor: 'bg-blue-100 text-blue-700',
      category: 'AI Powered'
    },
    {
      title: 'AI UGC Video',
      description: 'AI avatars speaking over your custom backgrounds',
      href: '/dashboard/create-new-short/create-ai-ugc-video',
      icon: <User size={32} className="text-emerald-600" />,
      isActive: true,
      credits: 15,
      duration: '3-5 min',
      badge: 'New',
      badgeColor: 'bg-emerald-100 text-emerald-700',
      category: 'AI Powered'
    },
    {
      title: 'Meme Video',
      description: 'Create viral meme content with custom text and images',
      href: '/dashboard/create-new-short/create-meme-video',
      icon: <Images size={32} className="text-purple-600" />,
      isActive: true,
      credits: 5,
      duration: '1-2 min',
      badge: 'Quick',
      badgeColor: 'bg-purple-100 text-purple-700',
      category: 'Quick Create'
    },
    {
      title: 'Podcast Clipper',
      description: 'Extract highlights from podcast episodes automatically',
      href: '/dashboard/create-new-short/create-podcast-clipper',
      icon: <Mic size={32} className="text-orange-600" />,
      isActive: true,
      credits: 8,
      duration: '2-3 min',
      badge: 'Pro',
      badgeColor: 'bg-orange-100 text-orange-700',
      category: 'Content Tools'
    },
    {
      title: 'Reddit Post Video',
      description: 'Turn Reddit posts into engaging video content',
      href: '/dashboard/create-new-short/create-reddit-post-video',
      icon: <FileText size={32} className="text-red-600" />,
      isActive: true,
      credits: 6,
      duration: '1-3 min',
      badge: 'Social',
      badgeColor: 'bg-red-100 text-red-700',
      category: 'Social Media'
    },
    {
      title: 'Twitter Post Video',
      description: 'Convert Twitter posts into shareable video content',
      href: '/dashboard/create-new-short/create-twitter-post-video',
      icon: <FileText size={32} className="text-blue-400" />,
      isActive: true,
      credits: 6,
      duration: '1-3 min',
      badge: 'Social',
      badgeColor: 'bg-blue-100 text-blue-700',
      category: 'Social Media'
    },
    {
      title: 'Stock Media Video',
      description: 'Generate videos using AI with stock media content',
      href: '/dashboard/create-new-short/create-stock-media-video',
      icon: <Film size={32} className="text-teal-600" />,
      isActive: true,
      credits: 12,
      duration: '3-4 min',
      badge: 'Premium',
      badgeColor: 'bg-teal-100 text-teal-700',
      category: 'AI Powered'
    },
    {
      title: 'Narrator Video',
      description: 'Create videos with AI narrator and custom visuals',
      href: '/dashboard/create-new-short/create-new-narrator-short',
      icon: <Upload size={32} className="text-indigo-600" />,
      isActive: true,
      credits: 7,
      duration: '2-3 min',
      badge: 'Classic',
      badgeColor: 'bg-indigo-100 text-indigo-700',
      category: 'Content Tools'
    },
    {
      title: 'Pinterest Video',
      description: 'Combine images with text and audio',
      href: '/dashboard/create-new-short/create-from-pinterest',
      icon: <Images size={32} className="text-green-600" />,
      isActive: false,
      credits: 8,
      duration: '2-3 min',
      badge: 'Coming Soon',
      badgeColor: 'bg-gray-100 text-gray-700',
      category: 'Social Media'
    },
  ];

  // Group options by category
  const categories = creationOptions.reduce((acc, option) => {
    if (!acc[option.category]) {
      acc[option.category] = [];
    }
    acc[option.category].push(option);
    return acc;
  }, {});

  // Animation variants for cards
  const cardVariants = {
    initial: {
      opacity: 0,
      scale: 0.8,
      y: 20,
    },
    animate: (index) => ({
      opacity: 1,
      scale: [0.8, 1.01, 1],
      y: 0,
      transition: {
        duration: 0.5,
        delay: index * 0.05,
        type: "spring",
        stiffness: 500,
        damping: 25,
        scale: {
          type: "tween",
          duration: 0.5,
          ease: [0.175, 0.885, 0.32, 1.275],
        },
      },
    }),
    exit: {
      opacity: 0,
      scale: 0.8,
      transition: {
        duration: 0.2,
      },
    },
  };

  const hoverVariants = {
    hover: {
      scale: 1.05,
      transition: {
        type: "spring",
        stiffness: 400,
        damping: 10,
      },
    },
  };

  const handleCardClick = (option) => {
    if (selectedCard) return;

    setSelectedCard(option.title);

    // Create random delays for other cards to fade out
    const otherCards = creationOptions.filter(opt => opt.title !== option.title);

    otherCards.forEach((card) => {
      setTimeout(() => {
        setFadingCards(prev => new Set([...prev, card.title]));
      }, Math.random() * 300);
    });

    // Navigate after animation
    setTimeout(() => {
      router.push(option.href);
    }, 800);
  };

  const shouldShowCard = (cardTitle) => {
    if (!selectedCard) return true;
    if (selectedCard === cardTitle) return true;
    return !fadingCards.has(cardTitle);
  };

  return (
    <div className="container mx-auto py-8 px-4 sm:px-6 lg:px-8 max-w-7xl">
      {/* Header */}
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold mb-4 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
          Create Amazing Videos
        </h1>
        <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
          Choose from our powerful AI-driven tools to create engaging video content in minutes
        </p>
      </div>

      {/* Popular Options */}
      <div className="mb-12">
        <div className="flex items-center gap-2 mb-6">
          <TrendingUp className="h-6 w-6 text-orange-500" />
          <h2 className="text-2xl font-bold">Most Popular</h2>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {creationOptions
            .filter(option => option.badge === 'Popular' || option.badge === 'New')
            .map((option, index) => (
              <div key={option.title} className="relative">
                <AnimatePresence mode="wait">
                  {shouldShowCard(option.title) ? (
                    <motion.div
                      key={`card-${option.title}`}
                      className={`relative group cursor-pointer ${selectedCard === option.title ? 'z-10' : ''}`}
                      variants={cardVariants}
                      initial="initial"
                      animate="animate"
                      exit="exit"
                      custom={index}
                      whileHover={selectedCard ? {} : "hover"}
                      onClick={() => option.isActive && handleCardClick(option)}
                    >
                      <motion.div variants={hoverVariants}>
                        <Card className="h-full border border-border/50 hover:border-border transition-colors duration-200 bg-card/50 backdrop-blur-sm hover:shadow-lg">
                          <CardContent className="p-6">
                            {option.isActive ? (
                              <div className="flex items-start gap-4">
                                <div className="p-3 rounded-lg bg-gradient-to-br from-muted to-muted/50 group-hover:from-primary/10 group-hover:to-primary/5 transition-colors">
                                  {option.icon}
                                </div>
                                <div className="flex-1 min-w-0">
                                  <div className="flex items-center gap-2 mb-2">
                                    <h3 className="font-semibold text-lg group-hover:text-primary transition-colors">{option.title}</h3>
                                    <Badge className={option.badgeColor}>{option.badge}</Badge>
                                  </div>
                                  <p className="text-muted-foreground text-sm mb-3">{option.description}</p>
                                  <div className="flex items-center gap-4 text-xs text-muted-foreground">
                                    <span className="flex items-center gap-1">
                                      <Zap className="h-3 w-3" />
                                      {option.credits} credits
                                    </span>
                                    <span className="flex items-center gap-1">
                                      <Clock className="h-3 w-3" />
                                      {option.duration}
                                    </span>
                                  </div>
                                </div>
                              </div>
                            ) : (
                              <div className="opacity-60 cursor-not-allowed">
                                <div className="flex items-start gap-4">
                                  <div className="p-3 rounded-lg bg-muted">
                                    {option.icon}
                                  </div>
                                  <div className="flex-1 min-w-0">
                                    <div className="flex items-center gap-2 mb-2">
                                      <h3 className="font-semibold text-lg text-muted-foreground">{option.title}</h3>
                                      <Badge className={option.badgeColor}>{option.badge}</Badge>
                                    </div>
                                    <p className="text-muted-foreground text-sm">{option.description}</p>
                                  </div>
                                </div>
                              </div>
                            )}
                          </CardContent>
                        </Card>
                      </motion.div>
                    </motion.div>
                  ) : (
                    <div key={`placeholder-${option.title}`} className="h-full" />
                  )}
                </AnimatePresence>
              </div>
            ))}
        </div>
      </div>

      {/* All Categories */}
      {Object.entries(categories).map(([category, options]) => (
        <div key={category} className="mb-10">
          <div className="flex items-center gap-2 mb-6">
            <Sparkles className="h-5 w-5 text-blue-500" />
            <h2 className="text-xl font-semibold">{category}</h2>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {options.map((option, index) => (
              <div key={option.title} className="relative">
                <AnimatePresence mode="wait">
                  {shouldShowCard(option.title) ? (
                    <motion.div
                      key={`card-${option.title}`}
                      className={`relative group cursor-pointer ${selectedCard === option.title ? 'z-10' : ''}`}
                      variants={cardVariants}
                      initial="initial"
                      animate="animate"
                      exit="exit"
                      custom={index}
                      whileHover={selectedCard ? {} : "hover"}
                      onClick={() => option.isActive && handleCardClick(option)}
                    >
                      <motion.div variants={hoverVariants}>
                        <Card className="h-full border border-border/50 hover:border-border transition-colors duration-200 bg-card/50 backdrop-blur-sm hover:shadow-md">
                          <CardContent className="p-4">
                            {option.isActive ? (
                              <div className="text-center">
                                <div className="mb-3 p-3 bg-gradient-to-br from-muted to-muted/50 group-hover:from-primary/10 group-hover:to-primary/5 rounded-lg inline-block transition-colors">
                                  {option.icon}
                                </div>
                                <div className="flex items-center justify-center gap-2 mb-2">
                                  <h3 className="font-semibold group-hover:text-primary transition-colors">{option.title}</h3>
                                  <Badge variant="outline" className={option.badgeColor}>{option.badge}</Badge>
                                </div>
                                <p className="text-muted-foreground text-sm mb-3">{option.description}</p>
                                <div className="flex items-center justify-center gap-3 text-xs text-muted-foreground">
                                  <span className="flex items-center gap-1">
                                    <Zap className="h-3 w-3" />
                                    {option.credits}
                                  </span>
                                  <span className="flex items-center gap-1">
                                    <Clock className="h-3 w-3" />
                                    {option.duration}
                                  </span>
                                </div>
                              </div>
                            ) : (
                              <div className="text-center opacity-60 cursor-not-allowed">
                                <div className="mb-3 p-3 bg-muted rounded-lg inline-block">
                                  {option.icon}
                                </div>
                                <div className="flex items-center justify-center gap-2 mb-2">
                                  <h3 className="font-semibold text-muted-foreground">{option.title}</h3>
                                  <Badge variant="outline" className={option.badgeColor}>{option.badge}</Badge>
                                </div>
                                <p className="text-muted-foreground text-sm">{option.description}</p>
                              </div>
                            )}
                          </CardContent>
                        </Card>
                      </motion.div>
                    </motion.div>
                  ) : (
                    <div key={`placeholder-${option.title}`} className="h-full" />
                  )}
                </AnimatePresence>
              </div>
            ))}
          </div>
        </div>
      ))}
    </div>
  );
}
