import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner'; // Using sonner for toasts

const MediaEvaluator = ({ script, mediaResults, onMediaEvaluated }) => {
  const [evaluatedMedia, setEvaluatedMedia] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  // No need for useToast() hook with sonner, just call toast() directly
  // const { toast } = useToast();

  useEffect(() => {
    // Trigger evaluation when media results are available and not already evaluating
    if (mediaResults && mediaResults.length > 0 && !loading) {
      evaluateMedia(script, mediaResults);
    } else if (!mediaResults || mediaResults.length === 0) {
      // Clear evaluated media if mediaResults become empty
      setEvaluatedMedia([]);
      onMediaEvaluated([]);
    }
  }, [script, mediaResults]); // Depend on script and mediaResults

  const evaluateMedia = async (scriptContent, mediaToEvaluate) => {
    setLoading(true);
    setError(null);
    setEvaluatedMedia([]);

    console.log('Evaluating media:', mediaToEvaluate, 'with script:', scriptContent);

    try {
      const response = await fetch('/api/evaluate-media', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ script: scriptContent, media: mediaToEvaluate }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to evaluate media');
      }

      const data = await response.json();
      setEvaluatedMedia(data.evaluatedMedia);
      if (onMediaEvaluated) {
        onMediaEvaluated(data.evaluatedMedia);
      }
       toast("Media Evaluation Complete", {
        description: "AI has evaluated the selected media.",
      });

    } catch (err) {
      console.error('Error evaluating media:', err);
      setError(err.message);
       toast("Error", {
        description: `Failed to evaluate media: ${err.message}`,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6"> {/* Increased vertical spacing */}
      <p className="text-gray-700 dark:text-gray-300">AI will automatically evaluate the quality and relevance of the selected media.</p> {/* Styled text */}
      {loading && <p className="text-blue-600 dark:text-blue-400">Evaluating media...</p>} {/* Styled loading message */}
       {error && (
        <div className="text-red-600 dark:text-red-400 text-sm mt-4">{error}</div> // Styled error message
      )}
      {evaluatedMedia.length > 0 && (
        <div className="mt-6"> {/* Added top margin */}
          <h3 className="text-xl font-semibold mb-4 text-gray-700 dark:text-gray-300">Evaluation Results</h3> {/* Styled header */}
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4"> {/* Responsive grid */}
            {evaluatedMedia.map((media) => (
              <div key={media.id} className="border border-gray-200 dark:border-gray-700 rounded-lg p-3 shadow-sm flex flex-col items-center text-center bg-white dark:bg-gray-700 transition-all duration-200 hover:shadow-md"> {/* Styled media item */}
                <p className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Type: {media.type}</p> {/* Styled type */}
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">Source: {media.source}</p> {/* Styled source */}
                {media.type === 'music' ? (
                   <div className="flex flex-col items-center justify-center h-32 w-full bg-gray-200 dark:bg-gray-600 rounded-md mb-2 p-2"> {/* Styled music placeholder */}
                     <span className="text-gray-600 dark:text-gray-300 text-sm mb-1">Music Track</span>
                     <p className="text-sm font-medium text-gray-800 dark:text-gray-200 truncate w-full px-1">{media.title || 'Untitled Track'}</p> {/* Styled title */}
                   </div>
                ) : (
                  <img src={media.previewUrl} alt="media preview" className="w-full h-32 object-cover rounded-md mb-2" /> // Styled image
                )}
                {media.evaluation && (
                  <div className="text-left w-full text-sm text-gray-700 dark:text-gray-300 space-y-1"> {/* Styled evaluation details container */}
                    <p>Quality: <span className="font-semibold">{media.evaluation.qualityScore.toFixed(2)}</span></p> {/* Styled score */}
                    <p>Relevance: <span className="font-semibold">{media.evaluation.relevanceScore.toFixed(2)}</span></p> {/* Styled score */}
                    <p>Notes: <span className="text-gray-600 dark:text-gray-400">{media.evaluation.notes}</span></p> {/* Styled notes */}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default MediaEvaluator;