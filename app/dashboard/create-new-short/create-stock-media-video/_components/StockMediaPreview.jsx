import React from 'react';
import { VideoPreviewPanel } from '@/components/shared/VideoPreviewPanel';

function StockMediaPreview({
  prompt,
  projectTitle,
  videoStyle,
  duration,
  aspectRatio,
  onGenerateVideo,
  isGenerating,
  generationMessage,
  isGenerateButtonDisabled,
  userDetail,
  VIDEO_GENERATION_COST
}) {
  // Configuration for VideoPreviewPanel
  const previewConfig = {
    showRemotionPlayer: false,
    showConfigSummary: true,
    showGenerationStatus: true,
    title: "Preview & Generate",
    description: "Review your video configuration",
    icon: () => <div className="h-6 w-6 text-white flex items-center justify-center text-lg">👁️</div>
  };

  // Configuration summary items
  const configSummary = [
    { isComplete: Boolean(projectTitle), label: `Project: ${projectTitle || 'Untitled'}`, icon: () => null, color: 'text-foreground' },
    { isComplete: <PERSON><PERSON><PERSON>(videoStyle), label: `Style: ${videoStyle || 'Not selected'}`, icon: () => null, color: 'text-foreground' },
    { isComplete: Boolean(duration), label: `Duration: ${duration}s`, icon: () => null, color: 'text-foreground' },
    { isComplete: Boolean(aspectRatio), label: `Aspect Ratio: ${aspectRatio}`, icon: () => null, color: 'text-foreground' }
  ];

  // Custom preview content
  const customPreviewContent = (
    <>
      {/* Video Preview Placeholder */}
      <div className="space-y-3">
        <h4 className="text-body font-medium">Video Preview</h4>
        <div className="bg-gradient-to-br from-muted to-muted/50 rounded-xl overflow-hidden border border-border/50">
          <div className="aspect-video flex items-center justify-center bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-900">
            <div className="text-center p-6">
              <div className="text-4xl mb-3">🎬</div>
              <h5 className="text-body font-medium mb-2">Stock Media Video</h5>
              <p className="text-body-small text-muted-foreground">
                {prompt ? 'AI will curate stock footage based on your description' : 'Enter a video concept to see preview'}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Concept Summary */}
      {prompt && (
        <div className="space-y-3">
          <h4 className="text-body font-medium">Video Concept</h4>
          <div className="p-3 bg-muted/20 rounded-lg border border-border/20">
            <p className="text-body-small text-muted-foreground line-clamp-3">
              {prompt}
            </p>
          </div>
        </div>
      )}

      {/* AI Process Info */}
      <div className="space-y-3">
        <h4 className="text-body font-medium">AI Generation Process</h4>
        <div className="p-3 bg-blue-50/50 dark:bg-blue-950/20 rounded-lg border border-blue-200/30 dark:border-blue-800/30">
          <div className="space-y-2 text-body-small text-blue-800 dark:text-blue-200">
            <div className="flex items-center gap-2">
              <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
              <span>Analyze your concept description</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
              <span>Search and curate relevant stock footage</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
              <span>Generate script and scene timing</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
              <span>Assemble final video with transitions</span>
            </div>
          </div>
        </div>
      </div>
    </>
  );

  return (
    <VideoPreviewPanel
      videoType="stock-media"
      previewConfig={previewConfig}
      formData={{ prompt, projectTitle, videoStyle, duration, aspectRatio }}
      onGenerate={onGenerateVideo}
      isGenerating={isGenerating}
      generationMessage={generationMessage}
      isGenerateButtonDisabled={isGenerateButtonDisabled}
      userDetail={userDetail}
      VIDEO_GENERATION_COST={VIDEO_GENERATION_COST}
      configSummary={configSummary}
    >
      {customPreviewContent}
    </VideoPreviewPanel>
  );
}

export default StockMediaPreview;
