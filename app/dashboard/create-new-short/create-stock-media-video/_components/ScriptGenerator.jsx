import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner'; // Using sonner for toasts

const ScriptGenerator = ({ setGeneratedScript }) => {
  const [topic, setTopic] = useState('');
  const [script, setScript] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  // No need for useToast() hook with sonner, just call toast() directly
  // const { toast } = useToast();

  const handleGenerateScript = async () => {
    setLoading(true);
    setError(null);
    setScript('');
    setGeneratedScript('');

    try {
      const response = await fetch('/api/generate-script', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ topic }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to generate script');
      }

      const data = await response.json();
      setScript(data.script);
      setGeneratedScript(data.script);
      toast("Script Generated", {
        description: "The script has been successfully generated.",
      });

    } catch (err) {
      console.error('Error generating script:', err);
      setError(err.message);
      toast("Error", {
        description: err.message,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6"> {/* Increased vertical spacing */}
      <div className="grid gap-2"> {/* Added grid for label/textarea pairing */}
        <Label htmlFor="topic" className="text-gray-700 dark:text-gray-300">Topic or Prompt</Label> {/* Styled label */}
        <Textarea
          id="topic"
          placeholder="Enter topic or prompt for script generation"
          value={topic}
          onChange={(e) => setTopic(e.target.value)}
          rows={4}
          disabled={loading}
          className="w-full px-3 py-2 text-gray-700 dark:text-gray-200 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-600 disabled:opacity-50 disabled:cursor-not-allowed" // Added comprehensive styling
        />
      </div>
      <Button
        onClick={handleGenerateScript}
        disabled={loading || !topic.trim()}
        className="w-full sm:w-auto px-6 py-2 text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200" // Styled button
      >
        {loading ? 'Generating...' : 'Generate Script'}
      </Button>
      {error && (
        <div className="text-red-600 dark:text-red-400 text-sm mt-4">{error}</div> // Styled error message
      )}
      {script && (
        <div className="grid gap-2 mt-6"> {/* Added grid and top margin */}
          <Label htmlFor="script" className="text-gray-700 dark:text-gray-300">Generated Script</Label> {/* Styled label */}
          <Textarea
            id="script"
            value={script}
            onChange={(e) => { // Modified onChange handler
              setScript(e.target.value);
              // Removed setGeneratedScript(e.target.value) here
            }}
            rows={10}
            className="w-full px-3 py-2 text-gray-700 dark:text-gray-200 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-600" // Removed readOnly styling, added focus styling
          />
          <Button
            onClick={() => { // Added Save Script button and handler
              setGeneratedScript(script); // Update parent state on save
              toast("Script Saved", { description: "Generated script updated." });
            }}
            disabled={loading} // Disable while generating
            className="w-full sm:w-auto px-6 py-2 text-white bg-green-600 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200" // Styled button
          >
            Save Script
          </Button>
        </div>
      )}
    </div>
  );
};

export default ScriptGenerator;