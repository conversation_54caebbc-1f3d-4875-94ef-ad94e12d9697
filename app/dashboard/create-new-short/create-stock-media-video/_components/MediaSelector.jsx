import React, { useState, useEffect } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner'; // Using sonner for toasts


const MediaSelector = ({ script, setSelectedMedia }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [mediaResults, setMediaResults] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);


 // Optional: Automatically generate search queries from the script using AI
 useEffect(() => {
   const generateKeywords = async () => {
     if (!script) {
       setSearchQuery('');
       setMediaResults([]);
       setSelectedMedia([]);
       return;
     }

     setLoading(true);
     setError(null);
     setSearchQuery('Generating keywords...'); // Indicate that keywords are being generated

     try {
       const response = await fetch('/api/generate-search-keywords', {
         method: 'POST',
         headers: {
           'Content-Type': 'application/json',
         },
         body: JSON.stringify({ scriptText: script }),
       });

       if (!response.ok) {
         const errorData = await response.json();
         throw new Error(errorData.error || 'Failed to generate search keywords');
       }

       const data = await response.json();
       const generatedKeywords = data.searchKeywords.join(', '); // Join keywords into a single query string
       setSearchQuery(generatedKeywords);

       // Automatically trigger media search with the generated keywords
       if (generatedKeywords.trim()) {
         handleSearch(generatedKeywords);
       } else {
          setLoading(false);
          toast("Keyword Generation Complete", {
           description: "No relevant keywords were generated from the script.",
           variant: "default",
         });
       }


     } catch (err) {
       console.error('Error generating search keywords:', err);
       setError(err.message);
       setSearchQuery(''); // Clear search query on error
        toast("Keyword Generation Failed", {
         description: `Error: ${err.message}`,
         variant: "destructive",
       });
       setLoading(false); // Ensure loading is set to false on error
     }
   };

   generateKeywords();

 }, [script]); // Depend on script

 const handleSearch = async (query = searchQuery) => {
   if (!query.trim()) {
     setMediaResults([]);
     setSelectedMedia([]);
     return;
   }

   setLoading(true);
   setError(null);
   setMediaResults([]);
   setSelectedMedia([]);

   try {
     // Fetch from Pixabay (Images)
     const pixabayResponse = await fetch(`/api/pixabay-search?query=${encodeURIComponent(query)}`);
     const pixabayData = await pixabayResponse.json();
     const pixabayImages = pixabayData.hits ? pixabayData.hits.map(hit => ({
       id: `pixabay-${hit.id}`,
       type: 'image',
       url: hit.largeImageURL,
       previewUrl: hit.webformatURL,
       tags: hit.tags.split(', '),
       source: 'pixabay',
     })) : [];

     // Fetch from Pexels (Images)
     const pexelsImageResponse = await fetch(`/api/pexels-search?query=${encodeURIComponent(query)}`);
     const pexelsImageData = await pexelsImageResponse.json();
     const pexelsImages = pexelsImageData.photos ? pexelsImageData.photos.map(photo => ({
       id: `pexels-image-${photo.id}`,
       type: 'image',
       url: photo.src.large,
       previewUrl: photo.src.small,
       tags: photo.alt?.split(', ') || [],
       source: 'pexels',
     })) : [];

     // Fetch from Pexels (Videos)
     const pexelsVideoResponse = await fetch(`/api/pexels-video-search?query=${encodeURIComponent(query)}`);
     const pexelsVideoData = await pexelsVideoResponse.json();
      const pexelsVideos = pexelsVideoData.videos ? pexelsVideoData.videos.map(video => ({
       id: `pexels-video-${video.id}`,
       type: 'video',
       url: video.video_files.find(file => file.quality === 'sd')?.link || video.video_files[0]?.link, // Get SD quality or first available
       previewUrl: video.image, // Use the video thumbnail as preview
       tags: video.url?.split('/').pop()?.replace(/-/g, ' ').split('_') || [], // Extract tags from URL as a placeholder
       source: 'pexels',
     })) : [];

     // Fetch from Music Search (Music)
     const musicResponse = await fetch(`/api/music-search?query=${encodeURIComponent(query)}`);
     const musicData = await musicResponse.json();
     const musicResults = musicData.music ? musicData.music.map(track => ({
       id: `music-${track.id}`,
       type: 'music',
       url: track.url,
       previewUrl: track.previewUrl,
       tags: track.tags,
       source: track.source,
       title: track.title,
     })) : [];


     const combinedResults = [...pixabayImages, ...pexelsImages, ...pexelsVideos, ...musicResults];

     setMediaResults(combinedResults);
     // For now, automatically select all results.
     // TODO: Implement actual media selection UI and logic.
     setSelectedMedia(combinedResults);

     if (combinedResults.length === 0) {
       toast("No Media Found", {
         description: "No media results found for your query.",
         variant: "default",
       });
     } else {
        toast("Media Search Complete", {
         description: `Found ${combinedResults.length} media items.`,
         variant: "default",
       });
     }


   } catch (err) {
     console.error('Error searching media:', err);
     setError(err.message);
      toast("Error", {
       description: `Failed to search media: ${err.message}`,
       variant: "destructive",
     });
   } finally {
     setLoading(false);
   }
 };

 return (
   <div className="space-y-6"> {/* Increased vertical spacing */}
     <div className="grid gap-2"> {/* Added grid for label/input pairing */}
       <Label htmlFor="media-search" className="text-gray-700 dark:text-gray-300">Search Query</Label> {/* Styled label */}
       <Input
         id="media-search"
         placeholder="Enter keywords to search for media"
         value={searchQuery}
         onChange={(e) => setSearchQuery(e.target.value)}
         disabled={loading}
         className="w-full px-3 py-2 text-gray-700 dark:text-gray-200 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-600 disabled:opacity-50 disabled:cursor-not-allowed" // Added comprehensive styling
       />
     </div>
     <Button
       onClick={() => handleSearch()}
       disabled={loading || !searchQuery.trim()}
       className="w-full sm:w-auto px-6 py-2 text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200" // Styled button
     >
       {loading ? 'Searching...' : 'Search Media'}
     </Button>
      {error && (
       <div className="text-red-600 dark:text-red-400 text-sm mt-4">{error}</div> // Styled error message
     )}
     {mediaResults.length > 0 && (
       <div className="mt-6"> {/* Added top margin */}
         <h3 className="text-xl font-semibold mb-4 text-gray-700 dark:text-gray-300">Search Results</h3> {/* Styled header */}
         <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6"> {/* Increased gap, Responsive grid */}
           {mediaResults.map((media) => (
             <div key={media.id} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 shadow-sm flex flex-col items-center text-center bg-white dark:bg-gray-800 transition-all duration-200 hover:shadow-md cursor-pointer"> {/* Increased padding, Adjusted background, Added cursor pointer */}
               {media.type === 'image' && <img src={media.previewUrl} alt="media preview" className="w-full h-40 object-cover rounded-md mb-3" />} {/* Increased height, Adjusted margin */}
               {media.type === 'video' && <video src={media.url} controls className="w-full h-40 object-cover rounded-md mb-3" />} {/* Increased height, Adjusted margin */}
               {media.type === 'music' && (
                 <div className="flex flex-col items-center justify-center h-40 w-full bg-gray-100 dark:bg-gray-700 rounded-md mb-3 p-3"> {/* Increased height, Adjusted background, Increased padding */}
                    <span className="text-gray-600 dark:text-gray-300 text-base font-semibold mb-2">Music Track</span> {/* Styled text */}
                    <p className="text-sm font-medium text-gray-800 dark:text-gray-200 truncate w-full px-1">{media.title || 'Untitled Track'}</p> {/* Styled title */}
                 </div>
               )}
               <p className="text-sm font-medium text-gray-700 dark:text-gray-300 mt-auto">Type: <span className="font-normal">{media.type}</span></p> {/* Styled type */}
               <p className="text-sm text-gray-600 dark:text-gray-400">Source: <span className="font-normal">{media.source}</span></p> {/* Styled source */}
               {/* TODO: Add functionality to select/deselect media */}
             </div>
           ))}
         </div>
       </div>
     )}
   </div>
 );
};

export default MediaSelector;