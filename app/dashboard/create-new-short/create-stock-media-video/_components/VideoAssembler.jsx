import React, { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner'; // Using sonner for toasts

const VideoAssembler = ({ script, evaluatedMedia, onVideoAssembled }) => {
  const [assemblyStatus, setAssemblyStatus] = useState('Idle'); // Idle, Initiating, Rendering, Complete, Error
  const [assembledVideoUrl, setAssembledVideoUrl] = useState(null);
  const [renderProgress, setRenderProgress] = useState(0);
  const [renderId, setRenderId] = useState(null);
  const [bucketName, setBucketName] = useState(null);
  const [error, setError] = useState(null); // Declare the error state
  const pollingIntervalRef = useRef(null);
  // No need for useToast() hook with sonner, just call toast() directly
  // const { toast } = useToast();

  const COMPOSITION_ID = 'StockMediaVideo'; // The ID of the Remotion composition

  useEffect(() => {
    // Trigger assembly when script and evaluated media are available and status is Idle
    if (script && evaluatedMedia && evaluatedMedia.length > 0 && assemblyStatus === 'Idle') {
      initiateAssembly(script, evaluatedMedia);
    } else if ((!script || !evaluatedMedia || evaluatedMedia.length === 0) && assemblyStatus !== 'Idle') {
      // Reset if script or media become unavailable while not Idle
      resetAssembly();
    }
  }, [script, evaluatedMedia, assemblyStatus]); // Depend on script, evaluatedMedia, and assemblyStatus

  useEffect(() => {
    // Cleanup polling interval on component unmount or when render is complete/error
    return () => {
      if (pollingIntervalRef.current) {
        clearInterval(pollingIntervalRef.current);
      }
    };
  }, []);

  const initiateAssembly = async (scriptContent, mediaToUse) => {
    setAssemblyStatus('Initiating');
    setAssembledVideoUrl(null);
    setRenderProgress(0);
    setError(null); // Clear previous errors

    console.log('Initiating video assembly with script and media...');

    try {
      // Generate a unique video ID (can be improved)
      const videoId = `stock-media-${Date.now()}`;

      const response = await fetch('/api/render-lambda', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          videoId: videoId,
          compositionId: COMPOSITION_ID,
          inputProps: {
            script: scriptContent,
            media: mediaToUse,
            // TODO: Add other necessary input props for the Remotion composition
          },
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.details || 'Failed to initiate render');
      }

      const data = await response.json();
      setRenderId(data.renderId);
      setBucketName(data.bucketName);
      setAssemblyStatus('Rendering');

      toast("Render Initiated", {
        description: "Video rendering has started.",
      });

      // Start polling for render status
      pollingIntervalRef.current = setInterval(() => {
        checkRenderStatus(data.renderId, data.bucketName);
      }, 3000); // Poll every 3 seconds

    } catch (err) {
      console.error('Error initiating render:', err);
      setError(err.message);
      setAssemblyStatus('Error');
       toast("Render Initiation Failed", {
        description: `Error: ${err.message}`,
        variant: "destructive",
      });
    }
  };

  const checkRenderStatus = async (currentRenderId, currentBucketName) => {
    if (!currentRenderId || !currentBucketName) {
      console.warn("Missing renderId or bucketName for status check.");
      resetAssembly(); // Reset if identifiers are missing
      return;
    }

    try {
      const response = await fetch(`/api/render-lambda?renderId=${currentRenderId}&bucketName=${currentBucketName}`);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.details || 'Failed to check render status');
      }

      const data = await response.json();
      setAssemblyStatus(data.status); // 'rendering', 'done', or 'error'

      if (data.status === 'rendering') {
        setRenderProgress(data.progress);
      } else if (data.status === 'done') {
        setAssembledVideoUrl(data.url);
        if (onVideoAssembled) {
          onVideoAssembled(data.url);
        }
        toast("Render Complete", {
          description: "Your video is ready!",
        });
        resetAssembly(); // Stop polling and reset state
      } else if (data.status === 'error') {
        console.error('Render error details:', data.errors);
        setError(data.message || 'Video rendering failed.');
         toast("Render Failed", {
          description: data.message || 'Video rendering failed.',
          variant: "destructive",
        });
        resetAssembly(); // Stop polling and reset state
      }

    } catch (err) {
      console.error('Error checking render status:', err);
      setError(err.message);
      setAssemblyStatus('Error');
       toast("Status Check Failed", {
        description: `Error: ${err.message}`,
        variant: "destructive",
      });
      resetAssembly(); // Stop polling and reset state
    }
  };

  const resetAssembly = () => {
    if (pollingIntervalRef.current) {
      clearInterval(pollingIntervalRef.current);
    }
    setAssemblyStatus('Idle');
    setRenderId(null);
    setBucketName(null);
    setRenderProgress(0);
    setError(null); // Clear error on reset
  };


  return (
    <div className="space-y-4">
      <p>The video will be assembled automatically once the script and evaluated media are ready.</p>
      <p>Status: {assemblyStatus}</p>
      {assemblyStatus === 'Rendering' && (
        <p>Progress: {(renderProgress * 100).toFixed(2)}%</p>
      )}
       {error && (
        <div className="text-red-500 text-sm">{error}</div>
      )}
      {assembledVideoUrl && (
        <div>
          <h3 className="text-xl font-semibold mb-2">Assembled Video Preview</h3>
          <video src={assembledVideoUrl} controls className="w-full h-auto" />
        </div>
      )}
      {/* Optional: Add a button to manually trigger assembly if needed */}
       {assemblyStatus === 'Idle' && script && evaluatedMedia && evaluatedMedia.length > 0 && (
         <Button onClick={() => initiateAssembly(script, evaluatedMedia)} disabled={assemblyStatus !== 'Idle'}>
           Manually Start Assembly
         </Button>
       )}
        {assemblyStatus !== 'Idle' && assemblyStatus !== 'Complete' && assemblyStatus !== 'Error' && (
           <Button onClick={resetAssembly} variant="outline">
             Cancel Assembly
           </Button>
        )}
    </div>
  );
};

export default VideoAssembler;