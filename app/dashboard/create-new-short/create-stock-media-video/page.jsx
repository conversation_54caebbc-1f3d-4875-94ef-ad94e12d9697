"use client";

import React, { useState, useEffect, useContext } from 'react';
import { useRouter } from 'next/navigation';
import { useUser } from "@clerk/nextjs";
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Sparkles, Loader2 } from 'lucide-react';
import { toast } from 'sonner';
import { UserDetailContext } from "@/context/UserDetailContext";
import { triggerStockMediaVideoGeneration } from '@/actions/stockMediaGeneration';
import { VIDEO_GENERATION_COST } from '@/lib/creditUtils';

// Import components (we'll create these)
import StockMediaPreview from './_components/StockMediaPreview';

const CreateStockMediaVideoPage = () => {
  const router = useRouter();
  const { user, isSignedIn } = useUser();
  const { userDetail, isLoaded } = useContext(UserDetailContext);

  // Form state
  const [prompt, setPrompt] = useState('');
  const [projectTitle, setProjectTitle] = useState('');
  const [videoStyle, setVideoStyle] = useState('');
  const [duration, setDuration] = useState(30);
  const [aspectRatio, setAspectRatio] = useState('9:16');

  // Generation state
  const [isGenerating, setIsGenerating] = useState(false);
  const [generationMessage, setGenerationMessage] = useState('');

  // Validation
  const isGenerateButtonDisabled = !prompt.trim() || !userDetail || userDetail.credits < VIDEO_GENERATION_COST || isGenerating;

  const handleGenerateVideo = async () => {
    if (!prompt.trim() || !user?.id) {
      return;
    }

    setIsGenerating(true);
    setGenerationMessage('Initializing stock media video generation...');

    try {
      const videoConfig = {
        projectTitle,
        videoStyle,
        duration,
        aspectRatio
      };

      const result = await triggerStockMediaVideoGeneration({
        userPrompt: prompt,
        videoConfig
      });

      if (result.success) {
        console.log("Triggered stock media video generation successfully:", result.data.message);
        setGenerationMessage('Video generation initiated successfully!');
        toast.success(result.data.message + " Check your dashboard for status updates.");

        // Reset form after successful generation
        setTimeout(() => {
          setPrompt('');
          setProjectTitle('');
          setIsGenerating(false);
          setGenerationMessage('');
          router.push('/dashboard');
        }, 2000);
      } else {
        console.error("Failed to trigger stock media video generation:", result.error);
        toast.error("Failed to initiate video generation: " + result.error);
        setIsGenerating(false);
        setGenerationMessage('');
      }

    } catch (error) {
      console.error("Error triggering video generation:", error);
      toast.error("Failed to initiate video generation.");
      setIsGenerating(false);
      setGenerationMessage('');
    }
  };

  // Only render the page content if the user is signed in
  if (isSignedIn) {
    return (
      <div className="space-y-8 max-w-7xl mx-auto">
        {/* Header Section */}
        <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50 via-blue-50 to-purple-50 dark:from-purple-950/20 dark:via-blue-950/20 dark:to-purple-950/20 p-8 border border-border/50">
          <div className="relative z-10">
            <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-6">
              <div className="space-y-3">
                <h1 className="text-heading-1 text-gradient">
                  Stock Media Video Creator 🎬
                </h1>
                <p className="text-body-large text-muted-foreground max-w-2xl">
                  Create professional videos using AI-curated stock footage. Transform your ideas into compelling visual stories with automated media selection.
                </p>
                <div className="flex items-center gap-4 text-body-small text-muted-foreground">
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                    <span>Stock media API ready</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span>⚡</span>
                    <span>{userDetail?.credits || 0} credits available</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span>🎬</span>
                    <span>Cost: {VIDEO_GENERATION_COST} credits</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          {/* Background decoration */}
          <div className="absolute top-0 right-0 w-64 h-64 bg-gradient-to-br from-purple-400/10 to-blue-400/10 rounded-full blur-3xl"></div>
          <div className="absolute bottom-0 left-0 w-48 h-48 bg-gradient-to-tr from-blue-400/10 to-purple-400/10 rounded-full blur-3xl"></div>
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 xl:grid-cols-3 gap-8">
          {/* Left Column - Form Sections */}
          <div className="xl:col-span-2 space-y-6">

            {/* Section 1: Project Setup */}
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-br from-purple-50/50 to-blue-50/50 dark:from-purple-950/10 dark:to-blue-950/10 rounded-2xl"></div>
              <div className="relative bg-background/80 backdrop-blur-sm border border-border/50 rounded-2xl p-6">
                {/* Section Header */}
                <div className="flex items-center gap-3 mb-6 pb-4 border-b border-border/30">
                  <div className="p-2 bg-gradient-to-br from-purple-500 to-blue-500 rounded-lg shadow-soft">
                    <div className="h-6 w-6 text-white flex items-center justify-center text-lg">📝</div>
                  </div>
                  <div>
                    <h2 className="text-heading-2 text-gradient">Project Setup</h2>
                    <p className="text-body-small text-muted-foreground">Configure your video project details</p>
                  </div>
                </div>

                {/* Project Title */}
                <div className="space-y-3">
                  <Label htmlFor="projectTitle" className="text-body font-medium">
                    Project Title <span className="text-body-small text-muted-foreground">(Optional)</span>
                  </Label>
                  <Input
                    type="text"
                    id="projectTitle"
                    value={projectTitle}
                    onChange={(e) => setProjectTitle(e.target.value)}
                    placeholder="e.g., My Stock Media Video"
                    className="w-full h-12 px-4 rounded-lg border border-border bg-background text-body focus:border-primary focus:ring-2 focus:ring-primary/20 transition-colors"
                  />
                </div>
              </div>
            </div>

            {/* Section Connector */}
            <div className="flex justify-center">
              <div className="w-px h-8 bg-gradient-to-b from-purple-300 to-emerald-300 dark:from-purple-600 dark:to-emerald-600"></div>
            </div>

            {/* Section 2: Content Creation */}
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-br from-emerald-50/50 to-purple-50/50 dark:from-emerald-950/10 dark:to-purple-950/10 rounded-2xl"></div>
              <div className="relative bg-background/80 backdrop-blur-sm border border-border/50 rounded-2xl p-6">
                {/* Section Header */}
                <div className="flex items-center gap-3 mb-6 pb-4 border-b border-border/30">
                  <div className="p-2 bg-gradient-to-br from-emerald-500 to-purple-500 rounded-lg shadow-soft">
                    <Sparkles className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <h2 className="text-heading-2 text-gradient">Content Creation</h2>
                    <p className="text-body-small text-muted-foreground">Describe your video concept and let AI find the perfect stock media</p>
                  </div>
                </div>

                <div className="space-y-6">
                  {/* Video Prompt */}
                  <div className="space-y-3">
                    <Label htmlFor="prompt" className="text-body font-medium">Video Concept & Description</Label>
                    <Textarea
                      id="prompt"
                      placeholder="Describe your video concept in detail. For example: 'A motivational video about entrepreneurship featuring diverse business professionals, modern office environments, and success moments. The tone should be inspiring and professional with a focus on teamwork and innovation.'"
                      value={prompt}
                      onChange={(e) => setPrompt(e.target.value)}
                      rows={6}
                      className="min-h-[120px] text-body resize-none"
                      disabled={isGenerating}
                    />
                    <p className="text-body-small text-muted-foreground">
                      Be specific about the mood, setting, subjects, and style you want. This helps our AI select the most relevant stock footage.
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Section Connector */}
            <div className="flex justify-center">
              <div className="w-px h-8 bg-gradient-to-b from-emerald-300 to-blue-300 dark:from-emerald-600 dark:to-blue-600"></div>
            </div>

            {/* Section 3: Video Configuration */}
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-br from-blue-50/50 to-cyan-50/50 dark:from-blue-950/10 dark:to-cyan-950/10 rounded-2xl"></div>
              <div className="relative bg-background/80 backdrop-blur-sm border border-border/50 rounded-2xl p-6">
                {/* Section Header */}
                <div className="flex items-center gap-3 mb-6 pb-4 border-b border-border/30">
                  <div className="p-2 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-lg shadow-soft">
                    <div className="h-6 w-6 text-white flex items-center justify-center text-lg">⚙️</div>
                  </div>
                  <div>
                    <h2 className="text-heading-2 text-gradient">Video Configuration</h2>
                    <p className="text-body-small text-muted-foreground">Set video parameters and styling options</p>
                  </div>
                </div>

                <div className="space-y-6">
                  {/* Video Style */}
                  <div className="space-y-3">
                    <Label htmlFor="videoStyle" className="text-body font-medium">Video Style</Label>
                    <select
                      id="videoStyle"
                      value={videoStyle}
                      onChange={(e) => setVideoStyle(e.target.value)}
                      className="w-full h-12 px-4 rounded-lg border border-border bg-background text-body focus:border-primary focus:ring-2 focus:ring-primary/20 transition-colors"
                    >
                      <option value="">Select a style...</option>
                      <option value="cinematic">Cinematic</option>
                      <option value="corporate">Corporate</option>
                      <option value="lifestyle">Lifestyle</option>
                      <option value="documentary">Documentary</option>
                      <option value="artistic">Artistic</option>
                    </select>
                  </div>

                  {/* Duration and Aspect Ratio */}
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-3">
                      <Label htmlFor="duration" className="text-body-small font-medium">Duration (seconds)</Label>
                      <Input
                        id="duration"
                        type="number"
                        value={duration}
                        onChange={(e) => setDuration(parseInt(e.target.value) || 30)}
                        min="15"
                        max="120"
                        className="h-10"
                      />
                    </div>
                    <div className="space-y-3">
                      <Label htmlFor="aspectRatio" className="text-body-small font-medium">Aspect Ratio</Label>
                      <select
                        id="aspectRatio"
                        value={aspectRatio}
                        onChange={(e) => setAspectRatio(e.target.value)}
                        className="w-full h-10 px-3 rounded-lg border border-border bg-background text-body focus:border-primary focus:ring-2 focus:ring-primary/20 transition-colors"
                      >
                        <option value="9:16">9:16 (Vertical)</option>
                        <option value="16:9">16:9 (Horizontal)</option>
                        <option value="1:1">1:1 (Square)</option>
                      </select>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Right Column - Preview */}
          <div className="xl:col-span-1">
            <StockMediaPreview
              prompt={prompt}
              projectTitle={projectTitle}
              videoStyle={videoStyle}
              duration={duration}
              aspectRatio={aspectRatio}
              onGenerateVideo={handleGenerateVideo}
              isGenerating={isGenerating}
              generationMessage={generationMessage}
              isGenerateButtonDisabled={isGenerateButtonDisabled}
              userDetail={userDetail}
              VIDEO_GENERATION_COST={VIDEO_GENERATION_COST}
            />
          </div>
        </div>
      </div>
    );
  }

  // Show loading or sign-in prompt if not authenticated
  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="text-center">
        <h1 className="text-2xl font-bold mb-4">Please sign in to create videos</h1>
        <p className="text-muted-foreground">You need to be signed in to access the video creation tools.</p>
      </div>
    </div>
  );
};

export default CreateStockMediaVideoPage;
