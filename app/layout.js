import { Geist, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import { Providers } from './components/providers';

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata = {
  title: 'AI Reel Gen - Create Amazing AI-Powered Videos',
  description: 'Generate professional videos with AI-powered scripts, voiceovers, and dynamic visuals. Perfect for social media, marketing, and educational content.',
  keywords: ['AI video generation', 'video creation', 'AI scripts', 'voiceover', 'social media videos'],
  authors: [{ name: 'AI Reel Gen Team' }],
  viewport: 'width=device-width, initial-scale=1',
  themeColor: '#000000',
};

/**
 * Root layout component - Server Component
 *
 * This layout enables server-side rendering while delegating all client-side
 * providers (authentication, theming, context) to the Providers component.
 *
 * Benefits:
 * - Enables SSR for better performance and SEO
 * - Proper metadata configuration
 * - Font optimization at build time
 * - Maintains all existing provider functionality
 */
export default function RootLayout({ children }) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <Providers>
          {children}
        </Providers>
      </body>
    </html>
  );
}
