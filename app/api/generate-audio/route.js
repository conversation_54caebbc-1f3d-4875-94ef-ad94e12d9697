import { storage } from "@/lib/firebase";
import { getDownloadURL, ref, uploadBytes } from "firebase/storage";
import { v4 as uuidv4 } from "uuid";
import { NextResponse } from "next/server";

// Use dynamic import for ESM compatibility in Next.js API routes
let textToSpeechClient;
async function getTextToSpeechClient() {
  if (!textToSpeechClient) {
    const { TextToSpeechClient } = await import("@google-cloud/text-to-speech");
    textToSpeechClient = new TextToSpeechClient({
      apiKey: process.env.GOOGLE_CONSOLE_KEY,
    });
  }
  return textToSpeechClient;
}

export async function POST(req) {
  try {
    const { script, voice: clientVoiceId, audioSpeed = 1.0 } = await req.json(); // Expect 'voice' and 'audioSpeed'

    if (!script || typeof script !== "string" || !script.trim()) {
      return NextResponse.json(
        { error: "Missing or invalid 'script' in request body." },
        { status: 400 }
      );
    }
    if (
      !clientVoiceId ||
      typeof clientVoiceId !== "string" ||
      !clientVoiceId.trim()
    ) {
      return NextResponse.json(
        { error: "Missing or invalid 'voice' in request body." },
        { status: 400 }
      );
    }

    // Validate audioSpeed
    const parsedAudioSpeed = parseFloat(audioSpeed);
    if (isNaN(parsedAudioSpeed) || parsedAudioSpeed < 0.5 || parsedAudioSpeed > 2.0) {
      // Google TTS supports 0.25 to 4.0. Adjust range as needed.
      return NextResponse.json(
        { error: "Invalid 'audioSpeed' value. Must be a number between 0.5 and 2.0." },
        { status: 400 }
      );
    }

    // --- Voice Mapping ---
    // Map client-side voice IDs to Google TTS voice names
    // IMPORTANT: Replace these with actual, desired Google TTS voice names.
    const voiceMap = {
      alloy: "en-US-Chirp3-HD-Umbriel", // Example: Male, Standard
      echo: "en-US-Chirp3-HD-Gacrux", // Example: Female, Wavenet
      fable: "en-US-Chirp3-HD-Fenrir", // Example: Male, British English
      onyx: "en-US-Chirp3-HD-Enceladus", // Example: Male, Wavenet (Deep)
      nova: "en-US-Chirp3-HD-Leda", // Example: Female, Standard
      shimmer: "en-US-Chirp3-HD-Kore", // Example: Female, Australian English
      // Add more mappings as needed
    };

    const googleVoiceName = voiceMap[clientVoiceId] || "en-US-Standard-A"; // Fallback voice
    // --- End Voice Mapping ---

    const id = uuidv4();
    const storageRef = ref(storage, `audio/${id}.mp3`);

    const client = await getTextToSpeechClient();

    const request = {
      input: { text: script },
      // Use the mapped Google TTS voice name
      voice: { languageCode: "en-US", name: googleVoiceName }, // languageCode might also need to be dynamic if supporting multiple languages
      audioConfig: {
        audioEncoding: "MP3",
        speakingRate: parsedAudioSpeed, // Use the validated audioSpeed from client
      },
    };
    console.log(
      `[API Route] /api/generate-audio: Using Google TTS voice: ${googleVoiceName} for client voice ID: ${clientVoiceId} at speed: ${parsedAudioSpeed}`
    );

    const [response] = await client.synthesizeSpeech(request);

    if (!response.audioContent) {
      return NextResponse.json(
        { error: "Failed to synthesize speech." },
        { status: 500 }
      );
    }

    const audioBuffer = Buffer.from(response.audioContent, "base64");

    await uploadBytes(storageRef, audioBuffer, { contentType: "audio/mp3" });

    const audioUrl = await getDownloadURL(storageRef);

    return NextResponse.json({ audioUrl });
  } catch (error) {
    console.error("Error in /api/generate-audio:", error);
    return NextResponse.json(
      { error: "Internal server error." },
      { status: 500 }
    );
  }
}
