import { db } from '@/configs/db';
import { users, creditTransactions, videoData } from '@/configs/schema';
import { auth } from '@clerk/nextjs/server';
import { eq } from 'drizzle-orm';
import { inngest } from '@/app/inngest/client';

/**
 * POST /api/video/initiate-generation
 * Atomically debit credits and initiate video generation
 */
export async function POST(request) {
  try {
    // 1. Authentication
    const { userId } = await auth();
    if (!userId) {
      return Response.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // 2. Input validation
    const { videoConfig, workflowType } = await request.json();
    if (!videoConfig || !workflowType) {
      return Response.json({ error: 'Invalid input' }, { status: 400 });
    }

    // 3. Determine cost
    const costInCredits = getCostForWorkflow(workflowType, videoConfig);

    // 4. Atomic database transaction
    const result = await db.transaction(async (tx) => {
      // 4a. Fetch user with pessimistic lock
      const [user] = await tx
        .select()
        .from(users)
        .where(eq(users.clerkId, userId))
        .for('update'); // Pessimistic lock

      if (!user) {
        throw new Error('User not found');
      }

      // 4b. Verify sufficient credits
      if (user.currentCreditBalance < costInCredits) {
        throw new Error('Insufficient credits');
      }

      // 4c. Calculate new balance
      const newBalance = user.currentCreditBalance - costInCredits;

      // 4d. Update user balance
      await tx
        .update(users)
        .set({ 
          currentCreditBalance: newBalance,
          updatedAt: new Date()
        })
        .where(eq(users.clerkId, userId));

      // 4e. Insert credit transaction
      const [creditTransaction] = await tx
        .insert(creditTransactions)
        .values({
          userId,
          transactionType: 'DEBIT',
          amount: -costInCredits,
          balanceBefore: user.currentCreditBalance,
          balanceAfter: newBalance,
          relatedEntityType: 'VIDEO',
          notes: `Video generation: ${workflowType}`,
          metadata: { workflowType, videoConfig }
        })
        .returning();

      // 4f. Insert video record
      const [video] = await tx
        .insert(videoData)
        .values({
          clerkId: userId,
          title: videoConfig.projectTitle || `${workflowType} Video`,
          topic: videoConfig.topic || '',
          script: videoConfig.script || '',
          videoStyle: videoConfig.videoStyle || '',
          aspectRatio: videoConfig.aspectRatio || '9:16',
          templateId: videoConfig.templateId || workflowType,
          voice: videoConfig.voice || '',
          captionName: videoConfig.captionName || '',
          captionStyleJson: videoConfig.captionStyleJson || null,
          captionContainerStyle: videoConfig.captionContainerStyle || null,
          audioSpeed: videoConfig.audioSpeed || 1.0,
          backgroundMusic: videoConfig.backgroundMusic || '',
          estimatedDurationSeconds: videoConfig.estimatedDurationSeconds || 60,
          costInCredits,
          creditTransactionId: creditTransaction.id,
          status: 'Pending',
          workflow_data: {
            workflowType,
            ...videoConfig
          }
        })
        .returning();

      return { video, creditTransaction, newBalance };
    });

    // 5. Enqueue Inngest job
    await inngest.send({
      name: getInngestEventName(workflowType),
      data: {
        videoId: result.video.id,
        userId,
        workflowType,
        ...videoConfig
      }
    });

    return Response.json({
      success: true,
      videoId: result.video.id,
      creditsRemaining: result.newBalance,
      eventId: `video-${result.video.id}`
    }, { status: 202 });

  } catch (error) {
    console.error('Video generation initiation failed:', error);
    
    if (error.message === 'Insufficient credits') {
      return Response.json({ error: 'Insufficient credits' }, { status: 402 });
    }
    
    return Response.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * Helper function to determine cost based on workflow type and configuration
 */
function getCostForWorkflow(workflowType, config) {
  const baseCosts = {
    'AI_VIDEO': 5,
    'MEME_VIDEO': 2,
    'UGC_VIDEO': 8,
    'NARRATOR_VIDEO': 4,
    'REDDIT_VIDEO': 3,
    'TWITTER_VIDEO': 3,
    'STOCK_MEDIA_VIDEO': 6
  };
  
  let cost = baseCosts[workflowType] || 5;
  
  // Adjust based on configuration
  if (config.estimatedDurationSeconds && config.estimatedDurationSeconds > 60) {
    cost += Math.ceil((config.estimatedDurationSeconds - 60) / 30);
  }
  
  return cost;
}

/**
 * Helper function to get the correct Inngest event name for each workflow type
 */
function getInngestEventName(workflowType) {
  const eventMap = {
    'AI_VIDEO': 'app/ai-video.generate',
    'MEME_VIDEO': 'app/meme-video.generate',
    'UGC_VIDEO': 'app/ai-ugc-video.generate',
    'NARRATOR_VIDEO': 'app/narrator.video.generate',
    'REDDIT_VIDEO': 'app/reddit-video.generate',
    'TWITTER_VIDEO': 'app/twitter-video.generate',
    'STOCK_MEDIA_VIDEO': 'generate-stock-media-video-event' // Special event name for stock media
  };

  return eventMap[workflowType] || 'app/ai-video.generate';
}
