import { db } from "@/configs/db"; // Adjust the import path as needed
import { videoData } from "@/configs/schema"; // Adjust the import path as needed
import { eq, and } from "drizzle-orm"; // Import 'and' operator
import { NextResponse } from "next/server";
import { auth } from '@clerk/nextjs/server'; // Import Clerk auth

export async function GET(req, { params }) {
    // --- Authentication ---
    let userId;
    try {
        const authResult = await auth();
        userId = authResult.userId;
    } catch (error) {
        console.error("[API Auth Error] /api/video/[videoId]: Error during authentication:", error);
        // Refined 500 error for auth failure
        return NextResponse.json({ error: 'Authentication error occurred.' }, { status: 500 });
    }

    if (!userId) {
        console.warn('[API Route Error] /api/video/[videoId]: Unauthorized access attempt.');
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // --- End Authentication ---

    // Await params before accessing properties
    const awaitedParams = await params;
    const { videoId: videoIdParam } = awaitedParams; // Get videoId from route params

    // --- Validate videoId format ---
    if (!videoIdParam) {
        // This case should ideally be handled by routing itself if the parameter is mandatory
        // but good to have a fallback check.
        return NextResponse.json({ error: "Video ID path parameter is missing" }, { status: 400 });
    }

    const parsedVideoId = parseInt(videoIdParam, 10); // Always specify radix 10

    // ** 2. Improved parseInt Handling **
    if (isNaN(parsedVideoId)) {
        console.warn(`[API Route Warn] Invalid video ID format received: ${videoIdParam}`);
        return NextResponse.json({ error: 'Invalid Video ID format. Must be an integer.' }, { status: 400 });
    }
    // --- End Validation ---


    try {
        console.log(`[API Route] User ${userId} requesting video ID: ${parsedVideoId}`);

        // Fetch the video data by ID, ensuring it belongs to the authenticated user
        const videoResult = await db.select()
            .from(videoData)
            .where(and(
                eq(videoData.id, parsedVideoId), // Use the parsed integer ID
                eq(videoData.clerkId, userId)    // Check ownership
            ))
            .limit(1); // Expecting only one result

        if (!videoResult || videoResult.length === 0) {
            // Log whether the video exists but belongs to another user, or doesn't exist at all
            console.warn(`[API Route Warn] Video not found or access denied for user ${userId}, video ID: ${parsedVideoId}`);
            return NextResponse.json({ error: "Video not found or access denied" }, { status: 404 });
        }

        console.log(`[API Route] Successfully fetched video ID ${parsedVideoId} for user ${userId}`);
        return NextResponse.json({ video: videoResult[0] }, { status: 200 });

    } catch (error) {
        // Log the detailed error for server-side debugging
        console.error(`[API Route Error] Error fetching video ID ${parsedVideoId} for user ${userId}:`, error);

        // ** 1. Generic 500 Error Message **
        // Return a generic error message to the client for unexpected server errors
        return NextResponse.json(
            { error: "An internal server error occurred while fetching the video." },
            { status: 500 }
        );
    }
}