import { db } from '@/configs/db';
import { users, creditTransactions, videoData } from '@/configs/schema';
import { eq, and } from 'drizzle-orm';

/**
 * POST /api/video/refund-credits
 * Refund credits for failed video generation
 * This endpoint is called internally by Inngest workflows
 */
export async function POST(request) {
  try {
    // 1. Internal authentication (from Inngest)
    const authHeader = request.headers.get('authorization');
    if (authHeader !== `Bearer ${process.env.INNGEST_SIGNING_KEY}`) {
      return Response.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // 2. Input validation
    const { videoId, reason } = await request.json();
    if (!videoId || !reason) {
      return Response.json({ error: 'Invalid input' }, { status: 400 });
    }

    // 3. Atomic refund transaction
    const result = await db.transaction(async (tx) => {
      // 3a. Fetch video record
      const [video] = await tx
        .select()
        .from(videoData)
        .where(eq(videoData.id, videoId));

      if (!video) {
        throw new Error('Video not found');
      }

      if (video.status === 'failed_refunded') {
        throw new Error('Video already refunded');
      }

      // 3b. Fetch user with pessimistic lock
      const [user] = await tx
        .select()
        .from(users)
        .where(eq(users.clerkId, video.clerkId))
        .for('update');

      if (!user) {
        throw new Error('User not found');
      }

      // 3c. Calculate new balance
      const refundAmount = video.costInCredits;
      const newBalance = user.currentCreditBalance + refundAmount;

      // 3d. Update user balance
      await tx
        .update(users)
        .set({ 
          currentCreditBalance: newBalance,
          updatedAt: new Date()
        })
        .where(eq(users.clerkId, video.clerkId));

      // 3e. Insert refund transaction
      const [refundTransaction] = await tx
        .insert(creditTransactions)
        .values({
          userId: video.clerkId,
          transactionType: 'REFUND',
          amount: refundAmount,
          balanceBefore: user.currentCreditBalance,
          balanceAfter: newBalance,
          relatedEntityId: videoId.toString(),
          relatedEntityType: 'VIDEO',
          notes: `Refund for failed video generation: ${reason}`,
          metadata: { 
            originalTransactionId: video.creditTransactionId, 
            reason,
            workflowType: video.workflow_data?.workflowType
          }
        })
        .returning();

      // 3f. Update video status
      await tx
        .update(videoData)
        .set({ 
          status: 'failed_refunded',
          refundTransactionId: refundTransaction.id,
          updatedAt: new Date()
        })
        .where(eq(videoData.id, videoId));

      return { refundTransaction, newBalance };
    });

    console.log(`Credits refunded: ${result.refundTransaction.amount} credits returned to user for video ${videoId}`);

    return Response.json({
      success: true,
      refundAmount: result.refundTransaction.amount,
      newBalance: result.newBalance
    });

  } catch (error) {
    console.error('Credit refund failed:', error);
    
    if (error.message === 'Video not found' || error.message === 'Video already refunded') {
      return Response.json({ error: error.message }, { status: 404 });
    }
    
    return Response.json({ error: 'Internal server error' }, { status: 500 });
  }
}
