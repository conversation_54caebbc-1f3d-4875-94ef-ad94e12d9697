// app/api/pixabay-search/route.js
import { NextResponse } from 'next/server';

export async function GET(request) {
  const { searchParams } = new URL(request.url);
  const query = searchParams.get('query');

  if (!query) {
    return NextResponse.json({ error: 'Missing search query' }, { status: 400 });
  }

  const pixabayApiKey = process.env.PIXABAY_API_KEY;

  if (!pixabayApiKey) {
    return NextResponse.json({ error: 'Pixabay API key not configured' }, { status: 500 });
  }

  const url = `https://pixabay.com/api/?key=${pixabayApiKey}&q=${encodeURIComponent(query)}&image_type=photo&per_page=15`;

  try {
    const response = await fetch(url);

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Pixabay API error: ${response.status} ${response.statusText} - ${errorText}`);
    }

    const data = await response.json();
    // Pixabay returns 'hits' instead of 'photos' and different structure
    // We might need to map this later if the display component expects a specific format
    return NextResponse.json(data);

  } catch (error) {
    console.error('Error fetching from Pixabay:', error);
    return NextResponse.json({ error: 'Failed to fetch images from Pixabay' }, { status: 500 });
  }
}