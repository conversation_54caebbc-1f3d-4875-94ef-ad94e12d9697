import { NextResponse } from 'next/server';
import { db } from '@/configs/db';
import { stockMediaVideo } from '@/configs/schema';
import { eq } from 'drizzle-orm';

export async function GET(request, { params }) {
  try {
    const id = params.id;
    
    if (!id) {
      return NextResponse.json(
        { error: 'Video ID is required' },
        { status: 400 }
      );
    }

    // Fetch the stock media video by ID
    const [video] = await db
      .select()
      .from(stockMediaVideo)
      .where(eq(stockMediaVideo.id, id))
      .limit(1);

    if (!video) {
      return NextResponse.json(
        { error: 'Video not found' },
        { status: 404 }
      );
    }

    // Fields like 'scenes', 'media', and 'render_settings' are likely already parsed by Drizzle for jsonb types.
    // If they are indeed strings, then JSON.parse is needed. Otherwise, direct assignment is fine.
    // For safety, we'll check the type before parsing, or assume they are parsed if using jsonb.
    const parsedVideo = {
      ...video,
      // Assuming Drizzle already parses jsonb fields to objects/arrays
      scenes: video.scenes || [], 
      media: video.media || [],
      render_settings: video.render_settings || {},
    };

    return NextResponse.json(parsedVideo);
  } catch (error) {
    console.error('Error fetching stock media video:', error);
    return NextResponse.json(
      { error: 'Failed to fetch video data' },
      { status: 500 }
    );
  }
}
