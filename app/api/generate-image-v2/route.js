import { Runware } from "@runware/sdk-js";
import { NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { hasSufficientCredits, VIDEO_GENERATION_COST } from '@/lib/creditUtils'; // Import credit utility

const runwareApiKey = process.env.RUNWERE_IMAGE_API;

// Instantiate Runware SDK
let runware;
if (runwareApiKey) {
  try {
    runware = new Runware({ apiKey: runwareApiKey });
    console.log("✅ Runware SDK initialized successfully.");
  } catch (error) {
    console.error("❌ Failed to initialize Runware SDK:", error);
    runware = null;
  }
} else {
  console.error("❌ RUNWERE_IMAGE_API key is not set in environment variables.");
  runware = null;
}

export async function POST(request) {
  // --- Authentication ---
  const { userId } = await auth();
  if (!userId) {
    console.warn('[API Route Error] /api/generate-image-v2: Unauthorized access attempt.');
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  // --- End Authentication ---

  if (!runware) {
    console.error("❌ Runware SDK not initialized. Check API key.");
    return NextResponse.json({ error: "Image generation service is unavailable." }, { status: 503 });
  }

  try {
    const { imagePrompt } = await request.json();
    const defaultModelId = 'runware:100@1';
    const defaultWidth = 640;
    const defaultHeight = 1152;

    // --- Input Validation ---
    if (!imagePrompt || typeof imagePrompt !== 'string') {
      console.warn("⚠️ Invalid imagePrompt:", imagePrompt);
      return NextResponse.json({ error: "Invalid image prompt." }, { status: 400 });
    }
    // --- End Input Validation ---

    // --- Credit Check ---
    console.log(`[API Route] User ${userId} requesting image generation (v2).`);
    const sufficientCredits = await hasSufficientCredits(userId, VIDEO_GENERATION_COST);
    if (!sufficientCredits) {
        console.warn(`[API Route Error] User ${userId} has insufficient credits for video generation (cost: ${VIDEO_GENERATION_COST}).`);
        return NextResponse.json({ error: "Insufficient credits for video generation." }, { status: 402 }); // 402 Payment Required
    }
    // --- End Credit Check ---

    console.log(`[Runware API] Requesting image generation for prompt: "${imagePrompt}" using model ${defaultModelId}`);

    // --- Call Runware API ---
    const imagesResponse = await runware.requestImages({
      positivePrompt: imagePrompt,
      width: defaultWidth,
      height: defaultHeight,
      model: defaultModelId,
      numberResults: 1,
      outputType: "URL",
      outputFormat: "WEBP",
    });

    console.log(`[Runware API] Response received:`, imagesResponse);

    // --- Process Response ---
    // Only one prompt, only one image expected
    if (!imagesResponse || !Array.isArray(imagesResponse) || imagesResponse.length === 0) {
      console.error("❌ Runware API returned no images.");
      return NextResponse.json({ error: "Image generation failed: No images returned." }, { status: 500 });
    }

    const firstImageObj = imagesResponse[0];
    const imageUrl = firstImageObj && (firstImageObj.imageURL || firstImageObj.imageUrl);

    if (!imageUrl) {
      console.error("❌ Runware API response did not contain a valid image URL:", imagesResponse);
      return NextResponse.json({ error: "Image generation failed: Could not retrieve image URL." }, { status: 500 });
    }

    // Return only the image URL as 'image' in the response
    return NextResponse.json({ image: imageUrl }, { status: 200 });

  } catch (error) {
    console.error("❗ Unexpected Error in Runware POST handler:", error);
    const errorMessage = error.message || "Internal server error during image generation.";
    const statusCode = error.statusCode || 500;
    return NextResponse.json({ error: errorMessage }, { status: statusCode });
  }
}