// app/api/evaluate-media/route.js
import { NextResponse } from 'next/server';
import { GoogleGenerativeAI } from '@google/generative-ai'; // Import Google Generative AI library

// Initialize Gemini client with the latest model
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);
const model = genAI.getGenerativeModel({ 
  model: "gemini-1.5-pro-latest", // Reverting to more stable model
  generationConfig: {
    maxOutputTokens: 2048,
    temperature: 0.3, // Lower temperature for more consistent results
  },
});

export async function POST(request) {
  try {
    const { script, media } = await request.json(); // script contains scene details, media is array of candidates

    if (!script || !media || !Array.isArray(media) || media.length === 0) {
      return NextResponse.json({ error: 'Invalid request body. Requires script and non-empty media array.' }, { status: 400 });
    }

    console.log('Received data for media evaluation:', { script: { desired_visual_concept: script.desired_visual_concept, evaluation_criteria: script.evaluation_criteria }, mediaCount: media.length });

    // Construct the prompt for the AI
    const promptParts = [
      { text: `Evaluate the following images/videos based on the desired visual concept and evaluation criteria for a video scene.` },
      { text: `Desired Visual Concept: ${script.desired_visual_concept}` },
      { text: `Evaluation Criteria: ${script.evaluation_criteria.join(', ')}` },
      { text: `Here are the media candidates:` },
    ];

    // Add each media candidate as an image/video part to the prompt
    // Note: Gemini's vision capabilities work best with image data or Google Cloud Storage URIs.
    // Directly using external URLs might not be supported or reliable for all models/versions.
    // For a robust implementation, you might need to download the media first or use a service
    // that provides GCS URIs or base64 encoding. For this example, we'll include URLs in text,
    // and rely on the model's ability to interpret them in context, which may vary.
    media.forEach((item, index) => {
      promptParts.push({ text: `\nCandidate ${index + 1} (URL: ${item.url}, Type: ${item.type || 'unknown'}):` });
      // If using base64 or GCS URIs, you would add image/video parts like this:
      // if (item.type === 'image' && item.base64Data) {
      //   promptParts.push({ inlineData: { mimeType: 'image/jpeg', data: item.base64Data } });
      // } else if (item.type === 'video' && item.gcsUri) {
      //   promptParts.push({ fileData: { mimeType: 'video/mp4', uri: item.gcsUri } });
      // }
    });

    promptParts.push({ text: `\nBased on the concept and criteria, which single media candidate (specify by its Candidate number) is the best fit? Provide only the Candidate number.` });


    // Call the Gemini API with better error handling
    let responseText;
    try {
      const result = await model.generateContent({
        contents: [{ 
          role: "user", 
          parts: promptParts 
        }],
      });
      
      if (!result || !result.response) {
        throw new Error("No response received from Gemini API");
      }
      
      responseText = result.response.text();
      
      if (!responseText) {
        throw new Error("AI response was empty");
      }
      
      console.log("Gemini API Response:", responseText);
      
    } catch (error) {
      console.error("Error calling Gemini API:", error);
      throw new Error(`Failed to evaluate media: ${error.message}`);
    }

    console.log("AI Evaluation Raw Response:", responseText);

    // Parse the AI's response to find the selected candidate number
    let selectedCandidateIndex = 0; // Default to first candidate
    
    try {
      // First try to find a candidate number in the response
      const candidateNumberMatch = responseText.match(/Candidate\s*(\d+)/i);
      
      if (candidateNumberMatch && candidateNumberMatch[1]) {
        const candidateNum = parseInt(candidateNumberMatch[1], 10);
        if (!isNaN(candidateNum) && candidateNum > 0 && candidateNum <= media.length) {
          selectedCandidateIndex = candidateNum - 1;
          console.log(`Selected candidate ${candidateNum} based on AI response`);
        } else {
          console.warn(`AI returned invalid candidate number: ${candidateNum}. Using first candidate.`);
        }
      } else {
        console.warn("No valid candidate number found in AI response. Using first candidate.");
      }
    } catch (parseError) {
      console.error("Error parsing AI response:", parseError);
      // Fall back to first candidate
      selectedCandidateIndex = 0;
    }

    // Ensure the selected index is within bounds
    if (selectedCandidateIndex < 0 || selectedCandidateIndex >= media.length) {
      console.warn(`Selected candidate index ${selectedCandidateIndex} is out of bounds. Using first candidate.`);
      selectedCandidateIndex = 0;
    }
    
    if (!media[selectedCandidateIndex]) {
      throw new Error(`Failed to select valid media. No media available at index ${selectedCandidateIndex}`);
    }

    const selectedMedia = media[selectedCandidateIndex];

    console.log('Media evaluation complete. Selected media:', selectedMedia);
    return NextResponse.json({ selectedMedia });

  } catch (error) {
    console.error('Error during media evaluation:', error);
    return NextResponse.json({ error: error.message || 'Failed to evaluate media using AI.' }, { status: 500 });
  }
}
