import { GoogleGenerativeAI } from "@google/generative-ai";
import { NextResponse } from "next/server";
import { auth } from '@clerk/nextjs/server'; // Import Clerk auth
import { hasSufficientCredits, VIDEO_GENERATION_COST } from '@/lib/creditUtils'; // Import credit utility

// --- Configuration ---
// Define a reasonable maximum length for the input script
const MAX_SCRIPT_LENGTH = 10000; // Example: 10,000 characters
// --- End Configuration ---

const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);

export async function POST(req) {
    // --- Authentication ---
    let userId;
    try {
        const authResult = await auth();
        userId = authResult.userId;
    } catch (error) {
        console.error("[API Auth Error] /api/generate-image-prompt: Error during authentication:", error);
        // Refined 500 error (though auth might prefer a 401/500 depending on failure)
        return NextResponse.json({ error: 'Authentication processing failed.' }, { status: 500 });
    }

    if (!userId) {
        console.warn('[API Route Error] /api/generate-image-prompt: Unauthorized access attempt.');
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // --- End Authentication ---
  try {
    const { scriptText, videoStyle } = await req.json();

    // --- Input Validation ---
    if (!scriptText || typeof scriptText !== 'string') { // Check presence and type
      return NextResponse.json({ error: "scriptText is required and must be a string." }, { status: 400 });
    }
    if (!videoStyle || typeof videoStyle !== 'string') { // Check presence and type for videoStyle
      return NextResponse.json({ error: "videoStyle is required and must be a string." }, { status: 400 });
    }

    // *** 2. Add Input Length Validation ***
    if (scriptText.length > MAX_SCRIPT_LENGTH) {
        console.warn(`[API Route Warning] /api/generate-image-prompt: Input script length (${scriptText.length}) exceeds maximum (${MAX_SCRIPT_LENGTH}).`);
        return NextResponse.json(
            { error: `Input script exceeds maximum length of ${MAX_SCRIPT_LENGTH} characters.` },
            // 413 Payload Too Large is appropriate here
            { status: 413 }
        );
    }
    // --- End Input Validation ---

    // --- Credit Check ---
    console.log(`[API Route] User ${userId} requesting image prompt generation.`);
    const sufficientCredits = await hasSufficientCredits(userId, VIDEO_GENERATION_COST);
    if (!sufficientCredits) {
        console.warn(`[API Route Error] User ${userId} has insufficient credits for video generation (cost: ${VIDEO_GENERATION_COST}).`);
        return NextResponse.json({ error: "Insufficient credits for video generation." }, { status: 402 }); // 402 Payment Required
    }
    // --- End Credit Check ---

    if (!process.env.GEMINI_API_KEY) {
        console.error("[API Config Error] /api/generate-image-prompt: GEMINI_API_KEY is not set.");
         // Refined 500 error
        return NextResponse.json({ error: "Server configuration error." }, { status: 500 });
    }

    // Choose a model that's appropriate for your use case.
    const model = genAI.getGenerativeModel({ model: "gemini-1.5-flash-latest"});

    const prompt = `Generate a list of concise image prompts for the following video script. Each prompt should describe a visual scene or concept from the script, keeping in mind the video style: "${videoStyle}". Return only the list of prompts, with each prompt on a new line. Do not include any introductory or concluding text.

Video Style: ${videoStyle}

Script:
${scriptText}`;

    console.log("[API Info] /api/generate-image-prompt: Calling Gemini API with video style:", videoStyle);
    const result = await model.generateContent(prompt);
    const response = await result.response;
    const text = response.text();
    console.log("[API Info] /api/generate-image-prompt: Received response from Gemini.");


    // Split the response into individual prompts based on newlines
    const imagePrompts = text.split('\n').map(p => p.trim()).filter(p => p.length > 0);

    return NextResponse.json({ imagePrompts }, { status: 200 });

  } catch (error) {
    // *** 1. Refine 500 Error Response ***
    // Log the detailed error for server-side debugging
    console.error("[API Error] /api/generate-image-prompt: Error generating image prompts:", error);
    // Return a generic error message to the client
    return NextResponse.json(
        { error: "An internal server error occurred while generating image prompts." },
        { status: 500 }
    );
  }
}