// /api/generate-caption (or ideally /api/v1/generate-caption)
import { createClient } from "@deepgram/sdk";
import { NextResponse } from "next/server";
import { auth } from '@clerk/nextjs/server';
import dotenv from "dotenv";
import { hasSufficientCredits, VIDEO_GENERATION_COST } from '@/lib/creditUtils'; // Import credit utility

dotenv.config(); // Ensure environment variables are loaded

// --- Configuration ---
// Consider moving these to environment variables or a config file if they might change
const DEEPGRAM_MODEL = "nova-2"; // Or "nova-3", "base", "enhanced", etc.
const DEEPGRAM_LANGUAGE = "en-US";
// --- End Configuration ---

export async function POST(req) {
    // --- Authentication ---
    let userId;
    try {
        const authResult = await auth();
        userId = authResult.userId; // Extract userId safely
    } catch (error) {
        // Handle potential errors during auth() call if necessary
        console.error("[API Auth Error] /api/generate-caption: Error during authentication:", error);
        return NextResponse.json({ error: 'Authentication error' }, { status: 500 });
    }

    if (!userId) {
        console.warn('[API Route Error] /api/generate-caption: Unauthorized access attempt.');
        // Use NextResponse for consistency
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // --- End Authentication ---

    try {
        const { audioUrl } = await req.json();

        // --- Input Validation ---
        if (!audioUrl || typeof audioUrl !== 'string') {
            return NextResponse.json(
                { error: "audioUrl is required and must be a string." },
                { status: 400 }
            );
        }

        // Basic URL format validation
        try {
            new URL(audioUrl); // Throws TypeError if invalid format
        } catch (e) {
            return NextResponse.json(
                { error: "Invalid audioUrl format." },
                { status: 400 }
            );
        }
        // --- End Input Validation ---

        // --- Credit Check ---
        console.log(`[API Route] User ${userId} requesting caption generation.`);
        const sufficientCredits = await hasSufficientCredits(userId, VIDEO_GENERATION_COST);
        if (!sufficientCredits) {
            console.warn(`[API Route Error] User ${userId} has insufficient credits for video generation (cost: ${VIDEO_GENERATION_COST}).`);
            return NextResponse.json({ error: "Insufficient credits for video generation." }, { status: 402 }); // 402 Payment Required
        }
        // --- End Credit Check ---

        // --- Rate Limiting (Placeholder - Highly Recommended!) ---
        // You MUST implement rate limiting here to prevent abuse and control costs.
        // Example using a hypothetical rate limiter:
        // const { success } = await rateLimiter.limit(userId); // Or use IP address if needed
        // if (!success) {
        //     return NextResponse.json({ error: 'Rate limit exceeded' }, { status: 429 });
        // }
        // Libraries like @upstash/ratelimit are good options.
        // --- End Rate Limiting Placeholder ---


        // --- Transcription Logic ---
        if (!process.env.DEEPGRAM_API_KEY) {
            console.error("[API Config Error] /api/generate-caption: DEEPGRAM_API_KEY is not set.");
            return NextResponse.json({ error: "Server configuration error." }, { status: 500 });
        }

        const deepgram = createClient(process.env.DEEPGRAM_API_KEY);

        console.log(`[API Info] /api/generate-caption: Requesting transcription for URL: ${audioUrl.substring(0, 50)}...`); // Log sanitized URL

        const source = { url: audioUrl };
        const options = {
            model: DEEPGRAM_MODEL,
            language: DEEPGRAM_LANGUAGE,
            punctuate: true,      // Automatically add punctuation
            smart_format: true,   // Apply formatting (numbers, dates etc.)
            // utterances: true, // Optionally get utterance-level timestamps
            // diarize: true,   // Optionally detect different speakers (usually requires tier change)
        };

        const { result, error: deepgramError } = await deepgram.listen.prerecorded.transcribeUrl(
            source,
            options
        );

        if (deepgramError) {
            console.error("[API Deepgram Error] /api/generate-caption:", deepgramError);
            // Avoid leaking detailed Deepgram errors to the client
            return NextResponse.json({ error: "Failed to process audio transcription." }, { status: 502 }); // 502 Bad Gateway might be appropriate
        }

        if (!result) {
             console.error("[API Deepgram Error] /api/generate-caption: No result received from Deepgram.");
             return NextResponse.json({ error: "Failed to get transcription result." }, { status: 502 });
        }

        // Extract words with timestamps (adjust path based on Deepgram's response structure if needed)
        const words = result.results?.channels?.[0]?.alternatives?.[0]?.words?.map(word => ({
            text: word.word,
            start: word.start * 1000, // Convert seconds to milliseconds
            end: word.end * 1000,     // Convert seconds to milliseconds
            // confidence: word.confidence // Optional: include confidence score
        })) ?? []; // Use nullish coalescing for safety

        console.log(`[API Info] /api/generate-caption: Successfully transcribed ${words.length} words.`);

        // Return the accumulated words
        return NextResponse.json({ result: words }); // Use default 200 OK status

    } catch (error) {
        // Catch unexpected errors (e.g., JSON parsing issues, runtime errors)
        console.error("[API Error] /api/generate-caption: Unexpected error:", error);
        // Return a generic server error message
        return NextResponse.json(
            { error: "An internal server error occurred." },
            { status: 500 }
        );
    }
}