import { NextResponse } from 'next/server';
import { GoogleGenerativeAI } from '@google/generative-ai'; // Import Google Generative AI library

// Initialize Gemini client
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY); // Ensure your Gemini API key is in your environment variables

export async function POST(request) {
  try {
    const { prompt } = await request.json();

    if (!prompt) {
      return NextResponse.json({ error: 'Missing prompt' }, { status: 400 });
    }

    console.log('Received prompt for stock media script generation:', prompt);

    // Construct the prompt for the AI
    const aiPrompt = `Generate a script for a short video based on the following topic: "${prompt}".
    Break the script into scenes. For each scene, provide:
    - scene_number: The sequential number of the scene.
    - script_text: The dialogue or narration for the scene.
    - desired_visual_concept: A description of the visual style or content for the scene.
    - search_keywords_for_stock_api: An array of keywords suitable for searching stock photo/video APIs (like Pexels or Pixabay).
    - evaluation_criteria: An array of criteria to evaluate potential stock media against.

    Format the output as a JSON object with 'script' (the full script text) and 'scenes' (an array of scene objects). Ensure the JSON is valid and can be parsed directly.`;

    // Get the generative model
    const model = genAI.getGenerativeModel({ model: "gemini-1.5-flash-latest" }); // Use a suitable Gemini model

    // Generate content
    const result = await model.generateContent(aiPrompt);
    const response = result.response;
    const responseText = response.text();

    if (!responseText) {
        throw new Error("AI did not return any content.");
    }

    // Attempt to parse the AI's JSON response
    let generatedScriptAndScenes;
    try {
        // Gemini might return markdown code block, try to extract JSON
        const jsonMatch = responseText.match(/```json\n([\s\S]*?)\n```/);
        if (jsonMatch && jsonMatch[1]) {
            generatedScriptAndScenes = JSON.parse(jsonMatch[1]);
        } else {
            // If no markdown block, try parsing directly
            generatedScriptAndScenes = JSON.parse(responseText);
        }
    } catch (parseError) {
        console.error("Failed to parse AI response as JSON:", parseError);
        console.error("AI Response Text:", responseText); // Log the raw response for debugging
        throw new Error("AI returned invalid or unparseable JSON.");
    }

    // Validate the structure of the AI's response
    if (!generatedScriptAndScenes || !generatedScriptAndScenes.script || !Array.isArray(generatedScriptAndScenes.scenes)) {
         console.error("AI response has unexpected structure:", generatedScriptAndScenes);
         throw new Error("AI response has an invalid structure.");
    }


    console.log('Stock media script generation complete.');
    return NextResponse.json(generatedScriptAndScenes);

  } catch (error) {
    console.error('Error during stock media script generation:', error);
    return NextResponse.json({ error: error.message || 'Failed to generate stock media script.' }, { status: 500 });
  }
}
