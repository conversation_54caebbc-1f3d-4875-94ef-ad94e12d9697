import { db } from '@/configs/db';
import { users } from '@/configs/schema';
import { eq } from 'drizzle-orm';
import { NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server'; // Import Clerk auth

export async function GET(request) {
    // --- Authentication ---
    let userId;
    try {
        // Get the authenticated user's ID directly from Clerk's auth()
        const authResult = await auth();
        userId = authResult.userId;
    } catch (error) {
        // Handle potential errors during auth() call if necessary
        console.error("[API Auth Error] /api/get-credits: Error during authentication:", error);
        return NextResponse.json({ error: 'Authentication error' }, { status: 500 });
    }

    // Check if a user ID was actually obtained (user is authenticated)
    if (!userId) {
        console.warn('[API Route Error] /api/get-credits: Unauthorized access attempt (no userId found in session).');
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // --- End Authentication ---

    try {
        // No need to parse query parameters for clerkId anymore

        console.log(`[API Route] /api/get-credits: Fetching credits for authenticated user: ${userId}`);

        // Fetch the user's credits using the authenticated userId from the session
        const userResult = await db.select({
            credits: users.credits,
        }).from(users).where(eq(users.clerkId, userId)).limit(1); // Directly use authenticated userId

        // Check if the user exists in the database
        if (!userResult || userResult.length === 0) {
            console.warn(`[API Route Warning] /api/get-credits: Authenticated user ${userId} not found in DB.`);
            // Return 404 Not Found if the authenticated user doesn't have a record in your DB
            return NextResponse.json({ error: 'User data not found' }, { status: 404 });
        }

        // Return the user's credits
        return NextResponse.json({ credits: userResult[0].credits });

    } catch (error) {
        // Catch unexpected errors (e.g., database connection issues)
        console.error(`[API Route Error] /api/get-credits: Error fetching credits for user ${userId}:`, error);

        // Return a structured error response with fallback credits
        const errorResponse = {
            error: 'An internal server error occurred while fetching credits.',
            credits: 0, // Fallback value
            success: false,
            details: error.message || 'Unknown error'
        };

        return NextResponse.json(errorResponse, {
            status: 500,
            headers: {
                'Content-Type': 'application/json'
            }
        });
    }
}