import { NextResponse, NextRequest } from 'next/server';
import { getFunctions, renderMediaOnLambda, getRenderProgress } from '@remotion/lambda/client';
import { auth } from '@clerk/nextjs/server'; // Import Clerk auth
import { db } from "@/configs/db"; // Import db
import { videoData } from "@/configs/schema"; // Import videoData schema
import { eq } from 'drizzle-orm'; // Import eq

// --- Configuration ---
const AWS_REGION = 'us-east-1'; // Your AWS region
const REMOTION_SERVE_URL = 'https://remotionlambda-useast1-nncr7mm6wd.s3.us-east-1.amazonaws.com/sites/ai-reel-gen/index.html'; // Your Remotion bundle URL

// Get Lambda function name
// This should ideally be done outside the handler or memoized
const getLambdaFunctionName = async () => {
    const functions = await getFunctions({
        region: AWS_REGION,
        compatibleOnly: true,
    });
    if (!functions || functions.length === 0) {
        throw new Error("No compatible Remotion Lambda functions found.");
    }
    return functions[0].functionName;
};


export async function POST(request) {
    // --- Authentication ---
    const { userId } = await auth();
    if (!userId) {
        console.warn('[API Route Error] /api/render-lambda: Unauthorized access attempt.');
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // --- End Authentication ---

    console.log("Received POST request to initiate render.");
    try {
        // --- 1. Parse Request ---
        let videoId; // Only need videoId from the frontend
        try {
            ({ videoId } = await request.json());
        } catch (parseError) {
            console.error("Failed to parse POST request JSON:", parseError);
            return NextResponse.json({ error: 'Invalid request body. Expected JSON with videoId.' }, { status: 400 });
        }

        if (!videoId) {
            console.warn("Missing videoId in POST request.");
            return NextResponse.json({ error: 'Missing required field: videoId' }, { status: 400 });
        }

        console.log(`Initiating render for videoId: ${videoId}`);

        // --- 2. Fetch Video Data from Unified Table ---
        const videoRecord = await db.query.videoData.findFirst({
            where: eq(videoData.id, videoId),
        });

        if (!videoRecord) {
            console.warn(`Video record with ID ${videoId} not found.`);
            return NextResponse.json({ error: `Video with ID ${videoId} not found.` }, { status: 404 });
        }

        // Ensure the video belongs to the authenticated user (optional but recommended)
        if (videoRecord.clerkId !== userId) {
             console.warn(`User ${userId} attempted to render video ${videoId} belonging to ${videoRecord.clerkId}.`);
             return NextResponse.json({ error: 'Unauthorized access to video.' }, { status: 403 });
        }

        // Extract compositionId and inputProps from the fetched videoRecord
        // The entire videoRecord can often serve as inputProps, or you can format it.
        // The templateId from the DB record is the compositionId for Remotion.
        const compositionId = videoRecord.templateId;
        // Pass the entire video record as inputProps for flexibility in Remotion composition
        const inputProps = { videoData: videoRecord };


        if (!compositionId) {
             console.error(`Video record ${videoId} is missing templateId.`);
             return NextResponse.json({ error: 'Video record is missing composition information.' }, { status: 500 });
        }


        console.log(`Fetched video data for ID ${videoId}. Composition: ${compositionId}`);

        // --- 3. Initiate Remotion Lambda Render ---
        const functionName = await getLambdaFunctionName(); // Get function name dynamically

        const { renderId, bucketName } = await renderMediaOnLambda({
            region: AWS_REGION,
            functionName,
            serveUrl: REMOTION_SERVE_URL,
            composition: compositionId,
            inputProps: inputProps, // Pass the fetched video data as inputProps
            codec: 'h264',
            framesPerLambda: 200,
            privacy: 'public',
        });

        console.log(`Lambda render initiated: RenderID=${renderId}, Bucket=${bucketName} for videoId=${videoId}`);

        // --- 4. Update Video Record with Render Details ---
        // Store renderId and bucketName in the video_data table for polling reference
        await db.update(videoData)
            .set({
                renderId: renderId, // Assuming you add renderId field to videoData schema
                bucketName: bucketName, // Assuming you add bucketName field to videoData schema
                status: 'rendering', // Update status to rendering
                updatedAt: new Date(),
            })
            .where(eq(videoData.id, videoId));
        console.log(`Updated video record ${videoId} with renderId and bucketName.`);


        // --- 5. Return Acceptance Response (Immediately) ---
        return NextResponse.json({
            message: 'Cloud render initiated successfully. Poll GET endpoint for status.',
            renderId: renderId,
            bucketName: bucketName,
            videoId: videoId, // Return videoId for frontend polling
        }, { status: 202 }); // 202 Accepted

    } catch (error) {
        console.error('Error in POST /api/render-lambda:', error);
        const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred during initiation.';
        const remotionErrors = error.errors ?? null;

        // Attempt to update video status to failed if videoId was available
        if (videoId) {
             try {
                 await db.update(videoData).set({ status: 'failed', updatedAt: new Date() }).where(eq(videoData.id, videoId));
                 console.log(`Updated video status to 'failed' for video ID: ${videoId} due to initiation error.`);
             } catch (updateError) {
                 console.error(`Failed to update videoData status to 'failed' for video ID ${videoId} after initiation error:`, updateError);
             }
        }


        return NextResponse.json({
            error: 'Failed to initiate cloud render.',
            details: errorMessage,
            ...(remotionErrors && { remotionErrors: remotionErrors }),
        }, { status: 500 });
    }
}

// --- GET Handler: Check Render Status ---
export async function GET(request) {
    // --- Authentication ---
    const { userId } = await auth();
    if (!userId) {
        console.warn('[API Route Error] GET /api/render-lambda: Unauthorized access attempt.');
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // --- End Authentication ---

    console.log("Received GET request to check render status.");
    try {
        // --- 1. Extract Identifiers from Query Parameters ---
        const { searchParams } = new URL(request.url);
        const videoId = searchParams.get('videoId'); // Expect videoId for fetching from DB
        // renderId and bucketName might still be passed, but we'll rely on DB
        const renderIdFromQuery = searchParams.get('renderId');
        const bucketNameFromQuery = searchParams.get('bucketName');


        if (!videoId) {
            console.warn("Missing videoId in GET request query parameters.");
            return NextResponse.json({ error: 'Missing required query parameter: videoId' }, { status: 400 });
        }

        // --- 2. Fetch Video Record to get Render Details ---
        const videoRecord = await db.query.videoData.findFirst({
            where: eq(videoData.id, videoId),
        });

        if (!videoRecord) {
            console.warn(`Video record with ID ${videoId} not found for status check.`);
            return NextResponse.json({ error: `Video with ID ${videoId} not found.` }, { status: 404 });
        }

         // Ensure the video belongs to the authenticated user
        if (videoRecord.clerkId !== userId) {
             console.warn(`User ${userId} attempted to check status for video ${videoId} belonging to ${videoRecord.clerkId}.`);
             return NextResponse.json({ error: 'Unauthorized access to video status.' }, { status: 403 });
        }


        // Use renderId and bucketName from the database record
        const renderId = videoRecord.renderId;
        const bucketName = videoRecord.bucketName;

        // If renderId or bucketName are missing in the DB record, it means rendering wasn't initiated successfully
        if (!renderId || !bucketName) {
             console.warn(`Render details missing in DB for videoId ${videoId}. Current status: ${videoRecord.status}`);
             // Return the current status from the DB if render wasn't initiated
             return NextResponse.json({
                 status: videoRecord.status || 'unknown',
                 message: videoRecord.status === 'failed' ? 'Render initiation failed previously.' : 'Render not initiated yet.',
                 videoId: videoId,
             }, { status: videoRecord.status === 'failed' ? 500 : 200 }); // Use appropriate status code
        }


        console.log(`Checking status for videoId=${videoId}, RenderID=${renderId}, Bucket=${bucketName}`);

        // --- 3. Call getRenderProgress using details from DB ---
        const functionName = await getLambdaFunctionName(); // Get function name dynamically

        const progress = await getRenderProgress({
            renderId,
            bucketName,
            functionName,
            region: AWS_REGION,
        });

        // --- 4. Interpret and Return Status ---
        let responseBody;
        let status = 200;

        if (progress.fatalErrorEncountered) {
            console.error(`Render failed for RenderID=${renderId} (videoId=${videoId}):`, progress.errors);
            responseBody = {
                status: 'error',
                message: 'Render encountered a fatal error.',
                errors: progress.errors,
                renderId: renderId,
                videoId: videoId,
            };
             // Update DB status to failed
             await db.update(videoData).set({ status: 'failed', updatedAt: new Date() }).where(eq(videoData.id, videoId));

        } else if (progress.done) {
            console.log(`Render finished successfully for RenderID=${renderId} (videoId=${videoId}). Output: ${progress.outputFile}`);
            responseBody = {
                status: 'done',
                message: 'Render finished successfully.',
                url: progress.outputFile, // This is the S3 URL
                renderId: renderId,
                videoId: videoId,
            };
             // Update DB status to Completed and save the final URL
             await db.update(videoData).set({ status: 'Completed', renderedVideoUrl: progress.outputFile, updatedAt: new Date() }).where(eq(videoData.id, videoId));

        } else {
            const overallProgress = progress.overallProgress ?? 0;
            const progressPercent = (overallProgress * 100).toFixed(2);
            console.log(`Render in progress for RenderID=${renderId} (videoId=${videoId}): ${progressPercent}%`);
            responseBody = {
                status: 'rendering',
                message: 'Render is in progress.',
                progress: overallProgress,
                renderId: renderId,
                videoId: videoId,
            };
             // Optionally update DB status to rendering if needed, but 'processing' might be sufficient
             // await db.update(videoData).set({ status: 'rendering', updatedAt: new Date() }).where(eq(videoData.id, videoId));
        }

        return NextResponse.json(responseBody, { status: status });

    } catch (error) {
        console.error('Error in GET /api/render-lambda:', error);
        const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred while checking status.';

        // Attempt to update video status to failed if videoId was available from query params
        const { searchParams } = new URL(request.url);
        const videoIdFromQuery = searchParams.get('videoId');
        if (videoIdFromQuery) {
             try {
                 await db.update(videoData).set({ status: 'failed', updatedAt: new Date() }).where(eq(videoData.id, videoIdFromQuery));
                 console.log(`Updated video status to 'failed' for video ID: ${videoIdFromQuery} due to status check error.`);
             } catch (updateError) {
                 console.error(`Failed to update videoData status to 'failed' for video ID ${videoIdFromQuery} after status check error:`, updateError);
             }
        }


        return NextResponse.json({
            error: 'Failed to retrieve render status.',
            details: errorMessage,
        }, { status: 500 });
    }
}
