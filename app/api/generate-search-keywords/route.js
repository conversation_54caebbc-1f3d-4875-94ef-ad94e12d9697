import { GoogleGenerativeAI } from "@google/generative-ai";
import { NextResponse } from "next/server";
import { auth } from '@clerk/nextjs/server'; // Import Clerk auth
import { hasSufficientCredits, VIDEO_GENERATION_COST } from '@/lib/creditUtils'; // Import credit utility

// --- Configuration ---
// Define a reasonable maximum length for the input script
const MAX_SCRIPT_LENGTH = 10000; // Example: 10,000 characters
// --- End Configuration ---

const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);

export async function POST(req) {
    // --- Authentication ---
    let userId;
    try {
        const authResult = await auth();
        userId = authResult.userId;
    } catch (error) {
        console.error("[API Auth Error] /api/generate-search-keywords: Error during authentication:", error);
        return NextResponse.json({ error: 'Authentication processing failed.' }, { status: 500 });
    }

    if (!userId) {
        console.warn('[API Route Error] /api/generate-search-keywords: Unauthorized access attempt.');
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // --- End Authentication ---
  try {
    const { scriptText } = await req.json(); // Only need scriptText

    // --- Input Validation ---
    if (!scriptText || typeof scriptText !== 'string') { // Check presence and type
      return NextResponse.json({ error: "scriptText is required and must be a string." }, { status: 400 });
    }

    // *** Add Input Length Validation ***
    if (scriptText.length > MAX_SCRIPT_LENGTH) {
        console.warn(`[API Route Warning] /api/generate-search-keywords: Input script length (${scriptText.length}) exceeds maximum (${MAX_SCRIPT_LENGTH}).`);
        return NextResponse.json(
            { error: `Input script exceeds maximum length of ${MAX_SCRIPT_LENGTH} characters.` },
            { status: 413 } // 413 Payload Too Large
        );
    }
    // --- End Input Validation ---

    // --- Credit Check ---
    console.log(`[API Route] User ${userId} requesting search keyword generation.`);
    // Assuming keyword generation has a cost, potentially different from video generation
    // For now, using the same cost as a placeholder. Adjust as needed.
    const KEYWORD_GENERATION_COST = VIDEO_GENERATION_COST; // Placeholder cost
    const sufficientCredits = await hasSufficientCredits(userId, KEYWORD_GENERATION_COST);
    if (!sufficientCredits) {
        console.warn(`[API Route Error] User ${userId} has insufficient credits for keyword generation (cost: ${KEYWORD_GENERATION_COST}).`);
        return NextResponse.json({ error: "Insufficient credits for keyword generation." }, { status: 402 }); // 402 Payment Required
    }
    // --- End Credit Check ---

    if (!process.env.GEMINI_API_KEY) {
        console.error("[API Config Error] /api/generate-search-keywords: GEMINI_API_KEY is not set.");
        return NextResponse.json({ error: "Server configuration error." }, { status: 500 });
    }

    // Choose a model that's appropriate for your use case.
    const model = genAI.getGenerativeModel({ model: "gemini-1.5-flash-latest"});

    // Modified prompt to generate search keywords/phrases
    const prompt = `Analyze the following video script and generate a list of concise search keywords or phrases that can be used to find relevant stock images, videos, and music. Focus on key objects, actions, emotions, and concepts mentioned in the script. Return only the list of keywords/phrases, with each item on a new line. Do not include any introductory or concluding text.

Script:
${scriptText}`;

    console.log("[API Info] /api/generate-search-keywords: Calling Gemini API for keyword generation.");
    const result = await model.generateContent(prompt);
    const response = await result.response;
    const text = response.text();
    console.log("[API Info] /api/generate-search-keywords: Received response from Gemini.");

    // Split the response into individual keywords/phrases based on newlines
    const searchKeywords = text.split('\n').map(p => p.trim()).filter(p => p.length > 0);

    return NextResponse.json({ searchKeywords }, { status: 200 });

  } catch (error) {
    console.error("[API Error] /api/generate-search-keywords: Error generating search keywords:", error);
    return NextResponse.json(
        { error: "An internal server error occurred while generating search keywords." },
        { status: 500 }
    );
  }
}