import { NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { generateYouTubeAuthUrl } from '@/lib/youtube-oauth';

export async function GET(request) {
  const { userId } = await auth();
  if (!userId) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const authorizationUrl = generateYouTubeAuthUrl(userId);
    return NextResponse.json({ redirectUrl: authorizationUrl }, { status: 200 });
  } catch (error) {
    console.error('Error in GET /api/social-media/youtube/oauth/initiate:', error);
    return NextResponse.json({ error: 'Failed to initiate YouTube OAuth.' }, { status: 500 });
  }
}
