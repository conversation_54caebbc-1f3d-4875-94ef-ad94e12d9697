import { NextResponse } from 'next/server';
import { google } from 'googleapis';
import { db } from '@/configs/db';
import { userSocialAccounts } from '@/configs/schema';
import { eq } from 'drizzle-orm';

const GOOGLE_CLIENT_ID = process.env.GOOGLE_CLIENT_ID;
const GOOGLE_CLIENT_SECRET = process.env.GOOGLE_CLIENT_SECRET;
const GOOGLE_REDIRECT_URI = process.env.GOOGLE_REDIRECT_URI;

const oauth2Client = new google.auth.OAuth2(
  GOOGLE_CLIENT_ID,
  GOOGLE_CLIENT_SECRET,
  GOOGLE_REDIRECT_URI
);

export async function GET(request) {
  const { searchParams } = new URL(request.url);
  const code = searchParams.get('code');
  const state = searchParams.get('state'); // This should be the Clerk userId

  if (!code || !state) {
    console.error('Missing code or state in OAuth callback.');
    return NextResponse.json({ error: 'Missing authorization code or state.' }, { status: 400 });
  }

  const clerkId = state; // Use state as clerkId

  try {
    // Exchange the authorization code for tokens
    const { tokens } = await oauth2Client.getToken(code);
    oauth2Client.setCredentials(tokens);

    // Get YouTube channel information
    const youtube = google.youtube({
      version: 'v3',
      auth: oauth2Client,
    });

    const channelResponse = await youtube.channels.list({
      part: 'snippet',
      mine: true, // Retrieve information for the authenticated user's channel
    });

    const channel = channelResponse.data.items[0];
    if (!channel) {
      console.error('No YouTube channel found for the authenticated user.');
      return NextResponse.json({ error: 'No YouTube channel found.' }, { status: 404 });
    }

    const platformAccountId = channel.id;
    const accountDisplayName = channel.snippet.title;

    // Save or update user's social account details in the database
    const existingAccount = await db.query.userSocialAccounts.findFirst({
      where: eq(userSocialAccounts.clerkId, clerkId) && eq(userSocialAccounts.platformName, 'youtube') && eq(userSocialAccounts.platformAccountId, platformAccountId),
    });

    const tokenExpiresAt = tokens.expiry_date ? new Date(tokens.expiry_date) : null;

    if (existingAccount) {
      await db.update(userSocialAccounts)
        .set({
          accessToken: tokens.access_token,
          refreshToken: tokens.refresh_token || existingAccount.refreshToken, // Only update if new refresh token is provided
          tokenExpiresAt: tokenExpiresAt,
          updatedAt: new Date(),
        })
        .where(eq(userSocialAccounts.id, existingAccount.id));
      console.log(`Updated YouTube account for user ${clerkId}: ${accountDisplayName}`);
    } else {
      await db.insert(userSocialAccounts).values({
        clerkId: clerkId,
        platformName: 'youtube',
        platformAccountId: platformAccountId,
        accountDisplayName: accountDisplayName,
        accessToken: tokens.access_token,
        refreshToken: tokens.refresh_token,
        tokenExpiresAt: tokenExpiresAt,
        otherCredentials: { scopes: tokens.scope?.split(' ') }, // Store granted scopes
        createdAt: new Date(),
        updatedAt: new Date(),
      });
      console.log(`Connected new YouTube account for user ${clerkId}: ${accountDisplayName}`);
    }

    // Redirect to the social scheduler page with a success message
    return NextResponse.redirect(new URL('/dashboard/social-scheduler?oauth_success=true', request.url));

  } catch (error) {
    console.error('Error handling YouTube OAuth callback:', error);
    // Redirect to the social scheduler page with an error message
    return NextResponse.redirect(new URL(`/dashboard/social-scheduler?oauth_error=true&message=${encodeURIComponent(error.message || 'Unknown error')}`, request.url));
  }
}
