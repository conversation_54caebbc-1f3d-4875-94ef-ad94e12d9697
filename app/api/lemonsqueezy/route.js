import { NextResponse } from 'next/server';
// Import the setup function ALONG WITH createCheckout
import { lemonSqueezySetup, createCheckout } from '@lemonsqueezy/lemonsqueezy.js';
import { auth } from '@clerk/nextjs/server'; // Import Clerk auth

// Define a logging prefix for consistency
const LOG_PREFIX = '[LemonSqueezy Checkout API]:';

export async function POST(request) {
  console.log(`${LOG_PREFIX} Received POST request.`);

  // --- Authentication ---
  const { userId: authenticatedUserId } = await auth();
  if (!authenticatedUserId) {
    console.warn(`${LOG_PREFIX} Unauthorized access attempt.`);
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  // --- End Authentication ---

  // --- 1. Parse Request Body ---
  let priceId, userId;
  try {
    const body = await request.json();
    priceId = body.priceId;
    userId = body.userId; // This is the userId from the client request body
    console.log(`${LOG_PREFIX} Parsed request body:`, { priceId, userId });
  } catch (err) {
    console.error(`${LOG_PREFIX} Failed to parse JSON request body:`, err);
    return NextResponse.json(
      { error: 'Invalid request format: Please provide a valid JSON body.', details: err.message },
      { status: 400 } // Bad Request
    );
  }

  // --- 2. Validate Input Parameters and Match Authenticated User ---
  const missingParams = [];
  if (!priceId) missingParams.push('priceId');
  if (!userId) missingParams.push('userId');

  if (missingParams.length > 0) {
    const errorMessage = `Missing required parameter(s): ${missingParams.join(', ')}`;
    console.error(`${LOG_PREFIX} ${errorMessage}`);
    return NextResponse.json(
      { error: errorMessage },
      { status: 400 } // Bad Request
    );
  }

  // Crucial check: Ensure the userId from the request body matches the authenticated user
  if (userId !== authenticatedUserId) {
    console.warn(`${LOG_PREFIX} User ID mismatch. Authenticated: ${authenticatedUserId}, Request Body: ${userId}`);
    return NextResponse.json({ error: 'Unauthorized: User ID mismatch.' }, { status: 403 }); // Forbidden
  }

  console.log(`${LOG_PREFIX} Input parameters validated and user ID matched successfully.`);

  // --- 3. Check Environment Variables ---
  const storeId = process.env.LEMONSQUEEZY_STORE_ID;
  const apiKey = process.env.LEMONSQUEEZY_API_KEY;

  const missingEnvVars = [];
  if (!storeId) missingEnvVars.push('LEMONSQUEEZY_STORE_ID');
  if (!apiKey) missingEnvVars.push('LEMONSQUEEZY_API_KEY');

  if (missingEnvVars.length > 0) {
    const errorMessage = `Server configuration error: Missing required environment variable(s): ${missingEnvVars.join(', ')}. Please check server setup.`;
    console.error(`${LOG_PREFIX} ${errorMessage}`);
    console.error(`${LOG_PREFIX} Current env var presence: STORE_ID=${!!storeId}, API_KEY=${!!apiKey}`);
    return NextResponse.json(
      { error: 'Server configuration error. Please contact support.' },
      { status: 500 } // Internal Server Error
    );
  }
  console.log(`${LOG_PREFIX} Environment variables loaded successfully.`);
  console.log(`${LOG_PREFIX} Using Store ID: ${storeId}`); // Avoid logging API key

  // --- 4. Initialize Lemon Squeezy SDK ---
  try {
    lemonSqueezySetup({
      apiKey: apiKey,
    });
    console.log(`${LOG_PREFIX} Lemon Squeezy SDK initialized successfully.`);
  } catch (setupError) {
    console.error(`${LOG_PREFIX} CRITICAL: Failed to initialize Lemon Squeezy SDK:`, setupError);
    return NextResponse.json(
      { error: 'Failed to initialize payment provider. Please try again later or contact support.', details: setupError.message },
      { status: 500 } // Internal Server Error
    );
  }

  // --- 5. Create Lemon Squeezy Checkout ---
  try {
    console.log(`${LOG_PREFIX} Attempting to create checkout for user ${userId} with price (variant ID) ${priceId} in store ${storeId}.`);

    // Construct the options object for createCheckout
    // Ensure keys are in snake_case as expected by the @lemonsqueezy/lemonsqueezy.js SDK v2+
    const checkoutOptions = {
      checkoutData: {
        // `checkout_data` is an OBJECT used for pre-fills (email, name, address) or
        // other 'custom' data visible directly on the order/subscription in the LS dashboard.
        // It's not the primary place for data intended for webhooks (use `metadata` for that).
        // Example pre-fills:
        // email: "<EMAIL>",
        // name: "Customer Prefill Name",
        // custom: {
        //   some_other_info: "details_for_ls_dashboard"
        // }

        // IMPORTANT: If you still get "The checkout_data field must be an array.",
        // it means the specific `priceId` (variant) you are using requires `checkout_data`
        // to be an array (e.g., for bundle selections). You'll need to check your
        // Lemon Squeezy product setup for that variant to know the required array structure.
        // For now, we'll assume a standard object. If no pre-fills are needed,
        // an empty object is fine:
        custom: {userId: String(userId),} // Or simply `checkout_data: {}`
      },
      meta: {
        // THIS IS WHERE YOU PUT DATA FOR WEBHOOKS.
        // It will appear in the `meta.custom_data` field of webhook events.
        userId: String(userId), // Pass the validated userId here.
        // You can add other relevant data:
        // clerk_session_id: request.headers.get('clerk-session-id'), // Example
      },
      product_options: {
        // Options related to the product during checkout
        // redirect_url: `${process.env.NEXT_PUBLIC_APP_URL}/payment-success?session_id={CHECKOUT_SESSION_ID}`,
        // receipt_button_text: "Return to Dashboard",
        // enabled_variants: [/* array of variant IDs if applicable */],
      },
      checkout_options: {
        // Options related to the checkout page appearance/behavior
        // embed: false, // true to get an embeddable checkout
        // logo: true, // true to show store logo
        // dark: false,
      }
    };

    // You can conditionally omit keys from checkoutOptions if their values are empty/not needed.
    // For instance, if you don't need any `checkout_data` pre-fills:
    // delete checkoutOptions.checkout_data; // (or just don't include it initially)

    console.log(`${LOG_PREFIX} SDK options for createCheckout:`, JSON.stringify(checkoutOptions, null, 2));

    const checkout = await createCheckout(
      String(storeId),
      String(priceId), // This is your Variant ID
      checkoutOptions
    );

    // --- 6. Handle Lemon Squeezy Response ---
    console.log(`${LOG_PREFIX} Raw response from createCheckout:`, JSON.stringify(checkout, null, 2));

    let apiErrors = null;
    let apiStatusCode = checkout ? checkout.statusCode : null;

    if (checkout && checkout.error && checkout.error.cause && Array.isArray(checkout.error.cause)) {
      apiErrors = checkout.error.cause;
      console.error(`${LOG_PREFIX} SDK caught API errors (from error.cause):`, apiErrors);
    } else if (checkout && checkout.errors && Array.isArray(checkout.errors)) {
      apiErrors = checkout.errors;
      console.error(`${LOG_PREFIX} API returned errors in top-level 'errors' property:`, apiErrors);
    } else if (checkout && checkout.data && checkout.data.errors && Array.isArray(checkout.data.errors)) {
      apiErrors = checkout.data.errors;
      console.error(`${LOG_PREFIX} API returned errors in data.errors payload:`, apiErrors);
    } else if (apiStatusCode && apiStatusCode >= 400) {
      console.error(`${LOG_PREFIX} Received error status code ${apiStatusCode} without detailed errors array. Response:`, checkout);
      apiErrors = [{ title: "API Error", detail: `Received status code ${apiStatusCode}. Response: ${JSON.stringify(checkout?.data || checkout?.error || checkout)}` }];
    }

    if (apiErrors && apiErrors.length > 0) {
      const errorDetails = apiErrors.map(e => e.detail || e.title || JSON.stringify(e)).join('; ');
      const responseStatus = (apiStatusCode && apiStatusCode >= 400) ? apiStatusCode : 400;
      console.error(`${LOG_PREFIX} Final Error Decision: API Error(s) detected. Details: ${errorDetails}`);
      return NextResponse.json(
        { error: `Failed to create checkout: ${errorDetails}`, details: apiErrors },
        { status: responseStatus }
      );
    }

    // --- VITAL SUCCESS CHECK for v2 SDK ---
    const checkoutUrl = checkout?.data?.data?.attributes?.url;

    if (checkoutUrl) {
      console.log(`${LOG_PREFIX} Checkout created successfully for user ${userId}. URL: ${checkoutUrl}`);
      return NextResponse.json({ checkoutUrl: checkoutUrl });
    }
    // --- END SUCCESS CHECK ---

    console.error(`${LOG_PREFIX} Unexpected response structure from Lemon Squeezy (not error, not expected success):`, checkout);
    return NextResponse.json(
      { error: 'Received an unexpected response format from the payment provider after checkout creation.' },
      { status: 500 }
    );

  } catch (error) {
    console.error(`${LOG_PREFIX} Exception occurred during checkout creation:`, error);

    let underlyingApiErrors = null;
    let errorStatus = 500;

    if (error.cause && Array.isArray(error.cause)) {
      underlyingApiErrors = error.cause;
      errorStatus = parseInt(underlyingApiErrors[0]?.status, 10) || 400;
    } else if (error.errors && Array.isArray(error.errors)) {
      underlyingApiErrors = error.errors;
      errorStatus = parseInt(underlyingApiErrors[0]?.status, 10) || 400;
    }

    if (underlyingApiErrors && underlyingApiErrors.length > 0) {
      const errorDetails = underlyingApiErrors.map(e => e.detail || e.title || JSON.stringify(e)).join('; ');
      console.error(`${LOG_PREFIX} Underlying Lemon Squeezy API error details from exception:`, errorDetails, underlyingApiErrors);
      return NextResponse.json(
        { error: `Failed to create checkout: ${errorDetails}`, details: underlyingApiErrors },
        { status: errorStatus }
      );
    }

    return NextResponse.json(
      { error: 'An unexpected error occurred while creating the checkout session.', details: error.message || String(error) },
      { status: 500 }
    );
  }
}