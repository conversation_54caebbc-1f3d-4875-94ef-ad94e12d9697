import axios from 'axios';
import FormData from 'form-data';
import { storage } from "@/lib/firebase";
import { ref, uploadBytes, getDownloadURL } from 'firebase/storage';
import { NextResponse } from 'next/server';
import { v4 as uuidv4 } from 'uuid'; // Import uuid
import { auth } from '@clerk/nextjs/server'; // Import Clerk auth

const apiKey = process.env.STABILITY_AI_API_KEY;
const requestedFormat = "webp";

// In-memory store for task status (replace with DB/Cache in production)
const taskStatusStore = {};

// --- Background Image Generation Function ---
async function processImageGeneration(imagePrompt, taskId) {
  try {
    taskStatusStore[taskId] = { status: 'processing', prompt: imagePrompt };
    console.log(`[Task ${taskId}] 🚀 Starting image generation for prompt:`, imagePrompt);

    // Build payload for Stability API
    const payload = {
      prompt: imagePrompt,
      output_format: requestedFormat
    };
    const formData = axios.toFormData(payload, new FormData());

    const aiResponse = await axios.post(
      `https://api.stability.ai/v2beta/stable-image/generate/core`,
      formData,
      {
        responseType: "arraybuffer",
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Accept': 'image/*'
        }
      }
    );

    if (aiResponse.status !== 200) {
      const errorText = Buffer.from(aiResponse.data).toString('utf-8');
      console.error(`[Task ${taskId}] ❌ Stability AI error:`, errorText);
      taskStatusStore[taskId] = { status: 'failed', error: `Image generation failed (Stability AI: ${aiResponse.status})` };
      return; // Stop processing on error
    }

    console.log(`[Task ${taskId}] ✅ Image generated (${aiResponse.data.byteLength} bytes)`);

    // Upload to Firebase
    const filename = `image_${taskId}_${Date.now()}.${requestedFormat}`;
    const storageRef = ref(storage, `images/${filename}`);

    const uploadResult = await uploadBytes(storageRef, Buffer.from(aiResponse.data));
    const downloadURL = await getDownloadURL(uploadResult.ref);

    console.log(`[Task ${taskId}] 📦 Uploaded to Firebase:`, downloadURL);
    taskStatusStore[taskId] = { status: 'Completed', imageUrl: downloadURL };

  } catch (err) {
    let errorMessage = "Internal server error during background processing.";
    let statusCode = 500;
    // If it's a known response error from Stability AI
    if (err.response) {
      const rawError = Buffer.from(err.response.data).toString('utf-8');
      console.error(`[Task ${taskId}] ❌ API Response Error:`, rawError);
      errorMessage = `Stability AI error (${err.response.status}).`;
      statusCode = err.response.status;
    } else {
      console.error(`[Task ${taskId}] ❗ Unexpected Background Error:`, err.message);
    }
    taskStatusStore[taskId] = { status: 'failed', error: errorMessage, statusCode: statusCode };
  }
}

// --- POST handler (Initiates Background Task) ---
export async function POST(request) {
    // --- Authentication ---
    const { userId } = await auth();
    if (!userId) {
        console.warn('[API Route Error] /api/generate-image: Unauthorized access attempt.');
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // --- End Authentication ---
  try {
    const { imagePrompt } = await request.json();
    const taskId = uuidv4(); // Generate unique task ID

    if (!apiKey) {
      console.error("❌ STABILITY_AI_API_KEY is not set.");
      return NextResponse.json({ error: "Server misconfigured." }, { status: 500 });
    }

    if (!imagePrompt || typeof imagePrompt !== 'string') {
      console.warn("⚠️ Invalid prompt:", imagePrompt);
      return NextResponse.json({ error: "Invalid prompt." }, { status: 400 });
    }

    // Initiate background processing (fire-and-forget)
    // IMPORTANT: In serverless, this might not complete if the function terminates.
    // Consider using a proper background job queue for production.
    processImageGeneration(imagePrompt, taskId).catch(initError => {
        // Catch errors *during the initiation* of the async function, though unlikely here.
        console.error(`[Task ${taskId}] ❗ Error initiating background task:`, initError);
        // Update status if possible, though the task likely didn't even start properly.
        taskStatusStore[taskId] = { status: 'failed', error: 'Failed to initiate background task.' };
    });

    console.log(`[API Route] Image generation task ${taskId} initiated for prompt: "${imagePrompt}"`);

    // Return immediately with task ID
    return NextResponse.json({
        message: "Image generation task initiated.",
        taskId: taskId
    }, { status: 202 }); // 202 Accepted

  } catch (err) {
    // Catch errors during request parsing or initial setup
    console.error("❗ Unexpected Error in POST handler:", err.message);
    return NextResponse.json({ error: "Internal server error." }, { status: 500 });
  }
}

// --- GET handler (Check Task Status - Example) ---
// NOTE: This uses the simple in-memory store. Replace with DB/Cache.
export async function GET(request) {
    // --- Authentication ---
    const { userId } = await auth();
    if (!userId) {
        console.warn('[API Route Error] /api/generate-image: Unauthorized access attempt.');
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // --- End Authentication ---
    const { searchParams } = new URL(request.url);
    const taskId = searchParams.get('taskId');

    if (!taskId) {
        return NextResponse.json({ error: 'Missing taskId query parameter.' }, { status: 400 });
    }

    const statusInfo = taskStatusStore[taskId];

    if (!statusInfo) {
        return NextResponse.json({ error: 'Task not found.' }, { status: 404 });
    }

    return NextResponse.json(statusInfo);
}
