import { NextRequest, NextResponse } from 'next/server';
import crypto from 'crypto';
import { db } from '@/configs/db'; // Import Drizzle instance
import { users } from '@/configs/schema'; // Import users schema
import { eq, sql } from 'drizzle-orm'; // Import eq and sql for Drizzle operations

async function verifySignature(req, secret) {
  const signature = req.headers.get('x-signature');
  if (!signature) return false;

  const body = await req.text();
  const hmac = crypto.createHmac('sha256', secret).update(body).digest('hex');

  return crypto.timingSafeEqual(Buffer.from(hmac), Buffer.from(signature));
}

export async function POST(request) {
  const secret = process.env.LEMONSQUEEZY_WEBHOOK_SECRET;

  if (!secret) {
    console.error('Webhook secret not set.');
    return NextResponse.json({ message: 'Server misconfigured' }, { status: 500 });
  }

  const isValid = await verifySignature(request.clone(), secret);
  if (!isValid) {
    console.warn('Invalid webhook signature');
    return NextResponse.json({ message: 'Invalid signature' }, { status: 401 });
  }

  let payload;
  try {
    payload = await request.json();
  } catch (err) {
    return NextResponse.json({ message: 'Invalid JSON' }, { status: 400 });
  }

  const { meta, data } = payload;
  const eventType = meta?.event_name;

  try {
    switch (eventType) {
      case 'order_created': {
        const order = data.attributes;
        // REVERTED: Extract userId from metadata, as it's now sent there from /api/lemonsqueezy
        const userId = meta.custom_data.user_id;
        const variantId = order.first_order_item?.variant_id; // Get variant ID from the first order item
        console.log(userId);
        console.log(variantId);

        if (!userId) {
          console.error('Webhook Error: Missing userId in order metadata.');
          // Log the received metadata for debugging
          console.log('Received metadata:',meta.custom_data.user_id );
          return NextResponse.json({ message: 'Missing required userId in metadata' }, { status: 400 });
        }

        if (!variantId) {
          console.error(`Webhook Error: Missing variant_id for order associated with user ${userId}.`);
          return NextResponse.json({ message: 'Missing required variant_id in order data' }, { status: 400 });
        }

        let creditsToAdd = 0;
        const smallPackageVariantId = 787432; // From creditPackages.js
        const mediumPackageVariantId = parseInt(process.env.LEMONSQUEEZY_MEDIUM_PRODUCT_ID || '0');
        const largePackageVariantId = parseInt(process.env.LEMONSQUEEZY_PROFESSIONAL_PRODUCT_ID || '0');

        switch (variantId) {
          case smallPackageVariantId:
            creditsToAdd = 100;
            break;
          case mediumPackageVariantId:
            creditsToAdd = 500;
            break;
          case largePackageVariantId:
            creditsToAdd = 1000;
            break;
          default:
            console.warn(`Webhook Warning: Unrecognized variant_id ${variantId} for user ${userId}. No credits added.`);
            // Optionally return an error or just add 0 credits
            return NextResponse.json({ message: `Webhook processed, but variant ID ${variantId} not recognized.` }, { status: 200 });
        }

        if (creditsToAdd > 0) {
          // Use Drizzle ORM to update credits
          try {
            const result = await db.update(users)
              .set({ credits: sql`${users.credits} + ${creditsToAdd}` }) // Use sql tagged template for increment
              .where(eq(users.clerkId, userId))
              .returning({ updatedCredits: users.credits }); // Get the new credit balance

            if (result.length > 0) {
              console.log(`Successfully added ${creditsToAdd} credits to user ${userId}. New balance: ${result[0].updatedCredits}`);
            } else {
              console.error(`Webhook Error: Failed to find user with clerkId ${userId} to add credits.`);
              return NextResponse.json({ message: `User with clerkId ${userId} not found.` }, { status: 404 });
            }
          } catch (dbError) {
             console.error(`Webhook DB Error updating credits for user ${userId}:`, dbError);
             return NextResponse.json({ message: 'Database error during credit update.' }, { status: 500 });
          }
        }
        break;
      }

      case 'subscription_created': {
        const subscription = data.attributes;
        // REVERTED: Extract userId from metadata for subscriptions too
        const userId = subscription.metadata?.userId;
        const status = subscription.status;

        if (!userId) {
           console.error('Webhook Error: Missing userId in subscription metadata.');
           console.log('Received subscription metadata:', subscription.metadata);
           return NextResponse.json({ message: 'Missing required userId in subscription metadata' }, { status: 400 });
        }
        if (!status) {
           console.warn(`Webhook Warning: Missing status for subscription_created event for user ${userId}.`);
           // Decide if this is an error or just needs logging
           // return NextResponse.json({ message: 'Missing status in subscription data' }, { status: 400 });
        }

        // // Use Drizzle ORM to update subscription status
        // // NOTE: Commented out as 'subscription_status' column is not in the 'users' schema.
        // try {
        //    await db.update(users)
        //      .set({ subscription_status: status }) // Schema needs 'subscription_status' column
        //      .where(eq(users.clerkId, userId));
        //    console.log(`Set subscription status '${status}' for user ${userId} (clerkId)`);
        // } catch (dbError) {
        //     console.error(`Webhook DB Error updating subscription status for user ${userId}:`, dbError);
        //     return NextResponse.json({ message: 'Database error during subscription update.' }, { status: 500 });
        // }
        console.log(`Received subscription_created event for user ${userId}, status: ${status}. DB update skipped (no subscription_status column).`);
        break;
      }

      case 'subscription_cancelled': {
        const subscription = data.attributes;
         // REVERTED: Extract userId from metadata for subscriptions too
        const userId = subscription.metadata?.userId;

        if (!userId) {
           console.error('Webhook Error: Missing userId in subscription metadata for cancellation.');
           console.log('Received subscription metadata:', subscription.metadata);
           return NextResponse.json({ message: 'Missing required userId in subscription metadata' }, { status: 400 });
        }

        // // Use Drizzle ORM to update subscription status to cancelled
        // // NOTE: Commented out as 'subscription_status' column is not in the 'users' schema.
        //  try {
        //     await db.update(users)
        //       .set({ subscription_status: 'cancelled' }) // Schema needs 'subscription_status' column
        //       .where(eq(users.clerkId, userId));
        //     console.log(`Cancelled subscription for user ${userId} (clerkId)`);
        //  } catch (dbError) {
        //      console.error(`Webhook DB Error cancelling subscription for user ${userId}:`, dbError);
        //      return NextResponse.json({ message: 'Database error during subscription cancellation.' }, { status: 500 });
        //  }
        console.log(`Received subscription_cancelled event for user ${userId}. DB update skipped (no subscription_status column).`);
        break;
      }

      default:
        console.log(`Unhandled event: ${eventType}`);
    }

    return NextResponse.json({ message: 'Webhook processed' }, { status: 200 });
  } catch (err) {
    console.error('Webhook handler error:', err);
    return NextResponse.json({ message: 'Internal server error' }, { status: 500 });
  }
}
