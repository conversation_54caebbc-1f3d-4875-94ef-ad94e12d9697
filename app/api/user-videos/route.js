import { db } from "@/configs/db"; // Adjust the import path as needed
import { videoData } from "@/configs/schema"; // Adjust the import path as needed
import { NextResponse } from "next/server";
import { auth } from '@clerk/nextjs/server'; // Import Clerk auth
import { eq, count, desc } from 'drizzle-orm'; // Import eq, count, desc operators

export async function GET(req) {
    // --- Authentication ---
    const { userId } = await auth();
    if (!userId) {
        console.warn('[API Route Error] /api/user-videos: Unauthorized access attempt.');
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // --- End Authentication ---

    // --- Pagination ---
    const url = new URL(req.url);
    const page = parseInt(url.searchParams.get('page') || '1', 10);
    const limit = parseInt(url.searchParams.get('limit') || '10', 10);
    const offset = (page - 1) * limit;
    // --- End Pagination ---

    try {
        console.log(`[API Route] Fetching videos for user: ${userId}, page: ${page}, limit: ${limit}`);

        // Fetch total count of videos for the user
        const totalCountResult = await db.select({ value: count() })
            .from(videoData)
            .where(eq(videoData.clerkId, userId));

        const totalCount = totalCountResult[0]?.value || 0;
        const totalPages = Math.ceil(totalCount / limit);

        // Fetch paginated video data only for the authenticated user, ordered by creation time descending
        // Select only the columns that exist in the database to avoid schema mismatch errors
        const userVideos = await db.select({
            id: videoData.id,
            clerkId: videoData.clerkId,
            title: videoData.title,
            topic: videoData.topic,
            script: videoData.script,
            videoStyle: videoData.videoStyle,
            aspectRatio: videoData.aspectRatio,
            templateId: videoData.templateId,
            voice: videoData.voice,
            captionName: videoData.captionName,
            audioSpeed: videoData.audioSpeed,
            backgroundMusic: videoData.backgroundMusic,
            estimatedDurationSeconds: videoData.estimatedDurationSeconds,
            status: videoData.status,
            audioUrl: videoData.audioUrl,
            renderedVideoUrl: videoData.renderedVideoUrl,
            costInCredits: videoData.costInCredits,
            createdAt: videoData.createdAt,
            updatedAt: videoData.updatedAt,
        })
            .from(videoData)
            .where(eq(videoData.clerkId, userId)) // Filter by authenticated user's clerkId
            .orderBy(desc(videoData.createdAt)) // Order by creation time descending
            .limit(limit)
            .offset(offset);

        console.log(`[API Route] Found ${userVideos.length} videos (Total: ${totalCount}) for user: ${userId} on page ${page}`);

        return NextResponse.json({
            videos: userVideos,
            pagination: {
                currentPage: page,
                totalPages: totalPages,
                totalCount: totalCount,
                limit: limit
            }
        }, { status: 200 });

    } catch (error) {
        console.error(`[API Route Error] Error fetching videos for user ${userId}:`, error);

        // Ensure we always return valid JSON with proper error structure
        const errorMessage = error.message || "An unknown error occurred while fetching videos";
        const errorResponse = {
            error: errorMessage,
            success: false,
            videos: [],
            pagination: {
                currentPage: page,
                totalPages: 0,
                totalCount: 0,
                limit: limit
            }
        };

        return NextResponse.json(errorResponse, {
            status: 500,
            headers: {
                'Content-Type': 'application/json'
            }
        });
    }
}