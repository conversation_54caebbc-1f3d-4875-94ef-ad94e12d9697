// app/api/pexels-video-search/route.js
import { NextResponse } from 'next/server';

export async function GET(request) {
  const { searchParams } = new URL(request.url);
  const query = searchParams.get('query');

  if (!query) {
    return NextResponse.json({ error: 'Missing search query' }, { status: 400 });
  }

  const pexelsApiKey = process.env.PEXELS_API_KEY;

  if (!pexelsApiKey) {
    return NextResponse.json({ error: 'Pexels API key not configured' }, { status: 500 });
  }

  // Pexels Video Search API endpoint
  const url = `https://api.pexels.com/videos/search?query=${encodeURIComponent(query)}&per_page=15`;

  try {
    const response = await fetch(url, {
      headers: {
        Authorization: pexelsApiKey,
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Pexels Video API error: ${response.status} ${response.statusText} - ${errorText}`);
    }

    const data = await response.json();
    // Pexels video results are in data.videos
    return NextResponse.json(data);

  } catch (error) {
    console.error('Error fetching videos from Pexels:', error);
    return NextResponse.json({ error: 'Failed to fetch videos from Pexels' }, { status: 500 });
  }
}