import { db } from "@/configs/db"; // Adjust the import path as needed
import { videoData, users } from "@/configs/schema"; // Adjust the import path as needed
import { NextResponse } from "next/server";
import { auth } from '@clerk/nextjs/server'; // Import Clerk auth
import { eq } from 'drizzle-orm'; // Import eq operator

// Custom Error class for specific API failures
class ApiError extends Error {
  constructor(message, statusCode) {
    super(message);
    this.name = 'ApiError';
    this.statusCode = statusCode;
  }
}

export async function POST(req) {
    // --- Authentication ---
    let userId;
    try {
        const authResult = await auth();
        userId = authResult.userId;
    } catch (error) {
        console.error("[API Auth Error] /api/save-video-data: Error during authentication:", error);
        return NextResponse.json({ error: 'Authentication error' }, { status: 500 });
    }

    if (!userId) {
        console.warn('[API Route Error] /api/save-video-data: Unauthorized access attempt.');
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    const clerkId = userId; // Use the authenticated user's ID for clarity
    // --- End Authentication ---

    let requestBody;
    try {
        requestBody = await req.json();
    } catch (error) {
        console.error(`[API Route Error] /api/save-video-data (User: ${clerkId}): Invalid JSON body`, error);
        return NextResponse.json({ error: "Invalid request body format." }, { status: 400 });
    }

    const {
      script,
      audioUrl,
      captionJson,
      images,
      title,
      style, // Note: Consider renaming this or `videoStyle` for consistency if they mean the same thing
      aspectRatio,
      templateId,
      topic,
      videoStyle,
      // New fields to accept
      voice,
      captionName,
      captionStyleJson, // Expect captionStyleJson from the hook
      captionContainerStyle,
      audioSpeed, // Add audioSpeed
      backgroundMusic, // Add backgroundMusic
      estimatedDurationSeconds, // Add estimatedDurationSeconds
    } = requestBody;

    // --- Input Validation ---
    const missingFields = [];
    if (!title) missingFields.push('title');
    if (!script) missingFields.push('script');
    if (!audioUrl) missingFields.push('audioUrl');
    if (!captionJson) missingFields.push('captionJson');
    if (!images || !Array.isArray(images)) missingFields.push('images (must be an array)');
    if (!templateId) missingFields.push('templateId'); // Assuming templateId is still required from client
    if (!topic) missingFields.push('topic');
    if (!aspectRatio) missingFields.push('aspectRatio'); // Assuming aspectRatio is required
    if (!voice) missingFields.push('voice'); // Assuming voice is required
    if (audioSpeed === null || audioSpeed === undefined) missingFields.push('audioSpeed'); // audioSpeed is required
    if (!backgroundMusic) missingFields.push('backgroundMusic'); // Assuming backgroundMusic is required
    if (estimatedDurationSeconds === null || estimatedDurationSeconds === undefined || typeof estimatedDurationSeconds !== 'number' || estimatedDurationSeconds <= 0) {
      missingFields.push('estimatedDurationSeconds (must be a positive number)');
    }
    // captionName and captionStyle can be optional or have defaults handled by client/schema
    // Add other checks like type validation or length limits if needed

    if (missingFields.length > 0) {
        console.warn(`[API Route Error] /api/save-video-data (User: ${clerkId}): Missing required fields: ${missingFields.join(', ')}`);
        return NextResponse.json({ error: `Missing required video data fields: ${missingFields.join(', ')}` }, { status: 400 });
    }
    // --- End Input Validation ---


    // --- Data Mapping ---
    // Prepare the data object for database insertion according to the 'videoData' schema.
    const newVideo = {
      clerkId: clerkId,
      title: title,
      topic: topic,
      script: script,
      videoStyle: videoStyle ?? style, // Use videoStyle, fallback to style if needed
      aspectRatio: aspectRatio, // Now directly from request
      templateId: templateId,   // Now directly from request
      voice: voice,             // New field
      captionName: captionName, // New field
      captionStyleJson: captionStyleJson, // Save to captionStyleJson field in DB for this step
      captionContainerStyle: captionContainerStyle,
      audioSpeed: audioSpeed, // Add audioSpeed to db object
      backgroundMusic: backgroundMusic, // Add backgroundMusic to db object
      estimatedDurationSeconds: estimatedDurationSeconds, // Add estimatedDurationSeconds to db object
      audioUrl: audioUrl,
      captionJson: captionJson,
      images: images,
      status: 'Completed',      // Changed from 'Completed'
    };
    // --- End Data Mapping ---


    try {
        // 1. Insert Video Data (Credit deduction removed from this API)
        const insertResult = await db.insert(videoData)
            .values(newVideo)
            .returning({ id: videoData.id });

        if (!insertResult || insertResult.length === 0 || !insertResult[0].id) {
            console.error(`[DB Operation] Failed to insert video data for user ${clerkId}.`);
            throw new Error("Failed to insert video data into the database.");
        }
        const savedVideoId = insertResult[0].id;
        console.log(`[DB Operation] Video data inserted successfully for user ${clerkId} with ID: ${savedVideoId}`);

        // All operations successful (credit deduction handled elsewhere)
        console.log(`[API Route] Video data saved (ID: ${savedVideoId}) for user ${clerkId}. Status set to Pending.`);
        return NextResponse.json({ result: { id: savedVideoId } }, { status: 200 });

    } catch (error) {
        // Catch errors from the transaction OR from outside steps like JSON parsing
        console.error(`[API Route Error] Error during save video process for user ${clerkId}:`, error);

        // Check if it's our custom ApiError to return specific status codes
        if (error instanceof ApiError) {
            return NextResponse.json({ error: error.message }, { status: error.statusCode });
        }

        // Handle generic errors (e.g., DB connection issues, unexpected transaction failures)
        return NextResponse.json({ error: "An unexpected server error occurred while saving video data." }, { status: 500 });
    }
}