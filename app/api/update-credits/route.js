import { NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server'; // Import Clerk auth
import { db } from '@/configs/db'; // Your Drizzle DB instance
import { users } from '@/configs/schema'; // Your Drizzle schema for users
import { eq, sql } from 'drizzle-orm'; // For the where clause and SQL expressions

export async function POST(request) {
    // --- Authentication ---
    const { userId } = await auth();
    if (!userId) {
        console.warn('[API Route Error] /api/update-credits: Unauthorized access attempt.');
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // --- End Authentication ---

    try {
        const reqBody = await request.json();
        const { credits, type } = reqBody; // Expecting 'credits' and 'type' ('add' or 'deduct')

        // Validate credits and type
        if (typeof credits !== 'number' || credits <= 0) {
            return NextResponse.json({ error: 'Valid credits amount is required' }, { status: 400 });
        }
        if (type !== 'add' && type !== 'deduct') {
            return NextResponse.json({ error: 'Invalid update type specified' }, { status: 400 });
        }

        console.log(`[API Route] User ${userId} requested to ${type} ${credits} credits.`);

        let updateOperation;
        if (type === 'add') {
            updateOperation = sql`${users.credits} + ${credits}`;
        } else { // type === 'deduct'
            // Optional: Add a check here or in a transaction to ensure sufficient credits before deducting
            updateOperation = sql`${users.credits} - ${credits}`;
        }

        // Update User Credits in Database using Drizzle
        const result = await db.update(users)
            .set({
                credits: updateOperation
            })
            .where(eq(users.clerkId, userId));

        if (result.rowsAffected === 0) {
             console.warn(`[API Route Warn] Database credit update failed for user: ${userId}. User not found or no changes made.`);
             return NextResponse.json({ success: false, error: 'User not found or credit update failed' }, { status: 404 });
        }

        console.log(`[API Route] Credit update successful for user: ${userId}. Type: ${type}, Amount: ${credits}.`);

        return NextResponse.json({ success: true }, { status: 200 });

    } catch (error) {
        console.error(`[API Route Error] Error in /api/update-credits for user ${userId}:`, error);
        const message = error.message || "An internal server error occurred during credit update";
        return NextResponse.json({ success: false, error: message }, { status: 500 });
    }
}