import crypto from 'crypto';
import { db } from '@/configs/db';
import { users, creditTransactions } from '@/configs/schema';
import { eq, and } from 'drizzle-orm';

/**
 * POST /api/lemonsqueezy-webhook
 * Handle LemonSqueezy webhook events for credit purchases
 */
export async function POST(request) {
  try {
    // 1. Webhook signature verification
    const body = await request.text();
    const signature = request.headers.get('x-signature');
    
    if (!process.env.LEMONSQUEEZY_WEBHOOK_SECRET) {
      console.error('LEMONSQUEEZY_WEBHOOK_SECRET not configured');
      return Response.json({ error: 'Webhook secret not configured' }, { status: 500 });
    }
    
    const expectedSignature = crypto
      .createHmac('sha256', process.env.LEMONSQUEEZY_WEBHOOK_SECRET)
      .update(body)
      .digest('hex');
    
    if (signature !== expectedSignature) {
      console.error('Invalid webhook signature');
      return Response.json({ error: 'Invalid signature' }, { status: 401 });
    }

    // 2. Parse webhook payload
    const payload = JSON.parse(body);
    const { event_name, data } = payload;
    
    console.log(`LemonSqueezy webhook received: ${event_name}`);
    
    if (event_name !== 'order_created') {
      return Response.json({ message: 'Event ignored' }, { status: 200 });
    }

    // 3. Extract purchase information
    const { 
      id: orderId, 
      attributes: { 
        user_email,
        custom_data
      }
    } = data;

    // Extract user ID and credits from custom data
    const userId = custom_data?.clerk_user_id;
    const creditsPurchased = custom_data?.credits_purchased;

    if (!userId || !creditsPurchased) {
      console.error('Missing required custom data in webhook:', { userId, creditsPurchased });
      return Response.json({ error: 'Missing required custom data' }, { status: 400 });
    }

    // 4. Idempotency check
    const existingTransaction = await db
      .select()
      .from(creditTransactions)
      .where(
        and(
          eq(creditTransactions.relatedEntityId, orderId.toString()),
          eq(creditTransactions.relatedEntityType, 'ORDER')
        )
      )
      .limit(1);

    if (existingTransaction.length > 0) {
      console.log(`Order ${orderId} already processed`);
      return Response.json({ message: 'Already processed' }, { status: 200 });
    }

    // 5. Atomic credit purchase transaction
    const result = await db.transaction(async (tx) => {
      // 5a. Fetch user with pessimistic lock
      const [user] = await tx
        .select()
        .from(users)
        .where(eq(users.clerkId, userId))
        .for('update');

      if (!user) {
        throw new Error('User not found');
      }

      // 5b. Calculate new balance
      const creditAmount = parseInt(creditsPurchased);
      const newBalance = user.currentCreditBalance + creditAmount;

      // 5c. Update user balance
      await tx
        .update(users)
        .set({ 
          currentCreditBalance: newBalance,
          updatedAt: new Date()
        })
        .where(eq(users.clerkId, userId));

      // 5d. Insert purchase transaction
      const [purchaseTransaction] = await tx
        .insert(creditTransactions)
        .values({
          userId,
          transactionType: 'PURCHASE',
          amount: creditAmount,
          balanceBefore: user.currentCreditBalance,
          balanceAfter: newBalance,
          relatedEntityId: orderId.toString(),
          relatedEntityType: 'ORDER',
          notes: `Credit purchase via LemonSqueezy`,
          metadata: { 
            orderId, 
            userEmail: user_email,
            purchaseAmount: creditAmount,
            webhookEventName: event_name
          }
        })
        .returning();

      return { purchaseTransaction, newBalance };
    });

    // 6. Log successful purchase
    console.log(`Credits purchased: ${userId} received ${creditsPurchased} credits from order ${orderId}`);

    return Response.json({ 
      success: true,
      creditsAdded: creditsPurchased,
      newBalance: result.newBalance
    }, { status: 200 });

  } catch (error) {
    console.error('LemonSqueezy webhook processing failed:', error);
    
    if (error.message === 'User not found') {
      return Response.json({ error: 'User not found' }, { status: 404 });
    }
    
    // Return 500 to trigger LemonSqueezy retry
    return Response.json({ error: 'Processing failed' }, { status: 500 });
  }
}
