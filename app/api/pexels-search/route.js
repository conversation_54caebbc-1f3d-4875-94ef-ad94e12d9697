// app/api/pexels-search/route.js
import { NextResponse } from 'next/server';

export async function GET(request) {
  const { searchParams } = new URL(request.url);
  const query = searchParams.get('query');

  if (!query) {
    return NextResponse.json({ error: 'Missing search query' }, { status: 400 });
  }

  const pexelsApiKey = process.env.PEXELS_API_KEY;

  if (!pexelsApiKey) {
    return NextResponse.json({ error: 'Pexels API key not configured' }, { status: 500 });
  }

  const url = `https://api.pexels.com/v1/search?query=${encodeURIComponent(query)}&per_page=15`;

  try {
    const response = await fetch(url, {
      headers: {
        Authorization: pexelsApiKey,
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Pexels API error: ${response.status} ${response.statusText} - ${errorText}`);
    }

    const data = await response.json();
    return NextResponse.json(data);

  } catch (error) {
    console.error('Error fetching from Pexels:', error);
    return NextResponse.json({ error: 'Failed to fetch images from Pexels' }, { status: 500 });
  }
}