// app/api/generate-script/route.js
import { NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server'; // Import Clerk auth
// import { db } from '@/configs/db'; // No longer needed directly
// import { users } from '@/configs/schema'; // No longer needed directly
// import { eq } from 'drizzle-orm'; // No longer needed directly
import { generateScriptFromAI } from '@/src/lib/aiUtils'; // Import the refactored AI utility function
import { hasSufficientCredits, VIDEO_GENERATION_COST } from '@/lib/creditUtils'; // Import credit utility


export async function POST(request) {
    // --- Authentication and User Check ---
    const { userId } = await auth();
    console.log(userId);
    
    if (!userId) {
      console.warn('[API Route Error] /api/generate-script: Unauthorized access attempt.');
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // --- End Authentication ---

    let topic;
    try {
        const body = await request.json();
        topic = body.topic;
        if (!topic || typeof topic !== 'string' || topic.trim() === '') {
            return NextResponse.json({ error: 'Missing or invalid topic' }, { status: 400 });
        }
        topic = topic.trim(); // Use trimmed topic
    } catch (error) {
        console.error("[API Route - Public] Error parsing request body:", error);
        return NextResponse.json({ error: 'Invalid request body' }, { status: 400 });
    }

    // Basic input validation (length check)
    if (topic.length > 200) { // Limit topic length
        return NextResponse.json({ error: 'Topic is too long (max 200 characters)' }, { status: 400 });
    }


    try {
        // --- Credit Check ---
        console.log(`[API Route] User ${userId} requesting script generation for topic: "${topic}"`);
        const sufficientCredits = await hasSufficientCredits(userId, VIDEO_GENERATION_COST);
        if (!sufficientCredits) {
            console.warn(`[API Route Error] User ${userId} has insufficient credits for video generation (cost: ${VIDEO_GENERATION_COST}).`);
            return NextResponse.json({ error: "Insufficient credits for video generation." }, { status: 402 }); // 402 Payment Required
        }
        // --- End Credit Check ---

        // Call the AI utility function to generate the script
        console.log(`[API Route] User ${userId} calling AI utility for topic: "${topic}"`);
        const generatedScript = await generateScriptFromAI(topic);

        // Return the generated script
        console.log(`[API Route] User ${userId} successfully generated script via utility for topic: "${topic}".`);
        return NextResponse.json({ script: generatedScript });

    } catch (error) {
        // Log the error that occurred during script generation
        console.error(`[API Route Error] /api/generate-script for user ${userId}:`, error);

        // Determine if the error originated from the AI utility
        // The utility function throws errors starting with "AI script generation failed:"
        const isAIError = error.message.startsWith("AI script generation failed:");

        // Provide a user-friendly error message
        const errorMessage = isAIError
            ? `Failed to generate script: ${error.message}` // Include specific AI error
            : 'An unexpected server error occurred while generating the script.';

            

        // Set appropriate status code: 503 for AI issues, 500 for other server errors
        const statusCode = isAIError ? 503 : 500;

        return NextResponse.json({ error: errorMessage }, { status: statusCode });
    }
}
