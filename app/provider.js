"use client"

import { useUser } from "@clerk/nextjs";
import React, { useEffect, useCallback } from "react";
import { UserDetailProvider } from '@/context/UserDetailContext';
import { VideoDataProvider } from '@/context/VideoDataContext';
import { initializeUser } from './lib/user-initialization';

/**
 * Provider component that wraps children with context providers
 * and handles user initialization using server actions.
 *
 * This component has been refactored to use server actions instead of
 * direct client-side database operations for better security and performance.
 */
function Provider({ children }) {
    const { user, isLoaded } = useUser();

    const handleUserInitialization = useCallback(async () => {
        try {
            // Extract plain data from Clerk user object to avoid client reference issues
            const userData = {
                id: user.id,
                fullName: user.fullName,
                primaryEmailAddress: {
                    emailAddress: user.primaryEmailAddress?.emailAddress
                },
                imageUrl: user.imageUrl,
            };

            // Call server action to initialize user
            const result = await initializeUser(userData);

            if (!result.success) {
                console.error('[Provider] User initialization failed:', result.error);
                // Note: We don't throw here to avoid breaking the app
                // The user can still use the app, but some features might be limited
            }
        } catch (error) {
            console.error('[Provider] Error during user initialization:', error);
            // Note: We don't throw here to avoid breaking the app
        }
    }, [user]);

    useEffect(() => {
        // Only attempt user initialization when Clerk has loaded and user exists
        if (isLoaded && user) {
            handleUserInitialization();
        }
    }, [user, isLoaded, handleUserInitialization]);

    // Wrap children with the context providers
    return (
        <UserDetailProvider>
            <VideoDataProvider>
                {children}
            </VideoDataProvider>
        </UserDetailProvider>
    );
}

export default Provider;
