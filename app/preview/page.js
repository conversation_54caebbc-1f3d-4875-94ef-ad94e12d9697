'use client';

import { useEffect, useState, useMemo, Suspense } from 'react';
import { useSearchParams } from 'next/navigation';
import { Player } from '@remotion/player';
import dynamic from 'next/dynamic';
import StockMediaVideoComposition from '@/remotion/compositions/StockMediaVideoComposition';

// Constants for the video composition
const COMPOSITION_ID = 'StockMediaVideo';
const COMPOSITION_WIDTH = 1080;
const COMPOSITION_HEIGHT = 1920;
const FPS = 30;

// Dynamically import the Remotion component to avoid SSR issues
const RemotionRoot = dynamic(
  () => import('@/remotion/Root').then((mod) => mod.RemotionRoot),
  { ssr: false }
);

function useVideoData(videoId) {
  const [videoData, setVideoData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  useEffect(() => {
    if (!videoId || !isClient) {
      if (isClient) setError('No video ID provided');
      setLoading(false);
      return;
    }

    const fetchStockMediaVideo = async () => {
      setLoading(true);
      try {
        const response = await fetch(`/api/stock-media-video/${videoId}`);
        if (!response.ok) {
          const errorData = await response.json().catch(() => ({ error: response.statusText }));
          throw new Error(errorData.error || `Failed to fetch stock media video data: ${response.status}`);
        }
        const apiData = await response.json();
        
        const transformedVideoData = {
          id: apiData.id,
          prompt: apiData.prompt,
          script: apiData.script || '',
          scenes: apiData.scenes || [], 
          mediaAssets: apiData.mediaAssets || { scenes: [], backgroundMusic: null, watermark: null },
          selectedMedia: apiData.selectedMedia || [],
          audioUrl: apiData.audioUrl || '',
          status: apiData.status,
          voiceConfig: apiData.voiceConfig || {},
          videoConfig: apiData.videoConfig || {},
          renderSettings: apiData.renderSettings || {},
          metadata: apiData.metadata || {},
          userId: apiData.userId,
          createdAt: apiData.createdAt,
          updatedAt: apiData.updatedAt,
          renderedVideoUrl: apiData.renderedVideoUrl,
        };
        
        setVideoData(transformedVideoData);
      } catch (err) {
        console.error('Error fetching stock media video:', err);
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchStockMediaVideo();
  }, [videoId, isClient]);

  return { videoData, loading, error, isClient };
}

function VideoPreviewContent() {
  const searchParams = useSearchParams();
  const videoId = searchParams.get('id');
  const { videoData, loading, error, isClient } = useVideoData(videoId);

  // Player dimensions and FPS, derived from videoData or defaults
  const playerCompositionId = COMPOSITION_ID; // Static for this page, as StockMediaVideoComposition is hardcoded
  const playerWidth = parseInt(videoData?.renderSettings?.resolution?.split('x')[0], 10) || COMPOSITION_WIDTH;
  const playerHeight = parseInt(videoData?.renderSettings?.resolution?.split('x')[1], 10) || COMPOSITION_HEIGHT;
  const playerFps = videoData?.renderSettings?.frameRate || FPS;
  
  console.log('VideoData for Player:', videoData); 

  // Calculate duration based on scenes or use a default from metadata
  const durationInFrames = useMemo(() => {
    if (videoData?.metadata?.totalDuration) {
      return videoData.metadata.totalDuration * playerFps;
    }
    if (videoData?.mediaAssets?.scenes?.length) {
        // Sum of durations from mediaAssets if available
        return videoData.mediaAssets.scenes.reduce((acc, scene) => acc + (scene.duration || 5) * playerFps, 0);
    }
    if (videoData?.scenes?.length) {
      return videoData.scenes.length * 5 * playerFps; // Fallback: 5 seconds per scene
    }
    return 10 * playerFps; // Absolute fallback: 10 seconds
  }, [videoData, playerFps]);

  // Prepare input props for Remotion Player, targeting StockMediaVideoComposition
  const inputProps = useMemo(() => {
    if (!videoData) {
      // Provide a default structure for StockMediaVideoComposition when videoData is null
      return {
        videoData: {
          script: '',
          scenes: [],
          mediaAssets: { scenes: [], backgroundMusic: null, watermark: null },
          selectedMedia: [],
          audioUrl: '',
          prompt: '',
        }
      };
    }
    // videoData from state is the direct payload for the 'videoData' prop
    return {
      videoData: videoData
    };
  }, [videoData]);

  if (!videoId) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-xl">No video ID provided. Please provide a video ID in the URL.</div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-xl">Loading video data...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-xl text-red-500">Error: {error}</div>
      </div>
    );
  }

  if (!videoData) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-xl">No video data found</div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-6">Video Preview: {videoData.prompt}</h1>
      
      <div className="bg-gray-100 p-4 rounded-lg mb-6">
        <div className="aspect-[9/16] w-full max-w-md mx-auto">
          {typeof window !== 'undefined' && (
            <Player
              component={StockMediaVideoComposition}
              inputProps={inputProps}
              durationInFrames={durationInFrames}
              compositionWidth={playerWidth}
              compositionHeight={playerHeight}
              fps={playerFps}
              style={{
                width: '100%',
                height: '100%',
                borderRadius: '8px',
                overflow: 'hidden',
              }}
              controls
              loop
              compositionId={playerCompositionId}
              acknowledgeRemotionLicense
            />
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-white p-4 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">Video Details</h2>
          <div className="space-y-2">
            <p><span className="font-medium">Status:</span> {videoData.status}</p>
            <p><span className="font-medium">Created:</span> {new Date(videoData.createdAt).toLocaleString()}</p>
            <p><span className="font-medium">Duration:</span> {Math.round(durationInFrames / 30)} seconds</p>
            <p><span className="font-medium">Scenes:</span> {videoData.scenes?.length || 0}</p>
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">Script</h2>
          <div className="whitespace-pre-line bg-gray-50 p-3 rounded">
            {videoData.script || 'No script available'}
          </div>
        </div>
      </div>
    </div>
  );
}

export default function VideoPreview() {
  return (
    <Suspense fallback={
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center space-y-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="text-muted-foreground">Loading video preview...</p>
        </div>
      </div>
    }>
      <VideoPreviewContent />
    </Suspense>
  );
}
