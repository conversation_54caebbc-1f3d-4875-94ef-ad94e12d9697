import { inngest } from "./client";
import { db } from "@/configs/db"; // Corrected import path
import { ListObjectsV2Command, S3Client } from "@aws-sdk/client-s3";
import { users, uploadedFile, clip, videoData } from "@/configs/schema"; // Import videoData schema, remove stockMediaVideo
import { eq, sql } from 'drizzle-orm'; // Add sql to the import

// Import the Stock Media Video Generation function from its new file
import { generateStockMediaVideo } from "./functions/stockMediaVideoGeneration";
import { redditVideoGeneration } from "./functions/redditVideoGeneration"; // Import Reddit function
import { twitterVideoGeneration } from "./functions/twitterVideoGeneration"; // Import Twitter function
import { aiVideoGeneration } from "./functions/aiVideoGeneration"; // Import AI Video function
import { memeVideoGeneration } from "./functions/memeVideoGeneration"; // Import Meme Video function
import { uploadYouTubeVideo } from "./functions/uploadYouTubeVideo"; // Import the new YouTube upload function
import { aiUgcVideoGeneration } from "./functions/aiUgcVideoGeneration"; // Import AI UGC Video function

const processVideo = inngest.createFunction(
  {
    id: "process-video",
    retries: 1,
    concurrency: {
      limit: 1,
      key: "event.data.userId",
    },
  },
  { event: "process-video-events" },
  async ({ event, step }) => {
    // Destructure new parameters from event.data
    const { uploadedFileId, userId, userPrompt, numberOfClips, requestId } = event.data;
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';
    let videoId; // Declare videoId in function scope

    try {
      // Step 1: Create a new video record in the database (in video_data table)
      const newVideo = await step.run("create-podcast-video-record", async () => {
        // Fetch uploaded file details to use in title/topic
        const uploadedFileRecord = await db.query.uploadedFile.findFirst({
            where: eq(uploadedFile.id, uploadedFileId),
        });

        const title = uploadedFileRecord?.displayName || `Podcast Clip: ${userPrompt?.substring(0, 40) || uploadedFileId}`;
        const topic = `Podcast Clipping: ${userPrompt || uploadedFileRecord?.displayName}`;

        const [insertedVideo] = await db.insert(videoData).values({
          clerkId: userId,
          title: title,
          topic: topic,
          templateId: 'PodcastClipVideo', // Set the correct template ID (assuming one exists)
          status: 'processing', // Initial status
          // Link to the original uploaded file
          workflow_data: {
              type: 'podcast-clipper',
              uploadedFileId: uploadedFileId,
              userPrompt: userPrompt,
              numberOfClips: numberOfClips,
              requestId: requestId,
              // Clips data will be added later
          }
        }).returning({ id: videoData.id });

        if (!insertedVideo) {
          throw new Error("Failed to create new video record in database.");
        }
        return insertedVideo;
      });

      videoId = newVideo.id; // Assign to the function-scoped variable
      console.log(`[Inngest Function] processVideo: Created video record with ID: ${videoId} in video_data.`);


      const { credits, s3Key } = await step.run(
        "check-credits",
        async () => {
          // Fetch uploaded file and related user credits
          const uploadedFileRecord = await db.query.uploadedFile.findFirst({
            where: eq(uploadedFile.id, uploadedFileId),
            with: {
              user: {
                columns: {
                  clerkId: true,
                  credits: true,
                },
              },
            },
          });

          if (!uploadedFileRecord) {
            throw new Error(`Uploaded file with ID ${uploadedFileId} not found.`);
          }

          // Ensure the userId from the event matches the user associated with the file
          if (uploadedFileRecord.user.clerkId !== userId) {
             throw new Error("User ID mismatch between event and uploaded file record.");
          }

          return {
            userId: uploadedFileRecord.user.clerkId, // Return clerkId
            credits: uploadedFileRecord.user.credits,
            s3Key: uploadedFileRecord.s3Key,
          };
        },
      );

      if (credits > 0) {
        await step.run("set-status-processing-uploaded-file", async () => {
          await db.execute(sql`UPDATE ${uploadedFile} SET status = 'processing' WHERE ${uploadedFile.id} = ${uploadedFileId}`);
        });
         // Also update the status in the video_data record
        await step.run("set-status-processing-video-data", async () => {
             await db.update(videoData).set({ status: 'processing', updatedAt: new Date() }).where(eq(videoData.id, videoId));
        });


        // Include new parameters in the fetch call body
        const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';
        await step.fetch(process.env.PROCESS_VIDEO_ENDPOINT, {
          method: "POST",
          body: JSON.stringify({
            s3_key: s3Key,
            user_prompt: userPrompt, // Added userPrompt
            number_of_clips: numberOfClips, // Added numberOfClips
            request_id: requestId, // Added requestId
          }),
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${process.env.PROCESS_VIDEO_ENDPOINT_AUTH}`,
          },
        });

        const { clipsFound } = await step.run(
          "create-clips-in-db",
          async () => {
            const folderPrefix = s3Key.split("/")[0];

            const allKeys = await listS3ObjectsByPrefix(folderPrefix);

            const clipKeys = allKeys.filter(
              (key) =>
                key !== undefined && !key.endsWith("original.mp4"),
            );

            if (clipKeys.length > 0) {
              // Use db.insert for createMany equivalent in Drizzle
              // We are still saving clip metadata in the 'clips' table
              await db.insert(clip).values(clipKeys.map((clipKey) => ({
                  s3Key: clipKey,
                  uploadedFileId: uploadedFileId, // Use uploadedFileId from event/context
                  userId: userId, // Use userId (Clerk ID) from event/context
                }))
              );

              // Fetch the created clips to store their data in video_data
              const createdClips = await db.query.clip.findMany({
                  where: eq(clip.uploadedFileId, uploadedFileId),
              });

              // --- Start: Fetch-Modify-Save approach for JSONB update ---
              // Fetch the existing video_data record
              const videoRecord = await db.query.videoData.findFirst({
                  where: eq(videoData.id, videoId),
              });

              if (!videoRecord) {
                  console.error(`[Inngest Error] Video record with ID ${videoId} not found for JSONB update.`);
                  throw new Error(`Video record with ID ${videoId} not found.`);
              }

              // Get the current workflow_data, default to empty object if null
              const currentWorkflowData = videoRecord.workflow_data || {};

              // Merge the new clips data into the workflow_data object
              const updatedWorkflowData = {
                  ...currentWorkflowData,
                  clips: createdClips, // Add or overwrite the 'clips' key
              };

              // Update the video_data record with the modified workflow_data object
              await db.update(videoData)
                .set({
                    workflow_data: updatedWorkflowData,
                    updatedAt: new Date(),
                })
                .where(eq(videoData.id, videoId)); // Use the videoId created earlier
              // --- End: Fetch-Modify-Save approach ---

            }

            return { clipsFound: clipKeys.length };
          },
        );

        await step.run("deduct-credits", async () => {
          // Update user credits using clerkId as per schema
          await db.update(users)
            .set({
              credits: sql`${users.credits} - ${Math.min(credits, clipsFound)}`,
            })
            .where(eq(users.clerkId, userId)); // Use clerkId for the where clause
        });

        await step.run("set-status-processed-uploaded-file", async () => {
          await db.execute(sql`UPDATE ${uploadedFile} SET status = 'processed' WHERE ${uploadedFile.id} = ${uploadedFileId}`);
        });
         // Also update the status in the video_data record
        await step.run("set-status-Completed-video-data", async () => {
             await db.update(videoData).set({ status: 'Completed', updatedAt: new Date() }).where(eq(videoData.id, videoId));
        });

      } else {
        await step.run("set-status-no-credits-uploaded-file", async () => {
          await db.execute(sql`UPDATE ${uploadedFile} SET status = 'no credits' WHERE ${uploadedFile.id} = ${uploadedFileId}`);
        });
         // Also update the status in the video_data record
        await step.run("set-status-no-credits-video-data", async () => {
             await db.update(videoData).set({ status: 'no credits', updatedAt: new Date() }).where(eq(videoData.id, videoId));
        });
      }
    } catch (error) {
      console.error("[Inngest Function Error] processVideo:", error);
      // Attempt to set status to failed if an error occurs
      try {
         // Update status in both tables
         await db.execute(sql`UPDATE ${uploadedFile} SET status = 'failed' WHERE ${uploadedFile.id} = ${uploadedFileId}`);
         if (videoId) {
             await db.update(videoData).set({ status: 'failed', updatedAt: new Date() }).where(eq(videoData.id, videoId));
         }
      } catch (updateError) {
         console.error("Failed to update status to 'failed':", updateError);
      }
      // Re-throw the error so Inngest knows the step failed
      throw error;
    }
  },
);


async function listS3ObjectsByPrefix(prefix) {
  const s3Client = new S3Client({
    region: process.env.AWS_REGION,
    credentials: {
      accessKeyId: process.env.AWS_ACCESS_KEY_ID,
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
    },
  });

  const listCommand = new ListObjectsV2Command({
    Bucket: process.env.S3_BUCKET_NAME,
    Prefix: prefix,
  });

  const response = await s3Client.send(listCommand);
  return response.Contents?.map((item) => item.Key).filter(Boolean) ?? [];
}

// Export the functions, including the imported ones
export const functions = [
  processVideo,
  generateStockMediaVideo,
  redditVideoGeneration,
  twitterVideoGeneration,
  aiVideoGeneration,
  memeVideoGeneration,
  uploadYouTubeVideo,
  aiUgcVideoGeneration,
  // Add the new narrator video generation function here
];
