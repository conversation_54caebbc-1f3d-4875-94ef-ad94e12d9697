import { inngest } from "../client";
import { hasSufficientCredits, VIDEO_GENERATION_COST } from '@/lib/creditUtils';
import { db } from "@/configs/db";
import { users, videoData, uploadedFile } from "@/configs/schema"; // Import videoData and uploadedFile schema
import { eq, sql } from 'drizzle-orm';

// Import resilient API helpers
import {
  createEmergencyFallbacks,
  checkAPIHealth
} from '@/lib/inngestApiHelpers';
import { withCircuitBreaker } from '@/lib/circuitBreaker';

// Import any necessary utilities or logic for processing Meme data
// (e.g., handling video source, applying text/styling, potentially rendering logic if done server-side)

export const memeVideoGeneration = inngest.createFunction(
  { id: "meme-video-generation" },
  { event: "app/meme-video.generate" }, // Updated event name to match new system
  async ({ event, step }) => {
    const { userId, videoId, workflowType, ...videoConfig } = event.data;
    const { projectTitle, videoSource, memeText, font, fontSize, textColor, textOutline, outlineColor, outlineThickness, textShadow, backgroundColor, textPosition, aspectRatio, videoStartTime, videoEndTime, useOriginalAudio, backgroundMusic, originalAudioVolume, backgroundMusicVolume } = videoConfig;

    // --- Input Validation (Basic) ---
    if (!userId || !projectTitle || !videoSource || !memeText || !font || !fontSize || !textColor || textOutline === null || outlineColor === null || outlineThickness === null || textShadow === null || textPosition === null || aspectRatio === null || videoStartTime === null || useOriginalAudio === null || originalAudioVolume === null || backgroundMusicVolume === null) {
         console.error("[Inngest Error] Missing required formData fields or userId:", { userId, formData });
         throw new Error("Missing required input data for Meme video generation.");
    }
    // Add more specific validation for videoSource (URL or uploaded file ID)
    // --- End Input Validation ---

    // --- Step 0: Check API Health ---
    const healthStatus = await checkAPIHealth(step);
    console.log('[Meme Video] API Health Status:', healthStatus);

    // --- Step 1: Verify Video Record Exists ---
    const videoRecord = await step.run("verify-video-record", async () => {
        console.log(`[Inngest] Verifying video record exists for video ID: ${videoId}...`);

        const [video] = await db
            .select()
            .from(videoData)
            .where(eq(videoData.id, videoId))
            .limit(1);

        if (!video) {
            throw new Error(`Video record not found for ID: ${videoId}`);
        }

        if (video.clerkId !== userId) {
            throw new Error(`Video record does not belong to user: ${userId}`);
        }

        console.log(`[Inngest] Video record verified for ID: ${videoId}`);
        return video;
    });

    // --- Step 2: Process Video Source (if it's an uploaded file) ---
    // If videoSource is an uploaded file ID, you might need to fetch its S3 URL
    let videoSourceUrl = videoSource;
    if (typeof videoSource === 'number') { // Assuming videoSource is the uploadedFileId if it's a number
        videoSourceUrl = await step.run("get-uploaded-file-url", async () => {
            const uploadedFileRecord = await db.query.uploadedFile.findFirst({
                where: eq(uploadedFile.id, videoSource),
            });
            if (!uploadedFileRecord) {
                throw new Error(`Uploaded file with ID ${videoSource} not found.`);
            }
            // Assuming S3 keys can be converted to URLs or used directly by rendering
            // This might require generating a pre-signed URL or using a CDN URL
            // For now, placeholder:
            return `placeholder_s3_url_for_${uploadedFileRecord.s3Key}`; // Replace with actual URL logic
        });
    }


    // --- Step 3: Update Video Record with Meme Configuration ---
    const savedVideoId = await step.run("update-meme-video-data", async () => {
        console.log("[Inngest] Updating video record with meme configuration...");

        const updatePayload = {
            script: memeText, // Use meme text as the script
            status: 'Ready for Rendering',
            updatedAt: new Date(),
            // Update workflow-specific data
            workflow_data: {
                ...videoRecord.workflow_data,
                processedConfig: {
                    videoSource: videoSourceUrl,
                    memeText: memeText,
                    font: font,
                    fontSize: fontSize,
                    textColor: textColor,
                    textOutline: textOutline,
                    outlineColor: outlineColor,
                    outlineThickness: outlineThickness,
                    textShadow: textShadow,
                    backgroundColor: backgroundColor,
                    textPosition: textPosition,
                    videoStartTime: videoStartTime,
                    videoEndTime: videoEndTime,
                    useOriginalAudio: useOriginalAudio,
                    originalAudioVolume: originalAudioVolume,
                    backgroundMusicVolume: backgroundMusicVolume
                }
            }
        };

        const result = await db
            .update(videoData)
            .set(updatePayload)
            .where(eq(videoData.id, videoId))
            .returning({ id: videoData.id });

        if (!result || result.length === 0) {
            console.error(`[Inngest Error] Failed to update meme video data for video ID: ${videoId}`);
            throw new Error("Failed to update meme video data in the database.");
        }

        console.log(`[Inngest] Meme video data updated successfully for video ID: ${videoId}`);
        return videoId;
    });

    return {
      message: "Meme Video Generation workflow Completed!",
      videoId: savedVideoId,
    };
  },
  {
    // Add error handling to trigger credit refunds
    onFailure: async ({ event, error }) => {
      console.error(`[Inngest] Meme Video Generation failed for video ${event.data.videoId}:`, error);

      // Import the refund function
      const { triggerCreditRefund } = await import('@/lib/atomicCreditSystem');
      await triggerCreditRefund(event.data.videoId, `Meme Video Generation failed: ${error.message}`);
    }
  }
);
