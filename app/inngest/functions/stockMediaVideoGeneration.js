import { inngest } from "../client";
import { db } from "@/configs/db";
import { ListObjectsV2Command, S3Client } from "@aws-sdk/client-s3";
import { users, stockMediaVideo, videoData } from "@/configs/schema"; // Import videoData schema
import { eq, sql } from 'drizzle-orm';

// Import resilient API helpers
import {
  resilientGoogleAI,
  resilientPexels,
  resilientPixabay,
  createEmergencyFallbacks,
  checkAPIHealth,
  rateLimitAwareBatch
} from '@/lib/inngestApiHelpers';
import { withCircuitBreaker } from '@/lib/circuitBreaker';

// Import any necessary utilities or logic for stock media generation
// (e.g., API calls for script/scene, search, evaluation, voiceover)

export const generateStockMediaVideo = inngest.createFunction(
  {
    id: "generate-stock-media-video",
    retries: 1,
    concurrency: {
      limit: 1,
      key: "event.data.userId", // Concurrency key based on user
    },
  },
  { event: "generate-stock-media-video-event" }, // New event trigger
  async ({ event, step }) => {
    const { userId, userPrompt, videoConfig } = event.data; // Destructure event data
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';
    let videoId; // Declare videoId in function scope to be accessible in error handler

    try {
      console.log(`[Inngest Function] generateStockMediaVideo: Initiated for user ${userId} with prompt: "${userPrompt}"`);

      // Step 0: Check API Health
      const healthStatus = await checkAPIHealth(step);
      console.log('[Stock Media Video] API Health Status:', healthStatus);

      // Step 1: Create a new video record in the database (in video_data table)
      const newVideo = await step.run("create-stock-media-video-record", async () => {
        // Initial minimal insert into video_data
        const [insertedVideo] = await db.insert(videoData).values({
          clerkId: userId,
          title: userPrompt.substring(0, 50) + '...', // Use prompt as initial title
          topic: userPrompt, // Use prompt as topic
          templateId: 'StockMediaVideo', // Set the correct template ID
          status: 'processing', // Initial status
          // Add other default fields if necessary
        }).returning({ id: videoData.id });

        if (!insertedVideo) {
          throw new Error("Failed to create new video record in database.");
        }
        return insertedVideo;
      });

      videoId = newVideo.id; // Assign to the function-scoped variable
      console.log(`[Inngest Function] generateStockMediaVideo: Created video record with ID: ${videoId} in video_data.`);

      // Step 2: Call AI for script and scene conceptualization with resilient API
      const scriptResult = await step.run("generate-script-and-scenes-resilient", async () => {
        console.log("Calling AI for stock media script and scene generation with resilient API, prompt:", userPrompt);

        try {
          return await withCircuitBreaker('GOOGLE_AI', async () => {
            const response = await step.fetch(`${baseUrl}/api/generate-stock-media-script`, {
              method: "POST",
              body: JSON.stringify({ prompt: userPrompt }),
              headers: { "Content-Type": "application/json" },
            });

            if (!response.ok) {
              const errorBody = await response.text();
              throw new Error(`Failed to generate script: ${response.status} ${response.statusText} - ${errorBody}`);
            }

            const data = await response.json();
            // Assuming the API returns an object with 'script' and 'scenes' properties
            if (!data || !data.script || !Array.isArray(data.scenes)) {
                 throw new Error("Invalid response from script generation API.");
            }
            return data;
          });
        } catch (error) {
          console.log("[Stock Media Video] AI script generation failed, using emergency fallback");
          const fallbacks = createEmergencyFallbacks();
          const fallbackScript = await fallbacks.scriptFallback(userPrompt, 'stock-media');

          return {
            script: fallbackScript,
            scenes: [
              {
                scene_number: 1,
                description: "Stock media scene for " + userPrompt,
                search_keywords_for_stock_api: userPrompt.split(' ').slice(0, 3)
              }
            ]
          };
        }
      });
      console.log(`[Inngest Function] generateStockMediaVideo: Generated script and scenes.`);

      // Step 3: Process each scene
      const selectedMediaForScenes = await Promise.all(scriptResult.scenes.map(async (scene) => {
        // Step 3a: Search stock media based on keywords with resilient APIs
        const mediaCandidates = await step.run(`search-stock-media-scene-resilient-${scene.scene_number}`, async () => {
          console.log(`Searching stock media for scene ${scene.scene_number} with resilient APIs, keywords: ${scene.search_keywords_for_stock_api.join(', ')}`);

          const searchQuery = scene.search_keywords_for_stock_api.join(' ');
          const allMedia = [];

          // Try Pexels first with circuit breaker protection
          try {
            await withCircuitBreaker('PEXELS', async () => {
              const [pexelsImages, pexelsVideos] = await Promise.all([
                step.fetch(`${baseUrl}/api/pexels-search?query=${encodeURIComponent(searchQuery)}`),
                step.fetch(`${baseUrl}/api/pexels-video-search?query=${encodeURIComponent(searchQuery)}`),
              ]);

              // Process Pexels Images
              if (pexelsImages.ok) {
                const data = await pexelsImages.json();
                if (data && Array.isArray(data.photos)) {
                  allMedia.push(...data.photos.map(photo => ({ url: photo.src.large, type: 'image', source: 'pexels' })));
                }
              }

              // Process Pexels Videos
              if (pexelsVideos.ok) {
                const data = await pexelsVideos.json();
                if (data && Array.isArray(data.videos)) {
                  allMedia.push(...data.videos.map(video => ({ url: video.video_files[0]?.link, type: 'video', source: 'pexels' })));
                }
              }
            });
          } catch (pexelsError) {
            console.log(`[Stock Media Video] Pexels failed for scene ${scene.scene_number}, trying Pixabay fallback`);
          }

          // Try Pixabay as fallback
          try {
            await withCircuitBreaker('PIXABAY', async () => {
              const pixabayImages = await step.fetch(`${baseUrl}/api/pixabay-search?query=${encodeURIComponent(searchQuery)}`);

              if (pixabayImages.ok) {
                const data = await pixabayImages.json();
                if (data && Array.isArray(data.hits)) {
                  allMedia.push(...data.hits.map(hit => ({ url: hit.webformatURL, type: 'image', source: 'pixabay' })));
                }
              }
            });
          } catch (pixabayError) {
            console.log(`[Stock Media Video] Both image APIs failed for scene ${scene.scene_number}`);
          }

          // If no media found, use emergency fallback
          if (allMedia.length === 0) {
            console.log(`[Stock Media Video] No media found, using emergency fallback for scene ${scene.scene_number}`);
            const fallbacks = createEmergencyFallbacks();
            const fallbackImages = await fallbacks.imageFallback([searchQuery]);
            allMedia.push(...fallbackImages.photos.map(photo => ({ url: photo.src.large, type: 'image', source: 'fallback' })));
          }

          console.log(`Found ${allMedia.length} media candidates for scene ${scene.scene_number}.`);
          return allMedia;
        });
        console.log(`[Inngest Function] generateStockMediaVideo: Found ${mediaCandidates.length} media candidates for scene ${scene.scene_number}.`);

        // Step 3b: Call AI for media evaluation and selection
        const selectedMedia = await step.run(`evaluate-media-scene-${scene.scene_number}`, async () => {
          console.log(`Calling AI for media evaluation for scene ${scene.scene_number}.`);
          // baseUrl is now defined at the function level
          const response = await step.fetch(`${baseUrl}/api/evaluate-media`, {
            method: "POST",
            body: JSON.stringify({ script: scene, media: mediaCandidates }),
            headers: { "Content-Type": "application/json" },
          });

          if (!response.ok) {
            const errorBody = await response.text();
            throw new Error(`Failed to evaluate media for scene ${scene.scene_number}: ${response.status} ${response.statusText} - ${errorBody}`);
          }

          const data = await response.json();
          // Assuming the API returns an object with the selected media details
          if (!data || !data.selectedMedia) {
               throw new Error("Invalid response from media evaluation API.");
          }
          return data.selectedMedia;
        });
        console.log(`[Inngest Function] generateStockMediaVideo: Selected media for scene ${scene.scene_number}: ${selectedMedia.url}`);

        return { scene_number: scene.scene_number, selectedMedia: selectedMedia };
      }));
      console.log(`[Inngest Function] generateStockMediaVideo: Selected media for all scenes.`);

      // Step 4: Generate voiceover
      const voiceoverResult = await step.run("generate-voiceover", async () => {
        console.log("Generating voiceover for script.");
        const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';
        
        // Default voice configuration if none provided
        const defaultVoiceConfig = {
          voice: 'en-US-Standard-C',  // Default voice
          audioSpeed: 1.0,            // Normal speed
          pitch: 0,                   // Neutral pitch
          volumeGainDb: 0             // No volume adjustment
        };
        
        // Merge provided config with defaults
        const voiceConfig = {
          ...defaultVoiceConfig,
          ...(videoConfig?.voiceConfig || {})
        };
        
        console.log(`Using voice config:`, voiceConfig);
        
        const response = await step.fetch(`${baseUrl}/api/generate-audio`, {
          method: "POST",
          body: JSON.stringify({ 
            script: scriptResult.script, 
            voice: voiceConfig.voice,
            audioSpeed: voiceConfig.audioSpeed,
            pitch: voiceConfig.pitch,
            volumeGainDb: voiceConfig.volumeGainDb
          }),
          headers: { "Content-Type": "application/json" },
        });

        if (!response.ok) {
          const errorBody = await response.text();
          throw new Error(`Failed to generate voiceover: ${response.status} ${response.statusText} - ${errorBody}`);
        }

        const data = await response.json();
        // Assuming the API returns an object with 'audioUrl'
        if (!data || !data.audioUrl) {
             throw new Error("Invalid response from voiceover generation API.");
        }
        return data;
      });
      console.log(`[Inngest Function] generateStockMediaVideo: Generated voiceover.`);

      // Step 5: Save video data (including selected media info and audio URL) to DB (in video_data table)
      await step.run("save-final-video-data", async () => {
        console.log(`Updating video record with ID: ${videoId} in video_data.`);
        
        // Format the Stock Media specific data for the jsonb field
        const stockMediaWorkflowData = {
            type: 'stock-media-video',
            originalPrompt: userPrompt,
            script: scriptResult.script,
            // Combine original scene data with selected media into a new scenes array
            scenes: scriptResult.scenes.map(originalScene => {
                const selectedMediaForScene = selectedMediaForScenes.find(s => s.scene_number === originalScene.scene_number);
                return {
                    ...originalScene,
                    media: selectedMediaForScene ? selectedMediaForScene.selectedMedia : null, // Embed selected media directly
                };
            }),
            voiceConfig: videoConfig?.voiceConfig || {}, // Voice settings
            videoConfig: videoConfig || {}, // Video settings
            // The 'selectedMedia' top-level property is no longer needed as it's embedded in scenes
            // mediaAssets is now implicitly part of the 'scenes' structure
        };

        await db.update(videoData)
          .set({
            script: scriptResult.script, // Save script in main field too for easier access
            audioUrl: voiceoverResult.audioUrl, // Save audio URL in main field
            status: 'data_saved', // Update status
            updatedAt: new Date(),
            workflow_data: stockMediaWorkflowData, // Store workflow-specific data in jsonb
          })
          .where(eq(videoData.id, videoId));
          
        console.log(`Updated video record with ID: ${videoId} in video_data.`);
      });
      console.log(`[Inngest Function] generateStockMediaVideo: Saved final video data to video_data.`);

      // Step 6: Skip video rendering and update status to 'Completed' with all data
      console.log(`[Inngest Function] generateStockMediaVideo: Skipping video rendering as per configuration.`);

      // Update video status to 'Completed' in the database (video_data table)
      await step.run("update-video-status-Completed", async () => {
        await db.update(videoData)
          .set({ status: 'Completed', updatedAt: new Date() })
          .where(eq(videoData.id, videoId));
          
        console.log(`Updated video status to 'Completed' for video ID: ${videoId} in video_data.`);
      });
      console.log(`[Inngest Function] generateStockMediaVideo: Video data status updated to Completed.`);

      console.log(`[Inngest Function] generateStockMediaVideo: Completed orchestration for user ${userId}. Rendering is now in progress.`);

    } catch (error) {
      console.error(`[Inngest Function Error] generateStockMediaVideo: Failed for user ${userId}:`, error);
      // Implement error handling and status update to 'failed' for video ID (in video_data table)
      if (videoId) {
        try {
          await db.update(videoData)
            .set({ status: 'failed', updatedAt: new Date() })
            .where(eq(videoData.id, videoId));
          console.log(`Updated video status to 'failed' for video ID: ${videoId} in video_data.`);
        } catch (updateError) {
          console.error(`Failed to update videoData status to 'failed' for video ID ${videoId}:`, updateError);
        }
      } else {
        console.log("Video ID not available, cannot update status to 'failed'.");
      }
      throw error; // Re-throw to indicate failure to Inngest
    }
  },
  {
    // Add error handling to trigger credit refunds
    onFailure: async ({ event, error }) => {
      console.error(`[Inngest] Stock Media Video Generation failed for user ${event.data.userId}:`, error);

      // Import the refund function
      const { triggerCreditRefund } = await import('@/lib/atomicCreditSystem');

      // If we have a videoId, trigger refund
      if (event.data.videoId) {
        await triggerCreditRefund(event.data.videoId, `Stock Media Video Generation failed: ${error.message}`);
      } else {
        console.log("[Inngest] No videoId available for credit refund");
      }
    }
  }
);


export async function listS3ObjectsByPrefix(prefix) {
  const s3Client = new S3Client({
    region: process.env.AWS_REGION,
    credentials: {
      accessKeyId: process.env.AWS_ACCESS_KEY_ID,
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
    },
  });

  const listCommand = new ListObjectsV2Command({
    Bucket: process.env.S3_BUCKET_NAME,
    Prefix: prefix,
  });

  const response = await s3Client.send(listCommand);
  return response.Contents?.map((item) => item.Key).filter(Boolean) ?? [];
}
