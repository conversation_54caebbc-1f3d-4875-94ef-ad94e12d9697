import { inngest } from "../client";
import { generateScript<PERSON>romAI } from '@/src/lib/aiUtils';
import { hasSufficientCredits, VIDEO_GENERATION_COST } from '@/lib/creditUtils';
import { db } from "@/configs/db";
import { users, videoData } from "@/configs/schema";
import { eq, sql } from 'drizzle-orm';

// Import resilient API helpers
import {
  resilientCaptionsAI,
  resilientGoogleAI,
  createEmergencyFallbacks,
  checkAPIHealth
} from '@/lib/inngestApiHelpers';
import { withCircuitBreaker } from '@/lib/circuitBreaker';

// Captions API configuration
const CAPTIONS_API_BASE = "https://api.captions.ai/api";
const CAPTIONS_API_KEY = process.env.CAPTIONS_API_KEY;

// Configuration
const UGC_VIDEO_GENERATION_COST = 30; // Credits cost for UGC video generation (estimated based on 30-second video)

// Resilient Captions API helper functions
async function createUgcVideoResilient(script, creatorName, mediaUrls = []) {
    return await withCircuitBreaker('CAPTIONS_AI', async () => {
        const requestBody = {
            script: script,
            creatorName: creatorName,
            mediaUrls: mediaUrls,
            resolution: "fhd"
        };

        const response = await fetch(`${CAPTIONS_API_BASE}/ads/submit`, {
            method: 'POST',
            headers: {
                'x-api-key': CAPTIONS_API_KEY,
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestBody),
        });

        if (!response.ok) {
            throw new Error(`Failed to create UGC video: ${response.statusText}`);
        }

        const data = await response.json();
        return data.operationId;
    });
}

async function getVideoStatusResilient(operationId) {
    return await withCircuitBreaker('CAPTIONS_AI', async () => {
        const requestBody = {
            operationId: operationId
        };

        const response = await fetch(`${CAPTIONS_API_BASE}/ads/poll`, {
            method: 'POST',
            headers: {
                'x-api-key': CAPTIONS_API_KEY,
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestBody),
        });

        if (!response.ok) {
            throw new Error(`Failed to get video status: ${response.statusText}`);
        }

        const data = await response.json();
        return data;
    });
}

export const aiUgcVideoGeneration = inngest.createFunction(
    { id: 'ai-ugc-video-generation' },
    { event: 'app/ai-ugc-video.generate' }, // Updated event name to match new system
    async ({ event, step }) => {
        const { userId, videoId, workflowType, ...videoConfig } = event.data;
        const {
            projectTitle,
            scriptTopic,
            userScript,
            avatarChoice,
            voiceChoice,
            userAssetUrls,
            aspectRatio = '9:16',
            templateId = 'AI_UGC_TALKING_HEAD'
        } = videoConfig;

        console.log(`[UGC Generation] Starting AI UGC video generation for user: ${userId}`);

        // Step 0: Check API Health
        const healthStatus = await checkAPIHealth(step);
        console.log('[UGC Video] API Health Status:', healthStatus);

        // Step 1: Verify Video Record Exists
        const videoRecord = await step.run("verify-video-record", async () => {
            console.log(`[UGC Generation] Verifying video record exists for video ID: ${videoId}...`);

            const [video] = await db
                .select()
                .from(videoData)
                .where(eq(videoData.id, videoId))
                .limit(1);

            if (!video) {
                throw new Error(`Video record not found for ID: ${videoId}`);
            }

            if (video.clerkId !== userId) {
                throw new Error(`Video record does not belong to user: ${userId}`);
            }

            console.log(`[UGC Generation] Video record verified for ID: ${videoId}`);
            return video;
        });

        // Step 2: Generate or use provided script with resilient API
        const finalScript = await step.run("generate-or-use-script-resilient", async () => {
            if (userScript && userScript.trim()) {
                console.log(`[UGC Generation] Using user-provided script.`);
                return userScript.trim();
            } else if (scriptTopic && scriptTopic.trim()) {
                console.log(`[UGC Generation] Generating script from topic with resilient API: ${scriptTopic}`);

                try {
                    return await withCircuitBreaker('GOOGLE_AI', async () => {
                        return await generateScriptFromAI(scriptTopic);
                    });
                } catch (error) {
                    console.log("[UGC Generation] Google AI failed, using emergency fallback");
                    const fallbacks = createEmergencyFallbacks();
                    return await fallbacks.scriptFallback(scriptTopic, 'UGC');
                }
            } else {
                throw new Error("Either userScript or scriptTopic must be provided.");
            }
        });

        // Step 3: Create UGC video with Resilient Captions API
        const operationId = await step.run("create-ugc-video-resilient", async () => {
            try {
                return await createUgcVideoResilient(finalScript, avatarChoice, userAssetUrls);
            } catch (error) {
                console.log("[UGC Generation] Captions AI failed for video creation");
                throw new Error(`UGC video creation failed: ${error.message}`);
            }
        });

        // Step 4: Poll for video completion with resilient API
        const finalVideoUrl = await step.run("wait-for-video-completion-resilient", async () => {
            let attempts = 0;
            const maxAttempts = 60; // 10 minutes max wait time

            while (attempts < maxAttempts) {
                try {
                    const status = await getVideoStatusResilient(operationId);

                    if (status.status === 'completed') {
                        console.log(`[UGC Generation] Video completed: ${status.videoUrl}`);
                        return status.videoUrl;
                    } else if (status.status === 'failed') {
                        throw new Error("Captions video generation failed.");
                    }

                    // Wait 10 seconds before next check
                    await new Promise(resolve => setTimeout(resolve, 10000));
                    attempts++;
                } catch (error) {
                    console.log(`[UGC Generation] Error polling video status (attempt ${attempts + 1}):`, error);

                    // If circuit breaker is open, fail fast
                    if (error.message.includes('circuit breaker')) {
                        throw new Error("Captions AI service is currently unavailable");
                    }

                    // Wait before retrying
                    await new Promise(resolve => setTimeout(resolve, 10000));
                    attempts++;
                }
            }

            throw new Error("Video generation timeout - please check status manually.");
        });

        // Step 5: Update Video Record with Generated Content
        const savedVideoId = await step.run("update-video-data", async () => {
            console.log("[UGC Generation] Updating video record with generated content...");

            const updatePayload = {
                script: finalScript,
                status: 'Completed',
                renderedVideoUrl: finalVideoUrl, // Store the Captions-generated video directly
                updatedAt: new Date(),
                // Update workflow-specific data
                workflow_data: {
                    ...videoRecord.workflow_data,
                    generatedContent: {
                        finalScript: finalScript,
                        captionsVideoUrl: finalVideoUrl,
                        captionsOperationId: operationId,
                        avatarChoice: avatarChoice,
                        userAssetUrls: userAssetUrls
                    }
                }
            };

            const result = await db
                .update(videoData)
                .set(updatePayload)
                .where(eq(videoData.id, videoId))
                .returning({ id: videoData.id });

            if (!result || result.length === 0) {
                console.error(`[UGC Generation] Failed to update video data for video ID: ${videoId}`);
                throw new Error("Failed to update video data in the database.");
            }

            console.log(`[UGC Generation] Video data updated successfully for video ID: ${videoId}`);
            return videoId;
        });

        console.log(`[UGC Generation] AI UGC video generation completed for user: ${userId}, video ID: ${savedVideoId}`);
        return { videoId: savedVideoId, status: 'completed' };
    },
    {
        // Add error handling to trigger credit refunds
        onFailure: async ({ event, error }) => {
            console.error(`[Inngest] UGC Video Generation failed for video ${event.data.videoId}:`, error);

            // Import the refund function
            const { triggerCreditRefund } = await import('@/lib/atomicCreditSystem');
            await triggerCreditRefund(event.data.videoId, `UGC Video Generation failed: ${error.message}`);
        }
    }
);
