import { inngest } from '../client';
import { db } from '@/configs/db';
import { scheduledPosts, userSocialAccounts } from '@/configs/schema';
import { eq } from 'drizzle-orm';
import YouTubePublisher from '@/lib/youtubePublisher'; // Import the publisher module

export const uploadYouTubeVideo = inngest.createFunction(
  { id: 'upload-youtube-video' },
  { event: 'social.post.publish' }, // Event triggered by scheduler or "publish now"
  async ({ event, step }) => {
    console.log(`[Inngest] uploadYouTubeVideo function started for event: ${event.name}`); // Add this line
    const { postId, socialAccountId, clerkId } = event.data;

    console.log(`[Inngest] Processing YouTube upload for postId: ${postId}`);

    // 1. Fetch post details and social account credentials
    const post = await step.run('fetch-post-details', async () => {
      return db.query.scheduledPosts.findFirst({
        where: eq(scheduledPosts.id, postId),
        with: {
          socialAccount: true, // Eager load the connected social account
        },
      });
    });

    if (!post) {
      console.error(`[Inngest] Post with ID ${postId} not found.`);
      await step.run('update-post-status-not-found', async () => {
        await db.update(scheduledPosts)
          .set({ status: 'failed', errorDetails: { message: 'Post not found.' }, updatedAt: new Date() })
          .where(eq(scheduledPosts.id, postId));
      });
      return { success: false, message: 'Post not found.' };
    }

    if (!post.socialAccount || post.socialAccount.platformName !== 'youtube') {
      console.error(`[Inngest] Invalid social account or platform for postId: ${postId}`);
      await step.run('update-post-status-invalid-account', async () => {
        await db.update(scheduledPosts)
          .set({ status: 'failed', errorDetails: { message: 'Invalid social account or platform.' }, updatedAt: new Date() })
          .where(eq(scheduledPosts.id, postId));
      });
      return { success: false, message: 'Invalid social account or platform.' };
    }

    // Ensure status is 'uploading' or 'pending' before proceeding
    if (post.status !== 'pending' && post.status !== 'uploading') {
      console.warn(`[Inngest] Post ${postId} already processed or in unexpected status: ${post.status}`);
      return { success: false, message: `Post already in status: ${post.status}` };
    }

    // Update status to 'uploading' if it's 'pending'
    if (post.status === 'pending') {
      await step.run('update-post-status-to-uploading', async () => {
        await db.update(scheduledPosts)
          .set({ status: 'uploading', updatedAt: new Date() })
          .where(eq(scheduledPosts.id, postId));
      });
    }

    try {
      // 2. Initialize YouTubePublisher and publish
      const publisher = new YouTubePublisher(post.socialAccount.id, clerkId);
      const publishResult = await step.run('publish-video-to-youtube', async () => {
        return publisher.publish(post);
      });

      if (publishResult.success) {
        // 3. Update post status to 'published' and save YouTube video ID
        await step.run('update-post-status-published', async () => {
          await db.update(scheduledPosts)
            .set({
              status: 'published',
              platformPostId: publishResult.platformPostId,
              publishedAt: new Date(),
              updatedAt: new Date(),
            })
            .where(eq(scheduledPosts.id, postId));
        });
        console.log(`[Inngest] Successfully uploaded and published video for postId: ${postId}. YouTube ID: ${publishResult.platformPostId}`);
        return { success: true, message: 'Video uploaded and published successfully.', youtubeVideoId: publishResult.platformPostId };
      } else {
        throw new Error(publishResult.message || 'YouTube publishing failed.');
      }

    } catch (error) {
      console.error(`[Inngest] Error during YouTube upload for postId ${postId}:`, error);
      // 4. Update post status to 'failed' with error details
      await step.run('update-post-status-failed', async () => {
        await db.update(scheduledPosts)
          .set({
            status: 'failed',
            errorDetails: { message: error.message, stack: error.stack },
            updatedAt: new Date(),
          })
          .where(eq(scheduledPosts.id, postId));
      });
      return { success: false, message: error.message || 'Failed to upload video to YouTube.' };
    }
  }
);
