import { inngest } from '../client';
import { db } from '@/configs/db';
import { videoData } from '@/configs/schema';
import { eq } from 'drizzle-orm';
import { generateVideo } from '@/actions/generation'; // Assuming this is the Remotion render trigger

// Import resilient API helpers
import {
  resilientGoogleAI,
  createEmergencyFallbacks,
  checkAPIHealth
} from '@/lib/inngestApiHelpers';
import { withCircuitBreaker } from '@/lib/circuitBreaker';

export const narratorVideoGeneration = inngest.createFunction(
  { id: 'narrator-video-generation' },
  { event: 'app/narrator.video.generate' }, // Updated event name to match new system
  async ({ event, step }) => {
    const { userId, videoId, workflowType, ...videoConfig } = event.data;
    const { videoUrl, title, aspectRatio, templateId, voice, captionName, captionStyleJson, captionContainerStyle, audioSpeed, backgroundMusic } = videoConfig;
    const clerkId = userId; // For backward compatibility

    // Step 0: Check API Health
    const healthStatus = await checkAPIHealth(step);
    console.log('[Narrator Video] API Health Status:', healthStatus);

    // 1. Simulate AI Video Analysis with Resilient API
    const videoAnalysis = await step.run('simulate-video-analysis-resilient', async () => {
      console.log(`Simulating AI analysis for video: ${videoUrl}`);

      try {
        return await withCircuitBreaker('GOOGLE_AI', async () => {
          // In a real scenario, call an external AI service here
          // For now, return simulated data but with resilient wrapper
          return {
            analysisResult: 'Detected various scenes and objects.',
            keyMoments: [{ timestamp: 0, description: 'Intro' }, { timestamp: 10, description: 'Main content' }],
          };
        });
      } catch (error) {
        console.log("[Narrator Video] AI analysis failed, using fallback");
        return {
          analysisResult: 'Basic video analysis (fallback mode)',
          keyMoments: [{ timestamp: 0, description: 'Video content' }],
        };
      }
    });

    // 2. AI Narrative Script Generation with Resilient API
    const narrativeScriptData = await step.run('narrative-script-generation-resilient', async () => {
      console.log('Generating AI narrative script with resilient API...');

      try {
        return await withCircuitBreaker('GOOGLE_AI', async () => {
          // In a real scenario, call an external AI service to generate script based on analysis
          // For now, return enhanced simulated data
          return {
            narrativeScript: "This is an AI-generated narrative script for your video. It describes the key moments and provides engaging context based on the video analysis.",
            scriptTimings: [
              { text: "Welcome to our video!", start: 0, end: 3 },
              { text: "Here's an interesting fact.", start: 5, end: 8 },
              { text: "And finally, a conclusion.", start: 10, end: 13 },
            ],
            estimatedDurationSeconds: 15,
          };
        });
      } catch (error) {
        console.log("[Narrator Video] AI script generation failed, using emergency fallback");
        const fallbacks = createEmergencyFallbacks();
        const fallbackScript = await fallbacks.scriptFallback(title, 'narrator');

        return {
          narrativeScript: fallbackScript,
          scriptTimings: [
            { text: fallbackScript.substring(0, 50), start: 0, end: 5 },
            { text: fallbackScript.substring(50), start: 5, end: 10 },
          ],
          estimatedDurationSeconds: 10,
        };
      }
    });

    // Update existing video record with generated content
    const updatedVideo = await step.run('update-narrator-video-data', async () => {
      console.log("[Inngest] Updating video record with narrator content...");

      const updatePayload = {
        script: narrativeScriptData.narrativeScript,
        estimatedDurationSeconds: narrativeScriptData.estimatedDurationSeconds,
        status: 'Script Generated',
        updatedAt: new Date(),
        workflow_data: {
          workflowType: 'narratorVideo',
          originalVideoUrl: videoUrl,
          narrativeScript: narrativeScriptData.narrativeScript,
          scriptTimings: narrativeScriptData.scriptTimings,
          videoAnalysis: videoAnalysis,
        },
      };

      const result = await db
        .update(videoData)
        .set(updatePayload)
        .where(eq(videoData.id, videoId))
        .returning();

      if (!result || result.length === 0) {
        throw new Error('Failed to update narrator video data.');
      }

      console.log(`[Inngest] Narrator video data updated successfully for video ID: ${videoId}`);
      return result[0];
    });

    // Trigger Remotion render
    await step.run('trigger-remotion-render', async () => {
      await generateVideo({
        videoDataId: updatedVideo.id,
        inputProps: {
          videoData: {
            ...updatedVideo,
            workflow_data: updatedVideo.workflow_data, // Ensure workflow_data is passed correctly
          },
        },
      });
    });

    return {
      message: 'Narrator video generation process initiated successfully!',
      videoDataId: updatedVideo.id,
    };
  },
  {
    // Add error handling to trigger credit refunds
    onFailure: async ({ event, error }) => {
      console.error(`[Inngest] Narrator Video Generation failed for video ${event.data.videoId}:`, error);

      // Import the refund function
      const { triggerCreditRefund } = await import('@/lib/atomicCreditSystem');
      await triggerCreditRefund(event.data.videoId, `Narrator Video Generation failed: ${error.message}`);
    }
  }
);
