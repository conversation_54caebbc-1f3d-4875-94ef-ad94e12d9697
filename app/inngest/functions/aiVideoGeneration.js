import { inngest } from "../client";
import { generateScriptFromAI } from '@/src/lib/aiUtils';
import { hasSufficientCredits, VIDEO_GENERATION_COST } from '@/lib/creditUtils';
import { db } from "@/configs/db";
import { users, videoData } from "@/configs/schema";
import { eq, sql } from 'drizzle-orm';

// Import resilient API helpers
import {
  resilientGoogleAI,
  resilientElevenLabs,
  resilientPexels,
  resilientPixabay,
  createEmergencyFallbacks,
  checkAPIHealth
} from '@/lib/inngestApiHelpers';
import { withCircuitBreaker } from '@/lib/circuitBreaker';
import { triggerCreditRefund } from '@/lib/atomicCreditSystem';

// Import dependencies and logic adapted from API routes
// Audio generation (Google TTS, Firebase Storage)
import { storage } from "@/lib/firebase";
import { getDownloadURL, ref, uploadBytes } from "firebase/storage";
import { v4 as uuidv4 } from "uuid";
import { TextToSpeechClient } from "@google-cloud/text-to-speech"; // Import directly

// Caption generation (Deepgram)
import { createClient as createDeepgramClient } from "@deepgram/sdk"; // Rename import to avoid conflict

// Image prompt generation (Gemini)
import { GoogleGenerativeAI } from "@google/generative-ai";

// Image generation (Runware)
import { Runware } from "@runware/sdk-js";

// --- Configuration ---
const MAX_SCRIPT_LENGTH = 10000; // From generate-image-prompt API
const DEEPGRAM_MODEL = "nova-2"; // From generate-caption API
const DEEPGRAM_LANGUAGE = "en-US"; // From generate-caption API
const DEFAULT_IMAGE_MODEL_ID = 'runware:100@1'; // From generate-image-v2 API
const DEFAULT_IMAGE_WIDTH = 640; // From generate-image-v2 API
const DEFAULT_IMAGE_HEIGHT = 1152; // From generate-image-v2 API
// --- End Configuration ---

// Initialize AI clients (outside the function for potential reuse, but be mindful of cold starts)
// In Inngest, these might be better initialized within step.run if they have state or connection issues
// Or use a helper function that memoizes the client.
let textToSpeechClient;
async function getTextToSpeechClient() {
  if (!textToSpeechClient) {
    textToSpeechClient = new TextToSpeechClient({
      apiKey: process.env.GOOGLE_CONSOLE_KEY,
    });
  }
  return textToSpeechClient;
}

let deepgramClient;
function getDeepgramClient() {
    if (!deepgramClient) {
        if (!process.env.DEEPGRAM_API_KEY) {
            console.error("[Inngest Config Error] DEEPGRAM_API_KEY is not set.");
            throw new Error("Server configuration error: Deepgram API key missing.");
        }
        deepgramClient = createDeepgramClient(process.env.DEEPGRAM_API_KEY);
    }
    return deepgramClient;
}

let geminiClient;
function getGeminiClient() {
    if (!geminiClient) {
        if (!process.env.GEMINI_API_KEY) {
            console.error("[Inngest Config Error] GEMINI_API_KEY is not set.");
            throw new Error("Server configuration error: Gemini API key missing.");
        }
        geminiClient = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);
    }
    return geminiClient;
}

let runwareClient;
function getRunwareClient() {
    if (!runwareClient) {
        const runwareApiKey = process.env.RUNWERE_IMAGE_API;
        if (!runwareApiKey) {
            console.error("❌ RUNWERE_IMAGE_API key is not set in environment variables.");
            throw new Error("Image generation service is unavailable: API key missing.");
        }
        try {
            runwareClient = new Runware({ apiKey: runwareApiKey });
            console.log("✅ Runware SDK initialized successfully.");
        } catch (error) {
            console.error("❌ Failed to initialize Runware SDK:", error);
            throw new Error("Image generation service is unavailable: SDK initialization failed.");
        }
    }
    return runwareClient;
}


export const aiVideoGeneration = inngest.createFunction(
  { id: "ai-video-generation" },
  { event: "app/ai-video.generate" }, // Updated event name to match new API
  async ({ event, step }) => {
    const { userId, videoId, workflowType, ...videoConfig } = event.data;
    const { projectTitle, topic, videoStyle, aspectRatio, caption, templateId, voice, audioSpeed, backgroundMusic } = videoConfig;

    // --- Input Validation (Basic) ---
    // More robust validation should happen on the frontend and server action triggering this event
    if (!userId || !videoId || !topic || !videoStyle || !aspectRatio || !templateId || !voice || audioSpeed === null || audioSpeed === undefined || !backgroundMusic) {
         console.error("[Inngest Error] Missing required fields:", { userId, videoId, videoConfig });
         throw new Error("Missing required input data for AI video generation.");
    }
     if (!topic?.trim()) {
        throw new Error("Topic cannot be empty.");
    }
     if (topic.length > 200) { // Limit topic length
        throw new Error('Topic is too long (max 200 characters)');
    }
    const parsedAudioSpeed = parseFloat(audioSpeed);
    if (isNaN(parsedAudioSpeed) || parsedAudioSpeed < 0.5 || parsedAudioSpeed > 2.0) {
      throw new Error("Invalid 'audioSpeed' value. Must be a number between 0.5 and 2.0.");
    }
    // --- End Input Validation ---


    // --- Step 1: Verify Video Record Exists ---
    // The video record should already exist from the atomic credit deduction
    const videoRecord = await step.run("verify-video-record", async () => {
        console.log(`[Inngest] Verifying video record exists for video ID: ${videoId}...`);

        const [video] = await db
            .select()
            .from(videoData)
            .where(eq(videoData.id, videoId))
            .limit(1);

        if (!video) {
            throw new Error(`Video record not found for ID: ${videoId}`);
        }

        if (video.clerkId !== userId) {
            throw new Error(`Video record does not belong to user: ${userId}`);
        }

        console.log(`[Inngest] Video record verified for ID: ${videoId}`);
        return video;
    });

    // --- Step 2: Generate Script with Resilient API ---
    const generatedScript = await step.run("generate-script-resilient", async () => {
        console.log("[Inngest] Generating script with resilient API...");

        try {
            return await withCircuitBreaker('GOOGLE_AI', async () => {
                const prompt = `Create a ${videoStyle} video script about: ${topic}.
                Make it engaging, informative, and under 150 words.
                Focus on key points that would work well with visual content.`;

                const script = await generateScriptFromAI(topic);

                if (!script || typeof script !== 'string' || !script.trim()) {
                    throw new Error("AI utility did not return valid script text.");
                }

                console.log("[Inngest] Script generated with resilient API.");
                return script.trim();
            });
        } catch (error) {
            console.log("[Inngest] Google AI failed, using emergency fallback");
            const fallbacks = createEmergencyFallbacks();
            return await fallbacks.scriptFallback(topic, videoStyle);
        }
    });

    // --- Step 3: Generate Audio ---
    const generatedAudioUrl = await step.run("generate-audio", async () => {
        console.log("[Inngest] Generating audio...");
        // Adapt logic from /api/generate-audio/route.js
        const client = await getTextToSpeechClient();
         // Map client-side voice IDs to Google TTS voice names (copied from API route)
        const voiceMap = {
            alloy: "en-US-Chirp3-HD-Umbriel",
            echo: "en-US-Chirp3-HD-Gacrux",
            fable: "en-US-Chirp3-HD-Fenrir",
            onyx: "en-US-Chirp3-HD-Enceladus",
            nova: "en-US-Chirp3-HD-Leda",
            shimmer: "en-US-Chirp3-HD-Kore",
        };
        const googleVoiceName = voiceMap[voice] || "en-US-Standard-A";

        const id = uuidv4();
        const storageRef = ref(storage, `audio/${id}.mp3`);

        const request = {
            input: { text: generatedScript },
            voice: { languageCode: "en-US", name: googleVoiceName },
            audioConfig: {
                audioEncoding: "MP3",
                speakingRate: parsedAudioSpeed,
            },
        };

        const [response] = await client.synthesizeSpeech(request);

        if (!response.audioContent) {
            throw new Error("Failed to synthesize speech.");
        }

        const audioBuffer = Buffer.from(response.audioContent, "base64");

        await uploadBytes(storageRef, audioBuffer, { contentType: "audio/mp3" });

        const audioUrl = await getDownloadURL(storageRef);
        console.log("[Inngest] Audio generated and uploaded.");
        return audioUrl;
    });

    // --- Step 4: Generate Captions ---
    const generatedCaptionsData = await step.run("generate-captions", async () => {
        console.log("[Inngest] Generating captions...");
        // Adapt logic from /api/generate-caption/route.js
        const deepgram = getDeepgramClient();

        const source = { url: generatedAudioUrl };
        const options = {
            model: DEEPGRAM_MODEL,
            language: DEEPGRAM_LANGUAGE,
            punctuate: true,
            smart_format: true,
        };

        const { result, error: deepgramError } = await deepgram.listen.prerecorded.transcribeUrl(
            source,
            options
        );

        if (deepgramError) {
            console.error("[Inngest Deepgram Error]:", deepgramError);
            throw new Error("Failed to process audio transcription.");
        }

        if (!result) {
             throw new Error("No result received from Deepgram.");
        }

        const words = result.results?.channels?.[0]?.alternatives?.[0]?.words?.map(word => ({
            text: word.word,
            start: word.start * 1000,
            end: word.end * 1000,
        })) ?? [];

        console.log(`[Inngest] Successfully transcribed ${words.length} words.`);
        return words;
    });

    // --- Step 5: Generate Image Prompts with Resilient API ---
     const imagePrompts = await step.run("generate-image-prompts-resilient", async () => {
        console.log("[Inngest] Generating image prompts with resilient API...");

        try {
            return await withCircuitBreaker('GOOGLE_AI', async () => {
                const gemini = getGeminiClient();
                const model = gemini.getGenerativeModel({ model: "gemini-1.5-flash-latest"});

                const prompt = `Generate a list of concise image prompts for the following video script. Each prompt should describe a visual scene or concept from the script, keeping in mind the video style: "${videoStyle}". Return only the list of prompts, with each prompt on a new line. Do not include any introductory or concluding text.

Video Style: ${videoStyle}

Script:
${generatedScript}`;

                const result = await model.generateContent(prompt);
                const response = await result.response;
                const text = response.text();

                const prompts = text.split('\n').map(p => p.trim()).filter(p => p.length > 0);
                console.log(`[Inngest] Generated ${prompts.length} image prompts with resilient API.`);
                return prompts;
            });
        } catch (error) {
            console.log("[Inngest] Google AI failed for image prompts, using fallback");
            // Generate basic prompts based on video style and topic
            return [
                `${videoStyle} scene about ${topic}`,
                `${videoStyle} background for ${topic}`,
                `${videoStyle} visual representation of ${topic}`
            ];
        }
    });


    // --- Step 6: Generate Images ---
    const generatedImageList = await step.run("generate-images", async () => {
        console.log("[Inngest] Generating images...");
        // Adapt logic from /api/generate-image-v2/route.js
        const runware = getRunwareClient();

        const imageList = [];
        if (!imagePrompts || imagePrompts.length === 0) {
            console.warn("[Inngest] No prompts provided for image generation.");
            return [];
        }

        for (const prompt of imagePrompts) {
            if (!prompt || typeof prompt !== 'string' || !prompt.trim()) {
                console.warn("[Inngest] Skipping empty or invalid image prompt.");
                imageList.push(null);
                continue;
            }
            console.log(`[Inngest] Generating image for prompt: ${prompt}`);
            try {
                const imagesResponse = await runware.requestImages({
                    positivePrompt: prompt,
                    width: DEFAULT_IMAGE_WIDTH,
                    height: DEFAULT_IMAGE_HEIGHT,
                    model: DEFAULT_IMAGE_MODEL_ID,
                    numberResults: 1,
                    outputType: "URL",
                    outputFormat: "WEBP",
                });

                const firstImageObj = imagesResponse?.[0];
                const imageUrl = firstImageObj?.imageURL || firstImageObj?.imageUrl;

                if (!imageUrl) {
                    console.error("[Inngest Error] Runware API response did not contain a valid image URL for prompt:", prompt, imagesResponse);
                    imageList.push(null); // Push null on failure
                } else {
                    console.log("[Inngest] Image generated for prompt:", prompt);
                    imageList.push(imageUrl);
                }
            } catch (error) {
                console.error(`[Inngest Error] Error generating image for prompt "${prompt}":`, error);
                imageList.push(null); // Push null on error
            }
        }
        console.log("[Inngest] All image generation attempts complete.");
        return imageList;
    });


    // --- Step 7: Update Video Record with Generated Content ---
    const savedVideoId = await step.run("update-video-data", async () => {
        console.log("[Inngest] Updating video record with generated content...");

        // Calculate estimated duration based on generated script
        const words = generatedScript.trim().split(/\s+/).filter(Boolean);
        const wordCount = words.length;
        const wordsPerSecondAtNormalSpeed = 2.5; // 150 WPM
        const estimatedDurationSeconds = Math.max(0, Math.round((wordCount / wordsPerSecondAtNormalSpeed) / parsedAudioSpeed));

        const updatePayload = {
            script: generatedScript,
            estimatedDurationSeconds: estimatedDurationSeconds,
            audioUrl: generatedAudioUrl,
            captionJson: generatedCaptionsData,
            images: generatedImageList?.filter(img => img !== null), // Filter out nulls
            status: 'Ready for Rendering', // Update status
            updatedAt: new Date(),
            // Update workflow-specific data
            workflow_data: {
                ...videoRecord.workflow_data,
                generatedContent: {
                    script: generatedScript,
                    audioUrl: generatedAudioUrl,
                    captions: generatedCaptionsData,
                    images: generatedImageList?.filter(img => img !== null),
                    imagePrompts: imagePrompts
                }
            }
        };

        const result = await db
            .update(videoData)
            .set(updatePayload)
            .where(eq(videoData.id, videoId))
            .returning({ id: videoData.id });

        if (!result || result.length === 0) {
            console.error(`[Inngest Error] Failed to update video data for video ID: ${videoId}`);
            throw new Error("Failed to update video data in the database.");
        }

        console.log(`[Inngest] Video data updated successfully for video ID: ${videoId}`);
        return videoId;
    });


    // --- Step 8: Trigger Rendering (Optional, could be a separate Inngest function or manual step) ---
    // For a fully automated pipeline, you might trigger the rendering Inngest function here.
    // await step.sendEvent("trigger-render", {
    //     name: "app/video.render",
    //     data: { videoId: savedVideoId, compositionId: templateId } // Pass necessary data
    // });


    return {
      message: "AI Video Generation workflow Completed!",
      videoId: savedVideoId,
    };
  },
  {
    // Add error handling to trigger credit refunds
    onFailure: async ({ event, error }) => {
      console.error(`[Inngest] AI Video Generation failed for video ${event.data.videoId}:`, error);

      // Trigger credit refund
      try {
        const response = await fetch(`${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/api/video/refund-credits`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${process.env.INNGEST_SIGNING_KEY}`
          },
          body: JSON.stringify({
            videoId: event.data.videoId,
            reason: `AI Video Generation failed: ${error.message}`
          })
        });

        if (response.ok) {
          console.log(`[Inngest] Credit refund triggered for video ${event.data.videoId}`);
        } else {
          console.error(`[Inngest] Failed to trigger credit refund for video ${event.data.videoId}`);
        }
      } catch (refundError) {
        console.error(`[Inngest] Error triggering credit refund:`, refundError);
      }
    }
  }
);
