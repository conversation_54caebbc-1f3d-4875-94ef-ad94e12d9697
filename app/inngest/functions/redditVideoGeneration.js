import { inngest } from "../client";
import { hasSufficientCredits, VIDEO_GENERATION_COST } from '@/lib/creditUtils';
import { db } from "@/configs/db";
import { users, videoData } from "@/configs/schema"; // Import videoData schema
import { eq, sql } from 'drizzle-orm';

// Import resilient API helpers
import {
  createEmergencyFallbacks,
  checkAPIHealth
} from '@/lib/inngestApiHelpers';
import { withCircuitBreaker } from '@/lib/circuitBreaker';

// Import any necessary utilities or logic for processing Reddit data
// (e.g., fetching data from Reddit API if needed, although the frontend page seems to collect it)

export const redditVideoGeneration = inngest.createFunction(
  { id: "reddit-video-generation" },
  { event: "app/reddit-video.generate" }, // Updated event name to match new system
  async ({ event, step }) => {
    const { userId, videoId, workflowType, ...videoConfig } = event.data;
    const { projectTitle, redditPost, backgroundVideoUrls, templateId, redditPostX, redditPostY, redditPostScale, aspectRatio } = videoConfig;

    // --- Input Validation (Basic) ---
    if (!userId || !projectTitle || !redditPost || !backgroundVideoUrls || !templateId || aspectRatio === null || aspectRatio === undefined) {
         console.error("[Inngest Error] Missing required formData fields or userId:", { userId, formData });
         throw new Error("Missing required input data for Reddit video generation.");
    }
    // Add more specific validation for redditPost object and backgroundVideoUrls array
    // --- End Input Validation ---

    // --- Step 0: Check API Health ---
    const healthStatus = await checkAPIHealth(step);
    console.log('[Reddit Video] API Health Status:', healthStatus);

    // --- Step 1: Verify Video Record Exists ---
    const videoRecord = await step.run("verify-video-record", async () => {
        console.log(`[Inngest] Verifying video record exists for video ID: ${videoId}...`);

        const [video] = await db
            .select()
            .from(videoData)
            .where(eq(videoData.id, videoId))
            .limit(1);

        if (!video) {
            throw new Error(`Video record not found for ID: ${videoId}`);
        }

        if (video.clerkId !== userId) {
            throw new Error(`Video record does not belong to user: ${userId}`);
        }

        console.log(`[Inngest] Video record verified for ID: ${videoId}`);
        return video;
    });

    // --- Step 2: Process and Save Reddit Video Data to video_data table ---
    let savedVideoId; // Declare savedVideoId in function scope

    // --- Step 2: Process and Save Reddit Video Data to video_data table ---
    const newVideo = await step.run("save-reddit-video-data", async () => {
        console.log("[Inngest] Saving Reddit video data...");

        // Format the data for the video_data table
        const saveDataPayload = {
            clerkId: userId,
            title: projectTitle.trim() || `Reddit Video: ${redditPost.title?.substring(0, 40)}...`,
            topic: `Reddit Post: ${redditPost.title}`, // Use topic field for context
            script: redditPost.postContent, // Use script field for post content
            videoStyle: 'reddit', // Or derive from frontend input if applicable
            aspectRatio: aspectRatio,
            templateId: templateId, // Should be 'RedditPostVideo'
            voice: null, // Not applicable for this type
            captionName: null, // Not applicable for this type
            captionStyleJson: null, // Not applicable for this type
            captionContainerStyle: null, // Not applicable for this type
            audioSpeed: 1.0, // Not applicable for this type
            backgroundMusic: null, // Background music might be handled differently
            estimatedDurationSeconds: null, // Can estimate later if needed
            audioUrl: null, // Not applicable for this type (unless background audio)
            captionJson: null, // Captions might be generated differently
            images: [], // Not applicable for this type (unless post has images)
            status: 'processing', // Initial status set to 'processing'
            // Store workflow-specific data in jsonb field
            workflow_data: {
                type: 'reddit-video',
                redditPost: redditPost, // Store the original Reddit post data
                backgroundVideoUrls: backgroundVideoUrls, // Store background video URLs
                redditPostX: redditPostX, // Store position/scale
                redditPostY: redditPostY,
                redditPostScale: redditPostScale,
                // Add any other Reddit-specific data needed for rendering
            }
        };

        const result = await db.insert(videoData).values(saveDataPayload).returning({ id: videoData.id });

        if (!result || result.length === 0 || !result[0].id) {
            console.error(`[Inngest Error] Failed to insert Reddit video data for user ${userId}.`);
            throw new Error("Failed to insert Reddit video data into the database.");
        }
        return result[0];
    });

    savedVideoId = newVideo.id; // Assign to the function-scoped variable
    console.log(`[Inngest] Reddit video data inserted successfully for user ${userId} with ID: ${savedVideoId}`);

    // --- Step 3: Trigger Rendering (Optional) ---
    // Trigger the rendering Inngest function here if rendering is automated after data save
    // await step.sendEvent("trigger-render", {
    //     name: "app/video.render",
    //     data: { videoId: savedVideoId, compositionId: templateId } // Pass necessary data
    // });

    // --- Step 4: Update status to Completed ---
    await step.run("update-reddit-video-status-completed", async () => {
        console.log(`[Inngest] Updating Reddit video status to 'Completed' for video ID: ${savedVideoId}`);
        await db.update(videoData)
            .set({ status: 'Completed', updatedAt: new Date() })
            .where(eq(videoData.id, savedVideoId));
    });

    return {
      message: "Reddit Video Generation workflow Completed!",
      videoId: savedVideoId,
    };
  },
  {
    // Add error handling to trigger credit refunds
    onFailure: async ({ event, error }) => {
      console.error(`[Inngest] Reddit Video Generation failed for video ${event.data.videoId}:`, error);

      // Import the refund function
      const { triggerCreditRefund } = await import('@/lib/atomicCreditSystem');
      await triggerCreditRefund(event.data.videoId, `Reddit Video Generation failed: ${error.message}`);
    }
  }
);
