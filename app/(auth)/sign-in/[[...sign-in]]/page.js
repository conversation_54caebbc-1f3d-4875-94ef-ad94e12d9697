import { SignIn } from "@clerk/nextjs";

export default function Page() {
  return (
    <div className="w-full">
      <SignIn
        appearance={{
          elements: {
            formButtonPrimary: 
              "bg-gradient-to-r from-purple-600 to-blue-500 hover:from-purple-700 hover:to-blue-600 text-sm normal-case",
            card: "shadow-none",
            headerTitle: "text-2xl font-bold text-gray-900",
            headerSubtitle: "text-gray-600",
            socialButtonsBlockButton: "border border-gray-300 hover:bg-gray-50",
            formFieldInput: "rounded-lg border-gray-300 focus:border-purple-500 focus:ring-purple-500",
            footerActionLink: "text-purple-600 hover:text-purple-700",
          },
        }}
      />
    </div>
  );
}