/**
 * User Management Server Actions
 * 
 * Server-side actions for user initialization, credit management,
 * and Clerk webhook processing.
 */

'use server';

import { db } from '@/configs/db';
import { users, creditTransactions } from '@/configs/schema';
import { eq } from 'drizzle-orm';

/**
 * Initialize user from Clerk webhook
 */
export async function initializeUser(webhookPayload, tx = null) {
  try {
    // Validate webhook payload
    if (!webhookPayload || !webhookPayload.data) {
      throw new Error('Invalid webhook data');
    }

    const { data } = webhookPayload;

    // Extract user information
    const clerkId = data.id;
    const emailAddresses = data.email_addresses || [];
    const firstName = data.first_name || '';
    const lastName = data.last_name || '';

    // Validate required fields
    if (!clerkId) {
      throw new Error('Invalid webhook data: missing user ID');
    }

    if (!emailAddresses.length || !emailAddresses[0]?.email_address) {
      throw new Error('Invalid webhook data: missing email address');
    }

    const email = emailAddresses[0].email_address;

    console.log('[User Init] Processing user initialization for:', clerkId);

    // Use provided transaction or create database operations
    const dbOps = tx || db;

    // Check if user already exists
    const existingUser = await dbOps
      .select()
      .from(users)
      .where(eq(users.clerkId, clerkId))
      .limit(1);

    if (existingUser.length > 0) {
      console.log('[User Init] User already exists:', clerkId);
      return {
        success: true,
        message: 'User already exists',
        user: existingUser[0]
      };
    }

    // Create new user with welcome credits
    const welcomeCredits = 10;
    const newUser = {
      clerkId,
      email,
      firstName,
      lastName,
      credits: welcomeCredits,
      currentCreditBalance: welcomeCredits,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    console.log('[User Init] Creating new user:', { clerkId, email, welcomeCredits });

    const createdUser = await dbOps
      .insert(users)
      .values(newUser)
      .returning();

    if (!createdUser || createdUser.length === 0) {
      throw new Error('Failed to create user record');
    }

    // Create welcome credit transaction
    try {
      await dbOps
        .insert(creditTransactions)
        .values({
          userId: clerkId,
          transactionType: 'WELCOME',
          amount: welcomeCredits,
          balanceBefore: 0,
          balanceAfter: welcomeCredits,
          relatedEntityType: 'USER_SIGNUP',
          notes: 'Welcome Credits',
          metadata: { 
            source: 'user_signup',
            firstName,
            lastName 
          }
        });

      console.log('[User Init] Welcome credit transaction created');
    } catch (creditError) {
      console.warn('[User Init] Failed to create welcome credit transaction:', creditError.message);
      // Don't fail user creation if credit transaction fails
    }

    console.log('[User Init] User initialization completed successfully');

    return {
      success: true,
      user: createdUser[0],
      welcomeCredits
    };

  } catch (error) {
    console.error('[User Init] Error initializing user:', error);
    throw error;
  }
}

/**
 * Get user by Clerk ID
 */
export async function getUserByClerkId(clerkId) {
  try {
    if (!clerkId) {
      throw new Error('Clerk ID is required');
    }

    const user = await db
      .select()
      .from(users)
      .where(eq(users.clerkId, clerkId))
      .limit(1);

    if (!user || user.length === 0) {
      return {
        success: false,
        error: 'User not found'
      };
    }

    return {
      success: true,
      user: user[0]
    };

  } catch (error) {
    console.error('[User] Error fetching user:', error);
    return {
      success: false,
      error: 'Failed to fetch user'
    };
  }
}

/**
 * Update user credits
 */
export async function updateUserCredits(clerkId, newCreditAmount, reason = 'Manual adjustment') {
  try {
    if (!clerkId) {
      throw new Error('Clerk ID is required');
    }

    if (typeof newCreditAmount !== 'number' || newCreditAmount < 0) {
      throw new Error('Invalid credit amount');
    }

    // Get current user
    const currentUser = await getUserByClerkId(clerkId);
    if (!currentUser.success) {
      throw new Error('User not found');
    }

    const user = currentUser.user;
    const oldBalance = user.credits || 0;
    const difference = newCreditAmount - oldBalance;

    // Update user credits
    const updatedUser = await db
      .update(users)
      .set({
        credits: newCreditAmount,
        currentCreditBalance: newCreditAmount,
        updatedAt: new Date()
      })
      .where(eq(users.clerkId, clerkId))
      .returning();

    if (!updatedUser || updatedUser.length === 0) {
      throw new Error('Failed to update user credits');
    }

    // Create credit transaction record
    try {
      await db
        .insert(creditTransactions)
        .values({
          userId: clerkId,
          transactionType: difference > 0 ? 'CREDIT' : 'DEBIT',
          amount: difference,
          balanceBefore: oldBalance,
          balanceAfter: newCreditAmount,
          relatedEntityType: 'MANUAL_ADJUSTMENT',
          notes: reason,
          metadata: { 
            adjustmentType: 'manual',
            reason 
          }
        });
    } catch (creditError) {
      console.warn('[User] Failed to create credit transaction:', creditError.message);
    }

    console.log('[User] Credits updated successfully:', { clerkId, oldBalance, newBalance: newCreditAmount });

    return {
      success: true,
      user: updatedUser[0],
      oldBalance,
      newBalance: newCreditAmount,
      difference
    };

  } catch (error) {
    console.error('[User] Error updating credits:', error);
    return {
      success: false,
      error: error.message || 'Failed to update credits'
    };
  }
}

/**
 * Get user credit history
 */
export async function getUserCreditHistory(clerkId, limit = 50) {
  try {
    if (!clerkId) {
      throw new Error('Clerk ID is required');
    }

    const transactions = await db
      .select()
      .from(creditTransactions)
      .where(eq(creditTransactions.userId, clerkId))
      .orderBy(creditTransactions.createdAt, 'desc')
      .limit(limit);

    return {
      success: true,
      transactions
    };

  } catch (error) {
    console.error('[User] Error fetching credit history:', error);
    return {
      success: false,
      error: 'Failed to fetch credit history'
    };
  }
}

/**
 * Validate user has sufficient credits
 */
export async function validateUserCredits(clerkId, requiredCredits) {
  try {
    const userResult = await getUserByClerkId(clerkId);
    
    if (!userResult.success) {
      return {
        success: false,
        error: 'User not found'
      };
    }

    const user = userResult.user;
    const currentCredits = user.credits || 0;

    if (currentCredits < requiredCredits) {
      return {
        success: false,
        error: `Insufficient credits. Required: ${requiredCredits}, Available: ${currentCredits}`,
        currentCredits,
        requiredCredits
      };
    }

    return {
      success: true,
      currentCredits,
      requiredCredits
    };

  } catch (error) {
    console.error('[User] Error validating credits:', error);
    return {
      success: false,
      error: 'Failed to validate credits'
    };
  }
}
