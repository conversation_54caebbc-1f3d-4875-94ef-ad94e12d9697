/**
 * Video Generation Server Actions
 * 
 * Server-side actions for coordinating video generation workflows,
 * credit management, and Inngest event triggering.
 */

'use server';

import { atomicVideoGeneration } from '@/lib/atomicCreditSystem';
import { validateAIVideoInput } from './aiVideoActions';
import { validateUserCredits } from './userActions';

/**
 * Initiate video generation workflow
 */
export async function initiateVideoGeneration(request, tx = null) {
  try {
    // Validate request structure
    if (!request || typeof request !== 'object') {
      throw new Error('Invalid request: request must be an object');
    }

    const { userId, workflowType, formData } = request;

    // Validate required fields
    if (!userId || typeof userId !== 'string') {
      throw new Error('User ID is required');
    }

    if (!workflowType || typeof workflowType !== 'string') {
      throw new Error('Workflow type is required');
    }

    if (!formData || typeof formData !== 'object') {
      throw new Error('Form data is required');
    }

    console.log('[Video Generation] Initiating video generation:', { userId, workflowType });

    // Validate workflow type
    const validWorkflowTypes = [
      'AI_VIDEO',
      'MEME_VIDEO', 
      'UGC_VIDEO',
      'NARRATOR_VIDEO',
      'PODCAST_CLIPPER',
      'SOCIAL_MEDIA_SCHEDULER',
      'BULK_VIDEO_GENERATOR'
    ];

    if (!validWorkflowTypes.includes(workflowType)) {
      throw new Error(`Invalid workflow type: ${workflowType}`);
    }

    // Validate form data based on workflow type
    let validationResult;
    switch (workflowType) {
      case 'AI_VIDEO':
        validationResult = await validateAIVideoInput(formData);
        break;
      default:
        // Basic validation for other workflow types
        validationResult = validateBasicVideoInput(formData);
    }

    if (!validationResult.isValid) {
      return {
        success: false,
        error: 'Validation failed',
        validationErrors: validationResult.errors
      };
    }

    // Get cost for this workflow
    const { getCostForWorkflow } = await import('@/lib/atomicCreditSystem');
    const requiredCredits = getCostForWorkflow(workflowType, formData);

    // Validate user has sufficient credits
    const creditValidation = await validateUserCredits(userId, requiredCredits);
    if (!creditValidation.success) {
      return {
        success: false,
        error: creditValidation.error,
        currentCredits: creditValidation.currentCredits,
        requiredCredits: creditValidation.requiredCredits
      };
    }

    // Execute atomic video generation
    const result = await atomicVideoGeneration(userId, workflowType, formData, tx);

    console.log('[Video Generation] Video generation initiated successfully:', result.video.id);

    return {
      success: true,
      videoId: result.video.id,
      newCreditBalance: result.newBalance,
      inngestEventId: result.inngestEventId || 'mock_event_id',
      video: result.video
    };

  } catch (error) {
    console.error('[Video Generation] Error initiating video generation:', error);
    
    return {
      success: false,
      error: error.message || 'Failed to initiate video generation'
    };
  }
}

/**
 * Basic validation for non-AI video workflows
 */
function validateBasicVideoInput(formData) {
  const errors = [];

  // Validate project title
  if (!formData.projectTitle || formData.projectTitle.trim().length === 0) {
    errors.push('Project title is required');
  } else if (formData.projectTitle.length > 100) {
    errors.push('Project title must be 100 characters or less');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Get video generation status
 */
export async function getVideoGenerationStatus(videoId) {
  try {
    if (!videoId) {
      throw new Error('Video ID is required');
    }

    const { db } = await import('@/configs/db');
    const { videoData } = await import('@/configs/schema');
    const { eq } = await import('drizzle-orm');

    const video = await db
      .select()
      .from(videoData)
      .where(eq(videoData.id, videoId))
      .limit(1);

    if (!video || video.length === 0) {
      return {
        success: false,
        error: 'Video not found'
      };
    }

    return {
      success: true,
      video: video[0]
    };

  } catch (error) {
    console.error('[Video Generation] Error getting video status:', error);
    
    return {
      success: false,
      error: 'Failed to get video status'
    };
  }
}

/**
 * Cancel video generation
 */
export async function cancelVideoGeneration(videoId, userId) {
  try {
    if (!videoId || !userId) {
      throw new Error('Video ID and User ID are required');
    }

    const { db } = await import('@/configs/db');
    const { videoData, users, creditTransactions } = await import('@/configs/schema');
    const { eq } = await import('drizzle-orm');

    // Get video details
    const video = await db
      .select()
      .from(videoData)
      .where(eq(videoData.id, videoId))
      .limit(1);

    if (!video || video.length === 0) {
      return {
        success: false,
        error: 'Video not found'
      };
    }

    const videoRecord = video[0];

    // Verify user owns this video
    if (videoRecord.clerkId !== userId) {
      return {
        success: false,
        error: 'Unauthorized: You can only cancel your own videos'
      };
    }

    // Check if video can be cancelled
    const cancellableStatuses = ['Pending', 'Processing'];
    if (!cancellableStatuses.includes(videoRecord.status)) {
      return {
        success: false,
        error: `Cannot cancel video with status: ${videoRecord.status}`
      };
    }

    // Process refund if credits were debited
    if (videoRecord.costInCredits > 0) {
      // Get user current balance
      const user = await db
        .select()
        .from(users)
        .where(eq(users.clerkId, userId))
        .limit(1);

      if (user && user.length > 0) {
        const currentBalance = user[0].credits;
        const newBalance = currentBalance + videoRecord.costInCredits;

        // Refund credits
        await db
          .update(users)
          .set({
            credits: newBalance,
            currentCreditBalance: newBalance,
            updatedAt: new Date()
          })
          .where(eq(users.clerkId, userId));

        // Create refund transaction
        await db
          .insert(creditTransactions)
          .values({
            userId,
            transactionType: 'REFUND',
            amount: videoRecord.costInCredits,
            balanceBefore: currentBalance,
            balanceAfter: newBalance,
            relatedEntityType: 'VIDEO',
            relatedEntityId: videoId,
            notes: 'Video generation cancelled',
            metadata: { 
              reason: 'user_cancellation',
              originalCost: videoRecord.costInCredits
            }
          });

        console.log('[Video Generation] Credits refunded for cancelled video:', { videoId, refundAmount: videoRecord.costInCredits });
      }
    }

    // Update video status
    const updatedVideo = await db
      .update(videoData)
      .set({
        status: 'Cancelled',
        updatedAt: new Date()
      })
      .where(eq(videoData.id, videoId))
      .returning();

    console.log('[Video Generation] Video cancelled successfully:', videoId);

    return {
      success: true,
      video: updatedVideo[0],
      refundAmount: videoRecord.costInCredits
    };

  } catch (error) {
    console.error('[Video Generation] Error cancelling video:', error);
    
    return {
      success: false,
      error: 'Failed to cancel video generation'
    };
  }
}

/**
 * Get user's video history
 */
export async function getUserVideoHistory(userId, limit = 20) {
  try {
    if (!userId) {
      throw new Error('User ID is required');
    }

    const { db } = await import('@/configs/db');
    const { videoData } = await import('@/configs/schema');
    const { eq, desc } = await import('drizzle-orm');

    const videos = await db
      .select()
      .from(videoData)
      .where(eq(videoData.clerkId, userId))
      .orderBy(desc(videoData.createdAt))
      .limit(limit);

    return {
      success: true,
      videos
    };

  } catch (error) {
    console.error('[Video Generation] Error getting video history:', error);
    
    return {
      success: false,
      error: 'Failed to get video history'
    };
  }
}
