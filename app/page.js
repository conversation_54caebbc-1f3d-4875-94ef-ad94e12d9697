import {getServices, renderMediaOnCloudrun} from '@remotion/cloudrun/client';


import Header from "@/components/landing/Header"; // Keep the existing header for the landing page
import HeroSection from "@/components/landing/HeroSection";
import FeaturesSection from "@/components/landing/FeaturesSection";
import HowItWorksSection from "@/components/landing/HowItWorksSection";
import UseCasesSection from "@/components/landing/UseCasesSection";
import SocialProofSection from "@/components/landing/SocialProofSection";
import PricingSection from "@/components/landing/PricingSection";
import FaqSection from "@/components/landing/FaqSection";
import Footer from "@/components/landing/Footer";


// Note: Removed 'use client' as the page itself doesn't need client-side hooks directly anymore.
// Individual sections like HeroSection might still use 'use client'.


export default function LandingPage() {
  return (
    <div className="flex flex-col min-h-screen bg-background">
      {/* Use the standard Header component */}
      <Header />

      {/* Main content area with unified spacing */}
      <main className="flex-grow space-y-0">
        <HeroSection />
        <FeaturesSection />
        <HowItWorksSection />
        <UseCasesSection />
        <SocialProofSection />
        <PricingSection />
        <FaqSection />
      </main>

      {/* Use the new Footer component */}
      <Footer />
    </div>
  );
}
