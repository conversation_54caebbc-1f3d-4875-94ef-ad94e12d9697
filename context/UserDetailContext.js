'use client';

import React, { createContext, useState, useEffect } from 'react';
import { useUser } from '@clerk/nextjs';
// Assuming an API route exists to fetch initial user data including credits
// import axios from 'axios';

export const UserDetailContext = createContext(null);

export const UserDetailProvider = ({ children }) => {
    const { user, isLoaded } = useUser();
    const [userDetail, setUserDetail] = useState({ credits: 0 }); // Default structure

    // TODO: Implement fetching initial user credits when user loads
    // useEffect(() => {
    //     if (isLoaded && user) {
    //         // Example: Fetch credits from your backend
    //         // axios.get(`/api/get-user-details?userId=${user.id}`)
    //         //     .then(response => {
    //         //         setUserDetail(response.data); // Assuming response.data = { credits: number }
    //         //     })
    //         //     .catch(error => {
    //         //         console.error("Failed to fetch user details:", error);
    //         //         // Handle error appropriately
    //         //     });
    //
    //         // Placeholder until backend is ready:
    //         setUserDetail({ credits: 50 }); // Example starting credits
    //     }
    // }, [user, isLoaded]);

    // Placeholder until backend fetch is implemented:
     useEffect(() => {
         if (isLoaded && user) {
             setUserDetail({ credits: 50 }); // Example starting credits
         } else {
             setUserDetail({ credits: 0 }); // Reset if user logs out
         }
     }, [user, isLoaded]);


    return (
        <UserDetailContext.Provider value={{ userDetail, setUserDetail }}>
            {children}
        </UserDetailContext.Provider>
    );
};