'use client';

import React, { createContext, useState } from 'react';

// Schema reference (from configs/schema.js):
// id: serial (not needed in context, handled by DB)
// clerkId: string (user id, not needed here)
// title: string
// topic: string
// script: string
// videoStyle: string
// voice: string
// captionName: string
// captionStyle: string
// status: string (default 'Pending')
// audioUrl: string
// captionJson: array/object
// images: array of strings
// createdAt, updatedAt: timestamps (not needed in context)

export const VideoDataContext = createContext(null);

export const VideoDataProvider = ({ children }) => {
    const [videoData, setvideoData] = useState({
        // All fields matching the schema, with sensible initial values
        title: '',                // string
        topic: '',                // string
        script: '',               // string
        videoStyle: '',           // string
        voice: '',                // string
        captionName: '',          // string
        captionStyle: '',         // string
        status: 'Pending',        // string, default as per schema
        audioUrl: '',             // string
        captionJson: [],          // array/object
        images: [],               // array of strings (image URLs or base64)
        // id, clerkId, createdAt, updatedAt are DB-managed, not needed here
    });

    return (
        <VideoDataContext.Provider value={{ videoData, setvideoData }}>
            {children}
        </VideoDataContext.Provider>
    );
};