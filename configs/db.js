import { neon } from '@neondatabase/serverless';
import { drizzle } from 'drizzle-orm/neon-http';



import * as schema from "./schema";


const sql = neon('postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require'
); // Use the renamed variable
// Note: The original code used drizzle({ client: sql }), which seems incorrect for neon-http.
// The correct usage is typically drizzle(sql). Let's assume the user intended drizzle(sql).
// If drizzle({ client: sql }) was intentional and correct for their specific setup, revert this change.
export const db = drizzle(sql, { schema });

// Removed the top-level await: const result = await db.execute('select 1');
// This avoids potential issues in environments that don't support top-level await,
// addressing the error mentioned in the context.
// If a connection test is needed, it should be performed within an async function elsewhere.
