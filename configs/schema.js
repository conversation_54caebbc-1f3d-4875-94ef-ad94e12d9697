import { pgTable, serial, varchar, integer, text, jsonb, timestamp, boolean, decimal, unique, check, index } from "drizzle-orm/pg-core";
import { relations, sql } from 'drizzle-orm';

// Enhanced users table with atomic credit system
export const users = pgTable('users', {
  // Using clerkId as the primary identifier from Clerk
  clerkId: varchar('clerk_id').primary<PERSON>ey(), // Clerk User ID
  name: varchar('name'), // Optional: Sync from Clerk
  email: varchar('email').notNull().unique(), // Optional: Sync from <PERSON>, ensure uniqueness
  imageUrl: varchar('image_url'), // Optional: Sync from Clerk
  currentCreditBalance: integer('current_credit_balance').default(10).notNull(), // New atomic credit balance
  // Keep legacy credits field for backward compatibility during migration
  credits: integer('credits').default(10).notNull(), // Legacy field - will be deprecated
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
}, (table) => ({
  // Ensure credit balance is never negative
  creditBalanceCheck: check('credit_balance_non_negative',
    sql`${table.currentCreditBalance} >= 0`),
}));

// New table for credit transactions (audit trail)
export const creditTransactions = pgTable('credit_transactions', {
  id: serial('id').primaryKey(),
  userId: varchar('user_id')
    .notNull()
    .references(() => users.clerkId, { onDelete: 'cascade' }),
  transactionType: varchar('transaction_type', { length: 50 })
    .notNull(), // 'DEBIT', 'CREDIT', 'REFUND', 'PURCHASE', 'ADJUSTMENT'
  amount: integer('amount').notNull(), // Positive for credits, negative for debits
  balanceBefore: integer('balance_before').notNull(),
  balanceAfter: integer('balance_after').notNull(),
  relatedEntityId: varchar('related_entity_id'), // videoId, orderId, etc.
  relatedEntityType: varchar('related_entity_type'), // 'VIDEO', 'ORDER', 'MANUAL'
  notes: text('notes'),
  metadata: jsonb('metadata'), // Additional context (workflow type, cost breakdown)
  createdAt: timestamp('created_at').defaultNow().notNull(),
}, (table) => ({
  // Indexes for performance
  userIdIdx: index('credit_transactions_user_id_idx').on(table.userId),
  transactionTypeIdx: index('credit_transactions_type_idx').on(table.transactionType),
  relatedEntityIdx: index('credit_transactions_entity_idx')
    .on(table.relatedEntityId, table.relatedEntityType),
  createdAtIdx: index('credit_transactions_created_at_idx').on(table.createdAt),

  // Ensure balance calculations are consistent
  balanceConsistencyCheck: check('balance_consistency',
    sql`${table.balanceBefore} + ${table.amount} = ${table.balanceAfter}`),
}));

// Define relations if needed (e.g., one user has many videos)
export const usersRelations = relations(users, ({ many }) => ({
	videos: many(videoData),
  scheduledPosts: many(scheduledPosts), // Add relation to new scheduledPosts table
  socialAccounts: many(userSocialAccounts), // Add relation to new userSocialAccounts table
  creditTransactions: many(creditTransactions), // Add relation to credit transactions
}));

// Define relations for creditTransactions
export const creditTransactionsRelations = relations(creditTransactions, ({ one }) => ({
  user: one(users, {
    fields: [creditTransactions.userId],
    references: [users.clerkId],
  }),
}));


// Table for Reddit post data
export const redditPostData = pgTable('reddit_post_data', {
  id: serial('id').primaryKey(),
  clerkId: varchar('clerk_id').notNull().references(() => users.clerkId, { onDelete: 'cascade' }), // Foreign key to users table
  postUrl: text('post_url').notNull(),
  subreddit: varchar('subreddit').notNull(),
  postTitle: text('post_title'),
  postContent: text('post_content'),
  postAuthor: varchar('post_author'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

// New table for video generation data
export const videoData = pgTable('video_data', {
  id: serial('id').primaryKey(),
  clerkId: varchar('clerk_id').notNull().references(() => users.clerkId, { onDelete: 'cascade' }), // Foreign key to users table
  title: varchar('title').notNull(),
  topic: text('topic'),
  script: text('script'),
  videoStyle: varchar('video_style'),
  aspectRatio: varchar('aspect_ratio'), 
  templateId: varchar('template_id'), 
  voice: varchar('voice'),
  captionName: varchar('caption_name'),
  // Remove the old captionStyle and audioSpeedOld fields
  captionStyleJson: jsonb('caption_style_jsonb'), 
  captionContainerStyle: jsonb('caption_container_style'),
  // Use a single audioSpeed field with the correct type
  audioSpeed: decimal('audio_speed_new', { precision: 3, scale: 2 }).default(1.0).notNull(),
  backgroundMusic: varchar('background_music'),
  estimatedDurationSeconds: integer('estimated_duration_seconds'),
  status: varchar('status').default('Pending').notNull(),
  audioUrl: text('audio_url'),
  captionJson: jsonb('caption_json'),
  images: text('images').array(),
  // Add fields for rendering pipeline
  renderId: varchar('render_id'), // Store Remotion Lambda render ID
  bucketName: varchar('bucket_name'), // Store S3 bucket name for render output
  renderedVideoUrl: text('rendered_video_url'), // Store the final rendered video URL
  // Promoted fields from workflow_data for better performance
  workflowType: varchar('workflow_type').notNull(), // Used in queries, indexing
  originalPrompt: text('original_prompt'), // Common across AI workflows
  avatarId: varchar('avatar_id'), // UGC videos, frequently queried
  operationId: varchar('operation_id'), // External API tracking (Captions, etc.)
  sourceMediaUrl: text('source_media_url'), // Narrator, Meme videos

  // Simplified workflow_data (complex nested data only)
  // Expected structure for workflow_data:
  // {
  //   // For AI Video:
  //   generatedContent: { script: string, audioUrl: string, captions: array, images: array },
  //   // For UGC Video:
  //   userAssetUrls: array, captionsOperationId: string,
  //   // For Stock Media:
  //   scenes: array, voiceConfig: object,
  //   // For Social Media:
  //   redditPost: object, twitterPost: object, backgroundVideoUrls: array
  //   // ... other workflow-specific nested data
  // }
  workflow_data: jsonb('workflow_data'),

  // Credit system fields
  costInCredits: integer('cost_in_credits').notNull().default(0),
  creditTransactionId: integer('credit_transaction_id')
    .references(() => creditTransactions.id),
  refundTransactionId: integer('refund_transaction_id')
    .references(() => creditTransactions.id),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
}, (table) => ({
  // Add indexes for promoted fields
  workflowTypeIdx: index('workflow_type_idx').on(table.workflowType),
  avatarIdIdx: index('avatar_id_idx').on(table.avatarId),
  operationIdIdx: index('operation_id_idx').on(table.operationId),
  originalPromptIdx: index('original_prompt_gin_idx').using('gin', sql`to_tsvector('english', ${table.originalPrompt})`),
}));

// Define relations for redditPostData
export const redditPostDataRelations = relations(redditPostData, ({ one }) => ({
	user: one(users, {
		fields: [redditPostData.clerkId],
		references: [users.clerkId],
	}),
}));

// Define relations for videoData
export const videoDataRelations = relations(videoData, ({ one }) => ({
	user: one(users, {
		fields: [videoData.clerkId],
		references: [users.clerkId],
	}),
	creditTransaction: one(creditTransactions, {
		fields: [videoData.creditTransactionId],
		references: [creditTransactions.id],
	}),
	refundTransaction: one(creditTransactions, {
		fields: [videoData.refundTransactionId],
		references: [creditTransactions.id],
	}),
}));

// Table for Stock Media Video data (AI Curator workflow)
export const stockMediaVideo = pgTable('stock_media_video', {
  id: serial('id').primaryKey(),
  userId: varchar('user_id').notNull().references(() => users.clerkId, { onDelete: 'cascade' }), // Foreign key to users table
  prompt: text('prompt').notNull(),
  status: varchar('status').default('queued').notNull(), // e.g., 'queued', 'processing', 'Completed', 'failed'
  
  // Script and Scenes
  script: text('script'), // Generated script
  scenes: jsonb('scenes'), // Original scene data
  selectedMedia: jsonb('selected_media'), // Selected media for each scene
  
  // Audio Information
  audioUrl: text('audio_url'), // URL to generated voiceover audio
  voiceConfig: jsonb('voice_config'), // Voice settings (voice type, speed, pitch, etc.)
  
  // Video Configuration
  videoConfig: jsonb('video_config'), // Video settings (resolution, FPS, etc.)
  
  // Media Assets
  mediaAssets: jsonb('media_assets'), // All media assets including scenes, background music, etc.
  
  // Rendering Settings
  renderSettings: jsonb('render_settings'), // Settings for video rendering
  
  // Metadata
  metadata: jsonb('metadata'), // Additional metadata (generation time, version, etc.)
  
  // Output
  renderedVideoUrl: text('rendered_video_url'), // URL to the final rendered video in S3
  
  // Timestamps
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

// Define relations for stockMediaVideo
export const stockMediaVideoRelations = relations(stockMediaVideo, ({ one }) => ({
	user: one(users, {
		fields: [stockMediaVideo.userId],
		references: [users.clerkId],
	}),
}));


export const uploadedFile = pgTable('uploaded_files', {
  id: serial('id').primaryKey(),
  userId: varchar('user_id').notNull().references(() => users.clerkId, { onDelete: 'cascade' }),
  s3Key: varchar('s3_key').notNull(),
  displayName: varchar('display_name').notNull(),
  uploaded: boolean('uploaded').default(false).notNull(),
  status: varchar('status').default('queued').notNull(), // Added status column
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

export const uploadedFileRelations = relations(uploadedFile, ({ one }) => ({
  user: one(users, {
    fields: [uploadedFile.userId],
    references: [users.clerkId],
  }),
}));


export const clip = pgTable('clips', {
  id: serial('id').primaryKey(),
  s3Key: varchar('s3_key').notNull(),
  uploadedFileId: integer('uploaded_file_id').notNull().references(() => uploadedFile.id, { onDelete: 'cascade' }),
  userId: varchar('user_id').notNull().references(() => users.clerkId, { onDelete: 'cascade' }),
  // Add other relevant fields for clips here (e.g., start time, end time, duration)
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

// Define relations if needed
export const clipRelations = relations(clip, ({ one }) => ({
  uploadedFile: one(uploadedFile, {
    fields: [clip.uploadedFileId],
    references: [uploadedFile.id],
  }),
  user: one(users, {
    fields: [clip.userId],
    references: [users.clerkId],
  }),
}));

// New table for user social media accounts
export const userSocialAccounts = pgTable('user_social_accounts', {
  id: serial('id').primaryKey(),
  clerkId: varchar('clerk_id').notNull().references(() => users.clerkId, { onDelete: 'cascade' }),
  platformName: varchar('platform_name').notNull(), // e.g., 'youtube', 'twitter', 'facebook'
  platformAccountId: varchar('platform_account_id').notNull(), // e.g., YouTube Channel ID, Twitter User ID
  accountDisplayName: varchar('account_display_name'), // e.g., YouTube Channel Name, Twitter @handle
  accessToken: text('access_token').notNull(),
  refreshToken: text('refresh_token'), // Important for YouTube
  tokenExpiresAt: timestamp('token_expires_at', { withTimezone: true }),
  otherCredentials: jsonb('other_credentials'), // For storing scopes, etc.
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
}, (table) => {
  return {
    unqPlatformAccount: unique('unq_platform_account').on(table.clerkId, table.platformName, table.platformAccountId),
  };
});

export const userSocialAccountsRelations = relations(userSocialAccounts, ({ one, many }) => ({
  user: one(users, {
    fields: [userSocialAccounts.clerkId],
    references: [users.clerkId],
  }),
  scheduledPosts: many(scheduledPosts),
}));

// New table for scheduled social media posts
export const scheduledPosts = pgTable('scheduled_posts', {
  id: serial('id').primaryKey(),
  clerkId: varchar('clerk_id').notNull().references(() => users.clerkId, { onDelete: 'cascade' }),
  socialAccountId: integer('social_account_id').references(() => userSocialAccounts.id, { onDelete: 'cascade' }), // Link to connected social account
  postType: varchar('post_type').notNull(), // e.g., 'youtube_video', 'twitter_text', 'facebook_image'
  contentText: text('content_text'), // Main text content (e.g., YouTube description, Twitter tweet)
  videoFilePathOnServer: text('video_file_path_on_server'), // Temporary path to video file on your server
  thumbnailFilePathOnServer: text('thumbnail_file_path_on_your_server'), // Temporary path to thumbnail file on your server
  scheduledAt: timestamp('scheduled_at', { withTimezone: true }), // When the post is scheduled to go live
  status: varchar('status').default('pending').notNull(), // e.g., 'pending', 'uploading', 'published', 'failed', 'processing_youtube'
  publishedAt: timestamp('published_at', { withTimezone: true }), // When the post was actually published
  platformPostId: varchar('platform_post_id'), // ID returned by the social media platform (e.g., YouTube video ID)
  errorDetails: jsonb('error_details'), // JSONB for storing error messages/details
  platformSpecificData: jsonb('platform_specific_data'), // JSONB for platform-specific metadata (e.g., YouTube title, tags, categoryId, privacyStatus, madeForKids)
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

export const scheduledPostsRelations = relations(scheduledPosts, ({ one }) => ({
  user: one(users, {
    fields: [scheduledPosts.clerkId],
    references: [users.clerkId],
  }),
  socialAccount: one(userSocialAccounts, {
    fields: [scheduledPosts.socialAccountId],
    references: [userSocialAccounts.id],
  }),
}));
