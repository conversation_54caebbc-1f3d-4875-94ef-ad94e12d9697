CREATE TABLE IF NOT EXISTS "users" (
	"clerk_id" varchar PRIMARY KEY NOT NULL,
	"name" varchar,
	"email" varchar NOT NULL,
	"image_url" varchar,
	"credits" integer DEFAULT 10 NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "users_email_unique" UNIQUE("email")
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "video_data" (
	"id" serial PRIMARY KEY NOT NULL,
	"clerk_id" varchar NOT NULL,
	"title" varchar NOT NULL,
	"topic" text,
	"script" text,
	"video_style" varchar,
	"aspect_ratio" varchar,
	"template_id" varchar,
	"voice" varchar,
	"caption_name" varchar,
	"caption_style" jsonb,
	"caption_container_style" jsonb,
	"status" varchar DEFAULT 'Pending' NOT NULL,
	"audio_url" text,
	"caption_json" jsonb,
	"images" text[],
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "video_data" DROP CONSTRAINT IF EXISTS "video_data_clerk_id_users_clerk_id_fk";--> statement-breakpoint
ALTER TABLE "video_data" ADD CONSTRAINT "video_data_clerk_id_users_clerk_id_fk" FOREIGN KEY ("clerk_id") REFERENCES "public"."users"("clerk_id") ON DELETE cascade ON UPDATE no action;