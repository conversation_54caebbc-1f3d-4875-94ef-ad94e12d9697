/**
 * Migration Script: Legacy Credit System to Atomic Credit System
 * 
 * This script migrates from the old 'credits' field to the new 'currentCreditBalance' field
 * and creates initial transaction records for audit trail.
 * 
 * Run this script once during deployment to migrate existing users.
 */

import { db } from '../configs/db.js';
import { users, creditTransactions } from '../configs/schema.js';
import { eq, isNotNull } from 'drizzle-orm';

async function migrateCreditSystem() {
  console.log('Starting credit system migration...');
  
  try {
    // 1. Get all users with legacy credits
    const usersToMigrate = await db
      .select({
        clerkId: users.clerkId,
        legacyCredits: users.credits,
        currentCreditBalance: users.currentCreditBalance
      })
      .from(users)
      .where(isNotNull(users.credits));

    console.log(`Found ${usersToMigrate.length} users to migrate`);

    let migratedCount = 0;
    let skippedCount = 0;
    let errorCount = 0;

    for (const user of usersToMigrate) {
      try {
        // Skip if already migrated (currentCreditBalance matches legacyCredits)
        if (user.currentCreditBalance === user.legacyCredits) {
          console.log(`User ${user.clerkId} already migrated, skipping`);
          skippedCount++;
          continue;
        }

        // Migrate in a transaction
        await db.transaction(async (tx) => {
          // Update currentCreditBalance to match legacy credits
          await tx
            .update(users)
            .set({ 
              currentCreditBalance: user.legacyCredits,
              updatedAt: new Date()
            })
            .where(eq(users.clerkId, user.clerkId));

          // Create initial transaction record for audit trail
          await tx
            .insert(creditTransactions)
            .values({
              userId: user.clerkId,
              transactionType: 'MIGRATION',
              amount: user.legacyCredits - 10, // Assuming 10 was the default starting balance
              balanceBefore: 10,
              balanceAfter: user.legacyCredits,
              relatedEntityType: 'MIGRATION',
              notes: 'Migration from legacy credit system',
              metadata: { 
                legacyCredits: user.legacyCredits,
                migrationDate: new Date().toISOString()
              }
            });
        });

        console.log(`Migrated user ${user.clerkId}: ${user.legacyCredits} credits`);
        migratedCount++;

      } catch (error) {
        console.error(`Error migrating user ${user.clerkId}:`, error);
        errorCount++;
      }
    }

    console.log('\nMigration completed!');
    console.log(`Migrated: ${migratedCount} users`);
    console.log(`Skipped: ${skippedCount} users`);
    console.log(`Errors: ${errorCount} users`);

    // 2. Verify migration integrity
    console.log('\nVerifying migration integrity...');
    await verifyMigrationIntegrity();

  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  }
}

async function verifyMigrationIntegrity() {
  try {
    const allUsers = await db
      .select({
        clerkId: users.clerkId,
        legacyCredits: users.credits,
        currentCreditBalance: users.currentCreditBalance
      })
      .from(users);

    let consistentCount = 0;
    let inconsistentCount = 0;

    for (const user of allUsers) {
      if (user.currentCreditBalance === user.legacyCredits) {
        consistentCount++;
      } else {
        console.warn(`Inconsistent user ${user.clerkId}: legacy=${user.legacyCredits}, current=${user.currentCreditBalance}`);
        inconsistentCount++;
      }
    }

    console.log(`Consistent users: ${consistentCount}`);
    console.log(`Inconsistent users: ${inconsistentCount}`);

    if (inconsistentCount === 0) {
      console.log('✅ Migration integrity verified successfully!');
    } else {
      console.log('⚠️  Migration has inconsistencies that need manual review');
    }

  } catch (error) {
    console.error('Verification failed:', error);
  }
}

/**
 * Rollback function (use with caution)
 */
async function rollbackMigration() {
  console.log('Rolling back credit system migration...');
  
  try {
    // Remove migration transactions
    const deletedTransactions = await db
      .delete(creditTransactions)
      .where(eq(creditTransactions.transactionType, 'MIGRATION'))
      .returning();

    console.log(`Deleted ${deletedTransactions.length} migration transactions`);

    // Reset currentCreditBalance to legacy credits (if needed)
    const allUsers = await db
      .select({
        clerkId: users.clerkId,
        legacyCredits: users.credits
      })
      .from(users);

    for (const user of allUsers) {
      await db
        .update(users)
        .set({ 
          currentCreditBalance: user.legacyCredits,
          updatedAt: new Date()
        })
        .where(eq(users.clerkId, user.clerkId));
    }

    console.log('Rollback completed');

  } catch (error) {
    console.error('Rollback failed:', error);
  }
}

// Run migration if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const command = process.argv[2];
  
  if (command === 'rollback') {
    rollbackMigration();
  } else {
    migrateCreditSystem();
  }
}

export { migrateCreditSystem, rollbackMigration, verifyMigrationIntegrity };
