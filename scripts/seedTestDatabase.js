#!/usr/bin/env node

/**
 * Test Database Seeding Script
 * 
 * Seeds the test database with baseline data for consistent testing.
 * This script is run during CI/CD setup and local test environment initialization.
 */

const { drizzle } = require('drizzle-orm/postgres-js');
const postgres = require('postgres');
const { users, creditTransactions, videoData } = require('../configs/schema');

async function seedTestDatabase() {
  console.log('🌱 Starting test database seeding...');
  
  try {
    // Connect to test database
    const testDbUrl = process.env.TEST_DATABASE_URL || 
      'postgresql://postgres:password@localhost:5432/ai_video_test';
    
    console.log(`📡 Connecting to test database: ${testDbUrl.replace(/:[^:]*@/, ':***@')}`);
    
    const client = postgres(testDbUrl, {
      max: 1,
      idle_timeout: 20,
      connect_timeout: 10,
    });
    
    const db = drizzle(client);
    
    // Clear existing data (in reverse dependency order)
    console.log('🧹 Clearing existing test data...');
    await db.delete(creditTransactions);
    await db.delete(videoData);
    await db.delete(users);
    
    // Seed baseline users
    console.log('👥 Seeding baseline users...');
    await db.insert(users).values([
      {
        clerkId: 'test_user_baseline',
        email: '<EMAIL>',
        credits: 50,
        currentCreditBalance: 50,
        createdAt: new Date('2024-01-01T00:00:00Z'),
        updatedAt: new Date('2024-01-01T00:00:00Z')
      },
      {
        clerkId: 'test_user_low_credits',
        email: '<EMAIL>',
        credits: 2,
        currentCreditBalance: 2,
        createdAt: new Date('2024-01-01T00:00:00Z'),
        updatedAt: new Date('2024-01-01T00:00:00Z')
      },
      {
        clerkId: 'test_user_no_credits',
        email: '<EMAIL>',
        credits: 0,
        currentCreditBalance: 0,
        createdAt: new Date('2024-01-01T00:00:00Z'),
        updatedAt: new Date('2024-01-01T00:00:00Z')
      },
      {
        clerkId: 'test_user_premium',
        email: '<EMAIL>',
        credits: 1000,
        currentCreditBalance: 1000,
        createdAt: new Date('2024-01-01T00:00:00Z'),
        updatedAt: new Date('2024-01-01T00:00:00Z')
      }
    ]);
    
    // Seed some baseline credit transactions
    console.log('💳 Seeding baseline credit transactions...');
    await db.insert(creditTransactions).values([
      {
        userId: 'test_user_baseline',
        type: 'WELCOME',
        amount: 50,
        balanceBefore: 0,
        balanceAfter: 50,
        description: 'Welcome Credits',
        createdAt: new Date('2024-01-01T00:00:00Z')
      },
      {
        userId: 'test_user_premium',
        type: 'PURCHASE',
        amount: 1000,
        balanceBefore: 0,
        balanceAfter: 1000,
        description: 'Premium Credit Package',
        createdAt: new Date('2024-01-01T00:00:00Z')
      }
    ]);
    
    // Seed some baseline video records
    console.log('🎥 Seeding baseline video records...');
    await db.insert(videoData).values([
      {
        clerkId: 'test_user_baseline',
        title: 'Baseline Test Video',
        workflowType: 'AI_VIDEO',
        status: 'Completed',
        costInCredits: 5,
        videoUrl: 'https://test-storage.com/baseline-video.mp4',
        thumbnailUrl: 'https://test-storage.com/baseline-thumbnail.jpg',
        createdAt: new Date('2024-01-01T00:00:00Z'),
        updatedAt: new Date('2024-01-01T00:00:00Z')
      },
      {
        clerkId: 'test_user_baseline',
        title: 'Processing Test Video',
        workflowType: 'MEME_VIDEO',
        status: 'Processing',
        costInCredits: 2,
        createdAt: new Date('2024-01-01T01:00:00Z'),
        updatedAt: new Date('2024-01-01T01:00:00Z')
      }
    ]);
    
    // Verify seeded data
    console.log('✅ Verifying seeded data...');
    const userCount = await db.select().from(users);
    const transactionCount = await db.select().from(creditTransactions);
    const videoCount = await db.select().from(videoData);
    
    console.log(`📊 Seeding completed successfully:`);
    console.log(`   - Users: ${userCount.length}`);
    console.log(`   - Credit Transactions: ${transactionCount.length}`);
    console.log(`   - Videos: ${videoCount.length}`);
    
    // Close database connection
    await client.end();
    
    console.log('🎉 Test database seeding completed successfully!');
    
  } catch (error) {
    console.error('❌ Test database seeding failed:', error);
    process.exit(1);
  }
}

// Run seeding if this script is executed directly
if (require.main === module) {
  seedTestDatabase();
}

module.exports = { seedTestDatabase };
