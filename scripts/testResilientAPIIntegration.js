/**
 * Test Script: Resilient API Integration Across All Video Workflows
 * 
 * This script verifies that all 7 video generation workflows have proper
 * resilient API integration including circuit breakers, retry logic, and fallbacks.
 */

import { getCircuitBreakerStatus, resetCircuitBreaker } from '../lib/circuitBreaker.js';
import { checkAPIHealth } from '../lib/inngestApiHelpers.js';

// Test configurations for each workflow type
const workflowTests = {
  AI_VIDEO: {
    name: 'AI Video Generation',
    file: 'app/inngest/functions/aiVideoGeneration.js',
    requiredAPIs: ['GOOGLE_AI', 'DEEPGRAM', 'RUNWARE'],
    resilientFeatures: [
      'withCircuitBreaker for Google AI',
      'Emergency fallback for script generation',
      'Emergency fallback for image prompts',
      'API health check',
      'Credit refund on failure'
    ]
  },
  
  MEME_VIDEO: {
    name: 'Meme Video Generation',
    file: 'app/inngest/functions/memeVideoGeneration.js',
    requiredAPIs: [],
    resilientFeatures: [
      'API health check',
      'Credit refund on failure',
      'Resilient imports available'
    ]
  },
  
  UGC_VIDEO: {
    name: 'UGC Video Generation',
    file: 'app/inngest/functions/aiUgcVideoGeneration.js',
    requiredAPIs: ['CAPTIONS_AI', 'GOOGLE_AI'],
    resilientFeatures: [
      'withCircuitBreaker for Captions AI',
      'withCircuitBreaker for Google AI',
      'Resilient script generation',
      'Resilient video creation and polling',
      'API health check',
      'Credit refund on failure'
    ]
  },
  
  NARRATOR_VIDEO: {
    name: 'Narrator Video Generation',
    file: 'app/inngest/functions/narratorVideoGeneration.js',
    requiredAPIs: ['GOOGLE_AI'],
    resilientFeatures: [
      'withCircuitBreaker for Google AI',
      'Emergency fallback for video analysis',
      'Emergency fallback for script generation',
      'API health check',
      'Credit refund on failure'
    ]
  },
  
  REDDIT_VIDEO: {
    name: 'Reddit Video Generation',
    file: 'app/inngest/functions/redditVideoGeneration.js',
    requiredAPIs: [],
    resilientFeatures: [
      'API health check',
      'Credit refund on failure',
      'Resilient imports available'
    ]
  },
  
  TWITTER_VIDEO: {
    name: 'Twitter Video Generation',
    file: 'app/inngest/functions/twitterVideoGeneration.js',
    requiredAPIs: [],
    resilientFeatures: [
      'API health check',
      'Credit refund on failure',
      'Resilient imports available'
    ]
  },
  
  STOCK_MEDIA_VIDEO: {
    name: 'Stock Media Video Generation',
    file: 'app/inngest/functions/stockMediaVideoGeneration.js',
    requiredAPIs: ['GOOGLE_AI', 'PEXELS', 'PIXABAY'],
    resilientFeatures: [
      'withCircuitBreaker for Google AI',
      'withCircuitBreaker for Pexels',
      'withCircuitBreaker for Pixabay',
      'Emergency fallback for script generation',
      'Emergency fallback for media search',
      'API health check',
      'Credit refund on failure'
    ]
  }
};

/**
 * Test all workflows for resilient API integration
 */
async function testAllResilientWorkflows() {
  console.log('🔧 Testing Resilient API Integration Across All Video Workflows...\n');
  
  const results = {
    passed: 0,
    failed: 0,
    details: []
  };
  
  // Test each workflow
  for (const [workflowType, config] of Object.entries(workflowTests)) {
    console.log(`\n📹 Testing ${config.name} (${workflowType})...`);
    
    try {
      const workflowTest = await testWorkflowResilience(workflowType, config);
      
      if (workflowTest.success) {
        console.log(`  ✅ ${config.name} resilience test passed`);
        results.passed++;
        results.details.push({
          workflow: workflowType,
          status: 'PASSED',
          features: workflowTest.features,
          apis: workflowTest.apis
        });
      } else {
        console.log(`  ❌ ${config.name} resilience test failed: ${workflowTest.error}`);
        results.failed++;
        results.details.push({
          workflow: workflowType,
          status: 'FAILED',
          error: workflowTest.error,
          missing: workflowTest.missing
        });
      }
      
    } catch (error) {
      console.log(`  ❌ ${config.name} test failed: ${error.message}`);
      results.failed++;
      results.details.push({
        workflow: workflowType,
        status: 'FAILED',
        error: error.message
      });
    }
  }
  
  // Test circuit breaker system
  console.log('\n🔍 Testing Circuit Breaker System...');
  try {
    const circuitBreakerTest = await testCircuitBreakerSystem();
    if (circuitBreakerTest.success) {
      console.log('  ✅ Circuit breaker system test passed');
    } else {
      console.log('  ⚠️  Circuit breaker system test found issues');
    }
  } catch (error) {
    console.log(`  ❌ Circuit breaker system test failed: ${error.message}`);
  }
  
  // Print summary
  console.log('\n📊 Resilient API Integration Test Summary:');
  console.log(`  ✅ Passed: ${results.passed}`);
  console.log(`  ❌ Failed: ${results.failed}`);
  console.log(`  📈 Success Rate: ${((results.passed / (results.passed + results.failed)) * 100).toFixed(1)}%`);
  
  // Print detailed results
  console.log('\n📋 Detailed Results:');
  results.details.forEach(result => {
    const status = result.status === 'PASSED' ? '✅' : '❌';
    console.log(`  ${status} ${result.workflow}`);
    if (result.features) {
      console.log(`    Features: ${result.features.join(', ')}`);
    }
    if (result.apis) {
      console.log(`    APIs: ${result.apis.join(', ')}`);
    }
    if (result.error) {
      console.log(`    Error: ${result.error}`);
    }
    if (result.missing) {
      console.log(`    Missing: ${result.missing.join(', ')}`);
    }
  });
  
  return results;
}

/**
 * Test individual workflow for resilient API features
 */
async function testWorkflowResilience(workflowType, config) {
  try {
    // Read the workflow file to check for resilient features
    const fs = await import('fs');
    const path = await import('path');
    
    const filePath = path.resolve(config.file);
    const fileContent = fs.readFileSync(filePath, 'utf8');
    
    const foundFeatures = [];
    const foundAPIs = [];
    const missingFeatures = [];
    
    // Check for required resilient features
    const featureChecks = {
      'withCircuitBreaker': /withCircuitBreaker\(/g,
      'createEmergencyFallbacks': /createEmergencyFallbacks\(/g,
      'checkAPIHealth': /checkAPIHealth\(/g,
      'resilient imports': /from ['"]@\/lib\/inngestApiHelpers['"];?/g,
      'circuit breaker imports': /from ['"]@\/lib\/circuitBreaker['"];?/g,
      'credit refund on failure': /onFailure.*triggerCreditRefund/gs,
      'emergency fallback': /fallback/gi
    };
    
    for (const [feature, regex] of Object.entries(featureChecks)) {
      if (regex.test(fileContent)) {
        foundFeatures.push(feature);
      }
    }
    
    // Check for API-specific circuit breaker usage
    for (const api of config.requiredAPIs) {
      const apiRegex = new RegExp(`withCircuitBreaker\\(['"]${api}['"]`, 'g');
      if (apiRegex.test(fileContent)) {
        foundAPIs.push(api);
      } else {
        missingFeatures.push(`Circuit breaker for ${api}`);
      }
    }
    
    // Check for minimum required features
    const requiredFeatures = ['resilient imports', 'checkAPIHealth', 'credit refund on failure'];
    for (const required of requiredFeatures) {
      if (!foundFeatures.includes(required)) {
        missingFeatures.push(required);
      }
    }
    
    // Determine if test passed
    const success = missingFeatures.length === 0;
    
    return {
      success,
      features: foundFeatures,
      apis: foundAPIs,
      missing: missingFeatures,
      error: success ? null : `Missing resilient features: ${missingFeatures.join(', ')}`
    };
    
  } catch (error) {
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Test circuit breaker system functionality
 */
async function testCircuitBreakerSystem() {
  try {
    // Get current circuit breaker status
    const status = getCircuitBreakerStatus();
    
    const results = {
      success: true,
      totalCircuitBreakers: Object.keys(status).length,
      healthyCircuitBreakers: 0,
      openCircuitBreakers: 0,
      halfOpenCircuitBreakers: 0
    };
    
    // Analyze circuit breaker states
    for (const [apiName, cbStatus] of Object.entries(status)) {
      switch (cbStatus.state) {
        case 'CLOSED':
          results.healthyCircuitBreakers++;
          break;
        case 'OPEN':
          results.openCircuitBreakers++;
          break;
        case 'HALF_OPEN':
          results.halfOpenCircuitBreakers++;
          break;
      }
    }
    
    console.log(`    Circuit Breakers: ${results.totalCircuitBreakers} total`);
    console.log(`    Healthy: ${results.healthyCircuitBreakers}, Open: ${results.openCircuitBreakers}, Half-Open: ${results.halfOpenCircuitBreakers}`);
    
    // Test reset functionality
    if (results.openCircuitBreakers > 0) {
      console.log(`    Testing circuit breaker reset functionality...`);
      // Note: In a real test, you might want to reset and verify
    }
    
    return results;
    
  } catch (error) {
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Test specific workflow resilience
 */
async function testSpecificWorkflowResilience(workflowType) {
  if (!workflowTests[workflowType]) {
    console.error(`❌ Unknown workflow type: ${workflowType}`);
    return;
  }
  
  console.log(`🔧 Testing ${workflowTests[workflowType].name} resilience specifically...\n`);
  
  const config = workflowTests[workflowType];
  const result = await testWorkflowResilience(workflowType, config);
  
  if (result.success) {
    console.log(`✅ ${config.name} resilience test passed!`);
    console.log(`  Features: ${result.features.join(', ')}`);
    console.log(`  APIs: ${result.apis.join(', ')}`);
  } else {
    console.log(`❌ ${config.name} resilience test failed: ${result.error}`);
    if (result.missing) {
      console.log(`  Missing: ${result.missing.join(', ')}`);
    }
  }
  
  return result;
}

// Export functions for use in other scripts
export { testAllResilientWorkflows, testSpecificWorkflowResilience, workflowTests };

// Run tests if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const workflowType = process.argv[2];
  
  if (workflowType && workflowType !== 'all') {
    testSpecificWorkflowResilience(workflowType.toUpperCase());
  } else {
    testAllResilientWorkflows();
  }
}
