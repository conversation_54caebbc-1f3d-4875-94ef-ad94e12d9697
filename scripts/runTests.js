#!/usr/bin/env node

/**
 * Test Runner Script for AI Video Generation Platform
 * 
 * This script provides a unified interface for running different types of tests
 * and handles test environment setup and cleanup.
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

// ANSI color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logHeader(message) {
  log('\n' + '='.repeat(60), 'cyan');
  log(`  ${message}`, 'bright');
  log('='.repeat(60), 'cyan');
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

// Check if required dependencies are installed
function checkDependencies() {
  logHeader('Checking Dependencies');
  
  const packageJsonPath = path.join(process.cwd(), 'package.json');
  if (!fs.existsSync(packageJsonPath)) {
    logError('package.json not found');
    return false;
  }
  
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  const devDeps = packageJson.devDependencies || {};
  
  const requiredDeps = [
    '@playwright/test',
    '@testing-library/jest-dom',
    '@testing-library/react',
    '@testing-library/user-event',
    'jest',
    'jest-environment-jsdom',
  ];
  
  const missingDeps = requiredDeps.filter(dep => !devDeps[dep]);
  
  if (missingDeps.length > 0) {
    logError('Missing required dependencies:');
    missingDeps.forEach(dep => log(`  - ${dep}`, 'red'));
    log('\nPlease install missing dependencies:', 'yellow');
    log(`npm install --save-dev ${missingDeps.join(' ')}`, 'cyan');
    return false;
  }
  
  logSuccess('All required dependencies are installed');
  return true;
}

// Run a command and return a promise
function runCommand(command, args = [], options = {}) {
  return new Promise((resolve, reject) => {
    const child = spawn(command, args, {
      stdio: 'inherit',
      shell: true,
      ...options,
    });
    
    child.on('close', (code) => {
      if (code === 0) {
        resolve(code);
      } else {
        reject(new Error(`Command failed with exit code ${code}`));
      }
    });
    
    child.on('error', (error) => {
      reject(error);
    });
  });
}

// Test runners for different test types
const testRunners = {
  async unit() {
    logHeader('Running Unit Tests');
    try {
      await runCommand('npx', ['jest', '--testPathPattern=tests/unit', '--verbose']);
      logSuccess('Unit tests completed successfully');
      return true;
    } catch (error) {
      logError('Unit tests failed');
      return false;
    }
  },
  
  async integration() {
    logHeader('Running Integration Tests');
    try {
      await runCommand('npx', ['jest', '--testPathPattern=tests/integration', '--verbose']);
      logSuccess('Integration tests completed successfully');
      return true;
    } catch (error) {
      logError('Integration tests failed');
      return false;
    }
  },
  
  async e2e() {
    logHeader('Running End-to-End Tests');
    try {
      // Check if Playwright browsers are installed
      logInfo('Checking Playwright browser installation...');
      try {
        await runCommand('npx', ['playwright', 'install', '--dry-run']);
      } catch {
        logWarning('Installing Playwright browsers...');
        await runCommand('npx', ['playwright', 'install']);
      }
      
      await runCommand('npx', ['playwright', 'test']);
      logSuccess('End-to-end tests completed successfully');
      return true;
    } catch (error) {
      logError('End-to-end tests failed');
      return false;
    }
  },
  
  async coverage() {
    logHeader('Running Tests with Coverage');
    try {
      await runCommand('npx', ['jest', '--coverage', '--verbose']);
      logSuccess('Coverage tests completed successfully');
      return true;
    } catch (error) {
      logError('Coverage tests failed');
      return false;
    }
  },
  
  async all() {
    logHeader('Running All Tests');
    const results = {
      unit: await testRunners.unit(),
      integration: await testRunners.integration(),
      e2e: await testRunners.e2e(),
    };
    
    const passed = Object.values(results).filter(Boolean).length;
    const total = Object.keys(results).length;
    
    log('\n' + '='.repeat(60), 'cyan');
    log('  Test Results Summary', 'bright');
    log('='.repeat(60), 'cyan');
    
    Object.entries(results).forEach(([type, passed]) => {
      const status = passed ? '✅ PASSED' : '❌ FAILED';
      log(`  ${type.toUpperCase().padEnd(12)} ${status}`);
    });
    
    log('='.repeat(60), 'cyan');
    
    if (passed === total) {
      logSuccess(`All ${total} test suites passed!`);
      return true;
    } else {
      logError(`${total - passed} of ${total} test suites failed`);
      return false;
    }
  },
};

// Main function
async function main() {
  const args = process.argv.slice(2);
  const testType = args[0] || 'all';
  
  log('🧪 AI Video Generation Platform - Test Runner', 'bright');
  log('================================================', 'cyan');
  
  // Check dependencies first
  if (!checkDependencies()) {
    process.exit(1);
  }
  
  // Validate test type
  if (!testRunners[testType]) {
    logError(`Unknown test type: ${testType}`);
    log('\nAvailable test types:', 'yellow');
    Object.keys(testRunners).forEach(type => {
      log(`  - ${type}`, 'cyan');
    });
    process.exit(1);
  }
  
  // Set test environment
  process.env.NODE_ENV = 'test';
  
  try {
    const success = await testRunners[testType]();
    
    if (success) {
      log('\n🎉 All tests completed successfully!', 'green');
      process.exit(0);
    } else {
      log('\n💥 Some tests failed. Please check the output above.', 'red');
      process.exit(1);
    }
  } catch (error) {
    logError(`Test execution failed: ${error.message}`);
    process.exit(1);
  }
}

// Handle uncaught errors
process.on('unhandledRejection', (error) => {
  logError(`Unhandled rejection: ${error.message}`);
  process.exit(1);
});

process.on('uncaughtException', (error) => {
  logError(`Uncaught exception: ${error.message}`);
  process.exit(1);
});

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = { testRunners, runCommand };
