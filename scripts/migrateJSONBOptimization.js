/**
 * Database Migration: JSONB Optimization for video_data table
 * 
 * This script promotes frequently queried fields from workflow_data JSONB
 * to dedicated columns for better performance and indexing.
 */

import { db } from '../configs/db.js';
import { videoData } from '../configs/schema.js';
import { sql } from 'drizzle-orm';

/**
 * Run the migration to promote JSONB fields
 */
async function runMigration() {
  console.log('🚀 Starting JSONB optimization migration...\n');

  try {
    // Step 1: Add new columns
    console.log('📝 Step 1: Adding new columns...');
    
    await db.execute(sql`
      ALTER TABLE video_data 
      ADD COLUMN IF NOT EXISTS workflow_type VARCHAR,
      ADD COLUMN IF NOT EXISTS original_prompt TEXT,
      ADD COLUMN IF NOT EXISTS avatar_id VARCHAR,
      ADD COLUMN IF NOT EXISTS operation_id VARCHAR,
      ADD COLUMN IF NOT EXISTS source_media_url TEXT;
    `);
    
    console.log('✅ New columns added successfully');

    // Step 2: Populate new columns from existing workflow_data
    console.log('\n📊 Step 2: Populating columns from workflow_data...');
    
    const updateResult = await db.execute(sql`
      UPDATE video_data 
      SET 
        workflow_type = COALESCE(
          workflow_data->>'workflowType',
          CASE 
            WHEN workflow_data->>'narrativeScript' IS NOT NULL THEN 'narratorVideo'
            WHEN workflow_data->>'avatarChoice' IS NOT NULL THEN 'ugcVideo'
            WHEN workflow_data->>'redditPost' IS NOT NULL THEN 'redditVideo'
            WHEN workflow_data->>'twitterPost' IS NOT NULL THEN 'twitterVideo'
            WHEN workflow_data->>'memeText' IS NOT NULL THEN 'memeVideo'
            WHEN workflow_data->>'scenes' IS NOT NULL THEN 'stockMediaVideo'
            ELSE 'aiVideo'
          END
        ),
        original_prompt = COALESCE(
          workflow_data->>'originalPrompt',
          workflow_data->>'userPrompt',
          topic,
          workflow_data->'generatedContent'->>'script'
        ),
        avatar_id = COALESCE(
          workflow_data->>'avatarChoice',
          workflow_data->>'avatarId'
        ),
        operation_id = COALESCE(
          workflow_data->>'captionsOperationId',
          workflow_data->>'operationId'
        ),
        source_media_url = COALESCE(
          workflow_data->>'originalVideoUrl',
          workflow_data->>'videoSource',
          workflow_data->>'videoUrl'
        )
      WHERE workflow_data IS NOT NULL;
    `);
    
    console.log(`✅ Updated ${updateResult.rowCount || 0} records with promoted field data`);

    // Step 3: Set workflow_type as NOT NULL with default values
    console.log('\n🔒 Step 3: Setting constraints...');
    
    // First, ensure all records have a workflow_type
    await db.execute(sql`
      UPDATE video_data 
      SET workflow_type = 'aiVideo' 
      WHERE workflow_type IS NULL;
    `);
    
    // Then add the NOT NULL constraint
    await db.execute(sql`
      ALTER TABLE video_data 
      ALTER COLUMN workflow_type SET NOT NULL;
    `);
    
    console.log('✅ Constraints applied successfully');

    // Step 4: Create indexes for performance
    console.log('\n📈 Step 4: Creating indexes...');
    
    await db.execute(sql`
      CREATE INDEX IF NOT EXISTS workflow_type_idx ON video_data(workflow_type);
    `);
    
    await db.execute(sql`
      CREATE INDEX IF NOT EXISTS avatar_id_idx ON video_data(avatar_id) WHERE avatar_id IS NOT NULL;
    `);
    
    await db.execute(sql`
      CREATE INDEX IF NOT EXISTS operation_id_idx ON video_data(operation_id) WHERE operation_id IS NOT NULL;
    `);
    
    await db.execute(sql`
      CREATE INDEX IF NOT EXISTS original_prompt_gin_idx ON video_data 
      USING gin(to_tsvector('english', original_prompt)) 
      WHERE original_prompt IS NOT NULL;
    `);
    
    console.log('✅ Indexes created successfully');

    // Step 5: Verify migration
    console.log('\n🔍 Step 5: Verifying migration...');
    
    const verificationResults = await db.execute(sql`
      SELECT 
        workflow_type,
        COUNT(*) as count,
        COUNT(original_prompt) as with_prompt,
        COUNT(avatar_id) as with_avatar,
        COUNT(operation_id) as with_operation,
        COUNT(source_media_url) as with_source_url
      FROM video_data 
      GROUP BY workflow_type
      ORDER BY count DESC;
    `);
    
    console.log('\n📊 Migration Results by Workflow Type:');
    console.table(verificationResults.rows);

    // Step 6: Clean up workflow_data (optional - remove promoted fields)
    console.log('\n🧹 Step 6: Cleaning up workflow_data...');
    
    await db.execute(sql`
      UPDATE video_data 
      SET workflow_data = workflow_data - 'workflowType' - 'originalPrompt' - 'userPrompt' - 'avatarChoice' - 'avatarId' - 'captionsOperationId' - 'operationId' - 'originalVideoUrl' - 'videoSource' - 'videoUrl'
      WHERE workflow_data IS NOT NULL;
    `);
    
    console.log('✅ Workflow_data cleaned up (promoted fields removed)');

    console.log('\n🎉 JSONB optimization migration completed successfully!');
    
    return {
      success: true,
      message: 'Migration completed successfully'
    };

  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  }
}

/**
 * Rollback the migration (for testing purposes)
 */
async function rollbackMigration() {
  console.log('🔄 Rolling back JSONB optimization migration...\n');

  try {
    // Drop indexes
    console.log('📉 Dropping indexes...');
    await db.execute(sql`DROP INDEX IF EXISTS workflow_type_idx;`);
    await db.execute(sql`DROP INDEX IF EXISTS avatar_id_idx;`);
    await db.execute(sql`DROP INDEX IF EXISTS operation_id_idx;`);
    await db.execute(sql`DROP INDEX IF EXISTS original_prompt_gin_idx;`);
    
    // Drop columns
    console.log('🗑️ Dropping columns...');
    await db.execute(sql`
      ALTER TABLE video_data 
      DROP COLUMN IF EXISTS workflow_type,
      DROP COLUMN IF EXISTS original_prompt,
      DROP COLUMN IF EXISTS avatar_id,
      DROP COLUMN IF EXISTS operation_id,
      DROP COLUMN IF EXISTS source_media_url;
    `);
    
    console.log('✅ Rollback completed successfully');
    
    return {
      success: true,
      message: 'Rollback completed successfully'
    };

  } catch (error) {
    console.error('❌ Rollback failed:', error);
    throw error;
  }
}

/**
 * Test the migration with integrity checks
 */
async function testMigration() {
  console.log('🧪 Testing JSONB optimization migration...\n');

  try {
    // Get current state
    const beforeState = await db.execute(sql`
      SELECT COUNT(*) as total_records,
             COUNT(workflow_data) as with_workflow_data
      FROM video_data;
    `);
    
    console.log('📊 Before Migration:');
    console.table(beforeState.rows);

    // Run migration
    await runMigration();

    // Verify after state
    const afterState = await db.execute(sql`
      SELECT COUNT(*) as total_records,
             COUNT(workflow_type) as with_workflow_type,
             COUNT(original_prompt) as with_original_prompt,
             COUNT(avatar_id) as with_avatar_id,
             COUNT(operation_id) as with_operation_id,
             COUNT(source_media_url) as with_source_media_url
      FROM video_data;
    `);
    
    console.log('\n📊 After Migration:');
    console.table(afterState.rows);

    // Test queries
    console.log('\n🔍 Testing query performance...');
    
    const queryTests = [
      {
        name: 'Workflow Type Filter',
        query: sql`SELECT COUNT(*) FROM video_data WHERE workflow_type = 'aiVideo';`
      },
      {
        name: 'Avatar ID Filter',
        query: sql`SELECT COUNT(*) FROM video_data WHERE avatar_id IS NOT NULL;`
      },
      {
        name: 'Text Search',
        query: sql`SELECT COUNT(*) FROM video_data WHERE to_tsvector('english', original_prompt) @@ to_tsquery('english', 'video');`
      }
    ];

    for (const test of queryTests) {
      const start = Date.now();
      const result = await db.execute(test.query);
      const duration = Date.now() - start;
      console.log(`  ${test.name}: ${result.rows[0].count} results in ${duration}ms`);
    }

    console.log('\n✅ Migration test completed successfully!');
    
    return {
      success: true,
      beforeState: beforeState.rows[0],
      afterState: afterState.rows[0]
    };

  } catch (error) {
    console.error('❌ Migration test failed:', error);
    throw error;
  }
}

// Export functions for use in other scripts
export { runMigration, rollbackMigration, testMigration };

// Run migration if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const command = process.argv[2];
  
  switch (command) {
    case 'rollback':
      rollbackMigration();
      break;
    case 'test':
      testMigration();
      break;
    default:
      runMigration();
  }
}
