/**
 * Test Script: All Video Generation Workflows with Atomic Credit System
 * 
 * This script tests all video generation workflows to ensure they work correctly
 * with the new atomic credit system.
 */

import { testCreditSystemIntegrity, testVideoGenerationWorkflow } from '../lib/creditSystemTest.js';
import { getCostForWorkflow } from '../lib/atomicCreditSystem.js';

// Test configurations for each workflow type
const testConfigs = {
  AI_VIDEO: {
    projectTitle: 'Test AI Video',
    topic: 'Artificial Intelligence in 2024',
    videoStyle: 'Professional',
    aspectRatio: '9:16',
    templateId: 'AI_VIDEO_TEMPLATE',
    voice: 'en-US-Wavenet-D',
    audioSpeed: 1.0,
    backgroundMusic: 'upbeat',
    estimatedDurationSeconds: 60
  },
  
  MEME_VIDEO: {
    projectTitle: 'Test Meme Video',
    memeText: 'When you finally understand atomic transactions',
    videoSource: 'test-video-url',
    font: 'Arial',
    fontSize: 48,
    textColor: '#FFFFFF',
    aspectRatio: '9:16',
    videoStartTime: 0,
    videoEndTime: 30
  },
  
  UGC_VIDEO: {
    projectTitle: 'Test UGC Video',
    scriptTopic: 'Benefits of atomic credit systems',
    avatarChoice: 'professional-woman',
    voiceChoice: 'natural',
    aspectRatio: '9:16',
    userAssetUrls: [],
    templateId: 'AI_UGC_TALKING_HEAD'
  },
  
  NARRATOR_VIDEO: {
    title: 'Test Narrator Video',
    videoUrl: 'test-original-video-url',
    aspectRatio: '16:9',
    templateId: 'NARRATOR_TEMPLATE',
    voice: 'en-US-Wavenet-A',
    audioSpeed: 1.0,
    backgroundMusic: 'ambient'
  },
  
  REDDIT_VIDEO: {
    projectTitle: 'Test Reddit Video',
    redditPost: {
      title: 'TIL about atomic database transactions',
      content: 'Today I learned that atomic transactions ensure data consistency...',
      author: 'test_user',
      subreddit: 'todayilearned'
    },
    backgroundVideoUrls: ['test-bg-video-1', 'test-bg-video-2'],
    aspectRatio: '9:16',
    templateId: 'REDDIT_TEMPLATE'
  },
  
  TWITTER_VIDEO: {
    projectTitle: 'Test Twitter Video',
    twitterPost: {
      text: 'Just implemented atomic credit system! 🚀 #webdev #database',
      author: '@testuser',
      timestamp: new Date().toISOString()
    },
    backgroundVideoUrls: ['test-bg-video-1'],
    aspectRatio: '9:16',
    templateId: 'TWITTER_TEMPLATE'
  },
  
  STOCK_MEDIA_VIDEO: {
    projectTitle: 'Test Stock Media Video',
    userPrompt: 'Create a video about technology innovation',
    aspectRatio: '16:9',
    templateId: 'STOCK_MEDIA_TEMPLATE'
  }
};

/**
 * Test all video generation workflows
 */
async function testAllWorkflows() {
  console.log('🧪 Starting comprehensive video workflow tests...\n');
  
  const testUserId = 'test_user_' + Date.now();
  const results = {
    passed: 0,
    failed: 0,
    details: []
  };
  
  // Test each workflow type
  for (const [workflowType, config] of Object.entries(testConfigs)) {
    console.log(`\n📹 Testing ${workflowType} workflow...`);
    
    try {
      // Test cost calculation
      const cost = getCostForWorkflow(workflowType, config);
      console.log(`  💰 Cost calculation: ${cost} credits`);
      
      // Test workflow simulation (without actually triggering Inngest)
      const workflowTest = await simulateWorkflow(workflowType, config);
      
      if (workflowTest.success) {
        console.log(`  ✅ ${workflowType} workflow test passed`);
        results.passed++;
        results.details.push({
          workflow: workflowType,
          status: 'PASSED',
          cost: cost,
          details: workflowTest
        });
      } else {
        console.log(`  ❌ ${workflowType} workflow test failed: ${workflowTest.error}`);
        results.failed++;
        results.details.push({
          workflow: workflowType,
          status: 'FAILED',
          cost: cost,
          error: workflowTest.error
        });
      }
      
    } catch (error) {
      console.log(`  ❌ ${workflowType} workflow test failed: ${error.message}`);
      results.failed++;
      results.details.push({
        workflow: workflowType,
        status: 'FAILED',
        error: error.message
      });
    }
  }
  
  // Test credit system integrity
  console.log('\n🔍 Testing credit system integrity...');
  try {
    const integrityTest = await testCreditSystemIntegrity(testUserId);
    if (integrityTest.isConsistent) {
      console.log('  ✅ Credit system integrity test passed');
    } else {
      console.log('  ⚠️  Credit system integrity test found inconsistencies');
    }
  } catch (error) {
    console.log(`  ❌ Credit system integrity test failed: ${error.message}`);
  }
  
  // Print summary
  console.log('\n📊 Test Summary:');
  console.log(`  ✅ Passed: ${results.passed}`);
  console.log(`  ❌ Failed: ${results.failed}`);
  console.log(`  📈 Success Rate: ${((results.passed / (results.passed + results.failed)) * 100).toFixed(1)}%`);
  
  // Print detailed results
  console.log('\n📋 Detailed Results:');
  results.details.forEach(result => {
    const status = result.status === 'PASSED' ? '✅' : '❌';
    console.log(`  ${status} ${result.workflow}: ${result.cost} credits`);
    if (result.error) {
      console.log(`    Error: ${result.error}`);
    }
  });
  
  return results;
}

/**
 * Simulate a workflow without actually triggering Inngest
 */
async function simulateWorkflow(workflowType, config) {
  try {
    // Validate configuration
    const validationResult = validateWorkflowConfig(workflowType, config);
    if (!validationResult.valid) {
      return {
        success: false,
        error: `Configuration validation failed: ${validationResult.error}`
      };
    }
    
    // Test cost calculation
    const cost = getCostForWorkflow(workflowType, config);
    if (cost <= 0) {
      return {
        success: false,
        error: 'Invalid cost calculation'
      };
    }
    
    // Test event name generation
    const { getInngestEventName } = await import('../lib/atomicCreditSystem.js');
    const eventName = getInngestEventName(workflowType);
    if (!eventName) {
      return {
        success: false,
        error: 'Failed to generate event name'
      };
    }
    
    return {
      success: true,
      cost: cost,
      eventName: eventName,
      configValid: true
    };
    
  } catch (error) {
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Validate workflow configuration
 */
function validateWorkflowConfig(workflowType, config) {
  const requiredFields = {
    AI_VIDEO: ['topic', 'videoStyle', 'aspectRatio'],
    MEME_VIDEO: ['memeText', 'videoSource', 'aspectRatio'],
    UGC_VIDEO: ['avatarChoice', 'aspectRatio'],
    NARRATOR_VIDEO: ['videoUrl', 'aspectRatio'],
    REDDIT_VIDEO: ['redditPost', 'aspectRatio'],
    TWITTER_VIDEO: ['twitterPost', 'aspectRatio'],
    STOCK_MEDIA_VIDEO: ['userPrompt', 'aspectRatio']
  };
  
  const required = requiredFields[workflowType];
  if (!required) {
    return {
      valid: false,
      error: `Unknown workflow type: ${workflowType}`
    };
  }
  
  for (const field of required) {
    if (!config[field]) {
      return {
        valid: false,
        error: `Missing required field: ${field}`
      };
    }
  }
  
  return { valid: true };
}

/**
 * Test specific workflow type
 */
async function testSpecificWorkflow(workflowType) {
  if (!testConfigs[workflowType]) {
    console.error(`❌ Unknown workflow type: ${workflowType}`);
    return;
  }
  
  console.log(`🧪 Testing ${workflowType} workflow specifically...\n`);
  
  const config = testConfigs[workflowType];
  const result = await simulateWorkflow(workflowType, config);
  
  if (result.success) {
    console.log(`✅ ${workflowType} test passed!`);
    console.log(`  Cost: ${result.cost} credits`);
    console.log(`  Event: ${result.eventName}`);
  } else {
    console.log(`❌ ${workflowType} test failed: ${result.error}`);
  }
  
  return result;
}

// Export functions for use in other scripts
export { testAllWorkflows, testSpecificWorkflow, testConfigs };

// Run tests if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const workflowType = process.argv[2];
  
  if (workflowType && workflowType !== 'all') {
    testSpecificWorkflow(workflowType.toUpperCase());
  } else {
    testAllWorkflows();
  }
}
