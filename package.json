{"name": "ai-reel-gen", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio", "remotion:studio": "remotion studio remotion/index.ts", "remotion:render": "remotion render src/remotion/index.ts MyVideo out/video.mp4", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:unit": "jest --testPathPattern=tests/unit", "test:integration": "jest --testPathPattern=tests/integration --runInBand --detectOpenHandles", "test:integration:watch": "jest --testPathPattern=tests/integration --watch --runInBand", "test:integration:coverage": "jest --testPathPattern=tests/integration --coverage --runInBand", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:all": "npm run test && npm run test:e2e", "db:migrate:test": "drizzle-kit migrate --config=drizzle.test.config.ts", "db:seed:test": "node scripts/seedTestDatabase.js"}, "dependencies": {"@aws-sdk/client-s3": "^3.812.0", "@aws-sdk/s3-request-presigner": "^3.812.0", "@clerk/nextjs": "6.17.0", "@clerk/themes": "^2.2.40", "@deepgram/sdk": "^3.12.1", "@google-cloud/text-to-speech": "^6.0.1", "@google/generative-ai": "^0.24.0", "@hookform/resolvers": "^5.0.1", "@lemonsqueezy/lemonsqueezy.js": "^4.0.0", "@neondatabase/serverless": "^1.0.0", "@radix-ui/react-accordion": "^1.2.7", "@radix-ui/react-alert-dialog": "^1.1.10", "@radix-ui/react-aspect-ratio": "^1.1.4", "@radix-ui/react-avatar": "^1.1.6", "@radix-ui/react-checkbox": "^1.2.2", "@radix-ui/react-collapsible": "^1.1.7", "@radix-ui/react-context-menu": "^2.2.11", "@radix-ui/react-dialog": "^1.1.10", "@radix-ui/react-dropdown-menu": "^2.1.11", "@radix-ui/react-hover-card": "^1.1.10", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-menubar": "^1.1.11", "@radix-ui/react-navigation-menu": "^1.2.9", "@radix-ui/react-popover": "^1.1.10", "@radix-ui/react-progress": "^1.1.4", "@radix-ui/react-radio-group": "^1.3.3", "@radix-ui/react-scroll-area": "^1.2.5", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-separator": "^1.1.4", "@radix-ui/react-slider": "^1.3.2", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.2.2", "@radix-ui/react-tabs": "^1.1.8", "@radix-ui/react-toggle": "^1.1.6", "@radix-ui/react-toggle-group": "^1.1.7", "@radix-ui/react-tooltip": "^1.2.3", "@remotion/cli": "4.0.290", "@remotion/cloudrun": "4.0.290", "@remotion/lambda": "4.0.290", "@remotion/renderer": "4.0.290", "@runware/sdk-js": "^1.1.38", "@types/uuid": "^10.0.0", "axios": "^1.8.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "drizzle-orm": "^0.42.0", "embla-carousel-react": "^8.6.0", "express": "^5.1.0", "firebase": "^11.6.1", "form-data": "^4.0.2", "formidable": "^3.5.4", "framer-motion": "^12.7.4", "googleapis": "^149.0.0", "inngest": "^3.35.1", "input-otp": "^1.4.2", "lucide-react": "^0.503.0", "next": "15.3.1", "next-themes": "^0.4.6", "react": "^19.0.0", "react-day-picker": "8.10.1", "react-dom": "^19.0.0", "react-hook-form": "^7.56.0", "react-resizable-panels": "^2.1.8", "recharts": "^2.15.3", "remotion": "4.0.290", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "tw-animate-css": "^1.2.8", "uuid": "^11.1.0", "vaul": "^1.1.2", "zod": "3.24.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/react": "19.1.4", "drizzle-kit": "^0.31.0", "eslint": "^9", "eslint-config-next": "15.3.1", "inngest-cli": "^1.5.13", "tailwindcss": "^4", "tsx": "^4.19.3", "@playwright/test": "^1.48.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.1.0", "@testing-library/user-event": "^14.5.2", "@types/jest": "^29.5.14", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "msw": "^2.6.4"}}