import { useEffect, useState } from 'react';
import { useUser } from '@clerk/nextjs';

const useBilling = () => {
  const { user } = useUser();
  const [creditBalance, setCreditBalance] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchCreditBalance = async () => {
      if (!user?.id) {
        setLoading(false);
        return;
      }

      try {
        // Assuming user-videos route can also return credit balance or create a new route
        const response = await fetch(`/api/get-credits`);
        if (!response.ok) {
          throw new Error('Failed to fetch credit balance');
        }
        const data = await response.json();
        // Assuming the API response includes a credit balance field, e.g., data.creditBalance
        setCreditBalance(data.credits || 0); // Default to 0 if not found
      } catch (err) {
        console.error('Error fetching credit balance:', err);
        setError('Failed to load credit balance.');
      } finally {
        setLoading(false);
      }
    };

    fetchCreditBalance();
  }, [user]);

  const handlePurchase = async (priceId) => {
    if (!user?.id) {
      // Redirect to login or show a message
      alert('Please log in to make a purchase.');
      return;
    }

    try {
      const response = await fetch('/api/lemonsqueezy', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ priceId, userId: user.id }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to initiate checkout');
      }

      const data = await response.json();
      if (data.checkoutUrl) {
        // Redirect the user to the Lemon Squeezy checkout page
        window.location.href = data.checkoutUrl;
      } else {
        throw new Error('Checkout URL not received');
      }

    } catch (err) {
      console.error('Error during purchase:', err);
      alert(`Purchase failed: ${err.message}`); // Simple error handling
    }
  };

  return {
    creditBalance,
    loading,
    error,
    handlePurchase,
  };
};

export default useBilling;