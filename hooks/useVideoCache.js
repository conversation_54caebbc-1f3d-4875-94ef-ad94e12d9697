'use client';

import { useState, useCallback, useEffect } from 'react';
import { useVideoCacheContext } from '@/contexts/VideoCacheContext';

// Cache configuration
const CACHE_EXPIRY_MS = 8 * 60 * 1000; // 8 minutes
const POLLING_INTERVAL_MS = 15000; // 15 seconds

// Global cache storage - shared across all hook instances
const globalCache = new Map();
let globalPollingInterval = null;

/**
 * Custom hook for managing video data with caching
 * Provides caching, manual refresh, and automatic cache invalidation
 */
export function useVideoCache() {
  // Get cache context for global invalidation (with fallback)
  let registerInvalidationCallback;
  try {
    const context = useVideoCacheContext();
    registerInvalidationCallback = context.registerInvalidationCallback;
  } catch (error) {
    console.warn('[VideoCache] VideoCacheContext not available, using fallback');
    registerInvalidationCallback = () => () => {}; // No-op fallback
  }

  // Use global cache storage - shared across all hook instances
  // This ensures all components see the same cache data

  // Component state
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isRefreshing, setIsRefreshing] = useState(false);

  /**
   * Check if cached data is still valid
   */
  const isCacheValid = useCallback((cacheEntry) => {
    if (!cacheEntry) return false;
    return Date.now() < cacheEntry.expiry;
  }, []);

  /**
   * Get cache key for a page
   */
  const getCacheKey = useCallback((page, limit) => {
    return `page_${page}_limit_${limit}`;
  }, []);

  /**
   * Store data in cache
   */
  const setCacheData = useCallback((page, limit, data) => {
    const cacheKey = getCacheKey(page, limit);
    const cacheEntry = {
      data,
      timestamp: Date.now(),
      expiry: Date.now() + CACHE_EXPIRY_MS,
      page,
      limit
    };
    globalCache.set(cacheKey, cacheEntry);
    console.log(`[VideoCache] Cached data for ${cacheKey}, expires in ${CACHE_EXPIRY_MS / 1000}s`);
  }, [getCacheKey]);

  /**
   * Get data from cache
   */
  const getCacheData = useCallback((page, limit) => {
    const cacheKey = getCacheKey(page, limit);
    const cacheEntry = globalCache.get(cacheKey);

    console.log(`[VideoCache] getCacheData - key: ${cacheKey}, entry exists: ${!!cacheEntry}`);

    if (cacheEntry) {
      const isValid = isCacheValid(cacheEntry);
      console.log(`[VideoCache] Cache entry found - valid: ${isValid}, expiry: ${new Date(cacheEntry.expiry).toLocaleTimeString()}`);

      if (isValid) {
        console.log(`[VideoCache] ✅ Cache hit for ${cacheKey}`);
        return cacheEntry.data;
      } else {
        console.log(`[VideoCache] ⏰ Cache expired for ${cacheKey}, removing`);
        globalCache.delete(cacheKey);
      }
    } else {
      console.log(`[VideoCache] ❌ No cache entry found for ${cacheKey}`);
    }

    return null;
  }, [getCacheKey, isCacheValid]);

  /**
   * Clear all cache entries
   */
  const clearCache = useCallback(() => {
    console.log('[VideoCache] Clearing all cache entries');
    globalCache.clear();
  }, []);

  /**
   * Clear cache for specific page
   */
  const clearPageCache = useCallback((page, limit) => {
    const cacheKey = getCacheKey(page, limit);
    console.log(`[VideoCache] Clearing cache for ${cacheKey}`);
    globalCache.delete(cacheKey);
  }, [getCacheKey]);

  /**
   * Fetch videos from API
   */
  const fetchVideosFromAPI = useCallback(async (page, limit) => {
    console.log(`[VideoCache] Fetching videos from API for page: ${page}, limit: ${limit}`);

    try {
      const response = await fetch(`/api/user-videos?page=${page}&limit=${limit}`);

      // Check if response is ok
      if (!response.ok) {
        let errorData = {};
        try {
          // Try to parse error response as JSON
          const contentType = response.headers.get('content-type');
          if (contentType && contentType.includes('application/json')) {
            errorData = await response.json();
          } else {
            // If not JSON, get text content
            const errorText = await response.text();
            errorData = { error: errorText || `HTTP ${response.status}` };
          }
        } catch (parseError) {
          console.warn('[VideoCache] Failed to parse error response:', parseError);
          errorData = { error: `HTTP ${response.status} ${response.statusText}` };
        }

        console.error(`[VideoCache] API Error: ${response.status} ${response.statusText}`, errorData);
        throw new Error(`Failed to load videos: ${errorData.error || response.statusText} (${response.status})`);
      }

      // Parse successful response
      let data;
      try {
        const contentType = response.headers.get('content-type');
        if (!contentType || !contentType.includes('application/json')) {
          throw new Error('Response is not JSON');
        }
        data = await response.json();
      } catch (parseError) {
        console.error('[VideoCache] Failed to parse JSON response:', parseError);
        throw new Error('Invalid response format from server');
      }

      // Validate response structure
      if (!data || typeof data !== 'object') {
        throw new Error('Invalid API response structure: not an object');
      }

      if (!Array.isArray(data.videos)) {
        console.warn('[VideoCache] API response missing videos array, using empty array');
        data.videos = [];
      }

      return data;
    } catch (error) {
      // Re-throw with more context
      if (error.name === 'TypeError' && error.message.includes('fetch')) {
        throw new Error('Network error: Unable to connect to server');
      }
      throw error;
    }
  }, []);

  /**
   * Get videos with caching logic
   */
  const getVideos = useCallback(async (page, limit, forceRefresh = false) => {
    console.log(`[VideoCache] getVideos called - page: ${page}, limit: ${limit}, forceRefresh: ${forceRefresh}`);
    setError(null);

    // Check cache first (unless force refresh)
    if (!forceRefresh) {
      const cachedData = getCacheData(page, limit);
      if (cachedData) {
        console.log(`[VideoCache] ✅ Cache HIT - returning cached data for page ${page}`);
        return cachedData;
      } else {
        console.log(`[VideoCache] ❌ Cache MISS - no cached data for page ${page}`);
      }
    } else {
      console.log(`[VideoCache] 🔄 Force refresh - skipping cache for page ${page}`);
    }

    // Set loading state
    if (forceRefresh) {
      setIsRefreshing(true);
    } else {
      setLoading(true);
    }

    try {
      console.log(`[VideoCache] 🌐 Fetching from API - page: ${page}, limit: ${limit}`);
      const data = await fetchVideosFromAPI(page, limit);

      // Cache the response
      setCacheData(page, limit, data);
      console.log(`[VideoCache] 💾 Data cached for page ${page}`);

      return data;
    } catch (err) {
      console.error('[VideoCache] Error fetching videos:', err);
      setError(err.message || 'Failed to load videos. Please try again later.');
      throw err;
    } finally {
      setLoading(false);
      setIsRefreshing(false);
    }
  }, [getCacheData, setCacheData, fetchVideosFromAPI]);

  /**
   * Manual refresh - clears cache and refetches
   */
  const refreshVideos = useCallback(async (page, limit) => {
    console.log(`[VideoCache] Manual refresh requested for page ${page}`);
    clearPageCache(page, limit);
    return await getVideos(page, limit, true);
  }, [clearPageCache, getVideos]);

  /**
   * Refresh all cached data
   */
  const refreshAllVideos = useCallback(() => {
    console.log('[VideoCache] Manual refresh all requested');
    clearCache();
  }, [clearCache]);

  /**
   * Check if any videos need polling (for status updates)
   */
  const needsPolling = useCallback((videos) => {
    return videos && videos.some(video => 
      video.status === 'Pending' || video.status === 'Processing'
    );
  }, []);

  /**
   * Start polling for status updates
   */
  const startPolling = useCallback((page, limit, videos, onUpdate) => {
    // Clear existing polling
    if (globalPollingInterval) {
      clearInterval(globalPollingInterval);
    }

    if (!needsPolling(videos)) {
      console.log('[VideoCache] No videos need polling');
      return;
    }

    console.log('[VideoCache] Starting polling for status updates');
    globalPollingInterval = setInterval(async () => {
      try {
        // Force refresh to get latest status
        const data = await getVideos(page, limit, true);
        onUpdate(data);

        // Stop polling if no videos need it anymore
        if (!needsPolling(data.videos)) {
          console.log('[VideoCache] Stopping polling - no videos need updates');
          clearInterval(globalPollingInterval);
          globalPollingInterval = null;
        }
      } catch (error) {
        console.error('[VideoCache] Polling error:', error);
      }
    }, POLLING_INTERVAL_MS);
  }, [needsPolling, getVideos]);

  /**
   * Stop polling
   */
  const stopPolling = useCallback(() => {
    if (globalPollingInterval) {
      console.log('[VideoCache] Stopping polling');
      clearInterval(globalPollingInterval);
      globalPollingInterval = null;
    }
  }, []);

  /**
   * Get cache statistics for debugging
   */
  const getCacheStats = useCallback(() => {
    const stats = {
      totalEntries: globalCache.size,
      validEntries: 0,
      expiredEntries: 0,
      entries: []
    };

    globalCache.forEach((entry, key) => {
      const isValid = isCacheValid(entry);
      if (isValid) {
        stats.validEntries++;
      } else {
        stats.expiredEntries++;
      }

      stats.entries.push({
        key,
        page: entry.page,
        limit: entry.limit,
        timestamp: entry.timestamp,
        expiry: entry.expiry,
        isValid,
        videosCount: entry.data?.videos?.length || 0
      });
    });

    return stats;
  }, [isCacheValid]);

  // Register this cache instance for global invalidation
  useEffect(() => {
    const cleanup = registerInvalidationCallback(clearCache);
    return cleanup;
  }, [registerInvalidationCallback, clearCache]);

  return {
    // Data fetching
    getVideos,
    refreshVideos,
    refreshAllVideos,

    // Polling
    startPolling,
    stopPolling,
    needsPolling,

    // Cache management
    clearCache,
    clearPageCache,
    getCacheStats,

    // State
    loading,
    error,
    isRefreshing,
    setError
  };
}
